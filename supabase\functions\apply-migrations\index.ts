import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

serve(async (req) => {
  // Handle CORS preflight request
  if (req.method === "OPTIONS") {
    return new Response(null, {
      headers: corsHeaders,
      status: 204,
    });
  }

  // Check authorization
  const authHeader = req.headers.get('Authorization');
  if (!authHeader) {
    return new Response(JSON.stringify({ error: 'No authorization header' }), {
      status: 401,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }

  // Create a Supabase client with the auth header
  const supabase = createClient(
    Deno.env.get("SUPABASE_URL") ?? "",
    Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? "",
    { global: { headers: { Authorization: authHeader } } }
  );

  // Verify the user has admin access
  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    return new Response(JSON.stringify({ error: "Not authenticated" }), {
      status: 401,
      headers: { ...corsHeaders, "Content-Type": "application/json" },
    });
  }

  // Check if the user is an admin
  if (user.user_metadata?.role !== "admin") {
    return new Response(JSON.stringify({ error: "Not authorized. Admin access required." }), {
      status: 403,
      headers: { ...corsHeaders, "Content-Type": "application/json" },
    });
  }

  // Fix movies_with_prompts view
  const updateViewQuery = `
  -- Drop the existing view if it exists
  DROP VIEW IF EXISTS public.movies_with_prompts;

  -- Recreate the view with fixed movie_generation_stats column names
  CREATE OR REPLACE VIEW public.movies_with_prompts AS
  SELECT
      m.id,
      m.title,
      m.streaming_platform,
      m.is_active,
      m.created_at,
      m.updated_at,
      COALESCE(m.default_creativity_level, 'Equilibrado') AS default_creativity_level,
      
      -- Base prompt
      COALESCE(
          m.base_prompt_override,
          (SELECT pt.template_text FROM prompt_templates pt WHERE pt.id = m.base_prompt_template_id)
      ) AS base_prompt,
      
      -- Safe prompt
      COALESCE(
          m.safe_prompt_override,
          (SELECT pt.template_text FROM prompt_templates pt WHERE pt.id = m.safe_prompt_template_id)
      ) AS safe_prompt,
      
      -- Male prompt
      COALESCE(
          m.gender_male_prompt_override,
          (SELECT pt.template_text FROM prompt_templates pt WHERE pt.id = m.gender_male_prompt_template_id)
      ) AS gender_male_prompt,
      
      -- Female prompt
      COALESCE(
          m.gender_female_prompt_override,
          (SELECT pt.template_text FROM prompt_templates pt WHERE pt.id = m.gender_female_prompt_template_id)
      ) AS gender_female_prompt,
      
      -- Couple prompt
      COALESCE(
          m.couple_prompt_override,
          (SELECT pt.template_text FROM prompt_templates pt WHERE pt.id = m.couple_prompt_template_id)
      ) AS couple_prompt,
      
      -- Template names for reference
      (SELECT pt.name FROM prompt_templates pt WHERE pt.id = m.base_prompt_template_id) AS base_template_name,
      (SELECT pt.name FROM prompt_templates pt WHERE pt.id = m.safe_prompt_template_id) AS safe_template_name,
      (SELECT pt.name FROM prompt_templates pt WHERE pt.id = m.gender_male_prompt_template_id) AS male_template_name,
      (SELECT pt.name FROM prompt_templates pt WHERE pt.id = m.gender_female_prompt_template_id) AS female_template_name,
      (SELECT pt.name FROM prompt_templates pt WHERE pt.id = m.couple_prompt_template_id) AS couple_template_name,
      
      -- Original template IDs and variables
      m.base_prompt_template_id,
      m.base_prompt_variables,
      m.safe_prompt_template_id,
      m.safe_prompt_variables,
      m.gender_male_prompt_template_id,
      m.gender_male_prompt_variables,
      m.gender_female_prompt_template_id,
      m.gender_female_prompt_variables,
      m.couple_prompt_template_id,
      m.couple_prompt_variables,
      
      -- Stats linking
      m.id as movie_id
  FROM
      public.movies m;
  `;

  try {
    // Apply the view update
    const { error } = await supabase.rpc('exec_sql', { sql: updateViewQuery });
    
    if (error) {
      console.error('Error updating view:', error);
      return new Response(JSON.stringify({ error: error.message }), {
        status: 500,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      });
    }
    
    return new Response(JSON.stringify({ 
      success: true, 
      message: "movies_with_prompts view updated successfully" 
    }), {
      status: 200,
      headers: { ...corsHeaders, "Content-Type": "application/json" },
    });
  } catch (error) {
    console.error('Error:', error);
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, "Content-Type": "application/json" },
    });
  }
});
