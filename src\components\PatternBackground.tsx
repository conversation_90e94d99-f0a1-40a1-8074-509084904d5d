import { useEffect, useState } from 'react';

const PatternBackground = () => {
  const [currentPattern, setCurrentPattern] = useState('');

  useEffect(() => {
    // Função para selecionar um padrão aleatório
    const selectRandomPattern = () => {
      const patternCount = 5; // Número de padrões disponíveis
      const randomIndex = Math.floor(Math.random() * patternCount);
      const patternPath = `/img/pattern/pattern_${randomIndex === 0 ? '0' : ` ${randomIndex + 1}`}.png`;
      setCurrentPattern(patternPath);
      
      // Agendar próxima mudança para a próxima hora
      const now = new Date();
      const minutesToNextHour = 60 - now.getMinutes();
      const timeout = setTimeout(selectRandomPattern, minutesToNextHour * 60 * 1000);
      
      return () => clearTimeout(timeout);
    };

    // Iniciar o ciclo de mudança de padrão
    selectRandomPattern();
  }, []);

  if (!currentPattern) return null;

  return (
    <div 
      className="fixed inset-0 -z-10"
      style={{
        backgroundImage: `url(${currentPattern})`,
        backgroundSize: '400px',
        backgroundPosition: 'center',
        backgroundRepeat: 'repeat',
        opacity: 0.25,
        pointerEvents: 'none',
      }}
    />
  );
};

export default PatternBackground;
