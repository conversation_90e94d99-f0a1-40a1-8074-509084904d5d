import React from 'react';
import CanvaPosterGenerator from '../components/CanvaPosterGenerator';
import { useTranslation } from 'react-i18next';

const CanvaPosterPage: React.FC = () => {
  const { t } = useTranslation();

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="text-center mb-12">
        <h1 className="text-5xl font-black text-brand-text mb-2">
          {t('canvaPosterPage.title')}
        </h1>
        <p className="text-lg text-brand-text/80">
          {t('canvaPosterPage.subtitle')}
        </p>
      </div>
      <div className="max-w-5xl mx-auto bg-brand-white rounded-xl shadow-brutal-lg border-2 border-brand-black p-6 md:p-10">
        <CanvaPosterGenerator />
      </div>
    </div>
  );
};

export default CanvaPosterPage;
