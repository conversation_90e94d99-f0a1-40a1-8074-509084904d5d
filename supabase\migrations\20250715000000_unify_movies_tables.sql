-- Cria a tabela unificada se não existir
CREATE TABLE IF NOT EXISTS unified_movies_series (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title TEXT NOT NULL,
    streaming_platform TEXT NOT NULL,
    type TEXT NOT NULL DEFAULT 'movie', -- 'movie' ou 'series'
    year INTEGER,
    genre TEXT,
    poster_style_prompt TEXT,
    base_prompt TEXT,
    safe_prompt TEXT,
    gender_male_prompt TEXT,
    gender_female_prompt TEXT,
    couple_prompt TEXT,
    is_active BOOLEAN DEFAULT true,
    default_creativity_level_id UUID REFERENCES creativity_levels(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    -- <PERSON>s adicionais de estatísticas
    total_attempts INTEGER DEFAULT 0,
    successful_attempts INTEGER DEFAULT 0,
    failed_attempts INTEGER DEFAULT 0,
    e005_errors INTEGER DEFAULT 0,
    success_rate NUMERIC(5, 2) DEFAULT 0,
    last_attempt_at TIMESTAMP WITH TIME ZONE,
    last_error_message TEXT
);

-- <PERSON><PERSON> índices para melhorar o desempenho
CREATE INDEX IF NOT EXISTS idx_unified_movies_platform ON unified_movies_series(streaming_platform);
CREATE INDEX IF NOT EXISTS idx_unified_movies_active ON unified_movies_series(is_active);
CREATE INDEX IF NOT EXISTS idx_unified_movies_type ON unified_movies_series(type);

-- Habilita RLS (Row Level Security) na tabela unificada
ALTER TABLE unified_movies_series ENABLE ROW LEVEL SECURITY;

-- Políticas de RLS para a tabela unificada
CREATE POLICY "Admin can manage unified_movies_series" ON unified_movies_series
    FOR ALL
    USING (auth.role() = 'authenticated' AND auth.uid() IN (
        SELECT id FROM auth.users WHERE raw_user_meta_data->>'role' = 'admin'
    ));

CREATE POLICY "Users can read active unified_movies_series" ON unified_movies_series
    FOR SELECT
    USING (is_active = true);

-- Atualiza a view movies_with_prompts para usar a tabela unificada
CREATE OR REPLACE VIEW public.movies_with_prompts AS
SELECT 
    m.id,
    m.title,
    m.streaming_platform,
    m.type,
    m.year,
    m.genre,
    m.is_active,
    COALESCE(m.base_prompt, '') as base_prompt,
    COALESCE(m.safe_prompt, '') as safe_prompt,
    COALESCE(m.gender_male_prompt, '') as gender_male_prompt,
    COALESCE(m.gender_female_prompt, '') as gender_female_prompt,
    COALESCE(m.couple_prompt, '') as couple_prompt,
    m.default_creativity_level_id,
    cl.name as default_creativity_level_name,
    cl.prompt_enhancement as default_creativity_prompt_enhancement,
    cl.temperature as default_creativity_temperature,
    m.total_attempts,
    m.successful_attempts,
    m.failed_attempts,
    m.e005_errors,
    m.success_rate,
    m.last_attempt_at,
    m.last_error_message,
    m.created_at,
    m.updated_at
FROM 
    unified_movies_series m
LEFT JOIN 
    creativity_levels cl ON m.default_creativity_level_id = cl.id;

-- Atualiza a função de atualização de estatísticas para usar a tabela unificada
CREATE OR REPLACE FUNCTION public.update_movie_generation_stats(
    p_movie_id UUID,
    p_success BOOLEAN,
    p_error_code TEXT DEFAULT NULL
) RETURNS VOID AS $$
DECLARE
    movie_record RECORD;
    new_success_rate NUMERIC(5, 2);
    error_count INTEGER;
BEGIN
    -- Obtém o registro atual do filme
    SELECT * INTO movie_record FROM unified_movies_series WHERE id = p_movie_id;
    
    -- Atualiza as estatísticas
    UPDATE unified_movies_series
    SET 
        total_attempts = COALESCE(total_attempts, 0) + 1,
        successful_attempts = CASE 
            WHEN p_success THEN COALESCE(successful_attempts, 0) + 1 
            ELSE COALESCE(successful_attempts, 0) 
        END,
        failed_attempts = CASE 
            WHEN NOT p_success THEN COALESCE(failed_attempts, 0) + 1 
            ELSE COALESCE(failed_attempts, 0) 
        END,
        e005_errors = CASE 
            WHEN p_error_code = 'E005' THEN COALESCE(e005_errors, 0) + 1 
            ELSE COALESCE(e005_errors, 0) 
        END,
        last_attempt_at = NOW(),
        last_error_message = CASE 
            WHEN NOT p_success AND p_error_code IS NOT NULL THEN p_error_code 
            ELSE last_error_message 
        END,
        updated_at = NOW()
    WHERE id = p_movie_id
    RETURNING 
        COALESCE(total_attempts, 0) as total,
        COALESCE(successful_attempts, 0) as success,
        COALESCE(failed_attempts, 0) as failed
    INTO movie_record;
    
    -- Calcula a nova taxa de sucesso
    IF movie_record.total > 0 THEN
        new_success_rate := (movie_record.success::NUMERIC / movie_record.total::NUMERIC) * 100;
    ELSE
        new_success_rate := 0;
    END IF;
    
    -- Atualiza a taxa de sucesso
    UPDATE unified_movies_series
    SET success_rate = ROUND(new_success_rate, 2)
    WHERE id = p_movie_id;
    
    -- Atualiza as estatísticas de geração
    INSERT INTO movie_generation_stats (movie_id, success, error_code, created_at)
    VALUES (p_movie_id, p_success, p_error_code, NOW())
    ON CONFLICT DO NOTHING;
    
    -- Se houver muitas tentativas com erro E005, desativa o filme
    IF p_error_code = 'E005' THEN
        SELECT COUNT(*) INTO error_count
        FROM movie_generation_stats
        WHERE movie_id = p_movie_id 
        AND error_code = 'E005' 
        AND created_at > (NOW() - INTERVAL '24 hours');
        
        IF error_count >= 3 THEN
            UPDATE unified_movies_series
            SET is_active = false, updated_at = NOW()
            WHERE id = p_movie_id;
        END IF;
    END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
