import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { supabase } from '../lib/supabase';
import { showToast } from '../utils/toast';
import { Loader2, Play, Settings, Copy, Download, ArrowLeft, Eye, EyeOff, Image, X } from 'lucide-react';
import BaseImageSelector from '../components/BaseImageSelector';

interface Movie {
  id: string;
  title: string;
  streaming_platform: string;
}

interface CreativityLevel {
  id: number;
  name: string;
  prompt_text: string;
}

interface TestParams {
  originalImageUrl: string;
  photoType: 'individual' | 'casal';
  gender: 'male' | 'female';
  streamingPlatform: 'netflix' | 'disney' | 'amazon';
  generateTitles: boolean;
  creativityLevelId?: number; // Changed from string to number ID
  customPrompt?: string;
  selectedMovie?: string;
  useCustomPrompt: boolean;
  selectedBaseImage?: BaseImage;
}

interface BaseImage {
  id: string;
  original_image_url: string;
  file_name: string;
  file_size: number;
  content_type: string;
  photo_type: 'individual' | 'casal';
  streaming_platform: 'netflix' | 'disney' | 'amazon';
  created_at: string;
  user_id: string;
}

interface TestResult {
  id: number;
  timestamp: string;
  params: TestParams;
  prompt: string;
  result: any;
  success: boolean;
  images: { url: string; title: string }[];
  promptInfo: any;
  error?: string;
}

export default function AdminPromptTester() {
  const navigate = useNavigate();
  const [results, setResults] = useState<TestResult[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationProgress, setGenerationProgress] = useState({ current: 0, total: 0 });
  const [currentGeneratingMovie, setCurrentGeneratingMovie] = useState('');
  const [showImageModal, setShowImageModal] = useState(false);
  const [movies, setMovies] = useState<Movie[]>([]);
  const [loadingMovies, setLoadingMovies] = useState(false);
  const [creativityLevels, setCreativityLevels] = useState<CreativityLevel[]>([]);
  const [loading, setLoading] = useState(true);
  const [user, setUser] = useState<any>(null);
  const [showPromptPreview, setShowPromptPreview] = useState(false);
  const [aspectRatio, setAspectRatio] = useState<string>('3:4');
  const [currentMoviePrompt, setCurrentMoviePrompt] = useState<string>('');
  const [selectedImageUrl, setSelectedImageUrl] = useState<string>('');
  const [showImageViewer, setShowImageViewer] = useState(false);

  const [params, setParams] = useState<TestParams>({
    originalImageUrl: '',
    photoType: 'individual',
    gender: 'male',
    streamingPlatform: 'netflix',
    generateTitles: false,
    creativityLevelId: undefined, // Default to undefined
    customPrompt: '',
    selectedMovie: '',
    useCustomPrompt: false,
    selectedBaseImage: undefined
  });

  const aspectRatios = [
    { value: '1:1', label: '1:1 (Quadrado)' },
    { value: '3:4', label: '3:4 (Retrato)' },
    { value: '4:3', label: '4:3 (Paisagem)' },
    { value: '5:4', label: '5:4 (Quase Quadrado)' },
    { value: '16:9', label: '16:9 (Widescreen)' },
    { value: '21:9', label: '21:9 (Ultra Wide)' },
    { value: '9:16', label: '9:16 (Vertical Stories)' },
    { value: '9:21', label: '9:21 (Ultra Vertical)' },
    { value: '2:3', label: '2:3 (Retrato Clássico)' },
    { value: '3:2', label: '3:2 (Paisagem Clássica)' }
  ];

  useEffect(() => {
    const checkAuthAndLoadData = async () => {
      setLoading(true);
      try {
        const { data: { session } } = await supabase.auth.getSession();
        if (session?.user) {
          setUser(session.user);
          if (session.user.user_metadata?.role !== 'admin') {
            showToast.error('Acesso negado. Apenas administradores podem acessar esta página.');
            navigate('/dashboard');
            return;
          }
          // Load initial data
          await Promise.all([
            getMoviesForPlatform(params.streamingPlatform),
            getCreativityLevels()
          ]);
        } else {
          navigate('/');
        }
      } catch (error) {
        console.error('Error checking auth:', error);
        navigate('/');
      } finally {
        setLoading(false);
      }
    };
    checkAuthAndLoadData();
  }, [navigate]);

  useEffect(() => {
    if (params.streamingPlatform) {
      getMoviesForPlatform(params.streamingPlatform);
    }
  }, [params.streamingPlatform]);

  useEffect(() => {
    if (params.selectedMovie) {
      fetchMoviePrompt(params.selectedMovie);
    } else {
      setCurrentMoviePrompt('');
    }
  }, [params.selectedMovie, params.gender, params.photoType, params.creativityLevelId, params.generateTitles]);

  const getMoviesForPlatform = async (platform: string) => {
    setLoadingMovies(true);
    try {
      const { data, error } = await supabase
        .from('movies_with_prompts')
        .select('id, title, streaming_platform')
        .eq('streaming_platform', platform)
        .eq('is_active', true)
        .order('title', { ascending: true });

      if (error) throw error;
      setMovies(data || []);
    } catch (error) {
      console.error('Error fetching movies:', error);
      showToast.error('Erro ao carregar filmes da plataforma.');
    } finally {
      setLoadingMovies(false);
    }
  };

  const getCreativityLevels = async () => {
    try {
      const { data, error } = await supabase
        .from('creativity_levels')
        .select('id, name, prompt_enhancement')
        .order('id', { ascending: true });
        
      if (error) throw error;
      
      // Mapeie prompt_enhancement para prompt_text para manter compatibilidade com o código existente
      const mappedData = data?.map(level => ({
        ...level,
        prompt_text: level.prompt_enhancement
      })) || [];
      
      setCreativityLevels(mappedData);
      // Set default creativity level if not already set
      if (!params.creativityLevelId && mappedData && mappedData.length > 0) {
        const defaultLevelId = mappedData[1]?.id || mappedData[0]?.id;
        console.log('Definindo nível de criatividade padrão:', defaultLevelId);
        setParams(prev => ({ ...prev, creativityLevelId: defaultLevelId }));
      }
    } catch (error) {
      console.error('Error fetching creativity levels:', error);
      showToast.error('Erro ao carregar níveis de criatividade.');
    }
  };

  const fetchMoviePrompt = async (movieTitle: string) => {
    try {
      const { data, error } = await supabase
        .from('movies_with_prompts')
        .select('base_prompt, gender_male_prompt, gender_female_prompt, couple_prompt, safe_prompt')
        .eq('title', movieTitle)
        .single();
      
      if (error) throw error;
      
      if (data) {
        let promptToUse = '';
        
        if (params.photoType === 'casal') {
          promptToUse = data.couple_prompt || data.base_prompt;
        } else {
          // Individual
          if (params.gender === 'male') {
            promptToUse = data.gender_male_prompt || data.base_prompt;
          } else {
            promptToUse = data.gender_female_prompt || data.base_prompt;
          }
        }
        
        console.log('Prompt selecionado:', promptToUse);
        setCurrentMoviePrompt(promptToUse || '');
      } else {
        console.log('Nenhum prompt encontrado para o filme:', movieTitle);
        setCurrentMoviePrompt('');
      }
    } catch (error) {
      console.error('Error fetching movie prompt:', error);
      setCurrentMoviePrompt('');
    }
  };

  const generateFinalPrompt = () => {
    console.log('Gerando prompt com creativityLevelId:', params.creativityLevelId);
    const creativityLevel = creativityLevels.find(cl => cl.id === params.creativityLevelId);
    console.log('Nível de criatividade encontrado:', creativityLevel);
    
    const creativityText = creativityLevel ? creativityLevel.prompt_text : '';
    const creativityName = creativityLevel?.name || 'N/A';
    const titlesText = params.generateTitles ? ' Inclua o título da série/filme na imagem de forma criativa e bem integrada.' : '';
    
    if (params.useCustomPrompt && params.customPrompt) {
        return (creativityText ? creativityText + ' ' : '') + params.customPrompt + titlesText;
    }
    
    if (params.selectedMovie) {      
      if (currentMoviePrompt) {
        // Combina o prompt do filme com o texto de criatividade
        const finalPrompt = `${creativityText ? creativityText + ' ' : ''}${currentMoviePrompt}${titlesText}`;
        console.log('Prompt final gerado:', finalPrompt);
        return finalPrompt + ` (Nível de criatividade: ${creativityName})`;
      } else {
        return `Carregando prompt para "${params.selectedMovie}"... (Nível de criatividade: ${creativityName})`;
      }
    }
    
    return 'Selecione um filme ou use um prompt customizado para ver a prévia.';
  };

  const handleGenerate = async () => {
    if (!params.selectedBaseImage && !params.originalImageUrl) {
      showToast.error('Por favor, insira uma URL de imagem ou selecione uma imagem base');
      return;
    }

    if (!params.useCustomPrompt && !params.selectedMovie) {
      showToast.error('Por favor, selecione um filme ou use um prompt customizado');
      return;
    }
    
    if (!params.creativityLevelId) {
      showToast.error('Por favor, selecione um nível de criatividade.');
      return;
    }

    setIsGenerating(true);
    
    try {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) throw new Error('Não autenticado');

      const finalPrompt = params.useCustomPrompt ? params.customPrompt : undefined;
      
      setGenerationProgress({ current: 0, total: 1 });
      setCurrentGeneratingMovie(params.selectedMovie || 'Teste Customizado');
      
      showToast.loading(`Iniciando geração de teste...`);
      
      const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/generate-single-cover`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          originalImageUrl: params.selectedBaseImage?.original_image_url || params.originalImageUrl,
          photoType: params.photoType,
          gender: params.gender,
          streamingPlatform: params.streamingPlatform,
          generateTitles: params.generateTitles,
          creativityLevelId: params.creativityLevelId, // Send ID instead of string
          userId: session.user.id,
          movieTitle: params.selectedMovie || 'Custom Test',
          customPrompt: finalPrompt,
          testMode: true,
          aspectRatio: aspectRatio,
          useBackup: true 
        })
      });

      const result = await response.json();
      
      if (!response.ok) {
        throw new Error(result.error || 'Erro na geração');
      }

      const newResult = {
        id: Date.now(),
        timestamp: new Date().toISOString(),
        params: { ...params, aspectRatio, creativityLevelName: creativityLevels.find(cl => cl.id === params.creativityLevelId)?.name },
        prompt: result.promptInfo?.lastPromptTried || finalPrompt || 'Prompt gerado no servidor',
        result: result,
        success: result.success || false,
        images: result.success ? [{ 
          url: result.imageUrl || result.image_url, 
          title: result.movieTitle || params.selectedMovie || 'Teste'
        }] : [],
        promptInfo: result.promptInfo || null
      };

      setResults(prev => [newResult, ...prev]);
      setGenerationProgress({ current: 1, total: 1 });
      
      if (result.success) {
        showToast.success(`Geração concluída com sucesso!`);
      } else {
        showToast.error(`Erro na geração: ${result.error || 'Erro desconhecido'}`);
      }

    } catch (error) {
      console.error('Error generating:', error);
      const errorResult = {
        id: Date.now(),
        timestamp: new Date().toISOString(),
        params: { ...params },
        prompt: generateFinalPrompt(),
        result: null,
        promptInfo: null,
        error: error instanceof Error ? error.message : 'Erro desconhecido',
        success: false,
        images: []
      };
      setResults(prev => [errorResult, ...prev]);
      showToast.error('Erro na geração: ' + (error instanceof Error ? error.message : 'Erro desconhecido'));
    } finally {
      setIsGenerating(false);
      setGenerationProgress({ current: 0, total: 0 });
      setCurrentGeneratingMovie('');
    }
  };

  const copyPrompt = (text: string) => {
    navigator.clipboard.writeText(text);
    showToast.success('Prompt copiado para área de transferência!');
  };

  const downloadImage = async (imageUrl: string, filename: string) => {
    try {
      const response = await fetch(imageUrl);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      showToast.success('Download iniciado!');
    } catch (error) {
      console.error('Error downloading image:', error);
      showToast.error('Erro ao fazer download da imagem');
    }
  };
  
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Loader2 className="w-12 h-12 text-blue-500 animate-spin" />
      </div>
    );
  }

  const finalPrompt = generateFinalPrompt();

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => navigate('/admin')}
                className="flex items-center space-x-2 px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
              >
                <ArrowLeft className="w-4 h-4" />
                <span>Voltar</span>
              </button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Admin - Testador de Prompts</h1>
                <p className="text-gray-600">Use os dados de produção para testes precisos.</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Settings className="w-5 h-5 text-gray-500" />
              <span className="text-sm text-gray-600">Modo Admin</span>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
          {/* Configuration Panel */}
          <div className="xl:col-span-2 space-y-6">
            {/* Basic Parameters */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h2 className="text-lg font-semibold mb-4">Parâmetros Básicos</h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Tipo de Foto
                  </label>
                  <select
                    value={params.photoType}
                    onChange={(e) => setParams(prev => ({ ...prev, photoType: e.target.value as 'individual' | 'casal' }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="individual">Individual</option>
                    <option value="casal">Casal</option>
                  </select>
                </div>

                {params.photoType === 'individual' && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Gênero
                    </label>
                    <select
                      value={params.gender}
                      onChange={(e) => setParams(prev => ({ ...prev, gender: e.target.value as 'male' | 'female' }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="male">Masculino</option>
                      <option value="female">Feminino</option>
                    </select>
                  </div>
                )}

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Plataforma de Streaming
                  </label>
                  <select
                    value={params.streamingPlatform}
                    onChange={(e) => setParams(prev => ({ ...prev, streamingPlatform: e.target.value as 'netflix' | 'disney' | 'amazon', selectedMovie: '' }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="netflix">Netflix</option>
                    <option value="disney">Disney+</option>
                    <option value="amazon">Amazon Prime</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Aspect Ratio
                  </label>
                  <select
                    value={aspectRatio}
                    onChange={(e) => setAspectRatio(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    {aspectRatios.map((ratio) => (
                      <option key={ratio.value} value={ratio.value}>
                        {ratio.label}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Nível de Criatividade
                  </label>
                  <select
                    value={params.creativityLevelId || ''}
                    onChange={(e) => {
                      const value = e.target.value ? parseInt(e.target.value) : undefined;
                      console.log('Selecionando nível de criatividade:', value);
                      setParams(prev => ({ ...prev, creativityLevelId: value }));
                    }}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    disabled={creativityLevels.length === 0}
                  >
                    <option value="">{creativityLevels.length === 0 ? 'Carregando...' : 'Selecione um nível'}</option>
                    {creativityLevels.map(level => (
                      <option key={level.id} value={level.id}>{level.name}</option>
                    ))}
                  </select>
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="generateTitles"
                    checked={params.generateTitles}
                    onChange={(e) => setParams(prev => ({ ...prev, generateTitles: e.target.checked }))}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="generateTitles" className="ml-2 block text-sm text-gray-700">
                    Gerar Títulos
                  </label>
                </div>
              </div>
            </div>

            {/* Prompt Configuration */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h2 className="text-lg font-semibold mb-4">Configuração do Prompt</h2>
              
              <div className="space-y-4">
                <div className="flex items-center space-x-4">
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="promptType"
                      checked={!params.useCustomPrompt}
                      onChange={() => setParams(prev => ({ ...prev, useCustomPrompt: false }))}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                    />
                    <span className="ml-2 text-sm text-gray-700">Usar prompt existente</span>
                  </label>
                  
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="promptType"
                      checked={params.useCustomPrompt}
                      onChange={() => setParams(prev => ({ ...prev, useCustomPrompt: true }))}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                    />
                    <span className="ml-2 text-sm text-gray-700">Prompt customizado</span>
                  </label>
                </div>

                {!params.useCustomPrompt ? (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Selecionar Filme/Série
                    </label>
                    <select
                      value={params.selectedMovie}
                      onChange={(e) => setParams(prev => ({ ...prev, selectedMovie: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      disabled={loadingMovies}
                    >
                      <option value="">{loadingMovies ? 'Carregando...' : 'Selecione um filme/série'}</option>
                      {movies.map(movie => (
                        <option key={movie.id} value={movie.title}>{movie.title}</option>
                      ))}
                    </select>
                  </div>
                ) : (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Prompt Customizado
                    </label>
                    <textarea
                      value={params.customPrompt}
                      onChange={(e) => setParams(prev => ({ ...prev, customPrompt: e.target.value }))}
                      rows={6}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Digite seu prompt customizado aqui..."
                    />
                  </div>
                )}
              </div>
            </div>

            {/* Base Image Selection */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h2 className="text-lg font-semibold mb-4">Imagem Base</h2>
              
              <div className="space-y-4">
                {/* URL Input */}
                <div>
                  <label htmlFor="baseImageUrl" className="block text-sm font-medium text-gray-700 mb-1">
                    URL da Imagem Base
                  </label>
                  <input
                    type="url"
                    id="baseImageUrl"
                    value={params.originalImageUrl}
                    onChange={(e) => setParams(prev => ({ ...prev, originalImageUrl: e.target.value, selectedBaseImage: undefined }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="https://exemplo.com/imagem.jpg"
                  />
                </div>

                {/* OR Divider */}
                <div className="flex items-center">
                  <div className="flex-1 border-t border-gray-300"></div>
                  <span className="px-3 text-sm text-gray-500">OU</span>
                  <div className="flex-1 border-t border-gray-300"></div>
                </div>

                {/* Base Image Selector */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Escolher Imagem Base Uploadada
                  </label>
                  {params.selectedBaseImage ? (
                    <div className="border border-gray-300 rounded-md p-3 bg-blue-50">
                      <div className="flex items-center space-x-3">
                        <img
                          src={params.selectedBaseImage.original_image_url}
                          alt="Imagem selecionada"
                          className="w-16 h-16 object-cover rounded"
                        />
                        <div className="flex-1">
                          <p className="text-sm font-medium text-gray-700">
                            {params.selectedBaseImage.file_name}
                          </p>
                          <p className="text-xs text-gray-500">
                            {params.selectedBaseImage.photo_type} • {params.selectedBaseImage.streaming_platform}
                          </p>
                        </div>
                        <button
                          onClick={() => setParams(prev => ({ ...prev, selectedBaseImage: undefined }))}
                          className="text-red-600 hover:text-red-800"
                        >
                          <X className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  ) : (
                    <button
                      onClick={() => setShowImageModal(true)}
                      className="w-full px-4 py-3 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors flex items-center justify-center space-x-2"
                    >
                      <Image className="w-5 h-5" />
                      <span>Selecionar Imagem Base</span>
                    </button>
                  )}
                </div>
              </div>
            </div>

            {/* Prompt Preview */}
            {finalPrompt && (
              <div className="bg-white rounded-lg shadow-sm p-6">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-lg font-semibold">Preview do Prompt Final</h2>
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => copyPrompt(finalPrompt)}
                      className="flex items-center space-x-1 px-3 py-1 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
                    >
                      <Copy className="w-4 h-4" />
                      <span className="text-sm">Copiar</span>
                    </button>
                    <button
                      onClick={() => setShowPromptPreview(!showPromptPreview)}
                      className="flex items-center space-x-1 px-3 py-1 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
                    >
                      {showPromptPreview ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                      <span className="text-sm">{showPromptPreview ? 'Ocultar' : 'Mostrar'}</span>
                    </button>
                  </div>
                </div>
                
                {showPromptPreview && (
                  <div className="bg-gray-50 rounded-md p-4">
                    <pre className="text-sm text-gray-700 whitespace-pre-wrap">{finalPrompt}</pre>
                  </div>
                )}
              </div>
            )}

            {/* Generate Button */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <button
                onClick={handleGenerate}
                disabled={isGenerating || (!params.selectedBaseImage && !params.originalImageUrl) || (!params.useCustomPrompt && !params.selectedMovie)}
                className="w-full flex items-center justify-center space-x-2 px-6 py-3 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed text-white font-medium rounded-md transition-colors"
              >
                {isGenerating ? (
                  <>
                    <Loader2 className="w-5 h-5 animate-spin" />
                    <span>Gerando...</span>
                  </>
                ) : (
                  <>
                    <Play className="w-5 h-5" />
                    <span>Gerar Teste</span>
                  </>
                )}
              </button>

              {/* Generation Progress */}
              {isGenerating && generationProgress.total > 0 && (
                <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-blue-800">Progresso da Geração</span>
                    <span className="text-sm text-blue-600">
                      {generationProgress.current} / {generationProgress.total}
                    </span>
                  </div>
                  <div className="w-full bg-blue-200 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${(generationProgress.current / generationProgress.total) * 100}%` }}
                    />
                  </div>
                  {currentGeneratingMovie && (
                    <p className="text-xs text-blue-600 mt-2">
                      Gerando: {currentGeneratingMovie}
                    </p>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Results Panel */}
          <div className="xl:col-span-1">
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h2 className="text-lg font-semibold mb-4">Resultados dos Testes</h2>
              
              {results.length === 0 ? (
                <div className="text-center py-8">
                  <div className="text-gray-400 mb-2">
                    <Play className="w-12 h-12 mx-auto" />
                  </div>
                  <p className="text-gray-500">Nenhum teste executado ainda</p>
                </div>
              ) : (
                <div className="space-y-4 max-h-[calc(100vh-200px)] overflow-y-auto pr-2">
                  {results.map((result) => (
                    <div
                      key={result.id}
                      className={`p-4 rounded-lg border-2 ${
                        result.success 
                          ? 'border-green-200 bg-green-50' 
                          : 'border-red-200 bg-red-50'
                      }`}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <span className={`text-sm font-medium ${
                          result.success ? 'text-green-700' : 'text-red-700'
                        }`}>
                          {result.success ? 'Sucesso' : 'Erro'}
                        </span>
                        <span className="text-xs text-gray-500">
                          {new Date(result.timestamp).toLocaleTimeString()}
                        </span>
                      </div>
                      
                      <div className="text-sm text-gray-600 mb-2">
                        <div><strong>Filme:</strong> {result.params.selectedMovie || 'Prompt customizado'}</div>
                        <div><strong>Criatividade:</strong> {creativityLevels.find(cl => cl.id === result.params.creativityLevelId)?.name || 'N/A'}</div>
                      </div>

                      {/* Informações do Prompt */}
                      {result.promptInfo && (
                        <div className="mt-2 p-3 bg-gray-50 border border-gray-200 rounded-md">
                          <h4 className="text-xs font-medium text-gray-700 mb-2">Informações da Geração:</h4>
                          <div className="text-xs text-gray-600 space-y-1">
                            <div><strong>Tentativas:</strong> {result.promptInfo.attempts}</div>
                            {result.promptInfo.fallbackUsed && (
                              <div><strong>Fallback usado:</strong> Sim</div>
                            )}
                            {result.promptInfo.safePromptUsed && (
                              <div><strong>Safe prompt usado:</strong> Sim</div>
                            )}
                            {result.result?.title_only_used && (
                              <div><strong>Apenas título usado:</strong> Sim</div>
                            )}
                            {result.promptInfo.promptUsed && (
                              <div className="mt-2">
                                <strong>Prompt final:</strong>
                                <div className="mt-1 p-2 bg-white border rounded text-xs max-h-16 overflow-y-auto">
                                  {result.promptInfo.promptUsed.substring(0, 200)}
                                  {result.promptInfo.promptUsed.length > 200 && '...'}
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      )}

                      {result.success && result.images && result.images.length > 0 && (
                        <div className="mt-2">
                          <div className="w-full">
                            {result.images.map((image: any, index: number) => (
                              <div key={index} className="relative group">
                                <div className="aspect-[3/4] bg-gray-100 rounded-md overflow-hidden">
                                  <img 
                                      <Download className="w-4 h-4" />
                                    </button>
                                  </div>
                                </div>
                                <p className="text-xs text-center mt-1 text-gray-700 truncate">{image.title || 'Imagem'}</p>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {!result.success && (
                        <div className="mt-2">
                          <div className="text-sm text-red-600 mb-2">
                            <strong>Erro:</strong> {result.error}
                          </div>
                          {result.promptInfo && (
                            <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                              <h4 className="text-xs font-medium text-red-700 mb-2">Informações das Tentativas:</h4>
                              <div className="text-xs text-red-600 space-y-1">
                                <div><strong>Tentativas realizadas:</strong> {result.promptInfo.attempts}</div>
                                {result.promptInfo.lastPromptTried && (
                                  <div>
                                    <strong>Último prompt tentado:</strong>
                                    <div className="mt-1 p-2 bg-white border rounded text-xs max-h-16 overflow-y-auto">
                                      {result.promptInfo.lastPromptTried.substring(0, 200)}
                                      {result.promptInfo.lastPromptTried.length > 200 && '...'}
                                    </div>
                                  </div>
                                )}
                                {result.promptInfo.fallbackUsed && (
                                  <div><strong>Fallback tentado:</strong> Sim</div>
                                )}
                                {result.promptInfo.attempts >= 2 && (
                                  <div><strong>Tentativa apenas título:</strong> Realizada</div>
                                )}
                                {result.promptInfo.safePromptUsed && (
                                  <div><strong>Safe prompt tentado:</strong> Sim</div>
                                )}
                              </div>
                            </div>
                          )}
                        </div>
                      )}

                      <div className="mt-2 flex items-center space-x-2">
                        <button
                          onClick={() => copyPrompt(result.prompt)}
                          className="text-xs px-2 py-1 bg-gray-100 hover:bg-gray-200 rounded transition-colors"
                        >
                          Copiar Prompt
                        </button>
                        {result.success && result.images && result.images.length > 0 && (
                          <button
                            onClick={() => {
                              const urls = result.images.map((img: any) => img.url).join('\n');
                              copyPrompt(urls);
                            }}
                            className="text-xs px-2 py-1 bg-blue-100 hover:bg-blue-200 rounded transition-colors"
                          >
                            Copiar URL
                          </button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Image Selection Modal */}
      {showImageModal && (
        <BaseImageSelector
          isOpen={showImageModal}
          onClose={() => setShowImageModal(false)}
          onSelectImage={(imageUrl, fileName) => {
            // Criar um objeto temporário para manter compatibilidade
            const tempImage = {
              id: Date.now().toString(),
              original_image_url: imageUrl,
              file_name: fileName,
              file_size: 0,
              content_type: 'image/jpeg',
              photo_type: 'individual' as const,
              streaming_platform: 'netflix' as const,
              created_at: new Date().toISOString(),
              user_id: user?.id || ''
            };
            setParams(prev => ({ ...prev, selectedBaseImage: tempImage, originalImageUrl: '' }));
            setShowImageModal(false);
          }}
          userId={user?.id || null}
        />
      )}

      {/* Image Viewer Modal */}
      {showImageViewer && selectedImageUrl && (
        <div className="fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center z-50 p-4">
          <div className="relative max-w-6xl max-h-[90vh] w-full h-full flex items-center justify-center">
            <button
              onClick={() => setShowImageViewer(false)}
              className="absolute top-4 right-4 text-white hover:text-gray-300 z-10"
            >
              <X className="w-8 h-8" />
            </button>
            
            <img
              src={selectedImageUrl}
              alt="Imagem em tamanho completo"
              className="max-w-full max-h-full object-contain"
            />
            
            <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
              <button
                onClick={() => downloadImage(selectedImageUrl, `imagem-${Date.now()}.jpg`)}
                className="px-4 py-2 bg-white bg-opacity-90 text-gray-800 rounded-md hover:bg-opacity-100 transition-colors flex items-center space-x-2"
              >
                <Download className="w-4 h-4" />
                <span>Download</span>
              </button>
              <button
                onClick={() => window.open(selectedImageUrl, '_blank')}
                className="px-4 py-2 bg-white bg-opacity-90 text-gray-800 rounded-md hover:bg-opacity-100 transition-colors flex items-center space-x-2"
              >
                <Eye className="w-4 h-4" />
                <span>Abrir em Nova Aba</span>
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
} 