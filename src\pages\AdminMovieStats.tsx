import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { supabase } from '../lib/supabase';
import { showToast } from '../utils/toast';
import { 
  ArrowLeft, 
  TrendingDown, 
  TrendingUp, 
  <PERSON>ert<PERSON><PERSON>gle, 
  BarChart3,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Edit
} from 'lucide-react';

interface MovieStats {
  id: string;
  title: string;
  streaming_platform: string;
  total_attempts: number;
  successful_attempts: number;
  failed_attempts: number;
  e005_errors: number;
  success_rate: number;
  last_attempt_at: string;
  last_error_message: string;
  is_active: boolean;
}

export default function AdminMovieStats() {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState<MovieStats[]>([]);
  const [filteredStats, setFilteredStats] = useState<MovieStats[]>([]);
  const [selectedPlatform, setSelectedPlatform] = useState<string>('all');
  const [sortBy, setSortBy] = useState<string>('e005_errors');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  useEffect(() => {
    const checkAuth = async () => {
      try {
        const { data: { session } } = await supabase.auth.getSession();
        if (session?.user) {
          if (session.user.user_metadata?.role !== 'admin') {
            showToast.error('Acesso negado. Apenas administradores podem acessar esta página.');
            navigate('/dashboard');
            return;
          }
          await loadStats();
        } else {
          navigate('/');
        }
      } catch (error) {
        console.error('Error checking auth:', error);
        navigate('/');
      }
    };

    checkAuth();
  }, [navigate]);

  const loadStats = async () => {
    setLoading(true);
    try {
      const { data: moviesData, error: moviesError } = await supabase
        .from('unified_movies_series')
        .select('*')
        .order('title');

      if (moviesError) throw moviesError;

      const processedStats: MovieStats[] = (moviesData || []).map(movie => ({
        id: movie.id,
        title: movie.title,
        streaming_platform: movie.streaming_platform,
        is_active: movie.is_active,
        total_attempts: movie.total_attempts || 0,
        successful_attempts: movie.successful_attempts || 0,
        failed_attempts: movie.failed_attempts || 0,
        e005_errors: movie.e005_errors || 0,
        success_rate: movie.success_rate || 0,
        last_attempt_at: movie.last_attempt_at || '',
        last_error_message: movie.last_error_message || ''
      }));

      setStats(processedStats);
      setFilteredStats(processedStats);
    } catch (error) {
      console.error('Error loading stats:', error);
      showToast.error('Erro ao carregar estatísticas');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    let filtered = stats;

    // Filter by platform
    if (selectedPlatform !== 'all') {
      filtered = filtered.filter(stat => stat.streaming_platform === selectedPlatform);
    }

    // Sort
    filtered.sort((a, b) => {
      const aValue = a[sortBy as keyof MovieStats];
      const bValue = b[sortBy as keyof MovieStats];
      
      if (typeof aValue === 'number' && typeof bValue === 'number') {
        return sortOrder === 'asc' ? aValue - bValue : bValue - aValue;
      }
      
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return sortOrder === 'asc' 
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      }
      
      return 0;
    });

    setFilteredStats(filtered);
  }, [stats, selectedPlatform, sortBy, sortOrder]);

  const toggleMovieStatus = async (movieId: string, currentStatus: boolean) => {
    try {
      const { error } = await supabase
        .from('unified_movies_series')
        .update({ is_active: !currentStatus })
        .eq('id', movieId);

      if (error) throw error;

      showToast.success(`Filme ${!currentStatus ? 'ativado' : 'desativado'} com sucesso`);
      await loadStats();
    } catch (error) {
      console.error('Error toggling movie status:', error);
      showToast.error('Erro ao alterar status do filme');
    }
  };

  const getSuccessRateColor = (rate: number) => {
    if (rate >= 80) return 'text-green-600 bg-green-100';
    if (rate >= 60) return 'text-yellow-600 bg-yellow-100';
    if (rate >= 40) return 'text-orange-600 bg-orange-100';
    return 'text-red-600 bg-red-100';
  };

  const getPlatformColor = (platform: string) => {
    switch (platform) {
      case 'netflix': return 'bg-red-100 text-red-800';
      case 'disney': return 'bg-blue-100 text-blue-800';
      case 'amazon': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <RefreshCw className="w-12 h-12 text-blue-500 animate-spin mx-auto mb-4" />
          <p className="text-gray-600">Carregando estatísticas...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => navigate('/admin')}
                className="flex items-center space-x-2 px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
              >
                <ArrowLeft className="w-4 h-4" />
                <span>Voltar</span>
              </button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Estatísticas de Filmes</h1>
                <p className="text-gray-600">Análise de performance e erros de geração</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={loadStats}
                className="flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
              >
                <RefreshCw className="w-4 h-4" />
                <span>Atualizar</span>
              </button>
              <button
                onClick={() => navigate('/admin-movies')}
                className="flex items-center space-x-2 px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors"
              >
                <Edit className="w-4 h-4" />
                <span>Gerenciar Filmes</span>
              </button>
            </div>
          </div>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <BarChart3 className="w-6 h-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total de Filmes</p>
                <p className="text-2xl font-bold text-gray-900">{stats.length}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <TrendingUp className="w-6 h-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Taxa de Sucesso Média</p>
                <p className="text-2xl font-bold text-gray-900">
                  {stats.length > 0 ? Math.round(stats.reduce((acc, stat) => acc + stat.success_rate, 0) / stats.length) : 0}%
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center">
              <div className="p-2 bg-red-100 rounded-lg">
                <AlertTriangle className="w-6 h-6 text-red-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Erros E005</p>
                <p className="text-2xl font-bold text-gray-900">
                  {stats.reduce((acc, stat) => acc + stat.e005_errors, 0)}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <TrendingDown className="w-6 h-6 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Filmes Problemáticos</p>
                <p className="text-2xl font-bold text-gray-900">
                  {stats.filter(stat => stat.success_rate < 50).length}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex flex-wrap items-center gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Plataforma
              </label>
              <select
                value={selectedPlatform}
                onChange={(e) => setSelectedPlatform(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="all">Todas</option>
                <option value="netflix">Netflix</option>
                <option value="disney">Disney+</option>
                <option value="amazon">Amazon Prime</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Ordenar por
              </label>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="e005_errors">Erros E005</option>
                <option value="success_rate">Taxa de Sucesso</option>
                <option value="total_attempts">Total de Tentativas</option>
                <option value="failed_attempts">Tentativas Falhadas</option>
                <option value="title">Nome do Filme</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Ordem
              </label>
              <select
                value={sortOrder}
                onChange={(e) => setSortOrder(e.target.value as 'asc' | 'desc')}
                className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="desc">Decrescente</option>
                <option value="asc">Crescente</option>
              </select>
            </div>
          </div>
        </div>

        {/* Stats Table */}
        <div className="bg-white rounded-lg shadow-sm overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Filme
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Plataforma
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Tentativas
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Taxa de Sucesso
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Erros E005
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Ações
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredStats.map((stat) => (
                  <tr key={stat.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{stat.title}</div>
                      {stat.last_attempt_at && (
                        <div className="text-xs text-gray-500">
                          Último teste: {new Date(stat.last_attempt_at).toLocaleDateString('pt-BR')}
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPlatformColor(stat.streaming_platform)}`}>
                        {stat.streaming_platform}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      <div>Total: {stat.total_attempts}</div>
                      <div className="text-xs text-gray-500">
                        ✅ {stat.successful_attempts} | ❌ {stat.failed_attempts}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getSuccessRateColor(stat.success_rate)}`}>
                        {stat.success_rate.toFixed(1)}%
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        {stat.e005_errors > 0 && (
                          <AlertTriangle className="w-4 h-4 text-red-500 mr-1" />
                        )}
                        <span className={`text-sm font-medium ${stat.e005_errors > 0 ? 'text-red-600' : 'text-gray-900'}`}>
                          {stat.e005_errors}
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        stat.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                      }`}>
                        {stat.is_active ? 'Ativo' : 'Inativo'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => toggleMovieStatus(stat.id, stat.is_active)}
                          className={`px-3 py-1 rounded text-xs font-medium transition-colors ${
                            stat.is_active 
                              ? 'bg-red-100 text-red-700 hover:bg-red-200' 
                              : 'bg-green-100 text-green-700 hover:bg-green-200'
                          }`}
                        >
                          {stat.is_active ? 'Desativar' : 'Ativar'}
                        </button>
                        {stat.last_error_message && (
                          <button
                            onClick={() => {
                              navigator.clipboard.writeText(stat.last_error_message);
                              showToast.success('Erro copiado para a área de transferência');
                            }}
                            className="px-3 py-1 bg-gray-100 text-gray-700 hover:bg-gray-200 rounded text-xs font-medium transition-colors flex items-center space-x-1"
                            title="Copiar mensagem de erro"
                          >
                            <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
                            </svg>
                            <span>Copiar erro</span>
                          </button>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {filteredStats.length === 0 && (
            <div className="text-center py-12">
              <BarChart3 className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">Nenhuma estatística encontrada</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
} 