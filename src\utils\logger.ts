// Utility for conditional logging that respects production environment

type LogLevel = 'log' | 'warn' | 'error' | 'info' | 'debug'

class Logger {
  private isDevelopment: boolean

  constructor() {
    // Check if we're in development mode
    this.isDevelopment = import.meta.env.DEV || import.meta.env.MODE === 'development'
  }

  private shouldLog(level: LogLevel): boolean {
    // Always allow errors in production for debugging
    if (level === 'error') return true
    
    // Only allow other logs in development
    return this.isDevelopment
  }

  log(...args: any[]): void {
    if (this.shouldLog('log')) {
      console.log(...args)
    }
  }

  warn(...args: any[]): void {
    if (this.shouldLog('warn')) {
      console.warn(...args)
    }
  }

  error(...args: any[]): void {
    if (this.shouldLog('error')) {
      console.error(...args)
    }
  }

  info(...args: any[]): void {
    if (this.shouldLog('info')) {
      console.info(...args)
    }
  }

  debug(...args: any[]): void {
    if (this.shouldLog('debug')) {
      console.debug(...args)
    }
  }

  // Group methods for better organization
  group(label?: string): void {
    if (this.isDevelopment) {
      console.group(label)
    }
  }

  groupEnd(): void {
    if (this.isDevelopment) {
      console.groupEnd()
    }
  }
}

// Export a singleton instance
export const logger = new Logger()

// Export individual methods for convenience
export const { log, warn, error, info, debug, group, groupEnd } = logger