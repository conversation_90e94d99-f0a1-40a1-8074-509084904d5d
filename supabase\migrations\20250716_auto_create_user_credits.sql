-- Migration: Auto-create user_credits record when user signs up
-- This prevents payment issues by ensuring every user has a credits record

-- Function to create user_credits record
CREATE OR REPLACE FUNCTION create_user_credits_record()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.user_credits (user_id, total_credits, available_credits, used_credits)
  VALUES (NEW.id, 0, 0, 0)
  ON CONFLICT (user_id) DO NOTHING;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to automatically create user_credits when user signs up
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION create_user_credits_record();

-- Backfill existing users who don't have user_credits records
INSERT INTO public.user_credits (user_id, total_credits, available_credits, used_credits)
SELECT 
  u.id,
  COALESCE(SUM(cp.credits), 0) as total_credits,
  COALESCE(SUM(cp.credits), 0) - COALESCE(SUM(cu.credits_used), 0) as available_credits,
  COALESCE(SUM(cu.credits_used), 0) as used_credits
FROM auth.users u
LEFT JOIN public.credit_purchases cp ON u.id = cp.user_id AND cp.status = 'completed'
LEFT JOIN public.credit_usage cu ON u.id = cu.user_id
WHERE u.id NOT IN (SELECT user_id FROM public.user_credits WHERE user_id IS NOT NULL)
GROUP BY u.id
ON CONFLICT (user_id) DO NOTHING;