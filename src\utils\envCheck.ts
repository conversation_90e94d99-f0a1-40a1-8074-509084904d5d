import { logger } from './logger'

// Environment variables checker
export interface EnvCheckResult {
  isValid: boolean
  missing: string[]
  warnings: string[]
  productIds: {
    starter: string
    popular: string
    master: string
  }
}

export function checkEnvironmentVariables(): EnvCheckResult {
  const required = [
    'VITE_SUPABASE_URL',
    'VITE_SUPABASE_ANON_KEY',
    'VITE_STRIPE_PUBLISHABLE_KEY'
  ]

  const optional = [
    'VITE_STRIPE_STARTER_CREDITS_ID',
    'VITE_STRIPE_POPULAR_CREDITS_ID', 
    'VITE_STRIPE_MASTER_CREDITS_ID',
    'VITE_N8N_CANVA_API_KEY'
  ]

  const missing: string[] = []
  const warnings: string[] = []

  // Check required variables
  required.forEach(varName => {
    if (!import.meta.env[varName]) {
      missing.push(varName)
    }
  })

  // Check optional variables and provide warnings
  optional.forEach(varName => {
    if (!import.meta.env[varName]) {
      warnings.push(`${varName} não configurado - usando valor padrão`)
    }
  })

  // Get product IDs (with fallbacks)
  const productIds = {
    starter: import.meta.env.VITE_STRIPE_STARTER_CREDITS_ID || 'prod_SVeFgga19mY0Gl',
    popular: import.meta.env.VITE_STRIPE_POPULAR_CREDITS_ID || 'prod_SVeHwRl5tn4cP6',
    master: import.meta.env.VITE_STRIPE_MASTER_CREDITS_ID || 'prod_SVeIPaf5R93PYg'
  }

  // Check if using default product IDs
  if (!import.meta.env.VITE_STRIPE_STARTER_CREDITS_ID) {
    warnings.push('Usando Product ID padrão para Starter Credits (teste)')
  }
  if (!import.meta.env.VITE_STRIPE_POPULAR_CREDITS_ID) {
    warnings.push('Usando Product ID padrão para Popular Credits (teste)')
  }
  if (!import.meta.env.VITE_STRIPE_MASTER_CREDITS_ID) {
    warnings.push('Usando Product ID padrão para Master Credits (teste)')
  }

  // Check Stripe key environment
  const stripeKey = import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY || ''
  if (stripeKey.startsWith('pk_test_')) {
    warnings.push('Usando chaves de teste do Stripe')
  } else if (stripeKey.startsWith('pk_live_')) {
    warnings.push('Usando chaves de produção do Stripe')
  }

  return {
    isValid: missing.length === 0,
    missing,
    warnings,
    productIds
  }
}

export function logEnvironmentStatus(): void {
  const result = checkEnvironmentVariables()
  
  logger.group('🔧 Environment Configuration')
  
  if (result.isValid) {
    logger.log('✅ Todas as variáveis obrigatórias estão configuradas')
  } else {
    logger.error('❌ Variáveis obrigatórias faltando:', result.missing)
  }

  if (result.warnings.length > 0) {
    logger.warn('⚠️ Avisos:')
    result.warnings.forEach(warning => logger.warn(`  - ${warning}`))
  }

  logger.log('📦 Product IDs configurados:')
  logger.log(`  - Starter Credits: ${result.productIds.starter}`)
  logger.log(`  - Popular Credits: ${result.productIds.popular}`)
  logger.log(`  - Master Credits: ${result.productIds.master}`)

  logger.groupEnd()
}

// Auto-run in development
if (import.meta.env.DEV) {
  logEnvironmentStatus()
}