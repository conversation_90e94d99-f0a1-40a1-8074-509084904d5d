import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from "https://esm.sh/@supabase/supabase-js@2"

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST',
}

const supabaseUrl = Deno.env.get('SUPABASE_URL')!
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  // 1. AUTHENTICATION
  const authHeader = req.headers.get('Authorization')
  if (!authHeader) {
    return new Response(JSON.stringify({ error: 'Falta o cabeçalho de autenticação' }), { status: 401, headers: corsHeaders })
  }
  
  const supabase = createClient(supabaseUrl, supabaseServiceKey)
  
  const { data: { user }, error: userError } = await supabase.auth.getUser(authHeader.replace('Bearer ', ''));
  if (userError || !user) {
    return new Response(JSON.stringify({ error: 'Token inválido ou expirado' }), { status: 401, headers: corsHeaders })
  }
  
  // Get user info securely from the token
  const userId = user.id;
  const userEmail = user.email;

  try {
    const { voucherCode } = await req.json()

    if (!voucherCode) {
      return new Response(
        JSON.stringify({ 
          success: false,
          error: 'MISSING_PARAMETERS',
          message: 'Código do voucher é obrigatório' 
        }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Validate voucher using database function (now includes user validation)
    const { data: validationResult, error: validationError } = await supabase
      .rpc('validate_voucher', { 
        voucher_code: voucherCode.toUpperCase(),
        user_email: userEmail, // Securely from token
        user_id: userId  // Securely from token
      })

    if (validationError) {
      console.error('Error validating voucher:', validationError)
      return new Response(
        JSON.stringify({ 
          success: false,
          error: 'VALIDATION_ERROR',
          message: 'Erro ao validar voucher' 
        }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Check if voucher is valid (includes all validations: exists, active, not expired, has uses left, user hasn't used it)
    if (!validationResult?.valid) {
      return new Response(
        JSON.stringify({ 
          success: false,
          error: validationResult?.error || 'INVALID_VOUCHER',
          message: validationResult?.message || 'Voucher inválido' 
        }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    const voucher = validationResult.voucher

    // Start transaction to redeem voucher
    const { data: transactionResult, error: transactionError } = await supabase.rpc('redeem_voucher_transaction', {
      p_voucher_id: voucher.id,
      p_user_id: userId,
      p_user_email: userEmail,
      p_credits: voucher.credits
    })

    if (transactionError) {
      console.error('Error redeeming voucher:', transactionError)
      return new Response(
        JSON.stringify({ 
          success: false,
          error: 'REDEMPTION_ERROR',
          message: 'Erro ao resgatar voucher' 
        }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Success response
    return new Response(
      JSON.stringify({ 
        success: true,
        message: `Voucher resgatado com sucesso! ${voucher.credits} créditos foram adicionados à sua conta.`,
        data: {
          credits: voucher.credits,
          voucherCode: voucher.code
        }
      }),
      { 
        status: 200, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )

  } catch (error) {
    console.error('Unexpected error in redeem-voucher function:', error)
    return new Response(
      JSON.stringify({ 
        success: false,
        error: 'INTERNAL_ERROR',
        message: 'Erro interno do servidor' 
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
}) 