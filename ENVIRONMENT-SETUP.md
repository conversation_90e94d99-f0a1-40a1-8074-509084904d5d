# Configuração de Variáveis de Ambiente

## Arquivo .env

Crie um arquivo `.env` na raiz do projeto com as seguintes variáveis:

```bash
# Supabase Configuration
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# OpenAI Configuration
OPENAI_API_KEY=********************************************************************************************************************************************************************

# Stripe Configuration
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# Stripe Product IDs (Test Environment)
VITE_STRIPE_ONE_PACK_PRODUCT_ID=prod_SVeFgga19mY0Gl
VITE_STRIPE_COMPLETE_PACK_PRODUCT_ID=prod_SVeHwRl5tn4cP6
VITE_STRIPE_MASTER_PACK_PRODUCT_ID=prod_SVeIPaf5R93PYg

# N8N/Canva Integration
VITE_N8N_CANVA_API_KEY=your_n8n_canva_api_key

# Environment
NODE_ENV=development
```

## IDs dos Produtos Stripe

### Ambiente de Teste (Test)

| Produto | ID | Descrição |
|---------|----|-----------| 
| One Pack | `prod_SVeFgga19mY0Gl` | Gera 1 poster a sua escolha |
| Complete Pack | `prod_SVeHwRl5tn4cP6` | Gera ate 3 posters a sua escolha |
| Master Pack | `prod_SVeIPaf5R93PYg` | Gera ate 10 posters a sua escolha |

### Ambiente de Produção (Live)

Para produção, você precisará criar produtos equivalentes no Stripe Live e atualizar as variáveis:

```bash
# Stripe Product IDs (Production Environment)
VITE_STRIPE_ONE_PACK_PRODUCT_ID=prod_your_production_one_pack_id
VITE_STRIPE_COMPLETE_PACK_PRODUCT_ID=prod_your_production_complete_pack_id
VITE_STRIPE_MASTER_PACK_PRODUCT_ID=prod_your_production_master_pack_id
```

## Como Configurar

### 1. Supabase

1. Acesse [supabase.com](https://supabase.com)
2. Crie um novo projeto
3. Vá em Settings > API
4. Copie a URL e as chaves anon/service_role

### 2. Stripe

1. Acesse [stripe.com](https://stripe.com)
2. Vá em Developers > API keys
3. Copie as chaves publishable e secret (test ou live)
4. Configure webhooks em Developers > Webhooks
5. Use os Product IDs fornecidos acima

### 3. Webhook do Stripe

Configure um webhook endpoint apontando para:
```
https://your-supabase-url.supabase.co/functions/v1/stripe-webhook
```

Eventos para escutar:
- `payment_intent.succeeded`
- `payment_intent.payment_failed`

### 4. OpenAI

Para usar a funcionalidade de regeneração de prompts com IA:
1. Acesse [platform.openai.com](https://platform.openai.com)
2. Vá em API keys
3. Crie uma nova API key
4. Configure a variável `OPENAI_API_KEY`

**Modelos disponíveis:**
- `gpt-4o` - Mais avançado e inteligente
- `gpt-4o-mini` - Mais rápido e econômico (recomendado)
- `gpt-4-turbo` - Balanceado entre qualidade e velocidade
- `gpt-3.5-turbo` - Mais econômico para tarefas simples

### 5. N8N/Canva (Opcional)

Se você estiver usando integração com Canva via N8N:
1. Configure seu workflow N8N
2. Obtenha a API key
3. Configure a variável `VITE_N8N_CANVA_API_KEY`

## Verificação da Configuração

Para verificar se tudo está configurado corretamente:

1. **Frontend**: As variáveis `VITE_*` devem estar disponíveis
2. **Backend**: As Edge Functions devem ter acesso às variáveis sem `VITE_`
3. **Stripe**: Teste um pagamento para verificar se o webhook funciona
4. **Supabase**: Verifique se as tabelas foram criadas corretamente

## Troubleshooting

### Erro: "Product ID not found"
- Verifique se os IDs dos produtos estão corretos
- Confirme se você está usando o ambiente correto (test/live)

### Erro: "Webhook signature verification failed"
- Verifique se `STRIPE_WEBHOOK_SECRET` está correto
- Confirme se o endpoint do webhook está configurado corretamente

### Erro: "Supabase connection failed"
- Verifique URL e chaves do Supabase
- Confirme se as políticas RLS estão configuradas

## Segurança

⚠️ **Importante:**
- Nunca commite o arquivo `.env` no Git
- Use variáveis diferentes para test/production
- Mantenha as chaves secretas seguras
- Configure CORS adequadamente no Supabase

## Deploy

Para deploy em produção:

1. Configure as variáveis de ambiente na plataforma de deploy
2. Use as chaves de produção do Stripe
3. Configure o webhook para o domínio de produção
4. Teste todos os fluxos de pagamento 