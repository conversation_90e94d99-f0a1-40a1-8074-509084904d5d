import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient, SupabaseClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { movieDatabase } from '../_shared/movieDatabase.ts'
import { 
  buildPrompt, 
  createSafePrompt, 
  createPromptVariation, 
  pollForResult 
} from '../_shared/promptBuilder.ts'
import { 
  getAspectRatio, 
  getCoverQuantity, 
  isValidPlatform 
} from '../_shared/platformConfig.ts'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
}

interface GeneratedCover {
  movie_title: string;
  image_url?: string;
  success: boolean;
  error?: string;
  fallback_used?: boolean;
  fallback_movie?: string;
  safe_prompt_used?: boolean;
  generated_at: string;
}

interface GenerateCoversRequest {
  originalImageUrl: string
  streamingPlatform: string
  photoType: string
  userId: string
  gender?: string
  language?: string
  generateTitles?: boolean
  creativityLevel?: 'criativo' | 'rostoFiel' | 'estrito'
}

interface CoverQuantities {
  netflix: number
  disney: number
  amazon: number
}

// Configurações agora vêm do platformConfig.ts centralizado

async function downloadAndUploadToSupabase(
  replicateImageUrl: string, 
  movieTitle: string, 
  streamingPlatform: string
): Promise<string | null> {
  try {
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceRoleKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const supabase = createClient(supabaseUrl, supabaseServiceRoleKey)

    console.log(`Downloading image from Replicate for ${movieTitle}...`)
    
    // Download da imagem do Replicate
    const imageResponse = await fetch(replicateImageUrl)
    if (!imageResponse.ok) {
      throw new Error(`Failed to download image: ${imageResponse.status}`)
    }
    
    const imageBlob = await imageResponse.blob()
    const imageBuffer = await imageBlob.arrayBuffer()
    
    // Gerar nome único para o arquivo
    const timestamp = Date.now()
    const sanitizedTitle = movieTitle.toLowerCase().replace(/[^a-z0-9]/g, '-')
    const filename = `covers/${timestamp}-${sanitizedTitle}-${streamingPlatform}.jpg`
    
    console.log(`Uploading to Supabase Storage: ${filename}`)
    
    // Upload para Supabase Storage
    const { data, error } = await supabase.storage
      .from('generated-images')
      .upload(filename, imageBuffer, {
        contentType: 'image/jpeg',
        upsert: false
      })
    
    if (error) {
      console.error(`Supabase upload error for ${movieTitle}:`, error)
      return null
    }
    
    // Obter URL pública
    const { data: { publicUrl } } = supabase.storage
      .from('generated-images')
      .getPublicUrl(filename)
    
    console.log(`Successfully uploaded ${movieTitle} to Supabase: ${publicUrl}`)
    return publicUrl
    
  } catch (error) {
    console.error(`Error downloading/uploading ${movieTitle}:`, error)
    return null
  }
}

async function syncMovieDatabase(supabase: SupabaseClient, db: typeof movieDatabase) {
  try {
    console.log('Starting movie database synchronization...');
    const promptsToUpsert: any[] = [];

    for (const platform in db) {
      for (const movie of db[platform as keyof typeof db]) {
        promptsToUpsert.push({
          title: movie.title,
          streaming_platform: platform,
          poster_style_prompt: movie.prompt, // Assumes `poster_style_prompt` is JSONB
        });
      }
    }

    const { error } = await supabase.from('movies_series').upsert(promptsToUpsert, {
      onConflict: 'title,streaming_platform', 
    });

    if (error) {
      console.error('Error syncing movie database. IMPORTANT: Ensure a UNIQUE constraint exists on (title, streaming_platform) in your `movies_series` table.', error);
    } else {
      console.log('Movie database synchronized successfully.');
    }
  } catch (e) {
    console.error('An unexpected error occurred during database sync:', e);
  }
}

async function saveSuccessfulCover(
  supabase: SupabaseClient, 
  userId: string, 
  generationId: string, 
  imageUrl: string, 
  movieTitle: string,
  platform: string, 
  photoType: string,
  aspectRatio: string,
) {
  try {
    // 🔥 CORREÇÃO: Salvar nas SERIES & FILMS (cover_generations) e não em cover_images
    // Buscar a geração atual
    const { data: currentGeneration, error: fetchError } = await supabase
      .from('cover_generations')
      .select('generated_covers')
      .eq('id', generationId)
      .single()

    if (fetchError) {
      console.error('Error fetching current generation:', fetchError)
      return
    }

    // Adicionar nova capa ao array
    const currentCovers = currentGeneration.generated_covers || []
    const newCover = {
      movie_title: movieTitle,
      image_url: imageUrl,
      streaming_platform: platform,
      photo_type: photoType,
      aspect_ratio: aspectRatio,
      success: true, // 🔥 ADICIONAR PROPRIEDADE SUCCESS
      created_at: new Date().toISOString()
    }
    currentCovers.push(newCover)

    // Atualizar array de capas geradas
    const { error: updateError } = await supabase
      .from('cover_generations')
      .update({ generated_covers: currentCovers })
      .eq('id', generationId)

    if (updateError) {
      console.error('Error updating generated covers array:', updateError);
    } else {
      console.log(`✅ Cover saved to Series & Films: ${movieTitle} - ${imageUrl}`);
    }

    // Try to update user stats
    const { error: statsError } = await supabase.rpc('update_user_stats', {
      p_user_id: userId,
      p_covers_generated: 1,
      p_poster_generated: false
    });

    if (statsError) {
      console.error('Error updating user stats for individual cover:', statsError);
      // Don't throw error, just log it
    }

  } catch(e) {
    console.error('Error in saveSuccessfulCover function:', e);
    // Don't throw error, just log it - we still want to return the URL to the user
  }
}

async function callSingleCoverGeneration(
  movieTitle: string,
  originalImageUrl: string,
  streamingPlatform: string,
  photoType: string,
  aspectRatio: string,
  userId: string,
  generationId: string,
  position: number,
  userToken: string, // 🔥 ADICIONAR TOKEN DO USUÁRIO
  gender?: string,
  language?: string,
  generateTitles?: boolean,
  creativityLevel?: 'criativo' | 'rostoFiel' | 'estrito'
): Promise<{ success: boolean, movie_title?: string, error?: string }> {
  
  try {
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    
    console.log(`📞 Calling generate-single-cover for position ${position}: ${movieTitle}`)
    
    const response = await fetch(`${supabaseUrl}/functions/v1/generate-single-cover`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${userToken}`, // 🔥 USAR TOKEN DO USUÁRIO
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        movieTitle,
        originalImageUrl,
        streamingPlatform,
        photoType,
        aspectRatio,
        userId,
        generationId,
        position,
        gender,
        language,
        generateTitles,
        creativityLevel,
        useBackup: true
      })
    })

    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`Single cover generation failed: ${errorText}`)
    }

    const result = await response.json()
    console.log(`📞 Single cover result for position ${position}:`, result.success ? '✅' : '❌')
    
    return {
      success: result.success,
      movie_title: result.movie_title,
      error: result.error
    }

  } catch (error) {
    console.error(`📞 Error calling single cover for position ${position}:`, error)
    return {
      success: false,
      error: error.message
    }
  }
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // 🔥 PEGAR TOKEN DO USUÁRIO
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      throw new Error('Missing authorization header')
    }
    const userToken = authHeader.replace('Bearer ', '')

    const {
      originalImageUrl,
      streamingPlatform,
      photoType,
      userId,
      gender,
      language = 'portuguese',
      generateTitles = false,
      creativityLevel = 'rostoFiel'
    }: GenerateCoversRequest = await req.json()

    console.log(`🎬 Starting orchestrated generation for ${streamingPlatform}`)
    console.log(`📋 Settings: Gender=${gender}, Titles=${generateTitles}, Creativity=${creativityLevel}`)

    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceRoleKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const supabase = createClient(supabaseUrl, supabaseServiceRoleKey)

    // Validar plataforma usando configuração centralizada
    if (!isValidPlatform(streamingPlatform)) {
      throw new Error(`Invalid streaming platform: ${streamingPlatform}`)
    }
    
    const movies = movieDatabase[streamingPlatform as keyof typeof movieDatabase]
    if (!movies) {
      throw new Error(`Invalid streaming platform: ${streamingPlatform}`)
    }

    // Obter quantidade de capas para a plataforma
    const totalCovers = getCoverQuantity(streamingPlatform)

    console.log(`🎯 Will generate ${totalCovers} covers for ${streamingPlatform}`)

    // Criar geração no banco
    const generationId = crypto.randomUUID()
    
    const { error: generationError } = await supabase
      .from('cover_generations')
      .insert({
        id: generationId,
        user_id: userId,
        original_image_url: originalImageUrl, // CORRIGIDO: Adicionado campo obrigatório
        streaming_platform: streamingPlatform,
        photo_type: photoType,
        total_covers: totalCovers,
        progress: 0,
        current_movie: 'Iniciando...',
        status: 'processing',
        settings: {
          gender,
          language,
          generateTitles,
          creativityLevel
        }
      })

    if (generationError) {
      throw new Error(`Failed to create generation: ${generationError.message}`)
    }

    console.log(`📝 Created generation ${generationId}`)

    // 🎯 FILMES PRINCIPAIS (posições 1-12)
    const primaryMovies = movies.slice(0, totalCovers)
    
    const results = []
    let successCount = 0

    // 🔄 PROCESSAR UM POR VEZ
    for (let i = 0; i < totalCovers; i++) {
      const position = i + 1
      const movie = primaryMovies[i]
      
      // Determinar aspect ratio usando configuração centralizada
      const aspectRatio = getAspectRatio(streamingPlatform, i)

      console.log(`\n🎬 Processing position ${position}/${totalCovers}: ${movie.title}`)
      
      // Atualizar progresso no banco (em porcentagem)
      const progressPercent = Math.round((i / totalCovers) * 100)
      await supabase
        .from('cover_generations')
        .update({
          progress: progressPercent,
          current_movie: movie.title
        })
        .eq('id', generationId)

      // Chamar geração individual
      const result = await callSingleCoverGeneration(
        movie.title,
        originalImageUrl,
        streamingPlatform,
        photoType,
        aspectRatio,
        userId,
        generationId,
        position,
        userToken, // 🔥 PASSAR TOKEN DO USUÁRIO
        gender,
        language,
        generateTitles,
        creativityLevel
      )

      results.push({
        position,
        original_movie: movie.title,
        final_movie: result.movie_title || movie.title,
        success: result.success,
        error: result.error
      })

      if (result.success) {
        successCount++
        console.log(`✅ Position ${position} SUCCESS: ${result.movie_title}`)
      } else {
        console.log(`❌ Position ${position} FAILED: ${result.error}`)
      }

      // Pequena pausa entre gerações para evitar rate limiting
      if (i < totalCovers - 1) {
        await new Promise(resolve => setTimeout(resolve, 1000))
      }
    }

    // Atualizar status final
    const finalStatus = successCount > 0 ? 'completed' : 'failed'
    
    // 🔥 MELHORADO: Atualização mais robusta do status final
    const { error: finalUpdateError } = await supabase
      .from('cover_generations')
      .update({
        progress: 100, // 100% quando completo
        current_movie: successCount > 0 ? 'Concluído' : 'Falhou',
        status: finalStatus,
        completed_at: new Date().toISOString()
      })
      .eq('id', generationId)

    if (finalUpdateError) {
      console.error('❌ ERRO CRÍTICO: Falha ao atualizar status final:', finalUpdateError)
      // Tentar novamente uma vez
      try {
        await supabase
          .from('cover_generations')
          .update({
            status: finalStatus,
            completed_at: new Date().toISOString(),
            progress: 100,
            current_movie: 'Finalizado'
          })
          .eq('id', generationId)
        console.log('✅ Status atualizado na segunda tentativa')
      } catch (retryError) {
        console.error('❌ FALHA NA SEGUNDA TENTATIVA:', retryError)
      }
    } else {
      console.log(`✅ Status final atualizado: ${finalStatus}`)
    }

    // Salvar log de geração
    await supabase
      .from('generation_logs')
      .insert({
        user_id: userId,
        streaming_platform: streamingPlatform,
        total_requested: totalCovers,
        total_generated: successCount,
        success_rate: Math.round((successCount / totalCovers) * 100),
        duration_seconds: 0, // Será calculado pelo frontend
        settings: {
          gender,
          language,
          generateTitles,
          creativityLevel
        },
        details: results
      })

    console.log(`\n🏁 Generation completed: ${successCount}/${totalCovers} successful`)

    return new Response(
      JSON.stringify({
        success: successCount > 0,
        generation_id: generationId,
        total_covers: totalCovers,
        successful_covers: successCount,
        failed_covers: totalCovers - successCount,
        results: results,
        message: `Generated ${successCount} out of ${totalCovers} covers`
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )

  } catch (error) {
    console.error('Error in generate-covers orchestrator:', error)
    return new Response(
      JSON.stringify({ 
        success: false,
        error: error.message 
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})

// Função para gerar uma única capa
async function generateSingleCover(
  movie: any,
  originalImageUrl: string,
  streamingPlatform: string,
  photoType: string,
  aspectRatio: string,
  gender?: string,
  language: string = 'portuguese',
  generateTitles: boolean = false,
  creativityLevel: 'criativo' | 'rostoFiel' | 'estrito' = 'rostoFiel'
): Promise<GeneratedCover> {
  
  const result: GeneratedCover = {
    movie_title: movie.title,
    success: false,
    generated_at: new Date().toISOString()
  }

  try {
    const replicateToken = Deno.env.get('REPLICATE_API_TOKEN')
    if (!replicateToken) {
      throw new Error('REPLICATE_API_TOKEN not found')
    }

    const moviePrompt = movie.prompt[language] || movie.prompt.english

    console.log(`DEBUG - Movie: ${movie.title}`)
    console.log(`DEBUG - Gender received: ${gender}`)
    console.log(`DEBUG - Generate titles: ${generateTitles}`)
    console.log(`DEBUG - Photo type: ${photoType}`)
    console.log(`DEBUG - Language: ${language}`)

    // Usar buildPrompt para incluir gender adjustments e títulos
    const optimizedPrompt = buildPrompt(moviePrompt, {
      language: language as 'english' | 'portuguese',
      photoType: photoType as 'individual' | 'casal',
      gender: gender as 'male' | 'female' | undefined,
      addTitle: generateTitles,
      movieTitle: movie.title,
      coverIndex: Math.floor(Math.random() * 12), // Variação aleatória de pose
      creativityLevel: creativityLevel
    })

    console.log(`DEBUG - Original movie prompt: ${moviePrompt}`)
    console.log(`DEBUG - Optimized prompt (${optimizedPrompt.length} chars): ${optimizedPrompt}`)

    // Truncar prompt se muito longo (max 500 caracteres)
    const truncatedPrompt = optimizedPrompt.length > 500 
      ? optimizedPrompt.substring(0, 497) + '...'
      : optimizedPrompt

    console.log(`DEBUG - Final truncated prompt (${truncatedPrompt.length} chars): ${truncatedPrompt}`)
    console.log(`DEBUG - Aspect ratio: ${aspectRatio}`)
    console.log(`DEBUG - Original image URL: ${originalImageUrl}`)
    
    // LOG COMPLETO DO PAYLOAD PARA REPLICATE
    const replicatePayload = {
      input: {
        input_image: originalImageUrl,
        prompt: truncatedPrompt,
        aspect_ratio: aspectRatio,
        output_format: 'jpg',
        safety_tolerance: 5,
        num_inference_steps: 28,
        guidance_scale: 3.5
      }
    }
    console.log(`🚀 PAYLOAD COMPLETO PARA REPLICATE:`, JSON.stringify(replicatePayload, null, 2))

    const prediction = await fetch('https://api.replicate.com/v1/models/black-forest-labs/flux-kontext-max/predictions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${replicateToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(replicatePayload)
    })

    if (!prediction.ok) {
      const errorText = await prediction.text()
      throw new Error(`Replicate API error: ${errorText}`)
    }

    const predictionData = await prediction.json()
    console.log(`Poll attempt 1 for ${movie.title}: ${predictionData.status}`)

    // Polling para resultado
    const pollResult = await pollForResult(predictionData.id, movie.title, replicateToken)
    
    if (pollResult.imageUrl) {
      // Download da imagem do Replicate e upload para Supabase Storage
      const supabaseImageUrl = await downloadAndUploadToSupabase(pollResult.imageUrl, movie.title, streamingPlatform)
      
      if (supabaseImageUrl) {
        result.success = true
        result.image_url = supabaseImageUrl
      } else {
        result.error = 'Failed to upload to Supabase Storage'
      }
    } else {
      // 🔥 PRESERVAR O ERRO ORIGINAL (incluindo E005)
      result.error = pollResult.error || 'Unknown polling error'
    }

  } catch (error) {
    console.error(`Error generating ${movie.title}:`, error)
    result.error = error.message
  }

  return result
}

// Função para regenerar filme específico
async function regenerateSpecificMovie(
  supabase: SupabaseClient,
  movieTitle: string,
  originalImageUrl: string,
  streamingPlatform: string,
  photoType: string,
  userId: string,
  generationId: string
) {
  const movies = movieDatabase[streamingPlatform as keyof typeof movieDatabase]
  const movie = movies.find(m => m.title === movieTitle)
  
  if (!movie) {
    throw new Error(`Movie ${movieTitle} not found`)
  }

  const config = {
    netflix: { aspectRatio: '5:4' },
    amazon: { aspectRatio: '5:4' },
    disney: { aspectRatio: '3:5' }
  }

  // Criar variação do prompt para regeneração usando função shared
  const variedPrompt = createPromptVariation(movie.prompt, photoType)
  const variedMovie = {
    ...movie,
    prompt: variedPrompt
  }

  const result = await generateSingleCover(
    variedMovie,
    originalImageUrl,
    streamingPlatform,
    photoType,
    config[streamingPlatform as keyof typeof config].aspectRatio,
    undefined, // gender
    'portuguese', // language
    false, // generateTitles - regeneração não precisa título
    'rostoFiel' // creativityLevel
  )

  if (result.success) {
    // Marcar anterior como regenerada
    await supabase
      .from('cover_images')
      .update({ is_regenerated: true })
      .eq('user_id', userId)
      .eq('movie_title', movieTitle)
      .eq('streaming_platform', streamingPlatform)

    // Salvar nova versão
    await supabase
      .from('cover_images')
      .insert({
        user_id: userId,
        image_url: result.image_url,
        movie_title: result.movie_title,
        streaming_platform: streamingPlatform,
        photo_type: photoType,
        aspect_ratio: config[streamingPlatform as keyof typeof config].aspectRatio,
        generation_id: generationId,
        is_regenerated: false
      })
  }

  return new Response(
    JSON.stringify({
      message: result.success ? `${movieTitle} regenerated successfully` : `Failed to regenerate ${movieTitle}`,
      result
    }),
    { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  )
}
