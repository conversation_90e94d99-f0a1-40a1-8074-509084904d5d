import { toast } from 'sonner'

export const showToast = {
  success: (message: string, options?: { duration?: number }) => {
    return toast.success(message, {
      duration: options?.duration ?? 4000,
      dismissible: true,
    })
  },

  error: (message: string, options?: { duration?: number }) => {
    return toast.error(message, {
      duration: options?.duration ?? 6000,
      dismissible: true,
    })
  },

  loading: (message: string, options?: { id?: string; duration?: number }) => {
    return toast.loading(message, {
      id: options?.id,
      duration: options?.duration ?? 30000,
      dismissible: true,
    })
  },

  info: (message: string, options?: { duration?: number }) => {
    return toast.info(message, {
      duration: options?.duration ?? 4000,
      dismissible: true,
    })
  },

  warning: (message: string, options?: { duration?: number }) => {
    return toast.warning(message, {
      duration: options?.duration ?? 5000,
      dismissible: true,
    })
  },

  dismiss: (toastId?: string) => {
    if (toastId) {
      toast.dismiss(toastId)
    } else {
      toast.dismiss()
    }
  },

  promise: <T,>(
    promise: Promise<T>,
    messages: {
      loading: string
      success: string | ((data: T) => string)
      error: string | ((error: any) => string)
    },
    options?: { duration?: number }
  ) => {
    return toast.promise(promise, messages, {
      duration: options?.duration,
      dismissible: true,
    })
  }
}