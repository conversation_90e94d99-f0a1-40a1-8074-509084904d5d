/*
  # Voucher System Database Schema

  1. New Tables
    - `vouchers`
      - `id` (uuid, primary key)
      - `code` (text, unique voucher code)
      - `credits` (integer, number of credits to give)
      - `max_uses` (integer, max number of uses, default 1)
      - `current_uses` (integer, current number of uses)
      - `email_restriction` (text, optional email restriction)
      - `expires_at` (timestamp, optional expiration date)
      - `created_by` (uuid, admin who created)
      - `status` (text, active/disabled/expired)
      - `created_at` (timestamp)

    - `voucher_redemptions`
      - `id` (uuid, primary key)
      - `voucher_id` (uuid, references vouchers)
      - `user_id` (uuid, references auth.users)
      - `credits_redeemed` (integer, credits given)
      - `user_email` (text, email of redeemer)
      - `redeemed_at` (timestamp)

  2. Security
    - Enable RLS on all tables
    - Admin-only access for vouchers management
    - User-specific access for redemptions viewing
*/

-- Create vouchers table
CREATE TABLE IF NOT EXISTS public.vouchers (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  code text UNIQUE NOT NULL,
  credits integer NOT NULL CHECK (credits > 0),
  max_uses integer DEFAULT 1 CHECK (max_uses > 0),
  current_uses integer DEFAULT 0 CHECK (current_uses >= 0),
  email_restriction text, -- Optional email restriction
  expires_at timestamptz, -- Optional expiration date
  created_by uuid REFERENCES auth.users(id),
  status text NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'disabled', 'expired')),
  description text, -- Optional description for admin reference
  created_at timestamptz DEFAULT now()
);

-- Create voucher_redemptions table
CREATE TABLE IF NOT EXISTS public.voucher_redemptions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  voucher_id uuid REFERENCES public.vouchers(id) NOT NULL,
  user_id uuid REFERENCES auth.users(id) NOT NULL,
  credits_redeemed integer NOT NULL CHECK (credits_redeemed > 0),
  user_email text NOT NULL,
  redeemed_at timestamptz DEFAULT now()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_vouchers_code ON public.vouchers(code);
CREATE INDEX IF NOT EXISTS idx_vouchers_status ON public.vouchers(status);
CREATE INDEX IF NOT EXISTS idx_vouchers_expires_at ON public.vouchers(expires_at);
CREATE INDEX IF NOT EXISTS idx_vouchers_created_by ON public.vouchers(created_by);
CREATE INDEX IF NOT EXISTS idx_voucher_redemptions_voucher_id ON public.voucher_redemptions(voucher_id);
CREATE INDEX IF NOT EXISTS idx_voucher_redemptions_user_id ON public.voucher_redemptions(user_id);
CREATE INDEX IF NOT EXISTS idx_voucher_redemptions_redeemed_at ON public.voucher_redemptions(redeemed_at);

-- Enable RLS
ALTER TABLE public.vouchers ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.voucher_redemptions ENABLE ROW LEVEL SECURITY;

-- RLS Policies for vouchers
-- Only service role can manage vouchers (admin functions)
CREATE POLICY "service_role_vouchers_all" ON public.vouchers
  FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

-- Users can only view active vouchers when redeeming
CREATE POLICY "users_can_view_active_vouchers" ON public.vouchers
  FOR SELECT USING (
    status = 'active' AND 
    (expires_at IS NULL OR expires_at > now()) AND
    current_uses < max_uses
  );

-- RLS Policies for voucher_redemptions
-- Users can only view their own redemptions
CREATE POLICY "users_can_view_own_redemptions" ON public.voucher_redemptions
  FOR SELECT USING (auth.uid() = user_id);

-- Service role can view all redemptions (for admin)
CREATE POLICY "service_role_redemptions_all" ON public.voucher_redemptions
  FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

-- Users can insert their own redemptions (through edge function)
CREATE POLICY "users_can_insert_redemptions" ON public.voucher_redemptions
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Add function to generate random voucher codes
CREATE OR REPLACE FUNCTION generate_voucher_code(length integer DEFAULT 8)
RETURNS text AS $$
DECLARE
  chars text := 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789'; -- Removed confusing chars like I, L, O, 0, 1
  result text := '';
  i integer := 0;
BEGIN
  FOR i IN 1..length LOOP
    result := result || substr(chars, floor(random() * length(chars) + 1)::integer, 1);
  END LOOP;
  RETURN result;
END;
$$ LANGUAGE plpgsql;

-- Add function to validate voucher
CREATE OR REPLACE FUNCTION validate_voucher(voucher_code text, user_email text DEFAULT NULL, user_id uuid DEFAULT NULL)
RETURNS json AS $$
DECLARE
  voucher_record public.vouchers%ROWTYPE;
  user_already_used_count integer;
BEGIN
  -- Get voucher
  SELECT * INTO voucher_record
  FROM public.vouchers
  WHERE code = voucher_code;

  -- Check if voucher exists
  IF voucher_record.id IS NULL THEN
    RETURN json_build_object(
      'valid', false,
      'error', 'VOUCHER_NOT_FOUND',
      'message', 'Código de voucher não encontrado'
    );
  END IF;

  -- Check if voucher is active
  IF voucher_record.status != 'active' THEN
    RETURN json_build_object(
      'valid', false,
      'error', 'VOUCHER_INACTIVE',
      'message', 'Este código de voucher não está ativo'
    );
  END IF;

  -- Check if voucher is expired
  IF voucher_record.expires_at IS NOT NULL AND voucher_record.expires_at <= now() THEN
    RETURN json_build_object(
      'valid', false,
      'error', 'VOUCHER_EXPIRED',
      'message', 'Este código de voucher expirou'
    );
  END IF;

  -- Check if voucher has uses left (GLOBAL limit)
  IF voucher_record.current_uses >= voucher_record.max_uses THEN
    RETURN json_build_object(
      'valid', false,
      'error', 'VOUCHER_LIMIT_REACHED',
      'message', 'Este código de voucher atingiu o limite máximo de usos'
    );
  END IF;

  -- Check if THIS USER already used this voucher (USER limit = 1)
  IF user_id IS NOT NULL THEN
    SELECT COUNT(*) INTO user_already_used_count
    FROM public.voucher_redemptions
    WHERE voucher_id = voucher_record.id AND user_id = user_id;

    IF user_already_used_count > 0 THEN
      RETURN json_build_object(
        'valid', false,
        'error', 'VOUCHER_ALREADY_USED',
        'message', 'Você já utilizou este código de voucher anteriormente'
      );
    END IF;
  END IF;

  -- Check email restriction
  IF voucher_record.email_restriction IS NOT NULL AND (user_email IS NULL OR voucher_record.email_restriction != user_email) THEN
    RETURN json_build_object(
      'valid', false,
      'error', 'EMAIL_RESTRICTION',
      'message', 'Este código de voucher é restrito a um email específico'
    );
  END IF;

  -- Voucher is valid
  RETURN json_build_object(
    'valid', true,
    'voucher', json_build_object(
      'id', voucher_record.id,
      'code', voucher_record.code,
      'credits', voucher_record.credits,
      'max_uses', voucher_record.max_uses,
      'current_uses', voucher_record.current_uses,
      'expires_at', voucher_record.expires_at
    )
  );
END;
$$ LANGUAGE plpgsql;

-- Add function to redeem voucher (atomic transaction)
CREATE OR REPLACE FUNCTION redeem_voucher_transaction(
  p_voucher_id uuid,
  p_user_id uuid,
  p_user_email text,
  p_credits integer
)
RETURNS json AS $$
DECLARE
  voucher_record public.vouchers%ROWTYPE;
  new_current_uses integer;
BEGIN
  -- Start transaction
  BEGIN
    -- Lock and get voucher for update
    SELECT * INTO voucher_record
    FROM public.vouchers
    WHERE id = p_voucher_id
    FOR UPDATE;

    -- Double-check voucher is still valid (race condition protection)
    IF voucher_record.status != 'active' THEN
      RAISE EXCEPTION 'Voucher não está ativo';
    END IF;

    IF voucher_record.expires_at IS NOT NULL AND voucher_record.expires_at <= now() THEN
      RAISE EXCEPTION 'Voucher expirou';
    END IF;

    IF voucher_record.current_uses >= voucher_record.max_uses THEN
      RAISE EXCEPTION 'Voucher esgotado';
    END IF;

    -- Add credit purchase record
    INSERT INTO public.credit_purchases (
      user_id,
      credits,
      amount,
      currency,
      status,
      plan_id,
      plan_name,
      created_at,
      completed_at
    ) VALUES (
      p_user_id,
      p_credits,
      0, -- Voucher = free credits
      'brl',
      'completed',
      'voucher',
      'Voucher: ' || voucher_record.code,
      now(),
      now()
    );

    -- Add voucher redemption record
    INSERT INTO public.voucher_redemptions (
      voucher_id,
      user_id,
      credits_redeemed,
      user_email,
      redeemed_at
    ) VALUES (
      p_voucher_id,
      p_user_id,
      p_credits,
      p_user_email,
      now()
    );

    -- Update voucher usage count
    new_current_uses := voucher_record.current_uses + 1;
    UPDATE public.vouchers 
    SET current_uses = new_current_uses
    WHERE id = p_voucher_id;

    -- If voucher is exhausted, mark as expired  
    IF new_current_uses >= voucher_record.max_uses THEN
      UPDATE public.vouchers 
      SET status = 'expired'
      WHERE id = p_voucher_id;
    END IF;

    RETURN json_build_object(
      'success', true,
      'credits_added', p_credits,
      'voucher_code', voucher_record.code
    );

  EXCEPTION WHEN OTHERS THEN
    -- Rollback transaction and re-raise error
    RAISE;
  END;
END;
$$ LANGUAGE plpgsql; 