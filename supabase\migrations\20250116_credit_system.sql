/*
  # Credit System Database Schema

  1. New Tables
    - `credit_purchases`
      - `id` (uuid, primary key)
      - `user_id` (uuid, references auth.users)
      - `credits` (integer, number of credits purchased)
      - `amount` (integer, amount paid in cents)
      - `currency` (text, currency code)
      - `stripe_payment_intent_id` (text, Stripe payment intent ID)
      - `status` (text, pending/completed/failed)
      - `created_at` (timestamp)
      - `completed_at` (timestamp, optional)

    - `credit_usage`
      - `id` (uuid, primary key)
      - `user_id` (uuid, references auth.users)
      - `credits_used` (integer, number of credits consumed)
      - `description` (text, description of what the credits were used for)
      - `generation_id` (uuid, optional, references cover_generations)
      - `created_at` (timestamp)

  2. Security
    - Enable RLS on all tables
    - User-specific access for credit_purchases and credit_usage
*/

-- Create credit_purchases table
CREATE TABLE IF NOT EXISTS public.credit_purchases (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) NOT NULL,
  credits integer NOT NULL CHECK (credits > 0),
  amount integer NOT NULL CHECK (amount > 0), -- Amount in cents
  currency text NOT NULL DEFAULT 'brl',
  stripe_payment_intent_id text,
  status text NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'completed', 'failed', 'cancelled')),
  plan_id text,
  plan_name text,
  created_at timestamptz DEFAULT now(),
  completed_at timestamptz
);

-- Create credit_usage table
CREATE TABLE IF NOT EXISTS public.credit_usage (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) NOT NULL,
  credits_used integer NOT NULL CHECK (credits_used > 0),
  description text NOT NULL,
  generation_id uuid REFERENCES public.cover_generations(id),
  created_at timestamptz DEFAULT now()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_credit_purchases_user_id ON public.credit_purchases(user_id);
CREATE INDEX IF NOT EXISTS idx_credit_purchases_status ON public.credit_purchases(status);
CREATE INDEX IF NOT EXISTS idx_credit_purchases_created_at ON public.credit_purchases(created_at);
CREATE INDEX IF NOT EXISTS idx_credit_usage_user_id ON public.credit_usage(user_id);
CREATE INDEX IF NOT EXISTS idx_credit_usage_created_at ON public.credit_usage(created_at);
CREATE INDEX IF NOT EXISTS idx_credit_usage_generation_id ON public.credit_usage(generation_id);

-- Enable RLS
ALTER TABLE public.credit_purchases ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.credit_usage ENABLE ROW LEVEL SECURITY;

-- RLS Policies for credit_purchases (user-specific)
CREATE POLICY "credit_purchases_select_policy" ON public.credit_purchases
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "credit_purchases_insert_policy" ON public.credit_purchases
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "credit_purchases_update_policy" ON public.credit_purchases
  FOR UPDATE USING (auth.uid() = user_id);

-- RLS Policies for credit_usage (user-specific)
CREATE POLICY "credit_usage_select_policy" ON public.credit_usage
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "credit_usage_insert_policy" ON public.credit_usage
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Add some additional fields to cover_generations for better tracking
ALTER TABLE public.cover_generations 
ADD COLUMN IF NOT EXISTS credits_used integer DEFAULT 0;

ALTER TABLE public.cover_generations 
ADD COLUMN IF NOT EXISTS user_name text;

ALTER TABLE public.cover_generations 
ADD COLUMN IF NOT EXISTS gender text CHECK (gender IN ('male', 'female'));

ALTER TABLE public.cover_generations 
ADD COLUMN IF NOT EXISTS language text DEFAULT 'portuguese';

ALTER TABLE public.cover_generations 
ADD COLUMN IF NOT EXISTS generate_titles boolean DEFAULT true;

ALTER TABLE public.cover_generations 
ADD COLUMN IF NOT EXISTS creativity_level text DEFAULT 'rostoFiel' CHECK (creativity_level IN ('criativo', 'rostoFiel', 'estrito'));

ALTER TABLE public.cover_generations 
ADD COLUMN IF NOT EXISTS progress integer DEFAULT 0 CHECK (progress >= 0 AND progress <= 100);

ALTER TABLE public.cover_generations 
ADD COLUMN IF NOT EXISTS current_movie text; 