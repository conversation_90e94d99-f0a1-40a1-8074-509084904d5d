import React, { useCallback, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { Upload, X, Image, Camera, FileImage, Folder } from 'lucide-react'
import { motion } from 'framer-motion'
import { showToast } from '../utils/toast'
import BaseImageSelector from './BaseImageSelector'

interface FileUploadProps {
  onFileSelect: (file: File) => void
  selectedFile: File | null
  onRemoveFile: () => void
  userId?: string | null
}

export default function FileUpload({ onFileSelect, selectedFile, onRemoveFile, userId }: FileUploadProps) {
  const { t } = useTranslation()
  const [isDragOver, setIsDragOver] = useState(false)
  const [showBaseImageSelector, setShowBaseImageSelector] = useState(false)
  const [selectedFromBase, setSelectedFromBase] = useState<{ url: string; fileName: string } | null>(null)

  const validateFile = (file: File): boolean => {
    // Check file type
    if (!file.type.startsWith('image/')) {
      showToast.error(t('fileUpload.errorImageType'))
      return false
    }

    // Check file size (max 10MB)
    const maxSize = 10 * 1024 * 1024 // 10MB in bytes
    if (file.size > maxSize) {
      showToast.error(t('fileUpload.errorImageSize'))
      return false
    }

    return true
  }

  const handleDrop = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    setIsDragOver(false)
    
    const files = Array.from(e.dataTransfer.files)
    const imageFile = files.find(file => file.type.startsWith('image/'))
    
    if (imageFile && validateFile(imageFile)) {
      setSelectedFromBase(null) // Clear base image selection
      onFileSelect(imageFile)
      showToast.success(t('fileUpload.successUpload'))
    }
  }, [onFileSelect, t])

  const handleDragOver = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    setIsDragOver(true)
  }, [])

  const handleDragLeave = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    setIsDragOver(false)
  }, [])

  const handleFileChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file && validateFile(file)) {
      setSelectedFromBase(null) // Clear base image selection
      onFileSelect(file)
      showToast.success(t('fileUpload.successUpload'))
    }
  }, [onFileSelect, t])

  const handleRemoveFile = () => {
    setSelectedFromBase(null)
    onRemoveFile()
    showToast.info(t('fileUpload.removed'))
  }

  const handleSelectBaseImage = (imageUrl: string, fileName: string) => {
    setSelectedFromBase({ url: imageUrl, fileName })
    
    // Convert URL to File object for compatibility
    fetch(imageUrl)
      .then(res => res.blob())
      .then(blob => {
        const file = new File([blob], fileName, { type: blob.type })
        onFileSelect(file)
        showToast.success('Foto base selecionada com sucesso!')
      })
      .catch(error => {
        console.error('Error converting base image to file:', error)
        showToast.error('Erro ao carregar a imagem selecionada')
      })
  }

  // Show selected file or base image
  if (selectedFile || selectedFromBase) {
    const previewUrl = selectedFromBase?.url || (selectedFile ? URL.createObjectURL(selectedFile) : '')
    const fileName = selectedFromBase?.fileName || selectedFile?.name || ''
    const fileSize = selectedFile?.size || 0
    
    return (
      <motion.div 
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className="w-full max-w-lg mx-auto"
      >
        <div className="bg-brand-white rounded-xl p-6 border-2 border-brand-black shadow-brutal">
          <div className="relative w-full h-80 rounded-lg overflow-hidden bg-gray-100 border-2 border-brand-black">
            <img 
              src={previewUrl} 
              alt={t('fileUpload.selectedImage')} 
              className="w-full h-full object-cover"
            />
            <motion.button
              onClick={handleRemoveFile}
              className="absolute top-3 right-3 w-10 h-10 bg-brand-accent text-white rounded-full border-2 border-brand-black hover:bg-red-700 transition-colors shadow-brutal-sm flex items-center justify-center"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              <X size={20} />
            </motion.button>
          </div>
          
          <div className="mt-6 text-center">
            <div className="flex items-center justify-center space-x-2 mb-3">
              <div className="w-10 h-10 bg-brand-primary rounded-lg flex items-center justify-center border-2 border-brand-black">
                <FileImage className="w-5 h-5 text-brand-black" />
              </div>
              <h3 className="text-xl font-bold text-brand-text">
                {selectedFromBase ? 'Foto Base Selecionada' : t('fileUpload.selectedImage')}
              </h3>
            </div>
            <p className="text-sm text-brand-text bg-gray-100 rounded-md px-3 py-2 inline-block border-2 border-brand-black">
              {fileName}
            </p>
            {selectedFile && (
              <p className="text-xs text-brand-text/70 mt-2 font-semibold">
                {(fileSize / (1024 * 1024)).toFixed(2)} MB
              </p>
            )}
            {selectedFromBase && (
              <p className="text-xs text-brand-primary mt-2 font-semibold">
                📁 Carregada da galeria de fotos base
              </p>
            )}
          </div>
        </div>
      </motion.div>
    )
  }

  return (
    <div className="w-full max-w-3xl mx-auto">
      <div
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        className={`relative w-full p-8 md:p-12 text-center transition-all duration-300 cursor-pointer rounded-xl border-2 border-dashed
          ${isDragOver ? 'bg-brand-primary/30 border-brand-primary' : 'bg-gray-50 border-gray-300'}
        `}
      >
        <div className="flex flex-col items-center space-y-6">
          <div className={`w-20 h-20 rounded-xl flex items-center justify-center transition-all duration-300 border-2 border-brand-black
            ${isDragOver ? 'bg-brand-primary' : 'bg-brand-white shadow-brutal-sm'}
          `}>
            <Upload size={40} className="text-brand-black"/>
          </div>
          
          <div className="space-y-2">
            <h3 className="text-3xl font-black text-brand-text">
              {t('fileUpload.uploadYourPhoto')}
            </h3>
            <p className="text-brand-text/80 text-lg">
              {t('fileUpload.dragAndDrop')}
            </p>
            <div className="bg-yellow-50 border-2 border-yellow-300 rounded-lg p-4 mt-4 max-w-md mx-auto">
              <p className="text-sm font-semibold text-yellow-800">
                💡 {t('fileUpload.photoTip')}
              </p>
            </div>
          </div>
            
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 pt-4">
            <div className="bg-brand-white rounded-lg p-4 border-2 border-brand-black shadow-brutal-sm text-center">
              <div className="w-12 h-12 bg-blue-200 rounded-lg flex items-center justify-center mx-auto mb-3 border-2 border-brand-black">
                <Camera className="w-6 h-6 text-brand-black" />
              </div>
              <h4 className="font-bold text-brand-text">{t('fileUpload.highQuality')}</h4>
              <p className="text-sm text-brand-text/70 mt-1">{t('fileUpload.highQualityDesc')}</p>
            </div>
            
            <div className="bg-brand-white rounded-lg p-4 border-2 border-brand-black shadow-brutal-sm text-center">
              <div className="w-12 h-12 bg-green-200 rounded-lg flex items-center justify-center mx-auto mb-3 border-2 border-brand-black">
                <FileImage className="w-6 h-6 text-brand-black" />
              </div>
              <h4 className="font-bold text-brand-text">{t('fileUpload.multipleFormats')}</h4>
              <p className="text-sm text-brand-text/70 mt-1">{t('fileUpload.multipleFormatsDesc')}</p>
            </div>
            
            <div className="bg-brand-white rounded-lg p-4 border-2 border-brand-black shadow-brutal-sm text-center">
              <div className="w-12 h-12 bg-purple-200 rounded-lg flex items-center justify-center mx-auto mb-3 border-2 border-brand-black">
                <Upload className="w-6 h-6 text-brand-black" />
              </div>
              <h4 className="font-bold text-brand-text">{t('fileUpload.upTo10MB')}</h4>
              <p className="text-sm text-brand-text/70 mt-1">{t('fileUpload.upTo10MBDesc')}</p>
            </div>
          </div>
          
          <div className="flex flex-col sm:flex-row gap-4 w-full max-w-md relative z-10">
            <label className="flex-1 bg-brand-secondary text-brand-black rounded-lg px-6 py-3 shadow-brutal-sm hover:shadow-brutal active:shadow-brutal-pressed transition-all font-bold border-2 border-brand-black cursor-pointer">
              {t('fileUpload.chooseFile')}
              <input
                type="file"
                accept="image/*"
                onChange={handleFileChange}
                className="hidden"
              />
            </label>
            
            {userId && (
              <button
                type="button"
                onClick={(e) => {
                  e.preventDefault()
                  e.stopPropagation()
                  setShowBaseImageSelector(true)
                }}
                className="flex-1 bg-brand-primary text-brand-black rounded-lg px-6 py-3 shadow-brutal-sm hover:shadow-brutal active:shadow-brutal-pressed transition-all font-bold border-2 border-brand-black flex items-center justify-center space-x-2"
              >
                <Folder className="w-5 h-5" />
                <span>Usar Foto Base</span>
              </button>
            )}
          </div>
        </div>
        
        {isDragOver && (
          <div className="absolute inset-0 bg-brand-primary/80 rounded-xl flex items-center justify-center backdrop-blur-sm">
            <div className="text-center">
              <motion.div
                animate={{ scale: [1, 1.1, 1] }}
                transition={{ repeat: Infinity, duration: 1 }}
                className="w-16 h-16 bg-white rounded-xl flex items-center justify-center mx-auto mb-4 border-2 border-brand-black shadow-brutal"
              >
                <Upload className="w-8 h-8 text-brand-black" />
              </motion.div>
              <p className="text-2xl font-black text-brand-black">{t('fileUpload.dropHere')}</p>
            </div>
          </div>
        )}
      </div>

      {/* Base Image Selector Modal */}
      <BaseImageSelector
        isOpen={showBaseImageSelector}
        onClose={() => setShowBaseImageSelector(false)}
        onSelectImage={handleSelectBaseImage}
        userId={userId || null}
      />
    </div>
  )
}