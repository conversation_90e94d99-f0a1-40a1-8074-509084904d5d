import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { X, Loader2 } from 'lucide-react';
import { signInWithGoogle, signInWithEmail, signUpWithEmail } from '../lib/supabase';
import { showToast } from '../utils/toast';

interface AuthModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAuthSuccess?: (user: any) => void;
}

export default function AuthModal({ isOpen, onClose, onAuthSuccess }: AuthModalProps) {
  const { t } = useTranslation();
  const [mode, setMode] = useState<'login' | 'signup'>('login');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);

  if (!isOpen) return null;

  const handleGoogleLogin = async () => {
    setLoading(true);
    try {
      await signInWithGoogle();
      showToast.success(t('authForm.googleRedirect'));
    } catch (error: any) {
      showToast.error(error.message || t('authForm.googleError'));
    } finally {
      setLoading(false);
    }
  };

  const handleEmailAuth = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    try {
      if (mode === 'login') {
        const data = await signInWithEmail(email, password);
        showToast.success(t('authForm.loginSuccess'));
        onAuthSuccess?.(data.user);
      } else {
        const data = await signUpWithEmail(email, password);
        showToast.success(t('authForm.signupSuccess'));
        onAuthSuccess?.(data.user);
      }
    } catch (error: any) {
      showToast.error(error.message || t('authForm.authError'));
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-brutal-lg border-2 border-black max-w-md w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="p-6 border-b-2 border-black">
          <div className="flex justify-between items-center">
            <div>
              <h2 className="text-2xl font-black text-black">
                {mode === 'login' ? t('authForm.login') : t('authForm.createAccount')}
              </h2>
              <p className="text-gray-700 text-sm mt-1">
                {mode === 'login' 
                  ? t('authForm.loginToContinue') 
                  : t('authForm.joinToStart')}
              </p>
            </div>
            <button 
              onClick={onClose} 
              className="p-2 bg-white rounded-lg border-2 border-black shadow-brutal-sm hover:shadow-brutal-hover active:shadow-brutal-pressed transition-all"
            >
              <X size={20} className="text-black" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Logo */}
          <div className="text-center mb-6">
            <img src="/img/logo-final-svg.png" alt="Logo" className="h-16 w-auto mx-auto" />
          </div>

          {/* Google Login */}
          <button
            onClick={handleGoogleLogin}
            disabled={loading}
            className="w-full flex items-center justify-center gap-3 bg-white text-black font-bold py-3 px-4 mb-4 
            border-2 border-black shadow-brutal-sm hover:shadow-brutal-hover 
            active:shadow-brutal-pressed active:translate-x-1 active:translate-y-1
            transition-all duration-150 disabled:opacity-50"
          >
            <svg width="20" height="20" viewBox="0 0 48 48" className="flex-shrink-0">
              <g clipPath="url(#clip0_17_40)">
                <path d="M47.5 24.552c0-1.636-.147-3.2-.42-4.704H24v9.02h13.22c-.57 3.08-2.28 5.68-4.86 7.44v6.16h7.86c4.6-4.24 7.28-10.5 7.28-17.916z" fill="#4285F4"/>
                <path d="M24 48c6.48 0 11.92-2.16 15.9-5.88l-7.86-6.16c-2.2 1.48-5.02 2.36-8.04 2.36-6.18 0-11.42-4.18-13.3-9.8H2.7v6.24C6.68 43.34 14.62 48 24 48z" fill="#34A853"/>
                <path d="M10.7 28.52A13.98 13.98 0 0 1 9.2 24c0-1.56.28-3.08.78-4.52v-6.24H2.7A23.98 23.98 0 0 0 0 24c0 3.98.98 7.74 2.7 10.76l8-6.24z" fill="#FBBC05"/>
                <path d="M24 9.52c3.52 0 6.66 1.2 9.14 3.56l6.84-6.84C35.92 2.16 30.48 0 24 0 14.62 0 6.68 4.66 2.7 13.24l8 6.24c1.88-5.62 7.12-9.8 13.3-9.8z" fill="#EA4335"/>
              </g>
              <defs>
                <clipPath id="clip0_17_40">
                  <rect width="48" height="48" fill="white"/>
                </clipPath>
              </defs>
            </svg>
            {t('authForm.continueWithGoogle')}
          </button>

          {/* Divider */}
          <div className="relative mb-4">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t-2 border-black"></div>
            </div>
            <div className="relative flex justify-center">
              <span className="px-4 bg-white text-black font-bold text-sm">{t('authForm.or')}</span>
            </div>
          </div>

          {/* Email Form */}
          <form onSubmit={handleEmailAuth} className="space-y-4">
            <div>
              <input
                type="email"
                placeholder={t('authForm.email')}
                value={email}
                onChange={e => setEmail(e.target.value)}
                required
                className="w-full px-4 py-3 bg-white border-2 border-black focus:ring-2 focus:ring-blue-500 focus:border-transparent text-black placeholder-gray-500 font-medium
                shadow-brutal-sm focus:shadow-brutal-hover focus:outline-none transition-all"
              />
            </div>
            <div>
              <input
                type="password"
                placeholder={t('authForm.password')}
                value={password}
                onChange={e => setPassword(e.target.value)}
                required
                className="w-full px-4 py-3 bg-white border-2 border-black focus:ring-2 focus:ring-blue-500 focus:border-transparent text-black placeholder-gray-500 font-medium
                shadow-brutal-sm focus:shadow-brutal-hover focus:outline-none transition-all"
              />
            </div>
            <button
              type="submit"
              disabled={loading}
              className="w-full bg-yellow-400 text-black font-bold py-3 px-4 border-2 border-black 
              shadow-brutal-sm hover:shadow-brutal-hover active:shadow-brutal-pressed transition-all"
            >
              {loading ? (
                <div className="flex items-center justify-center gap-2">
                  <Loader2 className="w-5 h-5 animate-spin" />
                  {t('authForm.loading')}
                </div>
              ) : (
                mode === 'login' ? t('authForm.login') : t('authForm.createAccount')
              )}
            </button>
          </form>

          {/* Toggle Mode */}
          <div className="mt-4 text-center">
            {mode === 'login' ? (
              <p className="text-black text-sm">
                {t('authForm.dontHaveAccount')} {' '}
                <button
                  type="button"
                  className="text-blue-600 font-bold hover:underline focus:outline-none"
                  onClick={() => setMode('signup')}
                >
                  {t('authForm.signUp')}
                </button>
              </p>
            ) : (
              <p className="text-black text-sm">
                {t('authForm.alreadyHaveAccount')} {' '}
                <button
                  type="button"
                  className="text-blue-600 font-bold hover:underline focus:outline-none"
                  onClick={() => setMode('login')}
                >
                  {t('authForm.doLogin')}
                </button>
              </p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
} 