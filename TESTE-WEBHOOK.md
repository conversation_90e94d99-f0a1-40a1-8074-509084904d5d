# 🧪 Guia de Teste - Webhook Atualizado

## ✅ **O que foi Atualizado:**

### 1. **IDs dos Produtos - Separação Test/Prod**

#### 🧪 **Test Mode (Desenvolvimento - Atual):**
- ✅ `prod_SVeFgga19mY0Gl` - Starter Credits (30 créditos)
- ✅ `prod_SVeHwRl5tn4cP6` - Popular Credits (100 créditos)  
- ✅ `prod_SVeIPaf5R93PYg` - Master Credits (300 créditos)

#### 🚀 **Live Mode (Produção - Seus IDs):**
- ✅ `prod_SadnLW125oNEVc` - Starter Credits (30 créditos)
- ✅ `prod_SadnvZanedY48s` - Popular Credits (100 créditos)  
- ✅ `prod_Sadn6aBJvTKEz` - Master Credits (300 créditos)

### 2. **Webhook Atualizado**
- ✅ Agora salva na tabela `credit_purchases` (sistema novo)
- ✅ Detecta automaticamente se é `credits` ou `packs` (legacy)
- ✅ Usa os metadados corretos do pagamento

## 🧪 **Como Testar (Modo Desenvolvimento):**

### 1. **Teste de Compra de Créditos**

1. **Acesse a aplicação**: http://localhost:5174
2. **Faça login** com sua conta
3. **Clique em "Buy More Packs"** (será renomeado depois)
4. **Escolha um plano** (ex: Starter Credits)
5. **Use cartão de teste**: `4242 4242 4242 4242`
   - Qualquer CVC (ex: 123)
   - Qualquer data futura (ex: 12/25)
6. **Complete o pagamento**

### 2. **Verificar se Funcionou**

#### No Dashboard da Aplicação:
1. Vá para **Dashboard → Aba "Créditos"**
2. Verifique se apareceu:
   - ✅ **Créditos disponíveis** aumentaram
   - ✅ **Histórico de compras** mostra a transação

#### No Supabase Dashboard:
1. Acesse: https://supabase.com/dashboard/project/uptmptfpumgrnlxukwau
2. Vá em **Table Editor**
3. Verifique a tabela `credit_purchases`:
   ```sql
   SELECT * FROM credit_purchases ORDER BY created_at DESC LIMIT 5;
   ```

#### No Stripe Dashboard:
1. Vá em **Webhooks** → Seu webhook
2. Verifique se apareceu evento `payment_intent.succeeded`
3. Status deve estar **✅ Succeeded**

### 3. **Teste de Consumo de Créditos**

1. **Tente gerar capas** na aplicação
2. **Verifique se:**
   - ✅ Créditos foram debitados antes da geração
   - ✅ Dashboard mostra uso correto
   - ✅ Tabela `credit_usage` tem registros

## 🚀 **Para Produção (Quando For Deploy):**

### Configurar Variáveis de Ambiente:
```env
# Frontend (.env.production)
VITE_STRIPE_PUBLISHABLE_KEY=pk_live_SUA_CHAVE_LIVE
VITE_STRIPE_STARTER_CREDITS_ID=prod_SadnLW125oNEVc
VITE_STRIPE_POPULAR_CREDITS_ID=prod_SadnvZanedY48s
VITE_STRIPE_MASTER_CREDITS_ID=prod_Sadn6aBJvTKEz

# Supabase (Dashboard)
STRIPE_SECRET_KEY=sk_live_SUA_CHAVE_SECRETA_LIVE
STRIPE_WEBHOOK_SECRET=whsec_SUA_WEBHOOK_SECRET_LIVE
```

## 🔍 **Logs para Monitorar**

### Supabase Edge Functions Logs:
```bash
# Ver logs do webhook
npx supabase functions logs stripe-webhook

# Ver logs das funções de crédito
npx supabase functions logs check-credit-balance
npx supabase functions logs consume-credits
```

### No Stripe Dashboard:
- **Webhooks** → **Logs** → Verificar eventos recentes
- **Payments** → Verificar se pagamentos estão sendo processados

## ⚠️ **Possíveis Problemas e Soluções**

### Problema: Webhook retorna 404
**Solução**: Verificar se a URL está correta:
```
https://uptmptfpumgrnlxukwau.supabase.co/functions/v1/stripe-webhook
```

### Problema: Créditos não aparecem
**Verificar**:
1. Logs do webhook no Supabase
2. Se tabela `credit_purchases` existe
3. Se RLS está configurado corretamente

### Problema: Erro de metadados
**Verificar**:
1. Se `planType: 'credits'` está sendo enviado
2. Se `credits` está nos metadados
3. Logs da função `create-payment-intent`

## 🎯 **Teste Completo - Checklist**

- [ ] **Compra**: Cartão de teste → Pagamento aprovado
- [ ] **Webhook**: Evento recebido e processado
- [ ] **Database**: Registro na `credit_purchases`
- [ ] **Dashboard**: Créditos aparecem na interface
- [ ] **Consumo**: Gerar capas debita créditos
- [ ] **Histórico**: Uso aparece na `credit_usage`

## 🚀 **Próximos Passos**

Após confirmar que está funcionando:

1. **Atualizar interface**: Trocar "Buy More Packs" por "Comprar Créditos"
2. **Remover sistema antigo**: Limpar código legacy de packs
3. **Produção**: Usar chaves live do Stripe + seus IDs de produção
4. **Monitoramento**: Configurar alertas de erro

---

**🔥 O webhook está atualizado e pronto para o sistema de créditos!**

### 📊 **Resumo da Configuração:**

| Ambiente | Stripe Mode | IDs Usados | Pagamentos |
|----------|-------------|------------|------------|
| **Desenvolvimento** | Test | `prod_SVeFgga19mY0Gl` etc | Simulados |
| **Produção** | Live | `prod_SadnLW125oNEVc` etc | Reais | 