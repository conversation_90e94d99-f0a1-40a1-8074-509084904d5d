import { movieDatabase, genderAdjustments, niveisDeCriatividade, coupleAdjustments } from './movieDatabase.ts'

// Função para estimar tokens (aproximação simples)
function estimateTokens(text: string): number {
  // Aproximação: 1 token ≈ 4 caracteres em português/inglês
  // Mais preciso que contar caracteres
  const words = text.split(/\s+/).filter(word => word.length > 0);
  const punctuation = (text.match(/[.,!?;:()[\]{}'"]/g) || []).length;
  return Math.ceil(words.length + punctuation * 0.3);
}

// Função para truncar mantendo tokens importantes
function truncateToTokenLimit(text: string, maxTokens: number = 512): string {
  const estimatedTokens = estimateTokens(text);
  
  if (estimatedTokens <= maxTokens) {
    return text;
  }
  
  // Calcular caracteres aproximados para o limite de tokens
  const targetChars = Math.floor(maxTokens * 3.5); // Aproximação mais conservadora
  
  // Truncar mantendo palavras completas
  const truncated = text.substring(0, targetChars);
  const lastSpace = truncated.lastIndexOf(' ');
  
  return lastSpace > 0 ? truncated.substring(0, lastSpace) + '...' : truncated;
}

export function buildPrompt(
  base: string,
  opts: {
    language: 'english' | 'portuguese',
    photoType: 'individual' | 'casal',
    gender?: 'male' | 'female',
    addTitle: boolean,
    movieTitle: string,
    variation?: number, // Para regeneração
    coverIndex?: number, // Para variação de pose
    creativityLevel?: 'criativo' | 'rostoFiel' | 'estrito', // Novo parâmetro
  }
) {
  const { language, photoType, gender, addTitle, movieTitle, variation = 0, coverIndex = 0, creativityLevel = 'rostoFiel' } = opts;

  // 🔥 TÍTULO PRIMEIRO (PRIORIDADE MÁXIMA)
  const titleInstruction = addTitle
    ? language === 'portuguese'
      ? `IMPORTANTE: Adicione o título "${movieTitle}" na parte INFERIOR CENTRAL da imagem com fonte elegante e legível. O texto deve estar SEMPRE VISÍVEL sobre um fundo escuro semi-transparente ou com contorno para garantir legibilidade.`
      : `IMPORTANT: Add the title "${movieTitle}" at the BOTTOM CENTER of the image with elegant and readable font. The text must ALWAYS be VISIBLE over a dark semi-transparent background or with outline to ensure readability.`
    : '';

  // 🔥 NÍVEL DE CRIATIVIDADE (SUBSTITUI A IDENTIDADE ANTIGA)
  const creativityInstruction = niveisDeCriatividade[creativityLevel][language];

  // 🔥 AJUSTE PARA CASAIS OU INDIVÍDUOS
  let adjustedBase = base;
  
  if (photoType === 'casal') {
    // 🔥 USAR AJUSTES ESPECÍFICOS PARA CASAIS
    const coupleAdjustment = (coupleAdjustments as any)[movieTitle];
    if (coupleAdjustment && coupleAdjustment[language]) {
      adjustedBase = coupleAdjustment[language];
    } else {
      // Fallback: usar prompt base mas adaptar para duas pessoas
      adjustedBase = base.replace(/a pessoa/gi, 'o casal').replace(/the person/gi, 'the couple');
    }
  } else {
    // 🔥 AJUSTE DE GÊNERO APLICADO APENAS PARA INDIVÍDUOS
    adjustedBase = gender ? applyGenderAdjustments(base, gender, language, movieTitle) : base;
  }

  // 🔥 POSES VARIADAS
  const poseVariations = language === 'portuguese' ? [
    'com pose frontal confiante e olhar direto',
    'com pose de três quartos e expressão determinada', 
    'com pose lateral dramática e iluminação cinematográfica',
    'com pose dinâmica em movimento e ângulo heroico',
    'com pose contemplativa e olhar distante',
    'com pose poderosa e postura imponente',
    'com pose casual mas carismática',
    'com pose misteriosa e sombras dramáticas',
    'com pose épica e composição grandiosa',
    'com pose intimista e foco no personagem',
    'com pose de ação e energia vibrante',
    'com pose elegante e sofisticada'
  ] : [
    'confident frontal pose',
    'determined three-quarter pose',
    'dramatic side pose', 
    'dynamic heroic pose',
    'serene contemplative pose',
    'intense action pose',
    'elegant sophisticated pose',
    'casual natural pose',
    'mysterious shadowy pose',
    'powerful dominant pose',
    'emotional expressive pose',
    'cinematic epic pose'
  ];

  const poseVariation = poseVariations[coverIndex % poseVariations.length];

  // 🔥 VARIAÇÃO PARA REGENERAÇÃO
  const variationText = variation > 0 
    ? (language === 'portuguese' ? `Variação ${variation}.` : `Variation ${variation}.`)
    : '';

  // 🔥 SEGURANÇA
  const safety = language === 'portuguese'
    ? 'Conteúdo seguro, respeitoso.'
    : 'Safe, respectful content.';

  // 🔥 CONTAGEM DE PESSOAS
  const peopleCount = photoType === 'casal'
    ? (language === 'portuguese' ? 'Duas pessoas formando um casal.' : 'Two people forming a couple.')
    : (language === 'portuguese' ? 'Uma pessoa.' : 'One person.');

  // 🔥 DICA DE GÊNERO (apenas para indivíduos)
  const genderHint = (photoType === 'individual' && gender) 
    ? (language === 'portuguese' 
        ? (gender === 'male' ? 'Pessoa masculina.' : 'Pessoa feminina.')
        : (gender === 'male' ? 'Male person.' : 'Female person.'))
    : '';

  // 🔥 CONSTRUIR PROMPT OTIMIZADO - TÍTULO EM PRIORIDADE MÁXIMA
  const promptParts = [
    titleInstruction, // TÍTULO SEMPRE EM PRIMEIRO
    adjustedBase.trim(),
    creativityInstruction,
    poseVariation,
    peopleCount,
    genderHint,
    variationText,
    safety
  ].filter(part => part.length > 0);

  const fullPrompt = promptParts.join(' ');
  
  // 🔥 TRUNCAR PARA LIMITE DE TOKENS
  const optimizedPrompt = truncateToTokenLimit(fullPrompt, 512);
  
  console.log(`🔍 Prompt tokens estimados: ${estimateTokens(optimizedPrompt)}/512`);
  console.log(`🔍 Prompt caracteres: ${optimizedPrompt.length}`);
  
  return optimizedPrompt;
}

export function applyGenderAdjustments(
  basePrompt: string, 
  gender: 'male' | 'female', 
  language: 'english' | 'portuguese',
  movieTitle: string
): string {
  // Check if this movie has gender-specific adjustments
  const movieAdjustments = genderAdjustments[movieTitle];
  if (movieAdjustments && movieAdjustments[gender] && movieAdjustments[gender][language]) {
    // 🔥 SUBSTITUIR COMPLETAMENTE A INSTRUÇÃO DE ROUPA PARA EVITAR DUPLICAÇÃO
    const genderSpecificClothing = movieAdjustments[gender][language];
    
    // Substituir instruções de roupa de forma mais precisa
    if (language === 'portuguese') {
      // Primeiro, tentar substituir frases completas de roupa
      let result = basePrompt
        .replace(/vista a pessoa com[^.]+\./i, genderSpecificClothing + '.')
        .replace(/troque as roupas por[^.]+\./i, genderSpecificClothing + '.')
        .replace(/mude as roupas para[^.]+\./i, genderSpecificClothing + '.')
        .replace(/substitua as roupas[^.]+\./i, genderSpecificClothing + '.')
        .replace(/roupas [^.]+\./i, genderSpecificClothing + '.');
      
      // Se não houve substituição, adicionar no início
      if (result === basePrompt) {
        result = genderSpecificClothing + '. ' + basePrompt;
      }
      
      return result;
    } else {
      // Inglês
      let result = basePrompt
        .replace(/dress them in[^.]+\./i, genderSpecificClothing + '.')
        .replace(/swap clothes for[^.]+\./i, genderSpecificClothing + '.')
        .replace(/change the clothes to[^.]+\./i, genderSpecificClothing + '.')
        .replace(/replace their clothing with[^.]+\./i, genderSpecificClothing + '.');
      
      // Se não houve substituição, adicionar no início
      if (result === basePrompt) {
        result = genderSpecificClothing + '. ' + basePrompt;
      }
      
      return result;
    }
  }

  return basePrompt;
}

export function createSafePrompt(
  movie: any, // 🔥 Alterado para receber o objeto do filme inteiro
  photoType: string
): any {
  const language = 'portuguese';

  // 🔥 LÓGICA NOVA: Usar o prompt seguro dedicado se existir
  if (movie.safe_prompt_portuguese) {
    console.log(`🛡️ Usando safe_prompt_portuguese para: ${movie.title}`);
    const safePrompt = movie.safe_prompt_portuguese;
    
    // Manter a instrução do título se existir no prompt original
    const originalPrompt = movie.prompt[language] || movie.prompt.english;
    const titleMatch = originalPrompt.match(/IMPORTANTE: Adicione o título "([^"]+)"[^.]*\./i);
    const titleText = titleMatch ? titleMatch[0] : '';

    const finalPrompt = titleText 
      ? `${titleText} ${safePrompt}`
      : safePrompt;

    return {
      [language]: `${finalPrompt} (versão segura).`,
      english: `${finalPrompt} (safe version).` // Fallback simples para inglês
    };
  }
  
  // Lógica antiga de fallback (caso o campo não exista)
  console.warn(`⚠️ safe_prompt_portuguese não encontrado para ${movie.title}. Usando método antigo.`);
  const basePrompt = movie.prompt[language] || movie.prompt.english;
  
  let saferPrompt = basePrompt.replace(/arma[s]?|gun[s]?/gi, 'objeto');
  // ... (outros replaces antigos) ...

  return {
    [language]: `${saferPrompt} VERSÃO SEGURA: Ambiente profissional e familiar.`,
    english: `${saferPrompt} SAFE VERSION: Professional and family-friendly environment.`
  };
}

export function createPromptVariation(originalPrompt: any, photoType: string): any {
  const language = 'portuguese'
  const basePrompt = originalPrompt[language] || originalPrompt.english
  
  // Extrair título se existir
  const titleMatch = basePrompt.match(/IMPORTANTE: Adicione o título "([^"]+)"[^.]*\./i)
  const titleText = titleMatch ? titleMatch[0] : ''
  
  const variations = [
    'com iluminação cinematográfica dramática e ângulo ligeiramente diferente',
    'com composição visual alternativa e paleta de cores mais vibrante', 
    'com atmosfera mais intensa e detalhes aprimorados',
    'com estilo visual ligeiramente diferente e melhor qualidade',
    'com ângulo de câmera alternativo e iluminação profissional',
    'com composição renovada e elementos visuais refinados'
  ]

  const randomVariation = variations[Math.floor(Math.random() * variations.length)]
  
  // Garantir que o título permaneça no início se existir
  const finalPrompt = titleText 
    ? `${titleText} ${basePrompt.replace(titleText, '').trim()}`
    : basePrompt
  
  return {
    [language]: `${finalPrompt} REGENERAÇÃO: ${randomVariation}.`,
    english: `${finalPrompt} REGENERATION: with alternative cinematic lighting and slightly different angle.`
  }
}

export function buildSaferPrompt(originalPrompt: string, language: 'english' | 'portuguese' = 'english') {
  // Extrair título se existir
  const titleMatch = originalPrompt.match(/IMPORTANTE: Adicione o título "([^"]+)"[^.]*\./i) || 
                    originalPrompt.match(/IMPORTANT: Add the title "([^"]+)"[^.]*\./i)
  const titleText = titleMatch ? titleMatch[0] : ''
  
  const extraSafety = language === 'portuguese'
    ? ' EXTRA SEGURO: Remova qualquer representação de armas ou violência. Use iluminação suave de estúdio e fundo neutro se necessário. Conteúdo totalmente familiar e seguro.'
    : ' EXTRA SAFE: Remove any depiction of weapons or violence. Employ soft studio lighting and neutral background if necessary. Completely family-friendly and safe content.';
  
  // Garantir que o título permaneça no início se existir
  const finalPrompt = titleText 
    ? `${titleText} ${originalPrompt.replace(titleText, '').trim()}`
    : originalPrompt
  
  return finalPrompt + extraSafety;
}

// Função de polling que ambas as funções usam
export async function pollForResult(predictionId: string, movieTitle: string, replicateToken: string): Promise<{ imageUrl?: string, error?: string }> {
  const maxAttempts = 30
  const delayMs = 10000

  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    await new Promise(resolve => setTimeout(resolve, delayMs))

    try {
      const response = await fetch(`https://api.replicate.com/v1/predictions/${predictionId}`, {
        headers: {
          'Authorization': `Token ${replicateToken}`,
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        console.error(`Poll attempt ${attempt} for ${movieTitle}: HTTP ${response.status}`)
        continue
      }

      const result = await response.json()
      console.log(`Poll attempt ${attempt} for ${movieTitle}: ${result.status}`)

      if (result.status === 'succeeded') {
        let imageUrl = result.output

        if (Array.isArray(imageUrl)) {
          imageUrl = imageUrl[0]
        }

        if (typeof imageUrl !== 'string') {
          console.log(`Warning: using 'prediction.output' for ${movieTitle} as fallback. Value:`, imageUrl)
          imageUrl = result.output
        }

        console.log(`Poll attempt ${attempt} for ${movieTitle}: succeeded`)
        return { imageUrl }
      }

      if (result.status === 'failed') {
        const errorMessage = result.error || 'Unknown error'
        console.error(`Poll attempt ${attempt} for ${movieTitle}: failed with error:`, errorMessage)
        return { error: errorMessage }
      }

    } catch (error) {
      console.error(`Poll attempt ${attempt} for ${movieTitle}: error:`, error)
    }
  }

  console.error(`Polling timeout for ${movieTitle}`)
  return { error: 'Polling timeout' }
} 

// 🔥 FUNÇÃO SIMPLIFICADA para evitar problemas de parsing da API
export function buildSimplePrompt(
  basePrompt: string,
  opts: {
    language: 'english' | 'portuguese',
    photoType: 'individual' | 'casal',
    gender?: 'male' | 'female',
    addTitle: boolean,
    movieTitle: string,
  }
): string {
  const { language, photoType, gender, addTitle, movieTitle } = opts;

  // 🔥 VERIFICAR SE TEMOS UM PROMPT BASE VÁLIDO
  if (!basePrompt || typeof basePrompt !== 'string') {
    console.error('❌ Invalid basePrompt:', basePrompt);
    return 'Generate a movie poster style image.'; // Fallback básico
  }

  // 🔥 INSTRUÇÕES ESSENCIAIS NO INÍCIO
  const instructions = [];

  // 1. TÍTULO (se solicitado) - SEMPRE EM PRIMEIRO
  if (addTitle) {
    const titleInstruction = language === 'portuguese'
      ? `IMPORTANTE: Adicione o título "${movieTitle}" na parte inferior da imagem com fonte legível e visível.`
      : `IMPORTANT: Add the title "${movieTitle}" at the bottom of the image with readable and visible font.`;
    instructions.push(titleInstruction);
  }

  // 2. NÚMERO DE PESSOAS E GÊNERO - INSTRUÇÕES CLARAS
  if (photoType === 'casal') {
    const coupleInstruction = language === 'portuguese'
      ? 'Duas pessoas formando um casal (um homem e uma mulher juntos).'
      : 'Two people forming a couple (a man and a woman together).';
    instructions.push(coupleInstruction);
  } else {
    // Para indivíduos, ser mais específico
    if (gender === 'male') {
      const maleInstruction = language === 'portuguese'
        ? 'Uma única pessoa do sexo masculino (homem).'
        : 'A single male person (man).';
      instructions.push(maleInstruction);
    } else if (gender === 'female') {
      const femaleInstruction = language === 'portuguese'
        ? 'Uma única pessoa do sexo feminino (mulher).'
        : 'A single female person (woman).';
      instructions.push(femaleInstruction);
    } else {
      const individualInstruction = language === 'portuguese'
        ? 'Uma única pessoa.'
        : 'A single person.';
      instructions.push(individualInstruction);
    }
  }

  // 3. APLICAR AJUSTES DE GÊNERO SE NECESSÁRIO
  let adjustedPrompt = basePrompt;
  
  // Se é individual e tem gênero definido, aplicar ajustes específicos
  if (photoType === 'individual' && gender) {
    try {
      const movieAdjustments = (genderAdjustments as any)[movieTitle];
      if (movieAdjustments && movieAdjustments[gender] && movieAdjustments[gender][language]) {
        const genderSpecificClothing = movieAdjustments[gender][language];
        
        // Substituir instruções de roupa ou adicionar no início
        if (language === 'portuguese') {
          let tempPrompt = adjustedPrompt
            .replace(/vista a pessoa com[^.]+\./i, genderSpecificClothing + '.')
            .replace(/troque as roupas por[^.]+\./i, genderSpecificClothing + '.')
            .replace(/mude as roupas para[^.]+\./i, genderSpecificClothing + '.')
            .replace(/substitua as roupas[^.]+\./i, genderSpecificClothing + '.');
          
          // Se não houve substituição, adicionar no início
          if (tempPrompt === adjustedPrompt) {
            adjustedPrompt = genderSpecificClothing + '. ' + adjustedPrompt;
          } else {
            adjustedPrompt = tempPrompt;
          }
        } else {
          let tempPrompt = adjustedPrompt
            .replace(/dress them in[^.]+\./i, genderSpecificClothing + '.')
            .replace(/swap clothes for[^.]+\./i, genderSpecificClothing + '.')
            .replace(/change the clothes to[^.]+\./i, genderSpecificClothing + '.')
            .replace(/replace their clothing with[^.]+\./i, genderSpecificClothing + '.');
          
          if (tempPrompt === adjustedPrompt) {
            adjustedPrompt = genderSpecificClothing + '. ' + adjustedPrompt;
          } else {
            adjustedPrompt = tempPrompt;
          }
        }
      }
    } catch (error) {
      console.error('❌ Error applying gender adjustments:', error);
      // Manter o prompt original se houver erro
    }
  }

  // 4. VERIFICAR SE adjustedPrompt AINDA É VÁLIDO
  if (!adjustedPrompt || typeof adjustedPrompt !== 'string') {
    console.error('❌ adjustedPrompt became invalid:', adjustedPrompt);
    adjustedPrompt = basePrompt; // Fallback para o prompt original
  }

  // 5. MONTAR PROMPT FINAL
  const finalInstructions = instructions.join(' ');
  const finalPrompt = finalInstructions 
    ? `${finalInstructions} ${adjustedPrompt}`
    : adjustedPrompt;

  console.log(`🔍 buildSimplePrompt result: "${finalPrompt.substring(0, 100)}..."`);
  
  return finalPrompt;
} 