import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { Moon, Sun, Play, Sparkles, Zap, Star, ArrowRight, Check, LogOut, Home } from 'lucide-react';
import { BrazilFlag, USFlag } from '../components/icons';
import InteractiveDemo from '../components/InteractiveDemo';
import BeforeAfterDemo from '../components/BeforeAfterDemo';
import { supabase } from '../lib/supabase';
import PricingModal from '../components/PricingModal';
import AuthModal from '../components/AuthModal';
import CheckoutForm from '../components/CheckoutForm';
import { usePayment } from '../hooks/usePayment';
import { useCreditBalance } from '../hooks/useCreditBalance';
import { useVoucher } from '../hooks/useVoucher';
import { showToast } from '../utils/toast';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Toaster } from 'sonner';
import { SparklesText } from '../components/magicui/sparkles-text';
import { VelocityScroll } from '../components/magicui/scroll-based-velocity';
import { BorderBeam } from '../components/magicui/border-beam';
import { NumberTicker } from '../components/magicui/number-ticker';
import { AnimatedGradientText } from '../components/magicui/animated-gradient-text';
import { WarpBackground } from '../components/magicui/warp-background';
import { AnimatedBeam } from '../components/magicui/animated-beam';

// Create a QueryClient for the landing page
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 30 * 1000,
      gcTime: 5 * 60 * 1000,
      retry: 3,
    },
  },
});

const LandingPageContent: React.FC = () => {
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const [isDark, setIsDark] = useState(false);
  const [user, setUser] = useState<{ id: string; email?: string } | null>(null);
  
  // Modal states
  const [showPricingModal, setShowPricingModal] = useState(false);
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [showCheckoutModal, setShowCheckoutModal] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState<string | null>(null);
  const [paymentIntent, setPaymentIntent] = useState<{ clientSecret: string } | null>(null);
  const [pendingVoucherCode, setPendingVoucherCode] = useState<string | null>(null);
  
  // Hooks
  const { createPaymentIntent, loading: paymentLoading, stripePromise } = usePayment();
  const { creditBalance, refetch: refetchCredits } = useCreditBalance(user?.id || null);
  const { redeemVoucher } = useVoucher();

  // Apply theme to document
  useEffect(() => {
    if (isDark) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  }, [isDark]);

  // Check if user is logged in
  useEffect(() => {
    const checkUser = async () => {
      const { data: { session } } = await supabase.auth.getSession();
      setUser(session?.user || null);
    };

    checkUser();

    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (_, session) => {
        setUser(session?.user || null);
      }
    );

    return () => subscription.unsubscribe();
  }, []);

  const handleLanguageChange = (lang: string) => {
    i18n.changeLanguage(lang);
  };

  const handleLogout = async () => {
    await supabase.auth.signOut();
    setUser(null);
  };

  const handleGoToDashboard = () => {
    navigate('/dashboard');
  };

  const handleWatchDemo = () => {
    const demoSection = document.getElementById('demo-section');
    if (demoSection) {
      demoSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  // Handle "VIRAR ESTRELA AGORA" button click
  const handleGetStarted = () => {
    if (user) {
      // Se logado, ir para o dashboard
      navigate('/dashboard');
    } else {
      // Se deslogado, abrir modal de preços (será redirecionado para login ao tentar comprar)
      setShowPricingModal(true);
    }
  };

  // Handle plan selection in pricing modal
  const handleSelectPlan = async (planId: string) => {
    if (!user) {
      // User not logged in, save selected plan and show auth modal
      setSelectedPlan(planId);
      setShowAuthModal(true);
      return;
    }

    // User is logged in, proceed with payment
    await proceedWithPayment(planId);
  };

  // Proceed with payment creation
  const proceedWithPayment = async (planId: string) => {
    try {
      const result = await createPaymentIntent(planId, user!.id);
      setPaymentIntent(result);
      setSelectedPlan(planId);
      setShowPricingModal(false);
      setShowCheckoutModal(true);
    } catch (error) {
      console.error('Error creating payment intent:', error);
    }
  };

  // Handle successful authentication
  const handleAuthSuccess = async (authenticatedUser: any) => {
    setUser(authenticatedUser);
    setShowAuthModal(false);
    
    // If user had a pending voucher, redeem it
    if (pendingVoucherCode) {
      try {
        const result = await redeemVoucher(pendingVoucherCode, authenticatedUser.id, authenticatedUser.email);
        if (result.success) {
          showToast.success(result.message);
          refetchCredits();
        } else {
          showToast.error(result.message);
        }
      } catch (error) {
        showToast.error('Erro ao resgatar voucher após login.');
      } finally {
        setPendingVoucherCode(null);
      }
      return;
    }
    
    // If user was trying to buy a plan, proceed with payment
    if (selectedPlan) {
      await proceedWithPayment(selectedPlan);
      return;
    }
    
    // If no specific action is pending, redirect to dashboard
    navigate('/dashboard');
  };

  // Handle voucher auth requirement
  const handleVoucherAuthRequired = (voucherCode: string) => {
    setPendingVoucherCode(voucherCode);
    setShowAuthModal(true);
  };

  // Handle successful payment
  const handlePaymentSuccess = () => {
    setShowCheckoutModal(false);
    setPaymentIntent(null);
    setSelectedPlan(null);
    refetchCredits();
    showToast.success(t('payment.success', 'Pagamento realizado com sucesso! Seus créditos foram adicionados.'));
  };

  // Handle payment cancellation
  const handlePaymentCancel = () => {
    setShowCheckoutModal(false);
    setPaymentIntent(null);
    setSelectedPlan(null);
    setShowPricingModal(true); // Go back to pricing modal
  };

  // Close all modals
  const closeAllModals = () => {
    setShowPricingModal(false);
    setShowAuthModal(false);
    setShowCheckoutModal(false);
    setPaymentIntent(null);
    setSelectedPlan(null);
  };

  return (
    <div className={`min-h-screen transition-colors duration-300 overflow-x-hidden max-w-screen relative ${
      isDark 
        ? 'bg-black text-white' 
        : 'bg-white text-black'
    }`}>
      {/* Header */}
      <header className="relative z-50 p-4 md:p-6">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          {/* Logo */}
          <div className="flex items-center space-x-3">
            <img 
              src="/img/logo-final-svg.png" 
              alt="PosterFlix Online" 
              className="h-8 md:h-12 w-auto"
            />
          </div>

          {/* Controls */}
          <div className="flex items-center space-x-2 md:space-x-4">
            {/* Login Button - Show if logged out */}
            {!user && (
              <button
                onClick={() => setShowAuthModal(true)}
                className={`flex items-center space-x-1 md:space-x-2 px-2 md:px-4 py-2 border-2 font-bold transition-all duration-200 text-sm md:text-base ${
                  isDark
                    ? 'border-white bg-blue-500 text-white shadow-brutal-sm hover:shadow-brutal-hover active:shadow-brutal-pressed'
                    : 'border-black bg-blue-500 text-white shadow-brutal-sm hover:shadow-brutal-hover active:shadow-brutal-pressed'
                } transform hover:-translate-y-1 rounded-lg`}
              >
                <span>Login</span>
              </button>
            )}

            {/* User Actions - Show if logged in */}
            {user && (
              <>
                <button
                  onClick={handleGoToDashboard}
                  className={`flex items-center space-x-1 md:space-x-2 px-2 md:px-4 py-2 border-2 font-bold transition-all duration-200 text-sm md:text-base ${
                    isDark
                      ? 'border-white bg-yellow-500 text-black shadow-brutal-sm hover:shadow-brutal-hover active:shadow-brutal-pressed'
                      : 'border-black bg-yellow-500 text-black shadow-brutal-sm hover:shadow-brutal-hover active:shadow-brutal-pressed'
                  } transform hover:-translate-y-1 rounded-lg`}
                >
                  <Home className="w-4 h-4" />
                  <span className="hidden sm:inline">Dashboard</span>
                </button>
                
                <button
                  onClick={handleLogout}
                  className={`flex items-center space-x-1 md:space-x-2 px-2 md:px-4 py-2 border-2 font-bold transition-all duration-200 text-sm md:text-base ${
                    isDark
                      ? 'border-white bg-red-500 text-white shadow-brutal-sm hover:shadow-brutal-hover active:shadow-brutal-pressed'
                      : 'border-black bg-red-500 text-white shadow-brutal-sm hover:shadow-brutal-hover active:shadow-brutal-pressed'
                  } transform hover:-translate-y-1 rounded-lg`}
                >
                  <LogOut className="w-4 h-4" />
                  <span className="hidden sm:inline">Sair</span>
                </button>
              </>
            )}

            {/* Language Switcher */}
            <div className={`flex items-center border-2 p-1 rounded-lg ${
              isDark ? 'border-white bg-gray-900 shadow-brutal-sm' : 'border-black bg-gray-100 shadow-brutal-sm'
            }`}>
              <button
                onClick={() => handleLanguageChange('pt-BR')}
                className={`p-2 transition-all duration-200 rounded ${
                  i18n.language === 'pt-BR' 
                    ? 'bg-green-500' 
                    : 'hover:bg-gray-200 dark:hover:bg-gray-800'
                }`}
              >
                <BrazilFlag className="w-5 h-5" />
              </button>
              <button
                onClick={() => handleLanguageChange('en')}
                className={`p-2 transition-all duration-200 rounded ${
                  i18n.language === 'en' 
                    ? 'bg-blue-500' 
                    : 'hover:bg-gray-200 dark:hover:bg-gray-800'
                }`}
              >
                <USFlag className="w-5 h-5" />
              </button>
            </div>

            {/* Theme Toggle */}
            <button
              onClick={() => setIsDark(!isDark)}
              className={`p-3 border-2 transition-all duration-200 rounded-lg ${
                isDark 
                  ? 'border-white bg-gray-900 hover:bg-gray-800 shadow-brutal-sm hover:shadow-brutal-hover active:shadow-brutal-pressed' 
                  : 'border-black bg-gray-100 hover:bg-gray-200 shadow-brutal-sm hover:shadow-brutal-hover active:shadow-brutal-pressed'
              } transform hover:-translate-y-1`}
            >
              {isDark ? <Sun className="w-5 h-5" /> : <Moon className="w-5 h-5" />}
            </button>
          </div>
        </div>
      </header>

      {/* Hero Section - Neo-Brutal with Warp Background */}
      <WarpBackground>
        <section className="relative px-4 md:px-6 py-16 md:py-32 overflow-hidden">
          {/* Brutal background shapes */}
          <div className="absolute inset-0 overflow-hidden">
            <div className={`absolute top-20 left-4 md:left-20 w-16 h-16 md:w-32 md:h-32 border-4 md:border-8 ${
              isDark ? 'border-yellow-400' : 'border-red-500'
            } transform rotate-45`} />
            <div className={`absolute bottom-20 right-4 md:right-20 w-12 h-12 md:w-24 md:h-24 ${
              isDark ? 'bg-green-400' : 'bg-blue-500'
            } transform -rotate-12`} />
            <div className={`hidden md:block absolute top-1/2 left-1/4 w-20 h-20 border-6 ${
              isDark ? 'border-pink-400' : 'border-purple-500'
            }`} />
          </div>

          <div className="max-w-7xl mx-auto text-center relative z-10">
            {/* Badge - Brutal */}
            <div className={`inline-flex items-center space-x-3 px-6 py-3 border-8 mb-12 ${
              isDark 
                ? 'border-white bg-gray-900 shadow-brutal-lg hover:shadow-brutal-hover' 
                : 'border-black bg-white shadow-brutal-lg hover:shadow-brutal-hover'
            } transform hover:scale-105 transition-all duration-200`}>
              <Zap className="w-5 h-5" style={{ color: '#FFC764' }} />
              <AnimatedGradientText
                className="text-sm font-bold uppercase tracking-wider"
                colorFrom={isDark ? "#FFC764" : "#ff0000"}
                colorTo={isDark ? "#FFE066" : "#ff6b6b"}
                speed={0.8}
              >
                {t('landing.hero.badge', '✨ Tecnologia Cinematográfica de Hollywood')}
              </AnimatedGradientText>
            </div>

            {/* Main Title - Com Animações */}
            <div className="mb-8 md:mb-12">
              <h1 className="text-4xl sm:text-5xl md:text-8xl lg:text-9xl font-black leading-none">
                <div className="block transform -rotate-1 hover:rotate-0 transition-transform duration-500 mb-4">
                  <span className="inline-block animate-fade-in-up" style={{ animationDelay: '0.2s' }}>
                    {t('landing.hero.title1', 'VOCÊ É O')}
                  </span>
                </div>
                <div className="block transform rotate-1 hover:rotate-0 transition-transform duration-500 mb-4">
                  <SparklesText
                    className={`inline-block animate-fade-in-up ${
                      isDark ? 'text-yellow-400' : 'text-green-500'
                    }`}
                    colors={{
                      first: isDark ? "#FFC764" : "#ff0000",
                      second: isDark ? "#FFE066" : "#ff6b6b"
                    }}
                    sparklesCount={15}
                  >
                    <span style={{ animationDelay: '0.4s' }}>
                      {t('landing.hero.title2', 'PROTAGONISTA')}
                    </span>
                  </SparklesText>
                </div>
                <div className="block transform -rotate-1 hover:rotate-0 transition-transform duration-500">
                  <span className="inline-block animate-fade-in-up" style={{ animationDelay: '0.6s' }}>
                    {t('landing.hero.title3', 'DA SUA HISTÓRIA')}
                  </span>
                </div>
              </h1>
            </div>

            {/* Subtitle - Com Animação */}
            <div className="animate-fade-in-up" style={{ animationDelay: '0.8s' }}>
              <p className="text-base md:text-xl lg:text-2xl xl:text-3xl font-bold mb-8 md:mb-12 max-w-5xl mx-auto leading-relaxed opacity-90 px-4 md:px-0">
                {t('landing.hero.subtitle', 'Desperte o ator que existe em você. Nossa IA transforma suas fotos comuns em pôsteres cinematográficos dignos de Hollywood. Não é só um pôster - é a sua entrada para o estrelato.')}
              </p>
            </div>

            {/* Premium Badge - Brutal */}
            <div className="animate-fade-in-up" style={{ animationDelay: '1s' }}>
              <div className={`inline-flex items-center space-x-4 px-8 py-4 border-8 mb-16 ${
                isDark 
                  ? 'border-yellow-500 bg-gray-900 shadow-brutal-lg hover:shadow-brutal-hover' 
                  : 'border-green-500 bg-white shadow-brutal-lg hover:shadow-brutal-hover'
              } transform hover:scale-105 transition-all duration-200`}>
                <Star className="w-6 h-6" style={{ color: '#FFC764' }} />
                <AnimatedGradientText
                  className="font-bold text-lg lg:text-xl"
                  colorFrom={isDark ? "#FFC764" : "#22c55e"}
                  colorTo={isDark ? "#FFE066" : "#86efac"}
                  speed={1}
                >
                  {t('landing.hero.premium', 'Qualidade Premium • Não é um filtro barato')}
                </AnimatedGradientText>
              </div>
            </div>

            {/* CTA Buttons - Brutal */}
            <div className="flex flex-col sm:flex-row items-center justify-center space-y-6 sm:space-y-0 sm:space-x-4 md:space-x-8 mb-12 md:mb-20 animate-fade-in-up px-4 md:px-0" style={{ animationDelay: '1.2s' }}>
              <button
                onClick={handleGetStarted}
                className={`group relative px-6 md:px-10 py-4 md:py-5 text-lg md:text-xl lg:text-2xl font-black border-4 md:border-8 overflow-hidden ${
                  isDark
                    ? 'bg-yellow-500 text-black border-white shadow-brutal-lg hover:shadow-brutal-hover active:shadow-brutal-pressed'
                    : 'bg-green-500 text-white border-black shadow-brutal-lg hover:shadow-brutal-hover active:shadow-brutal-pressed'
                } transform hover:-translate-y-1 transition-all duration-200`}
              >
                <BorderBeam
                  size={400}
                  duration={5}
                  colorFrom={isDark ? "#FFC764" : "#22c55e"}
                  colorTo={isDark ? "#FFE066" : "#86efac"}
                  borderWidth={8}
                />
                <span className="relative flex items-center space-x-3">
                  <span>{user ? 'IR PARA DASHBOARD' : t('landing.hero.cta', 'CRIAR MEU PÔSTER')}</span>
                  <ArrowRight className="w-6 h-6 group-hover:translate-x-2 transition-transform duration-300" />
                </span>
              </button>

              <button 
                onClick={handleWatchDemo}
                className={`group flex items-center space-x-2 md:space-x-3 px-6 md:px-8 py-4 md:py-5 text-lg md:text-xl font-black border-4 md:border-8 transition-all duration-200 ${
                isDark
                  ? 'border-white bg-gray-900 hover:bg-gray-800 text-white shadow-brutal-lg hover:shadow-brutal-hover active:shadow-brutal-pressed'
                  : 'border-black bg-white hover:bg-gray-100 text-black shadow-brutal-lg hover:shadow-brutal-hover active:shadow-brutal-pressed'
              } transform hover:-translate-y-1`}>
                <Play className="w-6 h-6 group-hover:scale-110 transition-transform duration-300" />
                <span>{t('landing.hero.watchDemo', 'Ver Demo')}</span>
              </button>
            </div>

            {/* Stats - Brutal */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-8 max-w-5xl mx-auto animate-fade-in-up px-4 md:px-0" style={{ animationDelay: '1.4s' }}>
              {[
                { number: 10000, suffix: '+', label: t('landing.hero.stat1', 'Pôsteres Criados'), icon: '🎬', color: 'bg-green-500' },
                { number: 50, suffix: '+', label: t('landing.hero.stat2', 'Estilos de Filme'), icon: '🎭', color: 'bg-yellow-500' },
                { number: 99, suffix: '%', label: t('landing.hero.stat3', 'Satisfação'), icon: '⭐', color: 'bg-purple-500' }
              ].map((stat, index) => (
                <div key={index} className={`relative p-6 md:p-8 border-4 md:border-8 ${
                  isDark ? 'border-white bg-gray-900 shadow-brutal-lg hover:shadow-brutal-hover' : 'border-black bg-white shadow-brutal-lg hover:shadow-brutal-hover'
                } transform hover:scale-105 hover:rotate-0 transition-all duration-200 ${
                  index % 2 === 0 ? 'rotate-1' : '-rotate-1'
                }`}>
                  <div className="text-4xl mb-3">
                    {stat.icon}
                  </div>
                  <div className={`text-4xl lg:text-5xl font-black mb-3 ${
                    isDark ? 'text-white' : 'text-black'
                  }`}>
                    <NumberTicker
                      value={stat.number}
                      delay={0.5 + index * 0.2}
                      className="tabular-nums"
                    />
                    <span>{stat.suffix}</span>
                  </div>
                  <div className={`font-bold text-lg ${
                    isDark ? 'text-white' : 'text-black'
                  }`}>{stat.label}</div>

                  {/* Brutal corner accent - IGUAL AOS OUTROS */}
                  <div className={`absolute -bottom-2 -right-2 md:-bottom-4 md:-right-4 w-8 h-8 md:w-16 md:h-16 ${stat.color} border-2 md:border-4 ${
                    isDark ? 'border-white' : 'border-black'
                  } transform rotate-45`} />
                </div>
              ))}
            </div>
          </div>
        </section>
      </WarpBackground>

      {/* Add CSS animations */}
      <style>{`
        @keyframes fade-in-up {
          from {
            opacity: 0;
            transform: translateY(30px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
        
        @keyframes gradient-x {
          0%, 100% {
            background-position: 0% 50%;
          }
          50% {
            background-position: 100% 50%;
          }
        }
        
        @keyframes subtle-shift {
          0%, 100% {
            transform: scale(1) rotate(0deg);
          }
          33% {
            transform: scale(1.1) rotate(1deg);
          }
          66% {
            transform: scale(0.95) rotate(-1deg);
          }
        }
        
        .animate-fade-in-up {
          animation: fade-in-up 0.8s ease-out forwards;
          opacity: 0;
        }
        
        .animate-gradient-x {
          animation: gradient-x 3s ease infinite;
        }

        /* Brutal Shadow System */
        .shadow-brutal-lg {
          box-shadow: 8px 8px 0px 0px rgba(0, 0, 0, 1);
        }
        
        .shadow-brutal-hover {
          box-shadow: 12px 12px 0px 0px rgba(0, 0, 0, 1);
        }
        
        .shadow-brutal-pressed {
          box-shadow: 4px 4px 0px 0px rgba(0, 0, 0, 1);
        }
        
        .shadow-brutal-sm {
          box-shadow: 4px 4px 0px 0px rgba(0, 0, 0, 1);
        }

        /* Dark mode brutal shadows */
        .dark .shadow-brutal-lg {
          box-shadow: 8px 8px 0px 0px rgba(255, 255, 255, 1);
        }
        
        .dark .shadow-brutal-hover {
          box-shadow: 12px 12px 0px 0px rgba(255, 255, 255, 1);
        }
        
        .dark .shadow-brutal-pressed {
          box-shadow: 4px 4px 0px 0px rgba(255, 255, 255, 1);
        }
        
        .dark .shadow-brutal-sm {
          box-shadow: 4px 4px 0px 0px rgba(255, 255, 255, 1);
        }

        /* Mobile responsive fixes */
        @media (max-width: 768px) {
          body {
            overflow-x: hidden;
          }
          
          * {
            box-sizing: border-box;
          }
          
          .max-w-screen {
            max-width: 100vw;
          }
        }
      `}</style>

      {/* How It Works - Neo-Brutal */}
      <section className={`py-16 md:py-32 relative overflow-hidden ${
        isDark ? 'bg-black' : 'bg-white'
      }`}>
        {/* Brutal background shapes */}
        <div className="absolute inset-0 overflow-hidden">
          <div className={`absolute top-20 left-4 md:left-20 w-16 h-16 md:w-32 md:h-32 ${
            isDark ? 'bg-yellow-400' : 'bg-red-500'
          } transform rotate-45`} />
          <div className={`absolute bottom-20 right-4 md:right-20 w-12 h-12 md:w-24 md:h-24 border-4 md:border-8 ${
            isDark ? 'border-green-400' : 'border-blue-500'
          } transform -rotate-12`} />
          <div className={`hidden md:block absolute top-1/2 right-1/4 w-20 h-20 border-6 ${
            isDark ? 'border-pink-400' : 'border-purple-500'
          } rounded-full`} />
        </div>

        <div className="max-w-7xl mx-auto px-4 md:px-6 relative z-10">
          <div className="text-center mb-16 md:mb-20">
            <h2 className={`text-3xl md:text-5xl lg:text-6xl xl:text-7xl font-black mb-6 md:mb-8 transform -rotate-1 hover:rotate-0 transition-transform duration-300 ${
              isDark ? 'text-white' : 'text-black'
            }`}>
              {t('landing.howItWorks.title', 'COMO FUNCIONA')}
            </h2>
            <p className={`text-base md:text-xl lg:text-2xl font-bold max-w-4xl mx-auto leading-relaxed px-4 md:px-0 ${
              isDark ? 'text-gray-300' : 'text-gray-700'
            }`}>
              Transforme sua foto em uma obra-prima cinematográfica em apenas 3 passos simples
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 md:gap-12">
            {[
              {
                step: '01',
                title: t('landing.howItWorks.step1', 'Upload sua foto'),
                description: t('landing.howItWorks.desc1', 'Escolha uma foto clara do seu rosto'),
                icon: '📸',
                gradient: 'from-green-400 to-blue-500',
                color: 'bg-green-500'
              },
              {
                step: '02', 
                title: t('landing.howItWorks.step2', 'Escolha o estilo'),
                description: t('landing.howItWorks.desc2', 'Selecione o gênero e plataforma'),
                icon: '🎨',
                gradient: 'from-yellow-400 to-orange-500',
                color: 'bg-yellow-500'
              },
              {
                step: '03',
                title: t('landing.howItWorks.step3', 'Baixe seu pôster'),
                description: t('landing.howItWorks.desc3', 'Receba seu pôster em alta qualidade'),
                icon: '⬇️',
                gradient: 'from-purple-400 to-pink-500',
                color: 'bg-purple-500'
              }
            ].map((item, index) => (
              <div key={index} className="group relative">
                {/* Connection Line */}
                {index < 2 && (
                  <div className={`hidden lg:block absolute top-12 -right-6 w-12 h-1 ${
                    isDark ? 'bg-gray-600' : 'bg-gray-300'
                  } z-0`} />
                )}

                <div className={`relative p-6 md:p-10 border-4 md:border-8 ${
                  isDark ? 'border-white bg-gray-900 shadow-brutal-lg hover:shadow-brutal-hover' : 'border-black bg-white shadow-brutal-lg hover:shadow-brutal-hover'
                } transform hover:scale-105 hover:rotate-0 transition-all duration-200 ${
                  index % 2 === 0 ? 'rotate-1' : '-rotate-1'
                } z-10`}>
                  
                  {/* Step Number - Brutal */}
                  <div className="relative mb-8">
                    <div className={`w-20 h-20 border-6 ${
                      isDark ? 'border-white' : 'border-black'
                    } ${item.color} flex items-center justify-center text-white font-black text-2xl group-hover:scale-110 transition-transform duration-300 transform rotate-12`}>
                      {item.step}
                    </div>
                    
                    {/* Floating Icon */}
                    <div className="absolute -top-2 -right-2 md:-top-3 md:-right-3 text-2xl md:text-3xl group-hover:scale-125 group-hover:rotate-12 transition-transform duration-300">
                      {item.icon}
                    </div>
                  </div>

                  <h3 className={`text-2xl lg:text-3xl font-black mb-4 transition-all duration-300 ${
                    isDark ? 'text-white' : 'text-black'
                  }`}>
                    {item.title}
                  </h3>
                  
                  <p className="text-lg font-medium opacity-80 leading-relaxed group-hover:opacity-100 transition-opacity duration-300">
                    {item.description}
                  </p>

                  {/* Brutal corner accent */}
                  <div className={`absolute -bottom-2 -right-2 md:-bottom-4 md:-right-4 w-8 h-8 md:w-16 md:h-16 ${item.color} border-2 md:border-4 ${
                    isDark ? 'border-white' : 'border-black'
                  } transform rotate-45`} />

                  {/* Hover Effect Background */}
                  <div className={`absolute inset-0 ${item.color} opacity-0 group-hover:opacity-5 transition-opacity duration-300`} />
                </div>
              </div>
            ))}
          </div>

          {/* Call to Action */}
          <div className="text-center mt-20">
            <div className={`inline-flex items-center space-x-4 px-8 py-4 border-8 ${
              isDark ? 'border-white bg-gray-900 shadow-brutal-lg' : 'border-black bg-gray-50 shadow-brutal-lg'
            }`}>
              <span className="text-2xl">⚡</span>
              <span className="font-bold text-lg">
                Pronto para se tornar protagonista? É mais fácil do que parece!
              </span>
            </div>
          </div>
        </div>
      </section>

      {/* Interactive Demo - Neo-Brutal */}
      <section id="demo-section" className={`py-16 md:py-32 relative overflow-hidden ${
        isDark ? 'bg-black' : 'bg-white'
      }`}>
        {/* Brutal background shapes */}
        <div className="absolute inset-0 overflow-hidden">
          <div className={`absolute top-20 left-4 md:left-20 w-16 h-16 md:w-32 md:h-32 border-4 md:border-8 ${
            isDark ? 'border-yellow-400' : 'border-red-500'
          } transform rotate-45`} />
          <div className={`absolute bottom-20 right-4 md:right-20 w-12 h-12 md:w-24 md:h-24 ${
            isDark ? 'bg-green-400' : 'bg-blue-500'
          } transform -rotate-12`} />
          <div className={`hidden md:block absolute top-1/2 right-10 w-20 h-20 border-6 ${
            isDark ? 'border-pink-400' : 'border-purple-500'
          }`} />
        </div>

        <div className="max-w-7xl mx-auto px-4 md:px-6 relative z-10">
          <div className="text-center mb-12 md:mb-20">
            <h2 className={`text-3xl md:text-5xl lg:text-6xl xl:text-7xl font-black mb-6 md:mb-8 transform -rotate-1 hover:rotate-0 transition-transform duration-300 ${
              isDark ? 'text-white' : 'text-black'
            }`}>
              <SparklesText
                colors={{
                  first: isDark ? "#9E7AFF" : "#8B5CF6",
                  second: isDark ? "#FE8BBB" : "#EC4899"
                }}
                sparklesCount={12}
              >
                {t('landing.demo.title', 'VEJA A MAGIA ACONTECER')}
              </SparklesText>
            </h2>
            <p className={`text-base md:text-xl lg:text-2xl font-bold max-w-4xl mx-auto leading-relaxed px-4 md:px-0 ${
              isDark ? 'text-gray-300' : 'text-gray-700'
            }`}>
              {t('landing.demo.subtitle', 'Teste nossa IA em tempo real! Veja como transformamos uma foto comum em capas de cinema profissionais.')}
            </p>
          </div>

          {/* Demo Container */}
          <div className={`max-w-7xl mx-auto p-4 md:p-12 ${
            isDark ? 'bg-gray-950/20' : 'bg-white/20'
          } backdrop-blur-sm rounded-2xl border-4 md:border-8 ${
            isDark ? 'border-white' : 'border-black'
          } shadow-brutal-lg`}>
            <BeforeAfterDemo isDark={isDark} />
          </div>
        </div>
      </section>

      {/* Platforms - Neo-Brutal */}
      <section className={`py-32 relative ${
        isDark ? 'bg-gray-900' : 'bg-gray-100'
      }`}>
        {/* Brutal background shapes */}
        <div className="absolute inset-0 overflow-hidden">
          <div className={`absolute top-20 right-20 w-32 h-32 border-8 ${
            isDark ? 'border-yellow-400' : 'border-red-500'
          } transform rotate-12`} />
          <div className={`absolute bottom-20 left-20 w-24 h-24 ${
            isDark ? 'bg-green-400' : 'bg-blue-500'
          } transform -rotate-45`} />
          <div className={`absolute top-1/2 left-1/4 w-20 h-20 border-6 ${
            isDark ? 'border-pink-400' : 'border-purple-500'
          }`} />
        </div>

        <div className="max-w-7xl mx-auto px-6 relative z-10">
          <div className="text-center mb-20">
            <h2 className={`text-5xl md:text-6xl lg:text-7xl font-black mb-8 transform rotate-1 hover:rotate-0 transition-transform duration-300 ${
              isDark ? 'text-white' : 'text-black'
            }`}>
              {t('landing.platforms.title', 'ESCOLHA SEU UNIVERSO CINEMATOGRÁFICO')}
            </h2>
            <p className={`text-xl md:text-2xl font-bold max-w-4xl mx-auto leading-relaxed ${
              isDark ? 'text-gray-300' : 'text-gray-700'
            }`}>
              {t('landing.platforms.subtitle', 'Cada plataforma tem sua identidade visual única. Nossa IA domina os códigos estéticos de cada uma para criar pôsteres autênticos.')}
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                platform: 'Netflix',
                title: t('landing.platforms.netflix.title', 'NETFLIX'),
                subtitle: t('landing.platforms.netflix.subtitle', 'Drama & Suspense'),
                description: t('landing.platforms.netflix.desc', 'Estética cinematográfica sombria e intensa. Tons dramáticos que capturam a essência dos grandes sucessos da plataforma.'),
                color: 'bg-red-600',
                features: [
                  t('landing.platforms.netflix.feature1', '12 capas únicas'),
                  t('landing.platforms.netflix.feature2', 'Estilo noir premium'),
                  t('landing.platforms.netflix.feature3', 'Tipografia icônica')
                ]
              },
              {
                platform: 'Disney+',
                title: t('landing.platforms.disney.title', 'DISNEY+'),
                subtitle: t('landing.platforms.disney.subtitle', 'Fantasia & Aventura'),
                description: t('landing.platforms.disney.desc', 'Magia cinematográfica com cores vibrantes e composições épicas. O visual que desperta o herói que existe em você.'),
                color: 'bg-blue-600',
                features: [
                  t('landing.platforms.disney.feature1', '9 capas mágicas'),
                  t('landing.platforms.disney.feature2', 'Estilo épico'),
                  t('landing.platforms.disney.feature3', 'Cores vibrantes')
                ]
              },
              {
                platform: 'Amazon',
                title: t('landing.platforms.amazon.title', 'PRIME VIDEO'),
                subtitle: t('landing.platforms.amazon.subtitle', 'Ação & Thriller'),
                description: t('landing.platforms.amazon.desc', 'Visual cinematográfico premium com foco em ação e intensidade. A estética dos blockbusters modernos.'),
                color: 'bg-cyan-600',
                features: [
                  t('landing.platforms.amazon.feature1', '11 capas dinâmicas'),
                  t('landing.platforms.amazon.feature2', 'Estilo blockbuster'),
                  t('landing.platforms.amazon.feature3', 'Composição premium')
                ]
              }
            ].map((platform, index) => (
              <div key={index} className={`relative p-10 border-8 ${
                isDark ? 'border-white bg-gray-800 shadow-brutal-lg hover:shadow-brutal-hover' : 'border-black bg-white shadow-brutal-lg hover:shadow-brutal-hover'
              } transform hover:scale-105 hover:rotate-0 transition-all duration-200 ${
                index % 2 === 0 ? 'rotate-1' : '-rotate-1'
              }`}>
                
                {/* Platform Badge */}
                <div className={`absolute -top-6 left-6 px-4 py-2 ${platform.color} text-white font-black text-sm border-4 ${
                  isDark ? 'border-white' : 'border-black'
                } transform -rotate-12`}>
                  {platform.platform}
                </div>

                {/* Content */}
                <div className="mt-6">
                  <h3 className={`text-3xl font-black mb-2 ${
                    isDark ? 'text-white' : 'text-black'
                  }`}>
                    {platform.title}
                  </h3>
                  <p className={`text-lg font-bold mb-4 ${
                    platform.color.replace('bg-red-600', 'text-red-500')
                                  .replace('bg-blue-600', 'text-blue-500')
                                  .replace('bg-cyan-600', 'text-cyan-500')
                  }`}>
                    {platform.subtitle}
                  </p>
                  <p className={`text-lg font-medium mb-6 leading-relaxed ${
                    isDark ? 'text-gray-300' : 'text-gray-700'
                  }`}>
                    {platform.description}
                  </p>

                  {/* Features */}
                  <ul className="space-y-3">
                    {platform.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center space-x-3">
                        <Check className={`w-5 h-5 ${
                          isDark ? 'text-yellow-400' : 'text-green-500'
                        }`} />
                        <span className={`font-medium ${
                          isDark ? 'text-white' : 'text-black'
                        }`}>
                          {feature}
                        </span>
                      </li>
                    ))}
                  </ul>
                </div>

                {/* Brutal corner accent */}
                <div className={`absolute -bottom-3 -right-3 w-12 h-12 ${platform.color} border-4 ${
                  isDark ? 'border-white' : 'border-black'
                } transform rotate-45`} />

                {/* Hover Effect */}
                <div className={`absolute inset-0 ${platform.color} opacity-0 hover:opacity-5 transition-opacity duration-300`} />
              </div>
            ))}
          </div>

          {/* Call to Action */}
          <div className="text-center mt-16">
            <p className={`text-xl font-bold mb-8 ${
              isDark ? 'text-gray-300' : 'text-gray-700'
            }`}>
              {t('landing.platforms.cta', 'Qual universo cinematográfico combina com você?')}
            </p>
            <button
              onClick={handleGetStarted}
              className={`px-12 py-6 text-xl font-black border-8 ${
                isDark
                  ? 'bg-yellow-500 text-black border-white shadow-brutal-lg hover:shadow-brutal-hover active:shadow-brutal-pressed'
                  : 'bg-green-500 text-white border-black shadow-brutal-lg hover:shadow-brutal-hover active:shadow-brutal-pressed'
              } transform hover:-translate-y-1 transition-all duration-200`}
            >
              {t('landing.platforms.ctaButton', 'DESCOBRIR MEU ESTILO')}
            </button>
          </div>
        </div>
      </section>

      {/* Velocity Scroll Section - Magic UI */}
      <section className={`py-16 overflow-hidden ${
        isDark ? 'bg-gray-900' : 'bg-gray-100'
      }`}>
        <VelocityScroll
          className={isDark ? 'text-white/10' : 'text-black/10'}
          defaultVelocity={20}
          numRows={1}
        >
          PROTAGONISTA ✦ CINEMA ✦ HOLLYWOOD ✦ ESTRELA ✦ 
        </VelocityScroll>
      </section>

      {/* Features - Neo-Brutal */}
      <section className={`py-32 relative ${
        isDark ? 'bg-black' : 'bg-white'
      }`}>
        {/* Brutal shapes */}
        <div className="absolute inset-0 overflow-hidden">
          <div className={`absolute top-20 left-20 w-32 h-32 border-8 ${
            isDark ? 'border-yellow-400' : 'border-red-500'
          } transform rotate-45`} />
          <div className={`absolute bottom-20 right-20 w-24 h-24 ${
            isDark ? 'bg-green-400' : 'bg-blue-500'
          } transform -rotate-12`} />
        </div>

        <div className="max-w-7xl mx-auto px-6 relative z-10">
          <h2 className={`text-5xl md:text-6xl lg:text-7xl font-black text-center mb-20 transform rotate-1 hover:rotate-0 transition-transform duration-300 ${
            isDark ? 'text-white' : 'text-black'
          }`}>
            {t('landing.features.title', 'RECURSOS INCRÍVEIS')}
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
            {[
              {
                icon: <Sparkles className="w-12 h-12" />,
                title: t('landing.features.ai', 'IA Avançada'),
                description: t('landing.features.aiDesc', 'Tecnologia de ponta para resultados realistas'),
                color: 'bg-purple-500'
              },
              {
                icon: <Zap className="w-12 h-12" />,
                title: t('landing.features.fast', 'Super Rápido'),
                description: t('landing.features.fastDesc', 'Gere seu pôster em menos de 2 minutos'),
                color: 'bg-yellow-500'
              },
              {
                icon: <Star className="w-12 h-12" />,
                title: t('landing.features.quality', 'Alta Qualidade'),
                description: t('landing.features.qualityDesc', 'Poster pronto pra impressão em alta qualidade'),
                color: 'bg-green-500'
              },
              {
                icon: <Check className="w-12 h-12" />,
                title: t('landing.features.easy', 'Fácil de Usar'),
                description: t('landing.features.easyDesc', 'Interface simples e intuitiva'),
                color: 'bg-blue-500'
              }
            ].map((feature, index) => (
              <div key={index} className={`relative p-10 border-8 ${
                isDark ? 'border-white bg-gray-900 shadow-brutal-lg hover:shadow-brutal-hover' : 'border-black bg-gray-50 shadow-brutal-lg hover:shadow-brutal-hover'
              } transform hover:scale-105 hover:rotate-0 transition-all duration-200 ${
                index % 2 === 0 ? '-rotate-1' : 'rotate-1'
              }`}>
                
                {/* Icon container - Brutal */}
                <div className={`w-20 h-20 border-6 ${
                  isDark ? 'border-white' : 'border-black'
                } ${feature.color} flex items-center justify-center text-white mb-8 transform rotate-12`}>
                  {feature.icon}
                </div>

                <h3 className={`text-3xl lg:text-4xl font-black mb-6 ${
                  isDark ? 'text-white' : 'text-black'
                }`}>
                  {feature.title}
                </h3>
                
                <p className={`text-xl font-bold leading-relaxed ${
                  isDark ? 'text-gray-300' : 'text-gray-700'
                }`}>
                  {feature.description}
                </p>

                {/* Brutal corner */}
                <div className={`absolute -top-3 -right-3 w-12 h-12 ${feature.color} border-4 ${
                  isDark ? 'border-white' : 'border-black'
                } transform rotate-45`} />
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Velocity Scroll Section 2 - Magic UI */}
      <section className={`py-12 overflow-hidden ${
        isDark ? 'bg-black' : 'bg-white'
      }`}>
        <VelocityScroll
          className={isDark ? 'text-yellow-400/15' : 'text-black/10'}
          defaultVelocity={20}
          numRows={2}
        >
          ⭐ IA AVANÇADA ⭐ ALTA QUALIDADE ⭐ RESULTADOS PROFISSIONAIS ⭐ 
        </VelocityScroll>
      </section>

      {/* Testimonials - Neo-Brutal */}
      <section className={`py-32 relative ${
        isDark ? 'bg-gray-900' : 'bg-gray-100'
      }`}>
        {/* Brutal background elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className={`absolute top-10 left-10 w-28 h-28 ${
            isDark ? 'bg-yellow-400' : 'bg-red-500'
          } transform rotate-12`} />
          <div className={`absolute bottom-10 right-10 w-20 h-20 border-6 ${
            isDark ? 'border-green-400' : 'border-blue-500'
          } transform -rotate-45`} />
        </div>

        <div className="max-w-7xl mx-auto px-6 relative z-10">
          <h2 className={`text-5xl md:text-6xl lg:text-7xl font-black text-center mb-20 transform -rotate-1 hover:rotate-0 transition-transform duration-300 ${
            isDark ? 'text-white' : 'text-black'
          }`}>
            {t('landing.testimonials.title', 'O QUE DIZEM OS USUÁRIOS')}
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-12">
            {[
              {
                quote: t('landing.testimonials.quote1', 'Nunca pensei que fosse tão fácil! Transformei uma foto comum numa capa de filme épica. Meus amigos adoraram!'),
                name: 'Joana S.',
                role: t('landing.testimonials.role1', 'Criadora de Conteúdo'),
                rating: 5,
                color: 'bg-pink-500'
              },
              {
                quote: t('landing.testimonials.quote2', 'A qualidade é incrível. Parece um pôster de cinema real. Usei para um presente e foi um sucesso absoluto.'),
                name: 'Miguel F.',
                role: t('landing.testimonials.role2', 'Designer Gráfico'),
                rating: 5,
                color: 'bg-purple-500'
              },
              {
                quote: t('landing.testimonials.quote3', 'Isso é viciante! Já criei pôsteres para toda minha família. A função de escolher o estilo é minha favorita.'),
                name: 'Carla M.',
                role: t('landing.testimonials.role3', 'Fã de Cinema'),
                rating: 5,
                color: 'bg-blue-500'
              }
            ].map((testimonial, index) => (
              <div key={index} className={`relative p-8 border-8 ${
                isDark ? 'border-white bg-black shadow-brutal-lg hover:shadow-brutal-hover' : 'border-black bg-white shadow-brutal-lg hover:shadow-brutal-hover'
              } transform hover:scale-105 hover:rotate-0 transition-all duration-200 ${
                index % 2 === 0 ? 'rotate-1' : '-rotate-1'
              }`}>
                
                {/* Stars - Brutal style */}
                <div className="flex space-x-2 mb-8">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <div key={i} className={`w-8 h-8 border-3 ${
                      isDark ? 'border-white bg-yellow-400' : 'border-black bg-yellow-400'
                    } flex items-center justify-center transform rotate-12`}>
                      <Star className="w-5 h-5 text-black fill-current" />
                    </div>
                  ))}
                </div>

                {/* Quote - Brutal typography */}
                <blockquote className={`text-xl font-black mb-8 leading-relaxed ${
                  isDark ? 'text-white' : 'text-black'
                }`}>
                  "{testimonial.quote}"
                </blockquote>

                {/* Author - Brutal design */}
                <div className="flex items-center space-x-6">
                  <div className={`w-16 h-16 border-6 ${
                    isDark ? 'border-white' : 'border-black'
                  } ${testimonial.color} flex items-center justify-center font-black text-2xl text-white transform rotate-12`}>
                    {testimonial.name.charAt(0)}
                  </div>
                  <div>
                    <p className={`font-black text-xl ${
                      isDark ? 'text-white' : 'text-black'
                    }`}>
                      {testimonial.name}
                    </p>
                    <p className={`font-bold text-lg ${
                      isDark ? 'text-gray-300' : 'text-gray-700'
                    }`}>
                      {testimonial.role}
                    </p>
                  </div>
                </div>

                {/* Brutal quote mark */}
                <div className={`absolute -top-4 -left-4 w-12 h-12 ${testimonial.color} border-4 ${
                  isDark ? 'border-white' : 'border-black'
                } flex items-center justify-center text-white font-black text-2xl transform rotate-12`}>
                  "
                </div>
              </div>
            ))}
          </div>

          {/* Stats - Brutal */}
          <div className="mt-20">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-5xl mx-auto">
              {[
                { number: '10K+', label: t('landing.testimonials.stat1', 'Usuários Felizes'), color: 'bg-red-500' },
                { number: '50K+', label: t('landing.testimonials.stat2', 'Pôsteres Criados'), color: 'bg-blue-500' },
                { number: '4.9/5', label: t('landing.testimonials.stat3', 'Avaliação Média'), color: 'bg-green-500' },
                { number: '98%', label: t('landing.testimonials.stat4', 'Recomendariam'), color: 'bg-purple-500' }
              ].map((stat, index) => (
                <div key={index} className={`text-center p-6 border-6 ${
                  isDark ? 'border-white bg-gray-800 shadow-brutal-lg hover:shadow-brutal-hover' : 'border-black bg-white shadow-brutal-lg hover:shadow-brutal-hover'
                } transform hover:scale-110 transition-all duration-200`}>
                  <div className={`text-4xl lg:text-5xl font-black mb-3 ${
                    isDark ? 'text-white' : 'text-black'
                  }`}>
                    {stat.number}
                  </div>
                  <div className={`font-bold text-lg ${
                    isDark ? 'text-gray-300' : 'text-gray-700'
                  }`}>
                    {stat.label}
                  </div>
                  {/* Brutal accent */}
                  <div className={`w-full h-2 ${stat.color} mt-4`} />
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* FAQ/Trust Section - Neo-Brutal */}
      <section className={`py-32 relative ${
        isDark ? 'bg-black' : 'bg-white'
      }`}>
        {/* Brutal geometric background */}
        <div className="absolute inset-0 overflow-hidden">
          <div className={`absolute top-20 right-20 w-36 h-36 border-8 ${
            isDark ? 'border-yellow-400' : 'border-red-500'
          } transform rotate-45`} />
          <div className={`absolute bottom-20 left-20 w-28 h-28 ${
            isDark ? 'bg-green-400' : 'bg-blue-500'
          } transform -rotate-12`} />
          <div className={`absolute top-1/2 right-1/4 w-20 h-20 border-6 ${
            isDark ? 'border-pink-400' : 'border-purple-500'
          } rounded-full`} />
        </div>

        <div className="max-w-7xl mx-auto px-6 relative z-10">
          <div className="text-center mb-20">
            <h2 className={`text-5xl md:text-6xl lg:text-7xl font-black mb-6 transform -rotate-1 hover:rotate-0 transition-transform duration-300 ${
              isDark ? 'text-white' : 'text-black'
            }`}>
              {t('landing.faq.title', 'POR QUE CONFIAR EM NÓS?')}
            </h2>
            <p className={`text-xl md:text-2xl font-bold max-w-4xl mx-auto leading-relaxed ${
              isDark ? 'text-gray-300' : 'text-gray-700'
            }`}>
              {t('landing.faq.subtitle', 'Transparência total. Sem pegadinhas. Sem letra miúda. Apenas resultados profissionais que você pode confiar.')}
            </p>
          </div>

          {/* Trust Cards - Brutal Design */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-12 mb-20">
            {[
              {
                icon: <Check className="w-12 h-12" />,
                title: t('landing.faq.secure', 'PRIVACIDADE ABSOLUTA'),
                description: t('landing.faq.secureDesc', 'Você tem controle absoluto dos seus dados e pode deletar as imagens quando quiser. Não usamos suas fotos para nenhum tipo de treinamento com IA. Não deletamos nada automaticamente.'),
                badge: t('landing.faq.secureBadge', 'SSL 256-bit'),
                color: 'bg-red-500',
                borderColor: 'border-red-500'
              },
              {
                icon: <Star className="w-12 h-12" />,
                title: t('landing.faq.quality', 'QUALIDADE GARANTIDA'),
                description: t('landing.faq.qualityDesc', 'Alta resolução, cores profissionais, composição cinematográfica. O resultado vai depender muito da qualidade da imagem e posição dos rostos da imagem que o usuário enviar, então pode variar dependendo da imagem.'),
                badge: t('landing.faq.qualityBadge', 'Alta Resolução'),
                color: 'bg-blue-500',
                borderColor: 'border-blue-500'
              },
              {
                icon: <Sparkles className="w-12 h-12" />,
                title: t('landing.faq.rights', 'SEUS DIREITOS PROTEGIDOS'),
                description: t('landing.faq.rightsDesc', 'Você possui 100% dos direitos comerciais das imagens geradas. Use como quiser: redes sociais, impressão, comercialização.'),
                badge: t('landing.faq.rightsBadge', '100% Seus'),
                color: 'bg-green-500',
                borderColor: 'border-green-500'
              }
            ].map((item, index) => (
              <div key={index} className={`relative p-10 border-8 ${
                isDark ? 'border-white bg-gray-900 shadow-brutal-lg hover:shadow-brutal-hover' : 'border-black bg-white shadow-brutal-lg hover:shadow-brutal-hover'
              } transform hover:scale-105 hover:rotate-0 transition-all duration-200 ${
                index % 2 === 0 ? 'rotate-1' : '-rotate-1'
              }`}>
                
                {/* Badge - Brutal */}
                <div className={`absolute -top-6 right-6 px-4 py-2 ${item.color} text-white font-black text-sm border-4 ${
                  isDark ? 'border-white' : 'border-black'
                } transform -rotate-12`}>
                  {item.badge}
                </div>

                {/* Icon - Brutal */}
                <div className={`w-20 h-20 border-6 ${
                  isDark ? 'border-white' : 'border-black'
                } ${item.color} flex items-center justify-center text-white mb-8 transform rotate-12`}>
                  {item.icon}
                </div>

                <h3 className={`text-3xl lg:text-4xl font-black mb-6 ${
                  isDark ? 'text-white' : 'text-black'
                }`}>
                  {item.title}
                </h3>
                
                <p className={`text-lg font-bold leading-relaxed ${
                  isDark ? 'text-gray-300' : 'text-gray-700'
                }`}>
                  {item.description}
                </p>

                {/* Brutal corner accent */}
                <div className={`absolute -bottom-3 -left-3 w-12 h-12 ${item.color} border-4 ${
                  isDark ? 'border-white' : 'border-black'
                } transform rotate-45`} />
              </div>
            ))}
          </div>

          {/* FAQ Grid - Brutal */}
          <div className="max-w-5xl mx-auto mb-20">
            <h3 className={`text-4xl md:text-5xl font-black text-center mb-16 transform rotate-1 ${
              isDark ? 'text-white' : 'text-black'
            }`}>
              {t('landing.faq.faqTitle', 'PERGUNTAS QUE VOCÊ PODE TER')}
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {[
                {
                  question: t('landing.faq.q1', 'Minha foto fica realmente boa?'),
                  answer: t('landing.faq.a1', 'Nossa IA foi treinada com milhares de pôsteres reais de Hollywood. O resultado vai depender muito da qualidade da imagem e posição dos rostos da imagem que você enviar.'),
                  color: 'bg-purple-500'
                },
                {
                  question: t('landing.faq.q2', 'É seguro enviar minha foto?'),
                  answer: t('landing.faq.a2', 'Totalmente. Você tem controle absoluto dos seus dados e pode deletar as imagens quando quiser. Não usamos suas fotos para nenhum tipo de treinamento com IA.'),
                  color: 'bg-pink-500'
                },
                {
                  question: t('landing.faq.q3', 'Posso usar comercialmente?'),
                  answer: t('landing.faq.a3', 'Sim! Você tem direitos totais. Venda, imprima, poste onde quiser. A imagem é 100% sua.'),
                  color: 'bg-orange-500'
                },
                {
                  question: t('landing.faq.q4', 'E se eu não gostar?'),
                  answer: t('landing.faq.a4', 'Você pode refazer as capas geradas se não gostar, cada geração de capa de série e filme custa 1 crédito.'),
                  color: 'bg-cyan-500'
                }
              ].map((faq, index) => (
                <div key={index} className={`relative p-8 border-6 ${
                  isDark ? 'border-white bg-gray-800 shadow-brutal-lg hover:shadow-brutal-hover' : 'border-black bg-gray-50 shadow-brutal-lg hover:shadow-brutal-hover'
                } transform hover:scale-105 transition-all duration-200`}>
                  
                  {/* Question mark accent */}
                  <div className={`absolute -top-4 -left-4 w-12 h-12 ${faq.color} border-4 ${
                    isDark ? 'border-white' : 'border-black'
                  } flex items-center justify-center text-white font-black text-2xl transform rotate-12`}>
                    ?
                  </div>

                  <h4 className={`text-xl font-black mb-4 ${
                    isDark ? 'text-white' : 'text-black'
                  }`}>
                    {faq.question}
                  </h4>
                  <p className={`font-bold leading-relaxed ${
                    isDark ? 'text-gray-300' : 'text-gray-700'
                  }`}>
                    {faq.answer}
                  </p>
                </div>
              ))}
            </div>
          </div>

          {/* Social Proof - Brutal */}
          <div className="text-center">
            <div className={`inline-flex items-center space-x-6 px-12 py-8 border-8 ${
              isDark ? 'border-white bg-gray-900 shadow-brutal-lg hover:shadow-brutal-hover' : 'border-black bg-gray-50 shadow-brutal-lg hover:shadow-brutal-hover'
            } transform hover:scale-105 transition-all duration-200`}>
              
              {/* Avatar stack - Brutal */}
              <div className="flex -space-x-4">
                {[...Array(5)].map((_, i) => (
                  <div key={i} className={`w-16 h-16 border-6 ${
                    isDark ? 'border-white' : 'border-black'
                  } bg-red-500 flex items-center justify-center text-white font-black text-xl transform ${
                    i % 2 === 0 ? 'rotate-12' : '-rotate-12'
                  }`}>
                    {String.fromCharCode(65 + i)}
                  </div>
                ))}
              </div>
              
              <div className="text-left">
                <p className={`font-black text-2xl ${
                  isDark ? 'text-white' : 'text-black'
                }`}>
                  {t('landing.faq.socialProof', '+2.847 pessoas criaram seus pôsteres hoje')}
                </p>
                <p className={`text-lg font-bold ${
                  isDark ? 'text-gray-300' : 'text-gray-700'
                }`}>
                  {t('landing.faq.socialProofSub', 'Junte-se à comunidade de estrelas')}
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section - Neo-Brutal Final */}
      <section className={`py-32 relative overflow-hidden ${
        isDark ? 'bg-[#FFC764]' : 'bg-[#00C9A7]'
      }`}>
        {/* Brutal background elements */}
        <div className="absolute inset-0">
          <div className="absolute top-20 left-20 w-48 h-48 bg-black opacity-10 transform rotate-12" />
          <div className="absolute bottom-20 right-20 w-32 h-32 border-8 border-black transform rotate-45 opacity-20" />
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-black opacity-5" />
          <div className="absolute top-32 right-32 w-24 h-24 bg-black transform -rotate-45 opacity-15" />
        </div>

        <div className="max-w-6xl mx-auto text-center px-6 relative z-10">
          {/* Urgency Badge - Brutal */}
          <div className="inline-flex items-center space-x-4 px-8 py-4 bg-black text-white border-8 border-black shadow-brutal-lg hover:shadow-brutal-hover mb-12 transform -rotate-1 hover:rotate-0 transition-all duration-200">
            <div className="relative">
              <Zap className="w-8 h-8 text-yellow-400" />
              <div className="absolute inset-0 w-8 h-8 bg-yellow-400/20 animate-ping" />
            </div>
            <span className="font-black text-xl uppercase tracking-wider">
              {t('landing.cta.urgency', 'OFERTA LIMITADA • APENAS HOJE')}
            </span>
          </div>

          <h2 className="text-6xl md:text-7xl lg:text-8xl font-black mb-8 text-black transform rotate-1 hover:rotate-0 transition-transform duration-300 leading-tight">
            {t('landing.cta.title', 'SUA ESTREIA ACONTECE AGORA')}
          </h2>
          
          <p className="text-2xl md:text-3xl lg:text-4xl font-black mb-12 max-w-5xl mx-auto leading-tight text-black">
            {t('landing.cta.subtitle', 'Não deixe seus sonhos cinematográficos esperando. Milhares já descobriram o protagonista que existe dentro deles.')}
          </p>

          {/* Stats Cards - Brutal */}
          <div className="flex flex-col md:flex-row items-center justify-center gap-8 mb-16">
            {[
              {
                icon: <Star className="w-8 h-8" />,
                number: '2.847',
                label: t('landing.cta.scarcity1', 'pôsteres'),
                sublabel: t('landing.cta.scarcity1Sub', 'criados hoje'),
                color: 'bg-red-500'
              },
              {
                icon: <Check className="w-8 h-8" />,
                number: '98%',
                label: t('landing.cta.scarcity2', 'satisfação'),
                sublabel: t('landing.cta.scarcity2Sub', 'dos usuários'),
                color: 'bg-blue-500'
              },
              {
                icon: <Sparkles className="w-8 h-8" />,
                number: '< 2',
                label: t('landing.cta.scarcity3', 'minutos'),
                sublabel: t('landing.cta.scarcity3Sub', 'para criar'),
                color: 'bg-purple-500'
              }
            ].map((stat, index) => (
              <div key={index} className={`flex items-center space-x-4 px-8 py-6 ${
                isDark ? 'bg-gray-900 shadow-brutal-lg hover:shadow-brutal-hover' : 'bg-white shadow-brutal-lg hover:shadow-brutal-hover'
              } border-8 ${
                isDark ? 'border-white' : 'border-black'
              } transform hover:scale-110 transition-all duration-200 ${
                index % 2 === 0 ? 'rotate-1' : '-rotate-1'
              }`}>
                <div className={`w-16 h-16 ${stat.color} border-4 ${
                  isDark ? 'border-white' : 'border-black'
                } flex items-center justify-center text-white transform rotate-12`}>
                  {stat.icon}
                </div>
                <div className="text-left">
                  <p className={`font-black text-2xl ${
                    isDark ? 'text-white' : 'text-black'
                  }`}>
                    {stat.number} {stat.label}
                  </p>
                  <p className={`text-lg font-bold ${
                    isDark ? 'text-gray-300' : 'text-gray-700'
                  }`}>
                    {stat.sublabel}
                  </p>
                </div>
              </div>
            ))}
          </div>

          {/* Main CTA Button - Ultra Brutal */}
          <div className="space-y-8">
            <button
              onClick={handleGetStarted}
              className="group relative px-16 py-8 text-3xl md:text-4xl font-black bg-black text-white border-8 border-black shadow-brutal-lg hover:shadow-brutal-hover active:shadow-brutal-pressed transform hover:-translate-y-2 hover:scale-110 transition-all duration-200 overflow-hidden"
            >
              {/* Animated background */}
              <div className="absolute inset-0 bg-gradient-to-r from-red-500 via-purple-500 to-blue-500 opacity-0 group-hover:opacity-20 transition-opacity duration-300" />
              
              <span className="relative flex items-center justify-center space-x-4">
                <span>{t('landing.cta.button', 'VIRAR ESTRELA AGORA')}</span>
                <ArrowRight className="w-10 h-10 group-hover:translate-x-4 transition-transform duration-300" />
              </span>
              
              {/* Brutal shine effect */}
              <div className="absolute inset-0 -translate-x-full group-hover:translate-x-full bg-gradient-to-r from-transparent via-white/30 to-transparent transition-transform duration-700" />
            </button>

            {/* Guarantee - Brutal */}
            <div className="flex items-center justify-center space-x-4 text-black">
              <div className="w-8 h-8 bg-black border-2 border-black flex items-center justify-center">
                <Check className="w-6 h-6 text-white" />
              </div>
              <span className="font-black text-xl">
                {t('landing.cta.guarantee', 'Garantia de satisfação • Você controla seus dados')}
              </span>
            </div>

            {/* Final Proof - Brutal */}
            <div className={`max-w-3xl mx-auto p-8 ${
              isDark ? 'bg-gray-900 shadow-brutal-lg' : 'bg-white shadow-brutal-lg'
            } border-8 ${
              isDark ? 'border-white' : 'border-black'
            } transform rotate-1`}>
              <p className={`text-xl font-black ${
                isDark ? 'text-white' : 'text-black'
              } leading-relaxed`}>
                {t('landing.cta.finalProof', '"Transformei uma selfie comum no pôster mais épico da minha vida. Agora me sinto como um verdadeiro protagonista!" - Ana, Designer')}
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className={`py-12 border-t-4 ${
        isDark ? 'border-white bg-black' : 'border-black bg-white'
      }`}>
        <div className="max-w-6xl mx-auto px-6 text-center">
          <div className="flex items-center justify-center space-x-3 mb-6">
            <img 
              src="/img/logo-final-svg.png" 
              alt="PosterFlix Online" 
              className="h-8 w-auto"
            />
          </div>
          <p className="font-medium opacity-60">
            {t('landing.footer.text', '© 2024 AI Streaming Poster. Todos os direitos reservados.')}
          </p>
        </div>
      </footer>

      {/* Modals */}
      {/* Pricing Modal */}
      <PricingModal
        isOpen={showPricingModal}
        onClose={closeAllModals}
        onSelectPlan={handleSelectPlan}
        loading={paymentLoading}
        currentCredits={creditBalance?.totalCredits || 0}
        user={user}
        onVoucherRedeemed={refetchCredits}
        onAuthRequired={handleVoucherAuthRequired}
      />

      {/* Auth Modal */}
      <AuthModal
        isOpen={showAuthModal}
        onClose={closeAllModals}
        onAuthSuccess={handleAuthSuccess}
      />

      {/* Checkout Modal */}
      {paymentIntent && showCheckoutModal && (
        <CheckoutForm
          stripePromise={stripePromise}
          clientSecret={paymentIntent.clientSecret}
          planId={selectedPlan || ''}
          onSuccess={handlePaymentSuccess}
          onCancel={handlePaymentCancel}
        />
      )}
    </div>
  );
};

// Wrapper component with QueryClientProvider
const LandingPage: React.FC = () => {
  return (
    <QueryClientProvider client={queryClient}>
      <Toaster />
      <LandingPageContent />
    </QueryClientProvider>
  );
};

export default LandingPage;
