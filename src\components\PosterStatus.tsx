import React, { useEffect, useState } from 'react'
import { X, <PERSON>, CheckCircle, Loader, Image, Sparkles } from 'lucide-react'
import { motion, AnimatePresence } from 'framer-motion'

interface PosterStatusProps {
  isOpen: boolean
  onClose: () => void
  totalImages: number
  generatedImages: number
  isGenerating: boolean
  templateName: string
}

export default function PosterStatus({ 
  isOpen, 
  onClose, 
  totalImages, 
  generatedImages, 
  isGenerating,
  templateName 
}: PosterStatusProps) {
  const [elapsedTime, setElapsedTime] = useState(0)

  useEffect(() => {
    let interval: NodeJS.Timeout
    if (isGenerating && isOpen) {
      interval = setInterval(() => {
        setElapsedTime(prev => prev + 1)
      }, 1000)
    } else {
      setElapsedTime(0)
    }
    return () => clearInterval(interval)
  }, [isGenerating, isOpen])

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const progress = totalImages > 0 ? (generatedImages / totalImages) * 100 : 0

  if (!isOpen) return null

  return (
    <AnimatePresence>
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <motion.div 
          initial={{ opacity: 0, scale: 0.9, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.9, y: 20 }}
          transition={{ duration: 0.3 }}
          className="bg-brand-white rounded-lg border-2 border-brand-black shadow-brutal-lg max-w-md w-full p-8"
        >
          {/* Header */}
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-brand-primary rounded-lg border-2 border-brand-black shadow-brutal-sm flex items-center justify-center">
                <Sparkles className="w-6 h-6 text-brand-black" />
              </div>
              <div>
                <h3 className="text-xl font-bold text-brand-text">Gerando Poster</h3>
                <p className="text-sm text-brand-text/70">AI está criando seu poster</p>
              </div>
            </div>
            {!isGenerating && (
              <button
                onClick={onClose}
                className="p-2 bg-brand-white rounded-lg border-2 border-brand-black shadow-brutal-sm hover:shadow-brutal-hover transition-all duration-200"
              >
                <X className="w-5 h-5 text-brand-text" />
              </button>
            )}
          </div>

          {/* Progress Info */}
          <div className="space-y-6">
            {/* Template Info */}
            <div className="bg-brand-white rounded-lg p-4 border-2 border-brand-black shadow-brutal-inset">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-blue-100 rounded-lg border-2 border-brand-black flex items-center justify-center">
                  <Image className="w-5 h-5 text-blue-600" />
                </div>
                <div>
                  <p className="font-bold text-brand-text">Template: {templateName}</p>
                  <p className="text-sm text-brand-text/70">Poster personalizado será criado</p>
                </div>
              </div>
            </div>

            {/* Progress Bar */}
            <div className="space-y-4">
              <div className="flex justify-between text-sm">
                <span className="text-brand-text/70 font-bold">Progresso</span>
                <span className="font-bold text-brand-text">
                  {generatedImages} de {totalImages}
                </span>
              </div>
              <div className="bg-brand-white rounded-full h-4 border-2 border-brand-black shadow-brutal-inset overflow-hidden">
                <motion.div 
                  className="bg-brand-primary h-full rounded-full"
                  initial={{ width: 0 }}
                  animate={{ width: `${progress}%` }}
                  transition={{ duration: 0.8, ease: "easeOut" }}
                />
              </div>
              <p className="text-xs text-brand-text/70 text-center font-bold">
                {progress.toFixed(0)}% concluído
              </p>
            </div>

            {/* Status */}
            <div className="bg-brand-white rounded-lg p-4 border-2 border-brand-black shadow-brutal-sm">
              <div className="flex items-center space-x-3">
                {isGenerating ? (
                  <>
                    <div className="w-10 h-10 bg-purple-100 rounded-lg border-2 border-brand-black flex items-center justify-center">
                      <Loader className="w-5 h-5 text-purple-600 animate-spin" />
                    </div>
                    <div>
                      <p className="font-bold text-brand-text">Processando...</p>
                      <p className="text-sm text-brand-text/70">Tempo: {formatTime(elapsedTime)}</p>
                    </div>
                  </>
                ) : generatedImages === totalImages ? (
                  <>
                    <div className="w-10 h-10 bg-green-100 rounded-lg border-2 border-brand-black flex items-center justify-center">
                      <CheckCircle className="w-5 h-5 text-green-600" />
                    </div>
                    <div>
                      <p className="font-bold text-green-800">Concluído!</p>
                      <p className="text-sm text-green-600">Poster criado com sucesso</p>
                    </div>
                  </>
                ) : (
                  <>
                    <div className="w-10 h-10 bg-orange-100 rounded-lg border-2 border-brand-black flex items-center justify-center">
                      <Clock className="w-5 h-5 text-orange-600" />
                    </div>
                    <div>
                      <p className="font-bold text-orange-800">Aguardando...</p>
                      <p className="text-sm text-orange-600">Preparando processamento</p>
                    </div>
                  </>
                )}
              </div>
            </div>

            {/* Steps */}
            <div className="bg-brand-white rounded-lg p-4 border-2 border-brand-black shadow-brutal-inset">
              <h4 className="font-bold text-brand-text text-sm mb-3">Etapas:</h4>
              <div className="space-y-3">
                <motion.div 
                  className={`flex items-center space-x-3 ${generatedImages > 0 ? 'text-green-600' : 'text-brand-text/50'}`}
                  animate={{ opacity: generatedImages > 0 ? 1 : 0.6 }}
                >
                  <div className={`w-3 h-3 rounded-full ${generatedImages > 0 ? 'bg-green-500 border-2 border-brand-black' : 'bg-gray-300 border-2 border-brand-black'}`} />
                  <span className="text-sm font-bold">Processando imagem original</span>
                </motion.div>
                
                <motion.div 
                  className={`flex items-center space-x-3 ${isGenerating ? 'text-purple-600' : generatedImages === totalImages ? 'text-green-600' : 'text-brand-text/50'}`}
                  animate={{ opacity: isGenerating || generatedImages === totalImages ? 1 : 0.6 }}
                >
                  <div className={`w-3 h-3 rounded-full border-2 border-brand-black ${isGenerating ? 'bg-purple-500 animate-pulse' : generatedImages === totalImages ? 'bg-green-500' : 'bg-gray-300'}`} />
                  <span className="text-sm font-bold">Gerando capa principal</span>
                </motion.div>
                
                <motion.div 
                  className={`flex items-center space-x-3 ${isGenerating ? 'text-blue-600' : generatedImages === totalImages ? 'text-green-600' : 'text-brand-text/50'}`}
                  animate={{ opacity: isGenerating || generatedImages === totalImages ? 1 : 0.6 }}
                >
                  <div className={`w-3 h-3 rounded-full border-2 border-brand-black ${isGenerating ? 'bg-blue-500 animate-pulse' : generatedImages === totalImages ? 'bg-green-500' : 'bg-gray-300'}`} />
                  <span className="text-sm font-bold">Criando poster no Canva</span>
                </motion.div>
                
                <motion.div 
                  className={`flex items-center space-x-3 ${generatedImages === totalImages ? 'text-green-600' : 'text-brand-text/50'}`}
                  animate={{ opacity: generatedImages === totalImages ? 1 : 0.6 }}
                >
                  <div className={`w-3 h-3 rounded-full border-2 border-brand-black ${generatedImages === totalImages ? 'bg-green-500' : 'bg-gray-300'}`} />
                  <span className="text-sm font-bold">Finalizando design</span>
                </motion.div>
              </div>
            </div>

            {/* Estimated Time */}
            {isGenerating && (
              <motion.div 
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-brand-secondary rounded-lg p-4 border-2 border-brand-black shadow-brutal-sm"
              >
                <div className="flex items-center space-x-2">
                  <div className="w-8 h-8 bg-yellow-100 rounded-lg border-2 border-brand-black flex items-center justify-center">
                    <Clock className="w-4 h-4 text-yellow-600" />
                  </div>
                  <p className="text-sm text-brand-text font-bold">
                    Tempo estimado: {Math.ceil((totalImages - generatedImages) * 30)} segundos
                  </p>
                </div>
              </motion.div>
            )}
          </div>

          {/* Footer */}
          {!isGenerating && generatedImages === totalImages && (
            <motion.div 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="mt-8 pt-6 border-t-2 border-brand-black"
            >
              <button
                onClick={onClose}
                className="w-full bg-brand-primary text-brand-black py-4 rounded-lg font-bold border-2 border-brand-black shadow-brutal-sm hover:shadow-brutal-hover transition-all duration-200 flex items-center justify-center space-x-2"
              >
                <CheckCircle className="w-5 h-5 text-brand-black" />
                <span>Ver Resultados</span>
              </button>
            </motion.div>
          )}
        </motion.div>
      </div>
    </AnimatePresence>
  )
} 