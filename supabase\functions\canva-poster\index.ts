import { serve } from 'https://deno.land/std@0.177.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.21.0';

interface CanvaApiConfig {
  clientId: string;
  clientSecret: string;
  accessToken?: string;
}

// Tipos para os dados recebidos
interface PosterData {
  TITLE?: string;
  DESCRIPTION?: string;
  CONTINUE?: string;
  ID?: string; // Template ID do Canva
  CAPA?: string;
  AVATAR?: string;
  CANVA_ACCESS_TOKEN?: string; // Token de acesso do Canva
  streaming_platform?: string; // Para tabela poster_images
  photo_type?: string; // Para tabela poster_images
  user_name?: string; // Para tabela poster_images
  user_id?: string; // User ID do usuário logado (vem da interface)
  [key: string]: string | undefined; // Para campos PHOTO_01, PHOTO_02, etc.
}

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

// Download da imagem de uma URL
async function downloadImage(url: string): Promise<ArrayBuffer> {
  const response = await fetch(url);
  if (!response.ok) {
    throw new Error(`Falha ao baixar a imagem: ${response.statusText}`);
  }
  return await response.arrayBuffer();
}

// Upload de imagem para o Canva - USANDO NOVA API
async function uploadImageToCanva(
  imageUrl: string,
  field: string,
  accessToken: string
): Promise<{ field: string; asset_id: string }> {
  console.log(`Iniciando upload da imagem para o campo ${field}`);
  
  // 1. Download da imagem
  const imageBuffer = await downloadImage(imageUrl);
  
  // 2. Preparar metadata para o header
  const metadata = {
    name_base64: btoa(`Image_${field}_${Date.now()}`) // Nome em base64
  };
  
  // 3. Upload direto para o Canva usando NOVA API
  const uploadResponse = await fetch('https://api.canva.com/rest/v1/asset-uploads', {
    method: 'POST',
    headers: {
      'Authorization': accessToken,
      'Content-Type': 'application/octet-stream',
      'Asset-Upload-Metadata': JSON.stringify(metadata),
    },
    body: imageBuffer,
  });

  if (!uploadResponse.ok) {
    throw new Error(`Falha no upload: ${await uploadResponse.text()}`);
  }

  const uploadData = await uploadResponse.json();
  const jobId = uploadData.job.id;
  
  console.log(`Upload iniciado, job ID: ${jobId}`);

  // 4. Verificar status do upload (polling)
  let assetId = null;
  let attempts = 0;
  
  while (attempts < 10) {
    attempts++;
    
    const statusResponse = await fetch(`https://api.canva.com/rest/v1/asset-uploads/${jobId}`, {
      headers: {
        'Authorization': accessToken,
      },
    });

    if (!statusResponse.ok) {
      throw new Error(`Falha ao verificar o status do upload: ${await statusResponse.text()}`);
    }

    const statusData = await statusResponse.json();
    
    if (statusData.job.status === 'success') {
      assetId = statusData.job.asset.id;
      break;
    } else if (statusData.job.status === 'failed') {
      throw new Error(`Upload falhou: ${JSON.stringify(statusData.job.error)}`);
    }

    // Aguardar antes da próxima verificação
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  if (!assetId) {
    throw new Error('Não foi possível obter o asset_id após múltiplas tentativas');
  }

  console.log(`Upload concluído para o campo ${field}, asset_id: ${assetId}`);
  return { field, asset_id: assetId };
}

// Criar design no Canva
async function createCanvaDesign(templateId: string, data: any, accessToken: string): Promise<any> {
  console.log(`Criando design com template ${templateId}`);
  
  const response = await fetch('https://api.canva.com/rest/v1/autofills', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      brand_template_id: templateId,
      data,
    }),
  });

  if (!response.ok) {
    throw new Error(`Falha ao criar o design: ${await response.text()}`);
  }

  return await response.json();
}

// Verificar status do design
async function checkDesignStatus(designId: string, accessToken: string): Promise<any> {
  const response = await fetch(`https://api.canva.com/rest/v1/autofills/${designId}`, {
    headers: {
      'Authorization': `Bearer ${accessToken}`,
    },
  });

  if (!response.ok) {
    throw new Error(`Falha ao verificar status do design: ${await response.text()}`);
  }

  return await response.json();
}

// ✅ FUNÇÃO PARA RENOVAR TOKEN AUTOMATICAMENTE
async function getValidCanvaToken(requestData: PosterData, canvaConfig: any): Promise<string> {
  // 1. Tentar usar token do payload primeiro
  if (requestData.CANVA_ACCESS_TOKEN) {
    console.log('🔑 Usando token do payload')
    // Garantir que tem Bearer no início
    const token = requestData.CANVA_ACCESS_TOKEN.startsWith('Bearer ') 
      ? requestData.CANVA_ACCESS_TOKEN 
      : `Bearer ${requestData.CANVA_ACCESS_TOKEN}`
    return token
  }

  // 2. Tentar usar token das variáveis de ambiente
  const envToken = Deno.env.get('CANVA_ACCESS_TOKEN')
  if (envToken) {
    console.log('🔑 Usando token do ambiente')
    // Garantir que tem Bearer no início
    const token = envToken.startsWith('Bearer ') 
      ? envToken 
      : `Bearer ${envToken}`
    return token
  }

  // 3. Tentar renovar token via refresh_token
  const refreshToken = Deno.env.get('CANVA_REFRESH_TOKEN')
  if (refreshToken) {
    console.log('🔄 Renovando token via refresh_token...')
    
    try {
      // Criar credenciais Basic Auth
      const credentials = btoa(`${canvaConfig.clientId}:${canvaConfig.clientSecret}`)
      
      const tokenResponse = await fetch('https://api.canva.com/rest/v1/oauth/token', {
        method: 'POST',
        headers: {
          'Authorization': `Basic ${credentials}`,
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: new URLSearchParams({
          grant_type: 'refresh_token',
          refresh_token: refreshToken
        })
      })

      if (!tokenResponse.ok) {
        const errorText = await tokenResponse.text()
        throw new Error(`Erro ao renovar token: ${errorText}`)
      }

      const tokenData = await tokenResponse.json()
      console.log('✅ Token renovado com sucesso! Expira em:', tokenData.expires_in, 'segundos')
      
      // TODO: Idealmente, salvar o novo refresh_token no banco para próximas renovações
      // Deno.env.set('CANVA_REFRESH_TOKEN', tokenData.refresh_token)
      
      return `Bearer ${tokenData.access_token}`
      
    } catch (error) {
      console.error('❌ Erro ao renovar token:', error)
      throw new Error(`Falha na renovação do token: ${error.message}`)
    }
  }

  throw new Error('❌ Nenhum token ou refresh_token disponível. Configure CANVA_ACCESS_TOKEN, CANVA_REFRESH_TOKEN ou inclua token no payload.')
}

// Função principal que serve as requisições
serve(async (req) => {
  // Tratar preflight requests para CORS
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }
  
  // 1. AUTHENTICATION
  const authHeader = req.headers.get('Authorization')
  if (!authHeader) {
    return new Response(JSON.stringify({ error: 'Falta o cabeçalho de autenticação' }), { status: 401, headers: corsHeaders })
  }
  
  const supabaseClient = createClient(
    Deno.env.get('SUPABASE_URL')!,
    Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!,
  )
  
  const { data: { user } } = await supabaseClient.auth.getUser(authHeader.replace('Bearer ', ''));
  if (!user) {
    return new Response(JSON.stringify({ error: 'Token inválido ou expirado' }), { status: 401, headers: corsHeaders })
  }
  
  try {
    // Configuração do Canva
    const canvaConfig = {
      clientId: Deno.env.get('CANVA_CLIENT_ID') || 'OC-AZdfZOcE7mgD',
      clientSecret: Deno.env.get('CANVA_CLIENT_SECRET') || 'cnvca9UbcRNeju-iX-YioJQNkDTYTxpbzcfrpZ_AGkA_vYEc50913078',
      // ⚠️ IMPORTANTE: Canva NÃO suporta client_credentials!
      // Só suporta: authorization_code, refresh_token
      // Por isso precisa usar token pré-autorizado via OAuth2 + PKCE
      accessToken: Deno.env.get('CANVA_ACCESS_TOKEN') // Token pré-autorizado (mesmo que N8N usa)
    };

    // Processar requisição POST para gerar um poster
    if (req.method === 'POST') {
      const requestData: PosterData = await req.json();
      
      // 🔥 CHECAR CRÉDITOS ANTES DE GERAR
      const creditsNeeded = 3; // Poster Canva custa 3 créditos
      
      // Verificar saldo de créditos
      const { data: purchases } = await supabaseClient
        .from('credit_purchases')
        .select('credits')
        .eq('user_id', user.id)
        .eq('status', 'completed')

      const { data: usage } = await supabaseClient
        .from('credit_usage')
        .select('credits_used')
        .eq('user_id', user.id)

      const totalCredits = purchases?.reduce((sum, p) => sum + p.credits, 0) || 0
      const usedCredits = usage?.reduce((sum, u) => sum + u.credits_used, 0) || 0
      const availableCredits = totalCredits - usedCredits

      if (availableCredits < creditsNeeded) {
        return new Response(
          JSON.stringify({ 
            error: 'Créditos insuficientes para gerar poster',
            availableCredits,
            requiredCredits: creditsNeeded,
            message: 'Compre mais créditos para continuar'
          }),
          { status: 402, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        )
      }
      
      console.log(`💳 Usuário tem ${availableCredits} créditos. Necessário: ${creditsNeeded}`);
      
      // Validar dados recebidos
      if (!requestData.ID) {
        return new Response(
          JSON.stringify({ error: 'Template ID é obrigatório' }),
          { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        );
      }

      // ✅ USAR TOKEN VÁLIDO JÁ AUTORIZADO (mesmo que o N8N usa)
      // Prioridade: 1) payload, 2) env var, 3) config
      const accessToken = await getValidCanvaToken(requestData, canvaConfig);
      
      // Registrar processo no banco de dados - USAR TABELA EXISTENTE
      const { data: generationRecord, error: insertError } = await supabaseClient
        .from('poster_images')
        .insert({
          user_id: user.id, // Usar user_id seguro do token
          streaming_platform: requestData.streaming_platform || 'netflix',
          photo_type: requestData.photo_type || 'individual',
          template_id: requestData.ID,
          poster_url: '', // Será preenchido depois
          canva_design_url: '', // Será preenchido depois
          cover_url: requestData.CAPA,
          avatar_url: requestData.AVATAR,
          user_name: requestData.user_name || 'Teste Edge Function',
          request_payload: requestData // 🔥 NOVO: Salvar payload completo para regeneração
        })
        .select()
        .single();
        
      if (insertError) {
        throw new Error(`Erro ao registrar processo: ${insertError.message}`);
      }
      
      const generationId = generationRecord.id;

      // Processar os campos de imagem
      const imgFields = [
        "CAPA", "AVATAR",
        ...Array(12).fill(0).map((_,i)=>`PHOTO_${(i+1).toString().padStart(2,'0')}`)
      ];

      // Filtrar campos de imagem que existem no requestData
      const imageFields = imgFields
        .filter(field => !!requestData[field])
        .map(field => ({ field, url: requestData[field]! }));

      console.log(`Processando ${imageFields.length} imagens`);

      // Upload de todas as imagens para o Canva
      const uploadPromises = imageFields.map(({ field, url }) => 
        uploadImageToCanva(url, field, accessToken)
      );
      
      const uploadResults = await Promise.all(uploadPromises);
      console.log('Uploads concluídos:', uploadResults);

      // Preparar os dados de texto
      const textFields = ["TITLE", "DESCRIPTION", "CONTINUE"].reduce((obj, key) => {
        if (requestData[key]) {
          obj[key] = { type: "text", text: requestData[key] };
        }
        return obj;
      }, {} as Record<string, any>);

      // Preparar os dados de imagem
      const imageData = Object.fromEntries(
        uploadResults.map(({ field, asset_id }) => [field, { type: "image", asset_id }])
      );

      // Montar payload final
      const designData = {
        ...textFields,
        ...imageData
      };

      // Criar design no Canva
      const createDesignResponse = await createCanvaDesign(requestData.ID, designData, accessToken);
      console.log('Design criado - resposta completa:', JSON.stringify(createDesignResponse, null, 2));

      // A API de autofills retorna um job, não um design direto
      const designJobId = createDesignResponse.job?.id || createDesignResponse.id;
      
      if (!designJobId) {
        console.error('Erro: Não foi possível obter job ID da resposta:', createDesignResponse);
        throw new Error('Não foi possível obter o job ID da criação do design');
      }
      
      console.log('Job ID do design:', designJobId);

      // Verificar status do design (polling)
      let designStatus;
      let attempts = 0;
      let designUrl = null;

      while (attempts < 10) {
        attempts++;
        
        console.log(`Tentativa ${attempts}: Verificando status do job ${designJobId}`);
        const statusData = await checkDesignStatus(designJobId, accessToken);
        console.log('Status atual:', JSON.stringify(statusData, null, 2));
        
        designStatus = statusData.job?.status;
        
        if (designStatus === 'success') {
          designUrl = statusData.job?.result?.url;
          console.log('Design concluído! URL:', designUrl);
          break;
        } else if (designStatus === 'failed') {
          throw new Error(`Geração de design falhou: ${JSON.stringify(statusData.job?.error)}`);
        }
        
        console.log(`Status atual: ${designStatus}, aguardando...`);
        // Aguardar antes da próxima verificação
        await new Promise(resolve => setTimeout(resolve, 2000));
      }

      if (!designUrl) {
        throw new Error('Não foi possível obter a URL do design após múltiplas tentativas');
      }

      // 🔥 CONSUMIR CRÉDITOS APENAS APÓS SUCESSO
      console.log(`💳 Consumindo ${creditsNeeded} créditos pelo poster Canva...`)
      
      const consumeResponse = await supabaseClient.functions.invoke('consume-credits', {
        body: { 
          amount: creditsNeeded, 
          description: `Poster Canva - ${requestData.ID}` 
        },
        headers: { Authorization: authHeader }
      });

      if (consumeResponse.error || !consumeResponse.data?.success) {
        console.error('❌ Erro ao consumir créditos após sucesso:', consumeResponse.error)
        // Não falhar a geração por causa disso, apenas logar
      } else {
        console.log(`✅ ${creditsNeeded} créditos consumidos com sucesso para poster Canva`)
      }

      // Atualizar registro no banco de dados
      await supabaseClient
        .from('poster_images')
        .update({
          poster_url: designUrl,
          canva_design_url: designUrl,
          completed_at: new Date().toISOString()
        })
        .eq('id', generationId);

      // Retornar resultado
      return new Response(
        JSON.stringify({
          id: generationId,
          status: 'completed',
          designUrl,
          templateId: requestData.ID
        }),
        { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }
    
    // Endpoint para verificar o status de uma geração
    else if (req.method === 'GET') {
      const url = new URL(req.url);
      const id = url.searchParams.get('id');
      
      if (!id) {
        return new Response(
          JSON.stringify({ error: 'ID é obrigatório' }),
          { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        );
      }
      
      const { data, error } = await supabaseClient
        .from('poster_images')
        .select('*')
        .eq('id', id)
        .single();
        
      if (error) {
        return new Response(
          JSON.stringify({ error: error.message }),
          { status: 404, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        );
      }
      
      return new Response(
        JSON.stringify(data),
        { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }
    
    return new Response(
      JSON.stringify({ error: 'Método não suportado' }),
      { status: 405, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
    
  } catch (error) {
    console.error('Erro:', error);
    
    return new Response(
      JSON.stringify({ error: error.message }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }
});
