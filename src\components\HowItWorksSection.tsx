import React from 'react';
import { UploadCloud, Sparkles, Download } from 'lucide-react';

const HowItWorksSection: React.FC = () => {
  const steps = [
    {
      icon: <UploadCloud className="w-12 h-12 text-yellow-400" />,
      title: '1. Carregue a sua foto',
      description: 'Escolha a sua melhor selfie. O nosso sistema aceita vários formatos e prepara a sua imagem para a transformação.',
    },
    {
      icon: <Sparkles className="w-12 h-12 text-pink-500" />,
      title: '2. A nossa IA faz a magia',
      description: 'A nossa tecnologia de ponta analisa a sua foto e recria-a no estilo de poster de cinema que sempre sonhou, com resultados autênticos.',
    },
    {
      icon: <Download className="w-12 h-12 text-green-400" />,
      title: '3. <PERSON>car<PERSON>gue o seu poster',
      description: '<PERSON><PERSON><PERSON> o seu poster em alta resolução, pronto para partilhar nas redes sociais, imprimir ou guardar como uma recordação única.',
    },
  ];

  return (
    <section className="py-20 px-8 bg-black">
      <div className="max-w-6xl mx-auto text-center">
        <h2 className="text-4xl md:text-5xl font-bold mb-12 uppercase text-white">Como Funciona</h2>
        <div className="grid md:grid-cols-3 gap-8">
          {steps.map((step, index) => (
            <div key={index} className="bg-[#1a1a1a] p-8 border-2 border-black shadow-[8px_8px_0px_rgba(255,255,255,0.1)]">
              <div className="flex justify-center mb-6">
                {step.icon}
              </div>
              <h3 className="text-2xl font-bold mb-4 text-white">{step.title}</h3>
              <p className="text-gray-400">{step.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default HowItWorksSection;
