// Google Tag Manager utilities
declare global {
  interface Window {
    dataLayer: any[];
    gtag?: (...args: any[]) => void;
  }
}

// Initialize dataLayer if not exists
if (typeof window !== 'undefined') {
  window.dataLayer = window.dataLayer || [];
}

// GTM Events
export const GTMEvents = {
  // Page Views
  PAGE_VIEW: 'page_view',
  
  // User Actions
  USER_SIGNUP: 'user_signup',
  USER_LOGIN: 'user_login',
  
  // Credit System
  CREDIT_PURCHASE_START: 'begin_checkout',
  CREDIT_PURCHASE_SUCCESS: 'purchase',
  CREDIT_PURCHASE_FAILED: 'purchase_failed',
  
  // Generation Events
  GENERATION_START: 'generation_start',
  GENERATION_COMPLETE: 'generation_complete',
  GENERATION_FAILED: 'generation_failed',
  
  // Cover Actions
  COVER_DOWNLOAD: 'cover_download',
  COVER_REGENERATE: 'cover_regenerate',
  
  // Poster Actions
  POSTER_CREATE: 'poster_create',
  POSTER_DOWNLOAD: 'poster_download',
  
  // UI Interactions
  BUTTON_CLICK: 'button_click',
  MODAL_OPEN: 'modal_open',
  MODAL_CLOSE: 'modal_close',
  
  // Errors
  ERROR_OCCURRED: 'error_occurred'
} as const;

// Send event to GTM
export function sendGTMEvent(eventName: string, parameters?: Record<string, any>) {
  if (typeof window === 'undefined') return;
  
  try {
    window.dataLayer = window.dataLayer || [];
    window.dataLayer.push({
      event: eventName,
      timestamp: new Date().toISOString(),
      ...parameters
    });
    
    console.log('🏷️ GTM Event:', eventName, parameters);
  } catch (error) {
    console.error('GTM Event Error:', error);
  }
}

// Convenience functions for common events
export const gtm = {
  // Page tracking
  pageView: (page_title: string, page_location?: string) => {
    sendGTMEvent(GTMEvents.PAGE_VIEW, {
      page_title,
      page_location: page_location || window.location.href
    });
  },

  // User events
  userSignup: (method: string = 'email') => {
    sendGTMEvent(GTMEvents.USER_SIGNUP, { method });
  },

  userLogin: (method: string = 'email') => {
    sendGTMEvent(GTMEvents.USER_LOGIN, { method });
  },

  // Credit events
  creditPurchaseStart: (plan_id: string, credits: number, amount: number, currency: string = 'BRL') => {
    sendGTMEvent(GTMEvents.CREDIT_PURCHASE_START, {
      currency,
      value: amount / 100,
      items: [{
        item_id: plan_id,
        item_name: `${credits} Credits`,
        item_category: 'credits',
        quantity: 1,
        price: amount / 100
      }]
    });
  },

  creditPurchaseSuccess: (plan_id: string, credits: number, amount: number, currency: string = 'BRL') => {
    sendGTMEvent(GTMEvents.CREDIT_PURCHASE_SUCCESS, {
      currency,
      transaction_id: Date.now().toString(),
      value: amount / 100,
      items: [{
        item_id: plan_id,
        item_name: `${credits} Credits`,
        item_category: 'credits',
        quantity: 1,
        price: amount / 100
      }]
    });
  },

  creditPurchaseFailed: (plan_id: string, error_message: string) => {
    sendGTMEvent(GTMEvents.CREDIT_PURCHASE_FAILED, {
      plan_id,
      error_message
    });
  },

  // Generation events
  generationStart: (platform: string, photo_type: string, credits_used: number) => {
    sendGTMEvent(GTMEvents.GENERATION_START, {
      platform,
      photo_type,
      credits_used
    });
  },

  generationComplete: (platform: string, photo_type: string, covers_generated: number, duration_seconds: number) => {
    sendGTMEvent(GTMEvents.GENERATION_COMPLETE, {
      platform,
      photo_type,
      covers_generated,
      duration_seconds
    });
  },

  generationFailed: (platform: string, error_message: string) => {
    sendGTMEvent(GTMEvents.GENERATION_FAILED, {
      platform,
      error_message
    });
  },

  // Cover actions
  coverDownload: (platform: string, cover_id: string) => {
    sendGTMEvent(GTMEvents.COVER_DOWNLOAD, {
      platform,
      cover_id
    });
  },

  coverRegenerate: (platform: string, cover_id: string) => {
    sendGTMEvent(GTMEvents.COVER_REGENERATE, {
      platform,
      cover_id
    });
  },

  // Poster actions
  posterCreate: (platform: string, covers_count: number) => {
    sendGTMEvent(GTMEvents.POSTER_CREATE, {
      platform,
      covers_count
    });
  },

  posterDownload: (platform: string, poster_id: string) => {
    sendGTMEvent(GTMEvents.POSTER_DOWNLOAD, {
      platform,
      poster_id
    });
  },

  // UI interactions
  buttonClick: (button_name: string, location: string) => {
    sendGTMEvent(GTMEvents.BUTTON_CLICK, {
      button_name,
      location
    });
  },

  modalOpen: (modal_name: string) => {
    sendGTMEvent(GTMEvents.MODAL_OPEN, {
      modal_name
    });
  },

  modalClose: (modal_name: string) => {
    sendGTMEvent(GTMEvents.MODAL_CLOSE, {
      modal_name
    });
  },

  // Error tracking
  errorOccurred: (error_message: string, error_location: string, error_type?: string) => {
    sendGTMEvent(GTMEvents.ERROR_OCCURRED, {
      error_message,
      error_location,
      error_type: error_type || 'javascript_error'
    });
  }
};

// Auto-track page views on route changes (for SPAs)
export function initGTMPageTracking() {
  if (typeof window === 'undefined') return;
  
  // Track initial page load
  gtm.pageView(document.title);
  
  // Track route changes
  const originalPushState = history.pushState;
  const originalReplaceState = history.replaceState;
  
  history.pushState = function(...args) {
    originalPushState.apply(history, args);
    setTimeout(() => gtm.pageView(document.title), 100);
  };
  
  history.replaceState = function(...args) {
    originalReplaceState.apply(history, args);
    setTimeout(() => gtm.pageView(document.title), 100);
  };
  
  window.addEventListener('popstate', () => {
    setTimeout(() => gtm.pageView(document.title), 100);
  });
} 