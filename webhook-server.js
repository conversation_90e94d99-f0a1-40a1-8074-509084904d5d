import express from 'express';
import { createClient } from '@supabase/supabase-js';
const app = express();
const port = 3000;

// Configuração do Supabase
const supabaseUrl = 'https://uptmptfpumgrnlxukwau.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.Hc6PYWzq9WtA98QtzHb9X6t8cBI1nhSV_GqnWHHM9Jc';
const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Middleware para parsing JSON
app.use(express.json());

// CORS para desenvolvimento
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, stripe-signature');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  if (req.method === 'OPTIONS') {
    res.sendStatus(200);
  } else {
    next();
  }
});

// Rota de teste
app.get('/', (req, res) => {
  res.json({ 
    message: 'Webhook server rodando!', 
    timestamp: new Date().toISOString(),
    ngrok_url: 'https://f7a3268b8578.ngrok-free.app'
  });
});

// Webhook do Stripe
app.post('/api/stripe/webhook', async (req, res) => {
  console.log('🔥 Webhook recebido:', req.body.type);
  console.log('📝 Headers:', req.headers);
  
  try {
    const event = req.body;
    
    switch (event.type) {
      case 'payment_intent.succeeded':
        const paymentIntent = event.data.object;
        const userId = paymentIntent.metadata.userId;
        const planId = paymentIntent.metadata.planId;
        const planType = paymentIntent.metadata.planType;
        const credits = parseInt(paymentIntent.metadata.credits) || 0;

        console.log(`💰 Pagamento confirmado:`, {
          paymentIntentId: paymentIntent.id,
          userId,
          planId,
          credits,
          amount: paymentIntent.amount
        });

        if (userId && planId && planType === 'credits') {
          // Atualizar status da compra
          const { error: updateError } = await supabase
            .from('credit_purchases')
            .update({ 
              status: 'completed',
              completed_at: new Date().toISOString()
            })
            .eq('stripe_payment_intent_id', paymentIntent.id);

          if (updateError) {
            console.error('❌ Erro ao atualizar compra:', updateError);
            return res.status(500).json({ error: updateError.message });
          }

          // Verificar se usuário tem registro de créditos
          const { data: existingCredits, error: checkError } = await supabase
            .from('user_credits')
            .select('*')
            .eq('user_id', userId)
            .single();

          if (checkError && checkError.code === 'PGRST116') {
            // Criar registro se não existir
            const { error: insertError } = await supabase
              .from('user_credits')
              .insert({
                user_id: userId,
                total_credits: credits,
                available_credits: credits,
                used_credits: 0
              });

            if (insertError) {
              console.error('❌ Erro ao criar registro de créditos:', insertError);
              return res.status(500).json({ error: insertError.message });
            }

            console.log(`✅ Registro de créditos criado com ${credits} créditos`);
          } else if (!checkError) {
            // Atualizar registro existente
            const { error: updateCreditsError } = await supabase
              .from('user_credits')
              .update({
                total_credits: (existingCredits.total_credits || 0) + credits,
                available_credits: (existingCredits.available_credits || 0) + credits,
                updated_at: new Date().toISOString()
              })
              .eq('user_id', userId);

            if (updateCreditsError) {
              console.error('❌ Erro ao atualizar créditos:', updateCreditsError);
              return res.status(500).json({ error: updateCreditsError.message });
            }

            console.log(`✅ ${credits} créditos adicionados ao usuário ${userId}`);
          }

          res.json({ received: true, message: `${credits} créditos processados` });
        } else {
          console.warn('⚠️ Metadata inválida:', { userId, planId, planType });
          res.json({ received: true, message: 'Metadata inválida' });
        }
        break;

      case 'payment_intent.payment_failed':
        const failedPayment = event.data.object;
        console.log(`❌ Pagamento falhou:`, failedPayment.id);
        
        // Atualizar status para failed
        const { error: failError } = await supabase
          .from('credit_purchases')
          .update({ status: 'failed' })
          .eq('stripe_payment_intent_id', failedPayment.id);

        if (failError) {
          console.error('❌ Erro ao marcar pagamento como falho:', failError);
        }
        
        res.json({ received: true, message: 'Pagamento falho processado' });
        break;

      default:
        console.log(`ℹ️ Evento não tratado: ${event.type}`);
        res.json({ received: true, message: 'Evento não tratado' });
    }
  } catch (error) {
    console.error('💥 Erro no webhook:', error);
    res.status(500).json({ error: error.message });
  }
});

// Rota de teste para simular webhook
app.post('/test-webhook', async (req, res) => {
  console.log('🧪 Teste de webhook manual');
  
  const testEvent = {
    type: 'payment_intent.succeeded',
    data: {
      object: {
        id: 'pi_test_' + Date.now(),
        metadata: {
          userId: 'a3379e9d-579b-4333-946a-f42584b93af6',
          planId: 'starter_credits',
          planType: 'credits',
          credits: '30'
        },
        amount: 4990
      }
    }
  };

  // Simular o processamento do webhook
  req.body = testEvent;
  
  // Chamar a rota do webhook
  return app._router.handle({ method: 'POST', url: '/api/stripe/webhook', body: testEvent }, res);
});

app.listen(port, () => {
  console.log(`🚀 Webhook server rodando na porta ${port}`);
  console.log(`🌐 URL pública: https://f7a3268b8578.ngrok-free.app`);
  console.log(`📡 Webhook URL: https://f7a3268b8578.ngrok-free.app/api/stripe/webhook`);
  console.log(`🧪 Teste manual: https://f7a3268b8578.ngrok-free.app/test-webhook`);
});