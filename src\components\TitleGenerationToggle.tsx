import React from 'react'
import { useTranslation } from 'react-i18next'
import { Type, AlertTriangle } from 'lucide-react'

interface TitleGenerationToggleProps {
  enabled: boolean
  onToggle: (enabled: boolean) => void
}

export default function TitleGenerationToggle({ enabled, onToggle }: TitleGenerationToggleProps) {
  const { t } = useTranslation()

  return (
    <div className="w-full">
      <div className="bg-gray-50 rounded-xl p-6 border border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-purple-100 rounded-lg">
              <Type size={20} className="text-purple-600" />
            </div>
            <div>
              <h4 className="font-semibold text-gray-800">{t('titleGeneration.title')}</h4>
              <p className="text-sm text-gray-600">{t('titleGeneration.subtitle')}</p>
            </div>
          </div>
          
          <button
            onClick={() => onToggle(!enabled)}
            className={`
              relative inline-flex h-7 w-12 items-center rounded-full transition-all duration-300
              ${enabled ? 'bg-purple-600 shadow-lg' : 'bg-gray-300'}
            `}
          >
            <span
              className={`
                inline-block h-5 w-5 transform rounded-full bg-white transition-transform duration-300 shadow-md
                ${enabled ? 'translate-x-6' : 'translate-x-1'}
              `}
            />
          </button>
        </div>
        
        {enabled && (
          <div className="bg-amber-50 border border-amber-200 rounded-lg p-4 mt-4">
            <div className="flex items-start space-x-3">
              <AlertTriangle className="w-5 h-5 text-amber-600 mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="text-amber-800 font-medium mb-1">{t('titleGeneration.importantNotice')}</h4>
                <p className="text-amber-700 text-sm">{t('titleGeneration.noticeText')}</p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}