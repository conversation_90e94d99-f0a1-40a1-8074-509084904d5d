// Teste simples do webhook
const https = require('https');

const data = JSON.stringify({
  type: 'payment_intent.succeeded',
  data: {
    object: {
      id: 'pi_test_123',
      metadata: {
        userId: 'a3379e9d-579b-4333-946a-f42584b93af6',
        planId: 'starter_credits',
        planType: 'credits',
        credits: '30'
      },
      amount: 4990
    }
  }
});

const options = {
  hostname: 'uptmptfpumgrnlxukwau.supabase.co',
  port: 443,
  path: '/functions/v1/stripe-webhook',
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Content-Length': data.length,
    'stripe-signature': 'test_signature'
  }
};

const req = https.request(options, (res) => {
  console.log(`Status: ${res.statusCode}`);
  console.log(`Headers: ${JSON.stringify(res.headers)}`);
  
  res.on('data', (d) => {
    console.log('Response:', d.toString());
  });
});

req.on('error', (e) => {
  console.error('Error:', e);
});

req.write(data);
req.end();

console.log('Teste do webhook enviado...');