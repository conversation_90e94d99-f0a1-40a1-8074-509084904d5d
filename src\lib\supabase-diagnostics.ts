// Diagnóstico do cliente Supabase
import { createClient } from '@supabase/supabase-js'
import { logger } from '../utils/logger'

logger.log('🔍 Iniciando diagnóstico do Supabase...')

// 🔥 USAR VARIÁVEIS DE AMBIENTE
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || ''
const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY || ''

logger.log('📍 URL:', supabaseUrl || '❌ NÃO DEFINIDA')
logger.log('🔑 Key:', supabaseKey ? 'Definida' : '❌ NÃO DEFINIDA')

// Interceptar fetch para ver os headers
const originalFetch = window.fetch
window.fetch = async (input: RequestInfo | URL, init?: RequestInit) => {
  logger.log('🌐 Interceptando requisição:', {
    url: input,
    headers: init?.headers
  })
  
  // Se for uma requisição para o Supabase, verificar headers
  if (typeof input === 'string' && input.includes('supabase.co')) {
    logger.log('📤 Headers enviados para Supabase:', init?.headers)
    
    // Verificar se tem apikey
    const headers = init?.headers as any
    if (!headers?.apikey) {
      logger.error('❌ ERRO: Header "apikey" não está sendo enviado!')
    }
    if (!headers?.Authorization && !headers?.authorization) {
      logger.warn('⚠️ AVISO: Header "Authorization" não está sendo enviado (pode ser normal para requisições anônimas)')
    }
  }
  
  return originalFetch(input, init)
}

// Testar criação do cliente
if (supabaseUrl && supabaseKey) {
  try {
    const testClient = createClient(supabaseUrl, supabaseKey)
    logger.log('✅ Cliente Supabase criado com sucesso no diagnóstico')
    
    // Verificar se o cliente tem as propriedades esperadas
    logger.log('🔍 Propriedades do cliente:', {
      auth: !!testClient.auth,
      from: !!testClient.from,
      storage: !!testClient.storage,
      functions: !!testClient.functions
    })
  } catch (error) {
    logger.error('❌ Erro ao criar cliente Supabase:', error)
  }
} else {
  logger.error('❌ Não foi possível criar cliente de teste - variáveis de ambiente não definidas')
}

export {}