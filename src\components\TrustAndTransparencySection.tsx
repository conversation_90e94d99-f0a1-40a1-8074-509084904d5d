import React, { useState } from 'react';
import { ChevronDown, <PERSON><PERSON>heck, FileImage, Award } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

const TrustAndTransparencySection: React.FC = () => {
  const faqs = [
    {
      icon: <ShieldCheck className="w-8 h-8 text-green-400" />,
      question: 'Os meus dados e fotos estão seguros?',
      answer: 'Sim. A sua privacidade é a nossa prioridade máxima. As suas fotos são processadas de forma segura e eliminadas dos nossos servidores 24 horas após a geração do póster. Nunca partilhamos os seus dados com terceiros.',
    },
    {
      icon: <FileImage className="w-8 h-8 text-blue-400" />,
      question: 'Quem detém os direitos da imagem gerada?',
      answer: 'Você. A imagem original é sua e o resultado final também. Concedemos-lhe todos os direitos para usar o seu póster como quiser, seja para fins pessoais ou comerciais.',
    },
    {
      icon: <Award className="w-8 h-8 text-yellow-400" />,
      question: 'Qual é a qualidade e resolução dos pósteres?',
      answer: 'Entregamos todos os pósteres em alta resolução (300 DPI), perfeitos para impressão em grande formato ou para partilhar nas redes sociais com a máxima qualidade. A nossa IA foi treinada para garantir resultados nítidos e detalhados.',
    },
  ];

  const [openIndex, setOpenIndex] = useState<number | null>(0);

  return (
    <section className="py-20 px-8 bg-[#1a1a1a] border-t-4 border-black">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-12">
          <h2 className="text-4xl md:text-5xl font-bold uppercase text-white">Confiança e Transparência</h2>
          <p className="text-lg text-gray-400 mt-4">As suas perguntas mais importantes, respondidas com clareza.</p>
        </div>
        <div className="space-y-4">
          {faqs.map((faq, index) => (
            <div key={index} className="bg-black border-2 border-black shadow-[4px_4px_0px_rgba(255,255,255,0.1)]">
              <button
                className="w-full flex justify-between items-center p-6 text-left"
                onClick={() => setOpenIndex(openIndex === index ? null : index)}
              >
                <div className="flex items-center">
                  <div className="mr-4">{faq.icon}</div>
                  <span className="text-xl font-bold text-white">{faq.question}</span>
                </div>
                <motion.div animate={{ rotate: openIndex === index ? 180 : 0 }}>
                  <ChevronDown className="w-6 h-6 text-yellow-400" />
                </motion.div>
              </button>
              <AnimatePresence>
                {openIndex === index && (
                  <motion.div
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: 'auto', opacity: 1 }}
                    exit={{ height: 0, opacity: 0 }}
                    className="overflow-hidden"
                  >
                    <div className="p-6 pt-0 text-gray-300">
                      {faq.answer}
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default TrustAndTransparencySection;
