import React from 'react';
import { useNavigate } from 'react-router-dom';

const LandingHero: React.FC = () => {
  const navigate = useNavigate();

  const handleCTAClick = () => {
    navigate('/login');
  };
  
  return (
    <section className="min-h-screen flex flex-col items-center justify-center text-center p-8 bg-[#1a1a1a] border-b-4 border-black">
      <div className="max-w-4xl">
        <h1 className="text-5xl md:text-7xl font-bold mb-4 uppercase tracking-wider text-yellow-400" style={{ textShadow: '4px 4px 0px #000000' }}>
          Torne-se a Estrela do Seu Próprio Filme.
        </h1>
        <p className="text-lg md:text-xl mb-8 text-gray-300 max-w-2xl mx-auto">
          Crie pósteres de cinema com o seu rosto, utilizando inteligência artificial. Transforme as suas fotos em obras de arte cinematográficas autênticas e partilháveis.
        </p>
        <button 
          onClick={handleCTAClick}
          className="bg-yellow-400 text-black font-bold py-4 px-8 text-xl border-2 border-black shadow-[8px_8px_0px_#000000] hover:shadow-none hover:translate-x-1 hover:translate-y-1 transition-all duration-200"
        >
          Crie o Meu Póster
        </button>
      </div>
    </section>
  );
};

export default LandingHero;
