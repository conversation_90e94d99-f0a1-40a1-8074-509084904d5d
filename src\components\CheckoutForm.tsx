import React, { useState, useRef } from 'react'
import { useTranslation } from 'react-i18next'
import {
  useStripe,
  useElements,
  PaymentElement,
  Elements
} from '@stripe/react-stripe-js'
import { X, CreditCard, Lock } from 'lucide-react'
import { CREDIT_PLANS } from '../lib/stripe'
import { gtm } from '../lib/gtm'
import { usePayment } from '../hooks/usePayment'
import { supabase } from '../lib/supabase'

interface CheckoutFormProps {
  clientSecret: string
  planId: string
  paymentIntentId: string
  onSuccess: () => void
  onCancel: () => void
}

function CheckoutFormContent({ planId, paymentIntentId, onSuccess, onCancel }: CheckoutFormProps) {
  const { t } = useTranslation()
  const stripe = useStripe()
  const elements = useElements()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const { cancelPayment } = usePayment()
  const canceledRef = useRef(false)

  const plan = CREDIT_PLANS[planId as keyof typeof CREDIT_PLANS]

  // Track modal open on component mount
  React.useEffect(() => {
    gtm.modalOpen('credit_purchase_checkout');
    gtm.creditPurchaseStart(planId, plan?.credits || 0, plan?.price || 0);
  }, [planId, plan]);

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault()

    if (!stripe || !elements) {
      return
    }

    setLoading(true)
    setError(null)

    const { error: confirmError } = await stripe.confirmPayment({
      elements,
      confirmParams: {
        return_url: window.location.origin,
      },
      redirect: 'if_required'
    })

    if (confirmError) {
      setError(confirmError.message || 'An unexpected error occurred.')
      gtm.creditPurchaseFailed(planId, confirmError.message || 'Payment confirmation failed');
      setLoading(false)
    } else {
      gtm.creditPurchaseSuccess(planId, plan?.credits || 0, plan?.price || 0);
      setLoading(false)
      onSuccess()
    }
  }

  const handleCancel = async () => {
    if (canceledRef.current) return // Prevent multiple cancellations
    canceledRef.current = true

    gtm.modalClose('credit_purchase_checkout');
    
    // Try to cancel the payment intent in the background
    try {
      const { data: { user } } = await supabase.auth.getUser()
      
      if (user?.id && paymentIntentId) {
        console.log('🚫 Attempting to cancel payment intent:', paymentIntentId)
        await cancelPayment(paymentIntentId, user.id)
      }
    } catch (error) {
      console.warn('⚠️ Failed to cancel payment intent (non-critical):', error)
      // Don't block the UI for cancellation failures
    }
    
    onCancel()
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl shadow-2xl max-w-md w-full">
        <div className="p-6 border-b border-gray-200">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-bold text-gray-800">{t('checkoutForm.title')}</h2>
            <button
              onClick={handleCancel}
              className="p-2 hover:bg-gray-100 rounded-full transition-colors"
            >
              <X size={20} className="text-gray-600" />
            </button>
          </div>
        </div>

        <div className="p-6">
          {/* Credit Plan Summary */}
          <div className="bg-gray-50 rounded-lg p-4 mb-6">
            <h3 className="font-semibold text-gray-800 mb-2">{t(plan?.name)}</h3>
            <div className="flex justify-between items-center mb-2">
              <span className="text-gray-600">{t('checkoutForm.credits')}</span>
              <span className="font-bold text-blue-600">{t('checkoutForm.creditsCount', { count: plan?.credits })}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600">{t('checkoutForm.total')}</span>
              <span className="text-2xl font-bold text-gray-800">{plan?.priceDisplay}</span>
            </div>
            <div className="text-sm text-gray-500 mt-2">
              {plan?.pricePerCredit} {t('checkoutForm.perCredit')}
            </div>
          </div>

          <form onSubmit={handleSubmit}>
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <CreditCard className="w-4 h-4 inline mr-2" />
                {t('checkoutForm.paymentInfo')}
              </label>
              <div className="border border-gray-300 rounded-lg p-3">
                <PaymentElement />
              </div>
            </div>

            {error && (
              <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-red-600 text-sm">{error}</p>
              </div>
            )}

            <button
              type="submit"
              disabled={!stripe || loading}
              className="w-full bg-gradient-to-r from-purple-600 to-pink-600 text-white py-3 rounded-lg font-semibold hover:from-purple-700 hover:to-pink-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? (
                <div className="flex items-center justify-center space-x-2">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  <span>{t('checkoutForm.processing')}</span>
                </div>
              ) : (
                <div className="flex items-center justify-center space-x-2">
                  <Lock className="w-4 h-4" />
                  <span>{t('checkoutForm.pay', { price: plan?.priceDisplay })}</span>
                </div>
              )}
            </button>

            <div className="mt-4 text-center">
              <p className="text-gray-500 text-xs">
                {t('checkoutForm.securePayment')}
              </p>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}

interface CheckoutFormWrapperProps extends CheckoutFormProps {
  stripePromise: Promise<any>
}

export default function CheckoutForm({ stripePromise, ...props }: CheckoutFormWrapperProps) {
  const { i18n } = useTranslation()
  const options = {
    clientSecret: props.clientSecret,
    appearance: { theme: 'stripe' as const },
    locale: (i18n.language.startsWith('pt') ? 'pt-BR' : 'en') as 'pt-BR' | 'en',
  }

  return (
    <Elements stripe={stripePromise} options={options}>
      <CheckoutFormContent {...props} />
    </Elements>
  )
} 