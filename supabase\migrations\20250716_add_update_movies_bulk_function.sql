-- Função para atualizar filmes em lote a partir de dados JSON
CREATE OR REPLACE FUNCTION public.update_movies_bulk(movies_data jsonb)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    movie_item jsonb;
    movie_id uuid;
    updated_count integer := 0;
    failed_count integer := 0;
    failed_ids jsonb := '[]';
    result jsonb;
BEGIN
    -- Iterar sobre cada item no array JSON
    FOR movie_item IN SELECT * FROM jsonb_array_elements(movies_data)
    LOOP
        BEGIN
            -- Extrair ID do filme
            movie_id := (movie_item->>'id')::uuid;
            
            -- Atualizar o filme na tabela unified_movies_series
            -- Mapear os campos do JSON para os campos corretos da tabela
            UPDATE public.unified_movies_series
            SET
                title = COALESCE(movie_item->>'title', title),
                streaming_platform = COALESCE(movie_item->>'streaming_platform', streaming_platform),
                -- Mapear os novos campos para os campos existentes
                base_prompt = COALESCE(
                    movie_item->>'base_prompt', 
                    movie_item->>'male_character_prompt', 
                    base_prompt
                ),
                safe_prompt = COALESCE(
                    movie_item->>'safe_prompt', 
                    safe_prompt
                ),
                gender_male_prompt = COALESCE(
                    movie_item->>'gender_male_prompt', 
                    movie_item->>'male_character_prompt', 
                    gender_male_prompt
                ),
                gender_female_prompt = COALESCE(
                    movie_item->>'gender_female_prompt', 
                    movie_item->>'female_character_prompt', 
                    gender_female_prompt
                ),
                couple_prompt = COALESCE(
                    movie_item->>'couple_prompt', 
                    couple_prompt
                ),
                ideal_prompt = COALESCE(
                    movie_item->>'ideal_prompt', 
                    ideal_prompt
                ),
                is_active = COALESCE((movie_item->>'is_active')::boolean, is_active),
                default_creativity_level = COALESCE(movie_item->>'default_creativity_level', default_creativity_level),
                updated_at = now()
            WHERE id = movie_id;
            
            IF FOUND THEN
                updated_count := updated_count + 1;
            ELSE
                failed_count := failed_count + 1;
                failed_ids := failed_ids || jsonb_build_object('id', movie_id, 'reason', 'Movie not found');
            END IF;
            
        EXCEPTION WHEN OTHERS THEN
            failed_count := failed_count + 1;
            failed_ids := failed_ids || jsonb_build_object('id', movie_id, 'reason', SQLERRM);
        END;
    END LOOP;
    
    -- Construir resultado
    result := jsonb_build_object(
        'success', true,
        'updated_count', updated_count,
        'failed_count', failed_count,
        'failed_ids', failed_ids
    );
    
    RETURN result;
END;
$function$;

-- Comentário explicativo
COMMENT ON FUNCTION public.update_movies_bulk(jsonb) IS 'Função para atualizar filmes em lote a partir de dados JSON. Mapeia automaticamente campos antigos (male_character_prompt, female_character_prompt) para os novos campos (gender_male_prompt, gender_female_prompt).';