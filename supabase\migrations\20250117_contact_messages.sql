/*
  # Contact Messages Table

  1. New Table
    - `contact_messages`
      - `id` (uuid, primary key)
      - `name` (text, nome do remetente)
      - `email` (text, email do remetente)
      - `subject` (text, assunto)
      - `message` (text, mensagem)
      - `type` (text, tipo: bug/suggestion/criticism/other)
      - `status` (text, status: pending/read/replied)
      - `email_sent` (boolean, se o email foi enviado)
      - `created_at` (timestamp)

  2. Security
    - Enable RLS
    - Only service role can access (admin only)
*/

-- Create contact_messages table
CREATE TABLE IF NOT EXISTS public.contact_messages (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  email text NOT NULL,
  subject text NOT NULL,
  message text NOT NULL,
  type text NOT NULL CHECK (type IN ('bug', 'suggestion', 'criticism', 'other')),
  status text NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'read', 'replied')),
  email_sent boolean DEFAULT false,
  user_agent text, -- Para debug
  ip_address text, -- Para debug
  created_at timestamptz DEFAULT now()
);

-- <PERSON>reate indexes for better performance
CREATE INDEX IF NOT EXISTS idx_contact_messages_created_at ON public.contact_messages(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_contact_messages_status ON public.contact_messages(status);
CREATE INDEX IF NOT EXISTS idx_contact_messages_type ON public.contact_messages(type);
CREATE INDEX IF NOT EXISTS idx_contact_messages_email_sent ON public.contact_messages(email_sent);

-- Enable RLS
ALTER TABLE public.contact_messages ENABLE ROW LEVEL SECURITY;

-- RLS Policies - Only service role can access (admin functions)
CREATE POLICY "service_role_contact_messages_all" ON public.contact_messages
  FOR ALL USING (auth.jwt() ->> 'role' = 'service_role'); 