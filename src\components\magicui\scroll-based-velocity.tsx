import React from "react";

interface VelocityScrollProps {
  children: React.ReactNode;
  className?: string;
  defaultVelocity?: number;
  numRows?: number;
}

export function VelocityScroll({
  children,
  className = "",
  defaultVelocity = 5,
  numRows = 2,
}: VelocityScrollProps) {
  return (
    <div className={`relative overflow-hidden ${className}`}>
      {Array.from({ length: numRows }).map((_, rowIndex) => (
        <div
          key={rowIndex}
          className="flex animate-scroll whitespace-nowrap py-4"
          style={{
            animationDirection: rowIndex % 2 === 0 ? 'normal' : 'reverse',
            animationDuration: `${defaultVelocity * 5}s`,
            width: 'max-content',
          }}
        >
          {Array.from({ length: 10 }).map((_, index) => (
            <span key={index} className="text-3xl md:text-5xl font-bold mx-8 tracking-wide">
              {children}
            </span>
          ))}
        </div>
      ))}

      <style>{`
        @keyframes scroll {
          0% {
            transform: translateX(0);
          }
          100% {
            transform: translateX(-100%);
          }
        }
        
        .animate-scroll {
          animation: scroll var(--velocity, 5s) linear infinite;
        }
      `}</style>
    </div>
  );
} 