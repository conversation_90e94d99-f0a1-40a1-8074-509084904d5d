import React from 'react'
import { useTranslation } from 'react-i18next'
import { motion } from 'framer-motion'
import { Wand2, User, Lock } from 'lucide-react'

interface CreativityLevelSelectorProps {
  selectedLevel: 'criativo' | 'rostoFiel' | 'estrito'
  onSelect: (level: 'criativo' | 'rostoFiel' | 'estrito') => void
}

export default function CreativityLevelSelector({ selectedLevel, onSelect }: CreativityLevelSelectorProps) {
  const { t } = useTranslation()

  const levels = [
    {
      id: 'criativo' as const,
      title: t('creativityLevel.moreCreative'),
      description: t('creativityLevel.moreCreativeDesc'),
      icon: Wand2,
      color: 'bg-purple-100 border-purple-400',
      iconColor: 'text-purple-600',
      example: t('creativityLevel.moreCreativeExample')
    },
    {
      id: 'rostoFiel' as const,
      title: t('creativityLevel.faithfulFace'),
      description: t('creativityLevel.faithfulFaceDesc'),
      icon: User,
      color: 'bg-blue-100 border-blue-400',
      iconColor: 'text-blue-600',
      example: t('creativityLevel.faithfulFaceExample')
    },
    {
      id: 'estrito' as const,
      title: t('creativityLevel.strictPreservation'),
      description: t('creativityLevel.strictPreservationDesc'),
      icon: Lock,
      color: 'bg-green-100 border-green-400',
      iconColor: 'text-green-600',
      example: t('creativityLevel.strictPreservationExample')
    }
  ]

  return (
    <motion.div initial={{ opacity: 0, x: 10 }} animate={{ opacity: 1, x: 0 }}>
      <div className="text-center mb-8">
        <div className="w-16 h-16 bg-brand-secondary mx-auto rounded-xl flex items-center justify-center border-2 border-brand-black mb-4">
          <Wand2 className="w-8 h-8 text-brand-black" />
        </div>
        <h3 className="text-3xl font-black text-brand-text mb-2">{t('creativityLevel.title')}</h3>
        <p className="text-brand-text/80">{t('creativityLevel.subtitle')}</p>
      </div>

      <div className="max-w-4xl mx-auto grid grid-cols-1 md:grid-cols-3 gap-6">
        {levels.map((level) => {
          const isSelected = selectedLevel === level.id
          const Icon = level.icon

          return (
            <motion.button
              key={level.id}
              onClick={() => onSelect(level.id)}
              className={`
                p-6 rounded-xl border-2 transition-all duration-200 text-left
                ${isSelected 
                  ? 'border-brand-accent bg-brand-accent/10 shadow-brutal' 
                  : 'border-brand-black bg-brand-white shadow-brutal-sm hover:shadow-brutal'
                }
              `}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <div className={`w-12 h-12 rounded-lg flex items-center justify-center mb-4 ${level.color}`}>
                <Icon className={`w-6 h-6 ${level.iconColor}`} />
              </div>
              
              <h4 className="text-xl font-bold text-brand-text mb-2">{level.title}</h4>
              <p className="text-sm text-brand-text/80 mb-3">{level.description}</p>
              <p className="text-xs text-brand-accent font-semibold">{level.example}</p>
              
              {isSelected && (
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  className="mt-4 w-6 h-6 bg-brand-accent rounded-full flex items-center justify-center"
                >
                  <div className="w-3 h-3 bg-white rounded-full" />
                </motion.div>
              )}
            </motion.button>
          )
        })}
      </div>

      <div className="mt-8 p-4 bg-yellow-50 border-2 border-yellow-300 rounded-lg">
        <h5 className="font-bold text-yellow-800 mb-2">💡 {t('creativityLevel.tip')}</h5>
        <p className="text-sm text-yellow-700">{t('creativityLevel.tipText')}</p>
      </div>
    </motion.div>
  )
} 