import React, { useState, useRef, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { <PERSON>, <PERSON>rk<PERSON>, Zap, ArrowR<PERSON>, RotateCc<PERSON>, Star } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

interface BeforeAfterDemoProps {
  isDark: boolean;
}

const BeforeAfterDemo: React.FC<BeforeAfterDemoProps> = ({ isDark }) => {
  const { t } = useTranslation();
  const [isGenerating, setIsGenerating] = useState(false);
  const [hasGenerated, setHasGenerated] = useState(false);
  const [selectedPlatform, setSelectedPlatform] = useState<'netflix' | 'disney' | 'amazon'>('netflix');
  const [progress, setProgress] = useState(0);
  const [currentStep, setCurrentStep] = useState('');
  const [currentCoverIndex, setCurrentCoverIndex] = useState(0);
  const [usedCovers, setUsedCovers] = useState<{[key: string]: number[]}>({
    netflix: [],
    disney: [],
    amazon: []
  });

  const containerRef = useRef<HTMLDivElement>(null);
  const beforeRef = useRef<HTMLDivElement>(null);
  const afterRef = useRef<HTMLDivElement>(null);

  const platforms = [
    { id: 'netflix' as const, name: 'Netflix', color: '#E50914', bgColor: 'bg-red-600' },
    { id: 'disney' as const, name: 'Disney+', color: '#113CCF', bgColor: 'bg-blue-600' },
    { id: 'amazon' as const, name: 'Prime Video', color: '#00A8E1', bgColor: 'bg-cyan-600' }
  ];

  const originalPhoto = '/casal-demo.jpg';
  
  const generatedCovers = {
    netflix: [
      '/generations/netflix/netflix-1.jpg', 
      '/generations/netflix/netflix-2.jpg',
      '/generations/netflix/netflix-3.jpg',
      '/generations/netflix/netflix-4.jpg',
      '/generations/netflix/netflix-5.jpg',
      '/generations/netflix/netflix-6.jpg'
    ],
    disney: [
      '/generations/disney/disney-1.jpg', 
      '/generations/disney/disney-2.jpg',
      '/generations/disney/disney-3.jpg',
      '/generations/disney/disney-4.jpg',
      '/generations/disney/disney-5.jpg',
      '/generations/disney/disney-6.jpg',
      '/generations/disney/disney-7.jpg'
    ],
    amazon: [
      '/generations/amazon/amazon-1.jpg', 
      '/generations/amazon/amazon-2.jpg',
      '/generations/amazon/amazon-3.jpg',
      '/generations/amazon/amazon-4.jpg',
      '/generations/amazon/amazon-5.jpg',
      '/generations/amazon/amazon-6.jpg',
      '/generations/amazon/amazon-7.jpg'
    ]
  };

  const steps = [
    { text: 'Analisando características faciais...', icon: '👁️', duration: 4000 },
    { text: 'Criando contexto cinematográfico...', icon: '🎬', duration: 5000 },
    { text: 'Aplicando estilo da plataforma...', icon: '🎨', duration: 4000 },
    { text: 'Finalizando composição...', icon: '✨', duration: 3000 }
  ];

  // Reset quando mudar de plataforma
  useEffect(() => {
    setHasGenerated(false);
    setIsGenerating(false);
    setProgress(0);
    setCurrentStep('');
    setCurrentCoverIndex(0);
  }, [selectedPlatform]);

  const getRandomCover = () => {
    const availableCovers = generatedCovers[selectedPlatform];
    const used = usedCovers[selectedPlatform];
    
    // Se usou todas as imagens, resetar
    if (used.length >= availableCovers.length) {
      setUsedCovers(prev => ({
        ...prev,
        [selectedPlatform]: []
      }));
    }
    
    // Pegar índices disponíveis (não usados)
    const availableIndices = availableCovers
      .map((_, index) => index)
      .filter(index => !used.includes(index));
    
    // Se não tem disponíveis, usar qualquer um
    if (availableIndices.length === 0) {
      return Math.floor(Math.random() * availableCovers.length);
    }
    
    // Pegar um aleatório dos disponíveis
    const randomIndex = availableIndices[Math.floor(Math.random() * availableIndices.length)];
    
    // Marcar como usado
    setUsedCovers(prev => ({
      ...prev,
      [selectedPlatform]: [...prev[selectedPlatform], randomIndex]
    }));
    
    return randomIndex;
  };

  const startGeneration = () => {
    if (isGenerating) return;
    
    setIsGenerating(true);
    setHasGenerated(false);
    setProgress(0);
    
    let currentStepIndex = 0;
    let totalElapsed = 0;
    const totalDuration = 16000;
    
    const runStep = () => {
      if (currentStepIndex < steps.length) {
        const step = steps[currentStepIndex];
        setCurrentStep(step.text);
        
        const stepInterval = setInterval(() => {
          totalElapsed += 100;
          const newProgress = Math.min((totalElapsed / totalDuration) * 100, 100);
          setProgress(newProgress);
        }, 100);
        
        setTimeout(() => {
          clearInterval(stepInterval);
          currentStepIndex++;
          if (currentStepIndex < steps.length) {
            runStep();
          } else {
            setProgress(100);
            setCurrentStep('Pôster criado com sucesso!');
            setTimeout(() => {
              setIsGenerating(false);
              setHasGenerated(true);
              // Pegar uma nova imagem aleatória
              const newCoverIndex = getRandomCover();
              setCurrentCoverIndex(newCoverIndex);
            }, 1000);
          }
        }, step.duration);
      }
    };
    
    runStep();
  };

  const resetDemo = () => {
    setProgress(0);
    setIsGenerating(false);
    setHasGenerated(false);
    setCurrentStep('');
  };

  const currentPlatform = platforms.find(p => p.id === selectedPlatform);

  return (
    <div ref={containerRef} className="relative w-full">
      {/* Platform Selector */}
      <div className="flex justify-center mb-12">
        <div className={`inline-flex border-8 p-2 ${
          isDark ? 'border-white bg-black' : 'border-black bg-white'
        } shadow-brutal-lg`}>
          {platforms.map((platform) => (
            <button
              key={platform.id}
              onClick={() => setSelectedPlatform(platform.id)}
              className={`px-8 py-4 font-black text-lg transition-all border-4 transform hover:scale-105 ${
                selectedPlatform === platform.id
                  ? `text-white border-black ${platform.bgColor}`
                  : isDark
                  ? 'bg-transparent text-white border-transparent hover:bg-gray-800'
                  : 'bg-transparent text-black border-transparent hover:bg-gray-100'
              }`}
            >
              {platform.name}
            </button>
          ))}
        </div>
      </div>

      {/* Main Demo Area */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-12 items-center min-h-[700px]">
        
        {/* ANTES - Left Side */}
        <motion.div 
          ref={beforeRef}
          className="relative"
          initial={{ opacity: 0, x: -50 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6 }}
        >
          <div className="text-center mb-6">
            <h3 className={`text-3xl font-black mb-4 ${
              isDark ? 'text-white' : 'text-black'
            }`}>
              ANTES
            </h3>
            <div className={`inline-block px-4 py-2 border-4 ${
              isDark ? 'border-gray-600 bg-gray-800 text-gray-400' : 'border-gray-300 bg-gray-100 text-gray-600'
            }`}>
              Foto Comum
            </div>
          </div>
          
          <div className={`relative mx-auto w-80 h-[480px] border-8 overflow-hidden transform hover:scale-105 transition-all duration-300 ${
            isDark ? 'border-white shadow-brutal-lg' : 'border-black shadow-brutal-lg'
          }`}>
            <img
              src={originalPhoto}
              alt="Foto original"
              className="w-full h-full object-cover"
            />
            
            {/* Badge */}
            <div className={`absolute top-4 left-4 px-3 py-2 border-3 text-sm font-black ${
              isDark ? 'border-white bg-black text-white' : 'border-black bg-white text-black'
            }`}>
              SUA FOTO
            </div>

            {/* Stats - Smaller and positioned better */}
            <div className={`absolute bottom-4 left-4 right-4 ${
              isDark ? 'bg-black/90 border-white' : 'bg-white/90 border-black'
            } border-2 p-2`}>
              <div className="text-xs font-bold text-center">
                <div className={isDark ? 'text-red-400' : 'text-red-600'}>❌ Não profissional</div>
                <div className={isDark ? 'text-red-400' : 'text-red-600'}>❌ Sem identidade visual</div>
              </div>
            </div>
          </div>
        </motion.div>

        {/* PROCESSO - Center */}
        <div className="relative z-10">
          <div className="text-center mb-8">
            <h3 className={`text-2xl font-black mb-4 ${
              isDark ? 'text-white' : 'text-black'
            }`}>
              TRANSFORMAÇÃO IA
            </h3>
          </div>

          {/* Process Visualization */}
          <div className={`relative mx-auto w-96 h-[480px] border-8 overflow-hidden ${
            isDark ? 'border-white bg-gray-900' : 'border-black bg-gray-100'
          } flex flex-col items-center justify-center`}>
            
            <AnimatePresence mode="wait">
              {isGenerating ? (
                <motion.div
                  key="generating"
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.8 }}
                  className="text-center p-6"
                >
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                    className="mb-6"
                  >
                    <Sparkles className={`w-16 h-16 mx-auto ${
                      isDark ? 'text-yellow-400' : 'text-blue-600'
                    }`} />
                  </motion.div>
                  
                  <h4 className={`text-lg font-black mb-4 ${
                    isDark ? 'text-white' : 'text-black'
                  }`}>
                    {currentStep}
                  </h4>
                  
                  {/* Progress Bar */}
                  <div className={`w-64 h-3 mb-4 border-2 overflow-hidden ${
                    isDark ? 'bg-gray-800 border-gray-600' : 'bg-gray-200 border-gray-400'
                  }`}>
                    <motion.div 
                      className={currentPlatform?.bgColor}
                      initial={{ width: 0 }}
                      animate={{ width: `${progress}%` }}
                      transition={{ duration: 0.5 }}
                    />
                  </div>
                  
                  <div className={`text-2xl font-black ${
                    isDark ? 'text-white' : 'text-black'
                  }`}>
                    {Math.round(progress)}%
                  </div>
                </motion.div>
              ) : hasGenerated ? (
                <motion.div
                  key="success"
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  className="text-center p-6"
                >
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ type: "spring", duration: 0.6 }}
                  >
                    <Star className={`w-16 h-16 mx-auto mb-4 ${
                      isDark ? 'text-yellow-400' : 'text-green-600'
                    }`} />
                  </motion.div>
                  
                  <h4 className={`text-xl font-black mb-2 ${
                    isDark ? 'text-white' : 'text-black'
                  }`}>
                    TRANSFORMAÇÃO
                  </h4>
                  <h4 className={`text-xl font-black mb-4 ${
                    isDark ? 'text-white' : 'text-black'
                  }`}>
                    CONCLUÍDA!
                  </h4>
                  
                  <div className={`inline-block px-4 py-2 border-3 ${currentPlatform?.bgColor} text-white border-black font-black`}>
                    {currentPlatform?.name} Ready!
                  </div>
                </motion.div>
              ) : (
                <motion.div
                  key="ready"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="text-center p-6"
                >
                  <Zap className={`w-16 h-16 mx-auto mb-4 ${
                    isDark ? 'text-gray-500' : 'text-gray-400'
                  }`} />
                  
                  <h4 className={`text-lg font-black mb-4 ${
                    isDark ? 'text-gray-400' : 'text-gray-600'
                  }`}>
                    Pronto para transformar
                  </h4>
                  
                  <p className={`text-sm ${
                    isDark ? 'text-gray-500' : 'text-gray-500'
                  }`}>
                    Clique em "TRANSFORMAR" para começar
                  </p>
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* Controls */}
          <div className="flex justify-center space-x-4 mt-6">
            <button
              onClick={startGeneration}
              disabled={isGenerating}
              className={`flex items-center space-x-3 px-8 py-4 border-8 font-black transition-all transform hover:scale-105 ${
                isDark
                  ? 'border-white bg-green-500 text-white hover:bg-green-400'
                  : 'border-black bg-green-500 text-white hover:bg-green-400'
              } disabled:opacity-50 disabled:cursor-not-allowed shadow-brutal-lg`}
            >
              {isGenerating ? (
                <>
                  <Zap className="w-5 h-5 animate-pulse" />
                  <span>GERANDO...</span>
                </>
              ) : (
                <>
                  <Play className="w-5 h-5" />
                  <span>TRANSFORMAR</span>
                </>
              )}
            </button>

            <button
              onClick={resetDemo}
              className={`flex items-center space-x-2 px-6 py-4 border-8 font-black transition-all transform hover:scale-105 ${
                isDark
                  ? 'border-white bg-gray-800 hover:bg-gray-700 text-white'
                  : 'border-black bg-white hover:bg-gray-100 text-black'
              } shadow-brutal-lg`}
            >
              <RotateCcw className="w-5 h-5" />
              <span>RESET</span>
            </button>
          </div>
        </div>

        {/* DEPOIS - Right Side */}
        <motion.div 
          ref={afterRef}
          className="relative"
          initial={{ opacity: 0, x: 50 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <div className="text-center mb-6">
            <h3 className={`text-3xl font-black mb-4 ${
              isDark ? 'text-white' : 'text-black'
            }`}>
              DEPOIS
            </h3>
            <div className={`inline-block px-4 py-2 border-4 ${currentPlatform?.bgColor} text-white border-black font-black`}>
              Pôster {currentPlatform?.name}
            </div>
          </div>
          
          <div className={`relative mx-auto w-80 h-[480px] border-8 overflow-hidden transform hover:scale-105 transition-all duration-300 ${
            isDark ? 'border-white shadow-brutal-lg' : 'border-black shadow-brutal-lg'
          }`}>
            <AnimatePresence mode="wait">
              {hasGenerated ? (
                <motion.img
                  key="generated"
                  initial={{ opacity: 0, scale: 1.1 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.6 }}
                  src={generatedCovers[selectedPlatform][currentCoverIndex]}
                  alt="Pôster gerado"
                  className="w-full h-full object-cover"
                />
              ) : (
                <motion.div
                  key="placeholder"
                  className={`w-full h-full flex items-center justify-center ${
                    isDark ? 'bg-gray-800' : 'bg-gray-200'
                  }`}
                >
                  <div className={`text-center ${
                    isDark ? 'text-gray-500' : 'text-gray-500'
                  }`}>
                    <Sparkles className="w-16 h-16 mx-auto mb-4 opacity-30" />
                    <h4 className="text-xl font-black mb-2">Seu pôster aparecerá aqui</h4>
                    <p className="text-sm opacity-70">Aguardando transformação...</p>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
            
            {/* Platform Badge - Only this badge remains */}
            {hasGenerated && (
              <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.8 }}
                className={`absolute top-4 right-4 px-3 py-2 border-3 text-sm font-black text-white border-black ${currentPlatform?.bgColor}`}
              >
                {currentPlatform?.name}
              </motion.div>
            )}
          </div>
        </motion.div>
      </div>

      {/* Bottom Info */}
      <div className="text-center mt-12">
        <p className={`text-lg font-bold max-w-2xl mx-auto ${
          isDark ? 'text-gray-300' : 'text-gray-700'
        }`}>
          {t('landing.demo.info', 'Demonstração interativa: Veja como nossa IA transforma uma foto comum em um pôster cinematográfico profissional!')}
        </p>
      </div>
    </div>
  );
};

export default BeforeAfterDemo; 