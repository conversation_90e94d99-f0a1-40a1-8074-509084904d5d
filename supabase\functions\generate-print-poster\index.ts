import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // 1. AUTHENTICATION
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      return new Response(JSON.stringify({ error: 'Falta o cabeçalho de autenticação' }), { status: 401, headers: corsHeaders })
    }
    
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL')!,
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!,
    )
    
    const { data: { user } } = await supabase.auth.getUser(authHeader.replace('Bearer ', ''));
    if (!user) {
      return new Response(JSON.stringify({ error: 'Token inválido ou expirado' }), { status: 401, headers: corsHeaders })
    }

    const canvaPayload = await req.json();

    // 2. VALIDATE PAYLOAD
    if (!canvaPayload.ID || !canvaPayload.CAPA || !canvaPayload.AVATAR) {
      return new Response(
        JSON.stringify({ error: 'Payload inválido. ID, CAPA, e AVATAR são obrigatórios.' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }
    
    // 🔥 CHECAR CRÉDITOS ANTES DE GERAR
    const creditsNeeded = 3; // Poster impresso custa 3 créditos
    
    // Verificar saldo de créditos
    const { data: purchases } = await supabase
      .from('credit_purchases')
      .select('credits')
      .eq('user_id', user.id)
      .eq('status', 'completed')

    const { data: usage } = await supabase
      .from('credit_usage')
      .select('credits_used')
      .eq('user_id', user.id)

    const totalCredits = purchases?.reduce((sum, p) => sum + p.credits, 0) || 0
    const usedCredits = usage?.reduce((sum, u) => sum + u.credits_used, 0) || 0
    const availableCredits = totalCredits - usedCredits

    if (availableCredits < creditsNeeded) {
      return new Response(
        JSON.stringify({ 
          error: 'Créditos insuficientes para gerar poster',
          availableCredits,
          requiredCredits: creditsNeeded,
          message: 'Compre mais créditos para continuar'
        }),
        { status: 402, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }
    
    console.log(`💳 Usuário tem ${availableCredits} créditos. Necessário: ${creditsNeeded}`);
    
    // 4. CALL N8N WEBHOOK
    const N8N_WEBHOOK_KEY = Deno.env.get('N8N_WEBHOOK_KEY');
    if (!N8N_WEBHOOK_KEY) {
      throw new Error('N8N_WEBHOOK_KEY não configurado no ambiente.');
    }
      
    const webhookResponse = await fetch('https://automate.felvieira.com.br/webhook/criar-design-canva', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json', 'key': N8N_WEBHOOK_KEY },
      body: JSON.stringify(canvaPayload)
    });

    if (!webhookResponse.ok) {
      const errorText = await webhookResponse.text();
      console.error(`Webhook error: ${webhookResponse.status} - ${errorText}`);
      // TODO: Implementar lógica para estornar os créditos em caso de falha no webhook.
      throw new Error(`Webhook error: ${webhookResponse.status}`);
    }

    const webhookResult = await webhookResponse.json();
    const posterUrl = webhookResult.design_url || webhookResult.url || webhookResult.urls?.[0];
    
    // 5. SAVE TO DATABASE
    if (posterUrl) {
      // 🔥 CONSUMIR CRÉDITOS APENAS APÓS SUCESSO
      console.log(`💳 Consumindo ${creditsNeeded} créditos pelo poster...`)
      
      const consumeResponse = await supabase.functions.invoke('consume-credits', {
        body: { 
          amount: creditsNeeded, 
          description: `Poster impresso - ${canvaPayload.ID}` 
        },
        headers: { Authorization: authHeader }
      });

      if (consumeResponse.error || !consumeResponse.data?.success) {
        console.error('❌ Erro ao consumir créditos após sucesso:', consumeResponse.error)
        // Não falhar a geração por causa disso, apenas logar
      } else {
        console.log(`✅ ${creditsNeeded} créditos consumidos com sucesso para poster`)
      }
      
      await supabase.from('poster_images').insert({
        user_id: user.id,
        poster_url: posterUrl,
        cover_url: canvaPayload.CAPA,
        avatar_url: canvaPayload.AVATAR,
        streaming_platform: canvaPayload.ID === 'EAGqDjHn_M4' ? 'netflix' : 
                            canvaPayload.ID === 'EAGqWLwQR0Q' ? 'disney' : 'amazon',
        photo_type: 'individual',
        user_name: canvaPayload.TITLE?.split(' - ')[1] || 'Usuário',
        template_id: canvaPayload.ID,
        canva_design_url: posterUrl,
        request_payload: canvaPayload
      });
    }

    return new Response(JSON.stringify({ success: true, posterUrl, webhookResult }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('Print poster generation error:', error);
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
}) 