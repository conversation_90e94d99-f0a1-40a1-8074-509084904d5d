import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { supabase } from '../lib/supabase';
import { showToast } from '../utils/toast';
import { 
  Plus, 
  Edit, 
  Trash2, 
  CheckCircle, 
  XCircle, 
  Loader2,
  TrendingUp,
  Save,
  Brain,
  AlertCircle,
  Wand2,
  Film,
  BookCopy,
  Download,
  Upload,
  Database,
  Code,
  Copy
} from 'lucide-react';

interface Movie {
  id: string;
  title: string;
  streaming_platform: string;
  base_prompt: string;
  safe_prompt: string;
  gender_male_prompt: string;
  gender_female_prompt: string;
  couple_prompt: string;
  ideal_prompt?: string;
  is_active: boolean;
  created_at: string;
  default_creativity_level: string | null; 
  movie_generation_stats: {
    successful_attempts: number;
    failed_attempts: number;
  }[];
  prompt_pt?: string;
  prompt_en?: string;
}

interface CreativityLevel {
  id: string;
  name: string;
  description?: string;
  prompt_enhancement: string;
  temperature?: number;
}

interface MovieForm {
  title: string;
  streaming_platform: string;
  base_prompt: string;
  safe_prompt: string;
  gender_male_prompt: string;
  gender_female_prompt: string;
  couple_prompt: string;
  auto_generate: boolean;
  default_creativity_level_id: string | null;
}

interface AIModalState {
  isOpen: boolean;
  instruction: string;
  selectedModel: string;
  isGenerating: boolean;
}

interface BulkEditState {
  movies: Movie[];
  isSaving: boolean;
  jsonEditor: {
    jsonString: string;
    isValid: boolean;
    errorMessage: string;
  };
}

const emptyForm: MovieForm = {
  title: '',
  streaming_platform: 'netflix',
  base_prompt: '',
  safe_prompt: '',
  gender_male_prompt: '',
  gender_female_prompt: '',
  couple_prompt: '',
  auto_generate: true,
  default_creativity_level_id: null,
};

const openAIModels = [
  { id: 'gpt-4o', name: 'GPT-4o (Mais Avançado)', description: 'O modelo mais avançado, multimodal, mais barato e rápido que o GPT-4 Turbo.' },
  { id: 'gpt-4-turbo', name: 'GPT-4 Turbo', description: 'Modelo de alta capacidade, ideal para tarefas complexas.' },
  { id: 'gpt-4o-mini', name: 'GPT-4o Mini (Rápido)', description: 'Versão mais rápida e econômica do GPT-4o, boa para testes.' },
  { id: 'gpt-3.5-turbo', name: 'GPT-3.5 Turbo (Econômico)', description: 'Mais econômico para tarefas simples e rápidas.' }
];

// Sub-component to manage Creativity Levels
const CreativityLevelManager: React.FC<{
  levels: CreativityLevel[];
  onLevelsUpdate: () => void;
}> = ({ levels, onLevelsUpdate }) => {
  const [editingLevel, setEditingLevel] = useState<Partial<CreativityLevel> | null>(null);
  const [isSaving, setIsSaving] = useState(false);

  const handleSave = async () => {
    if (!editingLevel || !editingLevel.name || !editingLevel.prompt_enhancement) {
      showToast.error('Nome e Texto do Prompt são obrigatórios.');
      return;
    }
    setIsSaving(true);
    try {
      const { id, ...dataToSave } = editingLevel;
      let error;
      if (id) {
        ({ error } = await supabase.from('creativity_levels').update(dataToSave).eq('id', id));
      } else {
        ({ error } = await supabase.from('creativity_levels').insert(dataToSave));
      }
      if (error) throw error;
      showToast.success(`Nível de criatividade ${id ? 'atualizado' : 'criado'} com sucesso!`);
      setEditingLevel(null);
      onLevelsUpdate();
    } catch (error) {
      console.error('Error saving creativity level:', error);
      showToast.error('Erro ao salvar nível de criatividade.');
    } finally {
      setIsSaving(false);
    }
  };

  const handleDelete = async (levelId: string) => {
    if (window.confirm('Tem certeza que deseja remover este nível? Filmes que o usam como padrão ficarão sem um.')) {
      try {
        const { error } = await supabase.from('creativity_levels').delete().eq('id', levelId);
        if (error) throw error;
        showToast.success('Nível de criatividade removido.');
        onLevelsUpdate();
      } catch (error) {
        console.error('Error deleting creativity level:', error);
        showToast.error('Erro ao remover. Verifique se não está em uso.');
      }
    }
  };

  const handleEdit = (level: CreativityLevel) => {
    setEditingLevel({
      id: level.id,
      name: level.name,
      description: level.description || '',
      prompt_enhancement: level.prompt_enhancement
    });
  };

  return (
    <div className="p-4 bg-gray-50 rounded-b-lg">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold text-gray-700">Gerenciar Níveis de Criatividade</h3>
        <button onClick={() => setEditingLevel({ name: '', description: '', prompt_enhancement: '' })} className="btn btn-primary btn-sm">
          <Plus size={16} className="mr-2" /> Novo Nível
        </button>
      </div>

      {editingLevel && (
        <div className="p-4 border rounded-lg bg-white mb-4 space-y-4">
          <h4 className="font-semibold">{editingLevel.id ? 'Editando Nível' : 'Novo Nível'}</h4>
          <input
            type="text"
            placeholder="Nome (ex: rostoFiel)"
            value={editingLevel.name || ''}
            onChange={(e) => setEditingLevel(prev => ({ ...prev, name: e.target.value }))}
            className="input input-bordered w-full"
          />
          <textarea
            placeholder="Descrição (para o admin)"
            value={editingLevel.description || ''}
            onChange={(e) => setEditingLevel(prev => ({ ...prev, description: e.target.value }))}
            className="textarea textarea-bordered w-full"
            rows={2}
          />
          <textarea
            placeholder="Texto do Prompt (em inglês, será adicionado antes do prompt principal)"
            value={editingLevel.prompt_enhancement || ''}
            onChange={(e) => setEditingLevel(prev => ({ ...prev, prompt_enhancement: e.target.value }))}
            className="textarea textarea-bordered w-full"
            rows={4}
          />
          <div className="flex justify-end gap-2">
            <button onClick={() => setEditingLevel(null)} className="btn btn-ghost">Cancelar</button>
            <button onClick={handleSave} className="btn btn-success" disabled={isSaving}>
              {isSaving ? <Loader2 className="animate-spin" /> : <Save size={18} />} Salvar
            </button>
          </div>
        </div>
      )}

      <div className="space-y-2">
        {levels.map(level => (
          <div key={level.id} className="p-3 border rounded-lg bg-white flex justify-between items-start">
            <div>
              <p className="font-bold text-gray-800">{level.name}</p>
              <p className="text-sm text-gray-600 italic mt-1">{level.description}</p>
              <p className="text-sm text-gray-500 bg-gray-50 p-2 rounded mt-2 font-mono">{level.prompt_enhancement}</p>
            </div>
            <div className="flex gap-2 flex-shrink-0 ml-4">
              <button onClick={() => handleEdit(level)} className="btn btn-sm btn-outline btn-primary">
                <Edit size={16}/>
              </button>
              <button onClick={() => handleDelete(level.id)} className="btn btn-sm btn-outline btn-error">
                <Trash2 size={16}/>
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};


export default function AdminMovies() {
  const navigate = useNavigate();
  const [movies, setMovies] = useState<Movie[]>([]);
  const [filteredMovies, setFilteredMovies] = useState<Movie[]>([]);
  const [selectedPlatform, setSelectedPlatform] = useState<string>('all');
  const [creativityLevels, setCreativityLevels] = useState<CreativityLevel[]>([]);
  const [loading, setLoading] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingMovie, setEditingMovie] = useState<Movie | null>(null);
  const [formData, setFormData] = useState<MovieForm>(emptyForm);
  const [saving, setSaving] = useState(false);
  const [activeModalTab, setActiveModalTab] = useState<'prompts' | 'creativity'>('prompts');
  const [activeMainTab, setActiveMainTab] = useState<'list' | 'bulk-edit'>('list');
  const [bulkEdit, setBulkEdit] = useState<BulkEditState>({
    movies: [],
    isSaving: false,
    jsonEditor: {
      jsonString: '[]',
      isValid: true,
      errorMessage: ''
    }
  });
  const [aiModal, setAiModal] = useState<AIModalState>({
    isOpen: false,
    instruction: '',
    selectedModel: 'gpt-4o',
    isGenerating: false
  });

  useEffect(() => {
    const checkAuth = async () => {
      try {
        const { data: { session } } = await supabase.auth.getSession();
        if (session?.user) {
          if (session.user.user_metadata?.role !== 'admin') {
            showToast.error('Acesso negado. Apenas administradores podem acessar esta página.');
            navigate('/dashboard');
            return;
          }
          await Promise.all([loadMovies(), loadCreativityLevels()]);
        } else {
          navigate('/');
        }
      } catch (error) {
        console.error('Error checking auth:', error);
        navigate('/');
      }
    };

    checkAuth();
  }, [navigate]);
  
  useEffect(() => {
     console.log('=== INÍCIO FILTRO DE FILMES ===');
     console.log('Total de filmes:', movies.length);
     console.log('Plataforma selecionada:', selectedPlatform);
     
     // Log detalhado de todos os filmes da Disney+ disponíveis
     const disneyMovies = movies.filter(m => m.streaming_platform === 'disney');
     console.log('=== FILMES DISNEY+ DISPONÍVEIS ===');
     disneyMovies.forEach((movie, index) => {
       console.log(`${index + 1}. ${movie.title} | ID: ${movie.id} | Ativo: ${movie.is_active}`);
     });
     
     let filtered = [...movies];
     
     if (selectedPlatform !== 'all') {
       console.log(`Filtrando filmes da plataforma: ${selectedPlatform}`);
       filtered = filtered.filter(movie => {
         const matches = movie.streaming_platform === selectedPlatform;
         console.log(`Filme: ${movie.title} | Plataforma: ${movie.streaming_platform} | Ativo: ${movie.is_active} | Match: ${matches}`);
         return matches;
       });
     } else {
       console.log('Mostrando filmes de todas as plataformas');
     }
     
     console.log('=== RESUMO ===');
     console.log(`Total de filmes após filtro: ${filtered.length}`);
     if (selectedPlatform === 'disney') {
       console.log('=== FILMES DISNEY+ FILTRADOS ===');
       filtered.forEach((movie, index) => {
         console.log(`${index + 1}. ${movie.title} | ID: ${movie.id}`);
       });
     }
     console.log('=== FIM FILTRO DE FILMES ===');
     
     setFilteredMovies(filtered);
   }, [movies, selectedPlatform]);

  const loadMovies = async () => {
    setLoading(true);
    try {
      // Consulta base da tabela unified_movies_series
      let queryStr = `
        id,
        title,
        streaming_platform,
        type,
        base_prompt,
        safe_prompt,
        gender_male_prompt,
        gender_female_prompt,
        couple_prompt,
        ideal_prompt,
        is_active,
        created_at,
        default_creativity_level
      `;
      
      // Aplicar filtragem por plataforma se necessário
      let { data, error } = selectedPlatform !== 'all'
        ? await supabase
            .from('unified_movies_series')
            .select(queryStr)
            .eq('streaming_platform', selectedPlatform)
            .order('title')
        : await supabase
            .from('unified_movies_series')
            .select(queryStr)
            .order('title');
      
      if (error) throw error;
      
      // Adicionar estatísticas padrão (sem consultar movie_generations por enquanto)
      const moviesWithStats = (data || []).map((movie: any) => ({
        ...movie,
        movie_generation_stats: [{ successful_attempts: 0, failed_attempts: 0 }],
        successful_attempts: 0,
        failed_attempts: 0
      }));
      
      // Converter explicitamente para o tipo Movie[] para evitar problemas de tipagem
      const typedMovies = moviesWithStats as unknown as Movie[];
      setMovies(typedMovies);
      setFilteredMovies(typedMovies);
      
      // Preparar o JSON para a edição em massa quando os filmes forem carregados
      // Isso garante que o JSON seja gerado automaticamente quando o usuário acessa a página
      const jsonData = moviesWithStats.map((movie: any) => ({
        id: movie.id,
        title: movie.title,
        streaming_platform: movie.streaming_platform,
        base_prompt: movie.base_prompt || '',
        safe_prompt: movie.safe_prompt || '',
        gender_male_prompt: movie.gender_male_prompt || '',
        gender_female_prompt: movie.gender_female_prompt || '',
        couple_prompt: movie.couple_prompt || '',
        ideal_prompt: movie.ideal_prompt || null
      }));
      
      // Atualizar o estado do bulkEdit com o JSON gerado
      setBulkEdit(prev => ({
        ...prev,
        movies: typedMovies,
        jsonEditor: {
          ...prev.jsonEditor,
          jsonString: JSON.stringify(jsonData, null, 2),
          isValid: true,
          errorMessage: ''
        }
      }));
      
    } catch (error) {
      console.error('Error loading movies:', error);
      showToast.error('Erro ao carregar filmes');
    } finally {
      setLoading(false);
    }
  };

  const loadCreativityLevels = async () => {
    try {
      const { data, error } = await supabase
        .from('creativity_levels')
        .select('*')
        .order('name');
      if (error) throw error;
      setCreativityLevels(data || []);
    } catch (error) {
      console.error('Error loading creativity levels:', error);
    }
  };

  const generateVariantPrompts = (basePrompt: string, movieTitle: string) => {
    if (!basePrompt.trim()) return { male: '', female: '', couple: '', safe: '' };

    const malePrompt = basePrompt
      .replace(/Change the clothes to/g, 'Change the clothes to male')
      .replace(/Transform the person/g, 'Transform the man in the photo')
      .replace(/the person/g, 'the man')
      .replace(/their/g, 'his')
      .replace(/they are/g, 'he is');
 
    const femalePrompt = basePrompt
      .replace(/Change the clothes to/g, 'Change the clothes to female')
      .replace(/Transform the person/g, 'Transform the woman in the photo')
      .replace(/the person/g, 'the woman')
      .replace(/their/g, 'her')
      .replace(/they are/g, 'she is');

    const couplePrompt = basePrompt
      .replace(/Transform the person/g, 'Transform the couple in the photo')
      .replace(/the person/g, 'the couple')
      .replace(/Change the clothes to/g, 'Change their clothes to matching')
      .replace(/while preserving the person's exact facial features/g, 'while preserving their exact facial features');

    const safePrompt = `A person wearing the iconic ${movieTitle} costume, while preserving the person's exact facial features. The background is a simplified, neutral-colored studio backdrop.`;

    return { male: malePrompt, female: femalePrompt, couple: couplePrompt, safe: safePrompt };
  }

  const handleBasePromptChange = (value: string) => {
    setFormData(prev => {
      const newForm = { ...prev, base_prompt: value };
      if (prev.auto_generate) {
        const { male, female, couple, safe } = generateVariantPrompts(value, prev.title);
        newForm.gender_male_prompt = male;
        newForm.gender_female_prompt = female;
        newForm.couple_prompt = couple;
        newForm.safe_prompt = safe;
      }
      return newForm;
    });
  };

  const regenerateWithAI = async () => {
    if (!editingMovie && !formData.title) {
        showToast.error("É necessário um filme ou título para usar a IA.");
        return;
    }
    setAiModal(prev => ({...prev, isGenerating: true}));

    try {
        const movieContext = editingMovie || formData;
        const response = await fetch('/api/regenerate-prompts', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${(await supabase.auth.getSession()).data.session?.access_token}`
            },
            body: JSON.stringify({
                instruction: aiModal.instruction,
                model: aiModal.selectedModel,
                movie: {
                    title: formData.title || movieContext.title,
                    base_prompt: formData.base_prompt,
                    safe_prompt: formData.safe_prompt,
                    gender_male_prompt: formData.gender_male_prompt,
                    gender_female_prompt: formData.gender_female_prompt,
                    couple_prompt: formData.couple_prompt,
                }
            })
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || 'Falha ao regenerar prompts');
        }

        const data = await response.json();
        
        setFormData(prev => ({
            ...prev,
            base_prompt: data.prompts.base_prompt || prev.base_prompt,
            safe_prompt: data.prompts.safe_prompt || prev.safe_prompt,
            gender_male_prompt: data.prompts.gender_male_prompt || prev.gender_male_prompt,
            gender_female_prompt: data.prompts.gender_female_prompt || prev.gender_female_prompt,
            couple_prompt: data.prompts.couple_prompt || prev.couple_prompt,
        }));

        showToast.success("Todos os 5 prompts foram regenerados com sucesso pela IA!");
        setAiModal(prev => ({...prev, isOpen: false, isGenerating: false}));

    } catch (error) {
        console.error('Error regenerating prompts with AI:', error);
        showToast.error((error as Error).message);
        setAiModal(prev => ({...prev, isGenerating: false}));
    }
  };

  const exportPrompts = () => {
    const data = {
      title: formData.title,
      base_prompt: formData.base_prompt,
      safe_prompt: formData.safe_prompt,
      gender_male_prompt: formData.gender_male_prompt,
      gender_female_prompt: formData.gender_female_prompt,
      couple_prompt: formData.couple_prompt,
    };
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${formData.title.replace(/ /g, '_')}_prompts.json`;
    a.click();
    URL.revokeObjectURL(url);
    showToast.success('Prompts exportados!');
  };

  const importPrompts = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const content = e.target?.result as string;
        const data = JSON.parse(content);
        setFormData(prev => ({
          ...prev,
          title: data.title || prev.title,
          base_prompt: data.base_prompt || '',
          safe_prompt: data.safe_prompt || '',
          gender_male_prompt: data.gender_male_prompt || '',
          gender_female_prompt: data.gender_female_prompt || '',
          couple_prompt: data.couple_prompt || '',
        }));
        showToast.success(`Prompts do "${data.title}" importados!`);
      } catch (error) {
        showToast.error('Arquivo de importação inválido.');
        console.error('Import error:', error);
      }
    };
    reader.readAsText(file);
    event.target.value = '';
  };

  const openCreateModal = () => {
    setEditingMovie(null);
    setFormData({
        ...emptyForm,
        default_creativity_level_id: creativityLevels.length > 0 ? creativityLevels[0].id : null
    });
    setActiveModalTab('prompts');
    setIsModalOpen(true);
  };

  const openEditModal = (movie: Movie) => {
    setEditingMovie(movie);
    
    setFormData({
      title: movie.title,
      streaming_platform: movie.streaming_platform,
      base_prompt: movie.base_prompt || '',
      safe_prompt: movie.safe_prompt || '',
      gender_male_prompt: movie.gender_male_prompt || '',
      gender_female_prompt: movie.gender_female_prompt || '',
      couple_prompt: movie.couple_prompt || '',
      auto_generate: true,
      default_creativity_level_id: creativityLevels.find(level => level.name === movie.default_creativity_level)?.id || null
    });
    
    setActiveModalTab('prompts');
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setEditingMovie(null);
    setFormData(emptyForm);
  };

  const handleSave = async () => {
    if (!formData.title) {
      showToast.error('O título do filme é obrigatório.');
      return;
    }
    setSaving(true);
    try {
      const { male, female, couple, safe } = generateVariantPrompts(formData.base_prompt, formData.title);
      
      let dataToSave: any = {
        title: formData.title,
        streaming_platform: formData.streaming_platform,
        is_active: true,
        default_creativity_level: creativityLevels.find(level => level.id === formData.default_creativity_level_id)?.name || null,
        
        base_prompt_override: formData.base_prompt,
        safe_prompt_override: formData.auto_generate ? safe : formData.safe_prompt,
        gender_male_prompt_override: formData.auto_generate ? male : formData.gender_male_prompt,
        gender_female_prompt_override: formData.auto_generate ? female : formData.gender_female_prompt,
        couple_prompt_override: formData.auto_generate ? couple : formData.couple_prompt,
      };
      
      let error;
      if (editingMovie) {
        ({ error } = await supabase.from('unified_movies_series').update(dataToSave).eq('id', editingMovie.id));
      } else {
        ({ error } = await supabase.from('unified_movies_series').insert(dataToSave).select().single());
      }

      if (error) throw error;
      showToast.success(`Filme ${editingMovie ? 'atualizado' : 'criado'} com sucesso!`);
      closeModal();
      loadMovies();
    } catch (error) {
      console.error('Error saving movie:', error);
      showToast.error('Erro ao salvar o filme.');
    } finally {
      setSaving(false);
    }
  };

  const handleDelete = async (movie: Movie) => {
    if (window.confirm(`Tem certeza que deseja apagar o filme "${movie.title}"? Esta ação não pode ser desfeita.`)) {
      try {
        const { error } = await supabase.from('unified_movies_series').delete().eq('id', movie.id);
        if (error) throw error;
        showToast.success('Filme apagado com sucesso.');
        loadMovies();
      } catch (error) {
        console.error('Error deleting movie:', error);
        showToast.error('Erro ao apagar o filme.');
      }
    }
  };

  const getPlatformColor = (platform: string) => {
    switch (platform) {
      case 'netflix': return 'bg-red-600';
      case 'disney': return 'bg-blue-900';
      case 'amazon': return 'bg-blue-500';
      default: return 'bg-gray-500';
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-screen bg-gray-100 text-gray-800">
        <Loader2 className="animate-spin mr-4" size={48} />
        <span className="text-xl">Carregando Filmes...</span>
      </div>
    );
  }

  // Logs para depuração
  console.log('=== DEBUG - ADMIN MOVIES ===');
  console.log('Total de filmes carregados:', movies.length);
  console.log('Total de filmes filtrados:', filteredMovies.length);
  console.log('Plataforma selecionada:', selectedPlatform);
  console.log('Filmes ativos:', movies.filter(m => m.is_active).length);
  console.log('Filmes inativos:', movies.filter(m => !m.is_active).length);
  console.log('Filmes por plataforma:', {
    all: movies.length,
    netflix: movies.filter(m => m.streaming_platform === 'netflix').length,
    disney: movies.filter(m => m.streaming_platform === 'disney').length,
    amazon: movies.filter(m => m.streaming_platform === 'amazon').length
  });

  const handleJsonChange = (jsonString: string) => {
    setBulkEdit(prev => ({
      ...prev,
      jsonEditor: {
        ...prev.jsonEditor,
        jsonString,
        isValid: true,
        errorMessage: ''
      }
    }));
    
    // Validar o JSON enquanto o usuário digita
    try {
      JSON.parse(jsonString);
    } catch (error) {
      setBulkEdit(prev => ({
        ...prev,
        jsonEditor: {
          ...prev.jsonEditor,
          isValid: false,
          errorMessage: (error as Error).message
        }
      }));
    }
  };
  
  const copyToClipboard = () => {
    navigator.clipboard.writeText(bulkEdit.jsonEditor.jsonString);
    showToast.success('JSON copiado para a área de transferência!');
  };

  const handleBulkSave = async () => {
    if (!bulkEdit.jsonEditor.isValid) {
      showToast.error('O JSON contém erros. Corrija antes de salvar.');
      return;
    }
    
    setBulkEdit(prev => ({ ...prev, isSaving: true }));
    showToast.info('Salvando todos os prompts...');

    try {
      // Converter o JSON string para objeto
      const moviesToUpdate = JSON.parse(bulkEdit.jsonEditor.jsonString);
      
      // Validar que temos um array com objetos que contêm id
      if (!Array.isArray(moviesToUpdate)) {
        throw new Error('O JSON deve ser um array de objetos.');
      }
      
      // Validar cada item no array
      moviesToUpdate.forEach((movie, index) => {
        if (!movie.id) {
          throw new Error(`Item ${index + 1}: ID é obrigatório.`);
        }
      });

      // Chamar a função do Supabase para atualização em massa
      const { error } = await supabase.functions.invoke('update-bulk-prompts', {
        body: { movies: moviesToUpdate }
      });

      if (error) {
        throw error;
      }

      showToast.success('Todos os prompts foram salvos com sucesso!');
      loadMovies(); // Recarregar para confirmar as alterações
    } catch (error) {
      console.error('Erro ao salvar prompts em massa:', error);
      showToast.error(`Falha ao salvar os prompts: ${(error as Error).message}`);
    } finally {
      setBulkEdit(prev => ({ ...prev, isSaving: false }));
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Gerenciar Filmes</h1>
        <div className="flex gap-2">
          <button onClick={openCreateModal} className="btn btn-primary">
            <Plus size={20} className="mr-2" /> Novo Filme
          </button>
          <button onClick={() => navigate('/admin/stats')} className="btn btn-secondary">
            <TrendingUp size={20} className="mr-2" /> Estatísticas
          </button>
        </div>
      </div>

      <div role="tablist" className="tabs tabs-boxed mb-6">
        <a 
          role="tab" 
          className={`tab ${activeMainTab === 'list' ? 'tab-active' : ''}`} 
          onClick={() => setActiveMainTab('list')}
        >
          <Film size={16} className="mr-2"/> Listagem de Filmes
        </a>
        <a 
          role="tab" 
          className={`tab ${activeMainTab === 'bulk-edit' ? 'tab-active' : ''}`} 
          onClick={() => setActiveMainTab('bulk-edit')}
        >
          <Database size={16} className="mr-2"/> Edição em Massa de Prompts
        </a>
      </div>

      {activeMainTab === 'list' && (
        <div className="mb-6 flex flex-wrap gap-2">
          <button onClick={() => setSelectedPlatform('all')} className={`btn ${selectedPlatform === 'all' ? 'btn-active' : ''}`}>Todos</button>
          <button onClick={() => setSelectedPlatform('netflix')} className={`btn ${selectedPlatform === 'netflix' ? 'btn-active' : ''}`}>Netflix</button>
          <button onClick={() => setSelectedPlatform('disney')} className={`btn ${selectedPlatform === 'disney' ? 'btn-active' : ''}`}>Disney+</button>
          <button onClick={() => setSelectedPlatform('amazon')} className={`btn ${selectedPlatform === 'amazon' ? 'btn-active' : ''}`}>Amazon Prime</button>
        </div>
      )}

      {activeMainTab === 'list' ? (
        <div className="mb-4">
          <div className="flex justify-between items-center mb-4">
            <p className="text-sm text-gray-500">Mostrando {filteredMovies.length} filmes</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {filteredMovies.map((movie) => (
            <div key={movie.id} className="card bg-base-100 shadow-xl">
              <div className="card-body p-4">
                <div className="flex justify-between items-start">
                  <div>
                    <h2 className="card-title text-lg">{movie.title}</h2>
                    <span className={`badge ${getPlatformColor(movie.streaming_platform)} text-white`}>
                      {movie.streaming_platform}
                    </span>
                  </div>
                  <div className="card-actions justify-end">
                    <button 
                      onClick={() => openEditModal(movie)}
                      className="btn btn-sm btn-circle btn-outline"
                    >
                      <Edit size={16} />
                    </button>
                    <button 
                      onClick={() => handleDelete(movie)} 
                      className="btn btn-sm btn-circle btn-outline btn-error"
                    >
                      <Trash2 size={16} />
                    </button>
                  </div>
                </div>
                
                <div className="mt-4 text-xs text-gray-500">
                  <p className="truncate" title={movie.base_prompt}>
                    <strong>Prompt:</strong> {movie.base_prompt || 'Não definido'}
                  </p>
                </div>
                
                <div className="mt-4 flex justify-between items-center text-sm border-t pt-3">
                  <div className="flex items-center gap-2 text-green-600">
                    <CheckCircle size={16} />
                    <span>{movie.movie_generation_stats?.[0]?.successful_attempts || 0}</span>
                  </div>
                  <div className="flex items-center gap-2 text-red-600">
                    <XCircle size={16} />
                    <span>{movie.movie_generation_stats?.[0]?.failed_attempts || 0}</span>
                  </div>
                  <span className={`badge ${movie.is_active ? 'badge-success' : 'badge-error'} badge-outline`}>
                    {movie.is_active ? 'Ativo' : 'Inativo'}
                  </span>
                </div>
              </div>
            </div>
          ))}
          </div>
        </div>
      ) : (
        <div className="mb-4">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-bold flex items-center">
              <Code className="mr-2" size={24} />
              Edição em Massa de Prompts (JSON)
            </h2>
            <div className="flex gap-2">
              <button 
                onClick={copyToClipboard} 
                className="btn btn-outline"
                title="Copiar JSON"
              >
                <Copy className="mr-2" size={16} />
                Copiar
              </button>
              <button 
                onClick={handleBulkSave} 
                className="btn btn-primary" 
                disabled={bulkEdit.isSaving || !bulkEdit.jsonEditor.isValid}
              >
                {bulkEdit.isSaving ? (
                  <>
                    <Loader2 className="mr-2 animate-spin" size={16} />
                    Salvando...
                  </>
                ) : (
                  <>
                    <Save className="mr-2" size={16} />
                    Salvar JSON
                  </>
                )}
              </button>
            </div>
          </div>

          <div className="mb-4 bg-gray-800 text-white p-3 rounded-md text-xs">
            <p>Edite o JSON abaixo para alterar TODOS os prompts de todos os filmes de uma vez.</p>
            <p>Campos disponíveis por filme:</p>
            <ul className="list-disc pl-4 mt-2 space-y-1">
              <li><code>id</code>: ID do filme (obrigatório)</li>
              <li><code>title</code>: Título do filme</li>
              <li><code>streaming_platform</code>: Plataforma (netflix, disney, amazon)</li>
              <li><code>base_prompt</code>: Prompt principal</li>
              <li><code>safe_prompt</code>: Versão segura do prompt</li>
              <li><code>gender_male_prompt</code>: Prompt masculino</li>
              <li><code>gender_female_prompt</code>: Prompt feminino</li>
              <li><code>couple_prompt</code>: Prompt para casal</li>
              <li><code>prompt_pt</code>: Prompt em português</li>
              <li><code>prompt_en</code>: Prompt em inglês</li>
            </ul>
          </div>
          
          {!bulkEdit.jsonEditor.isValid && (
            <div className="mb-4 bg-red-100 border-l-4 border-red-500 text-red-700 p-4 rounded">
              <p className="font-bold">Erro de JSON:</p>
              <p>{bulkEdit.jsonEditor.errorMessage}</p>
            </div>
          )}
          
          <div className="form-control w-full mb-4">
            <textarea 
              value={bulkEdit.jsonEditor.jsonString} 
              onChange={(e) => handleJsonChange(e.target.value)}
              className={`textarea font-mono text-sm h-[70vh] bg-gray-900 text-gray-100 p-4 ${!bulkEdit.jsonEditor.isValid ? 'border-red-500' : 'border-gray-700'}`}
              placeholder={`[
  {
    "id": "uuid-do-filme",
    "title": "Nome do Filme",
    "streaming_platform": "netflix",
    "base_prompt": "Transform the person in the photo...",
    "safe_prompt": "Safe version of the prompt...",
    "gender_male_prompt": "Male specific version...",
    "gender_female_prompt": "Female specific version...",
    "couple_prompt": "Couple specific version...",
    "prompt_pt": "Prompt em português",
    "prompt_en": "Prompt in English"
  }
]`}
              spellCheck="false"
            ></textarea>
          </div>
        </div>
      )}

      {isModalOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 overflow-y-auto">
          <div className="bg-white rounded-lg shadow-xl w-11/12 max-w-4xl max-h-[90vh] overflow-y-auto p-6 relative">
            <button onClick={closeModal} className="btn btn-sm btn-circle absolute right-2 top-2 z-20">✕</button>
            <h3 className="font-bold text-xl mb-4">{editingMovie ? 'Editar Filme' : 'Criar Novo Filme'}</h3>
            
            <div role="tablist" className="tabs tabs-boxed mb-4">
              <a role="tab" className={`tab ${activeModalTab === 'prompts' ? 'tab-active' : ''}`} onClick={() => setActiveModalTab('prompts')}>
                <Film size={16} className="mr-2"/> Prompts do Filme
              </a>
              <a role="tab" className={`tab ${activeModalTab === 'creativity' ? 'tab-active' : ''}`} onClick={() => setActiveModalTab('creativity')}>
                 <BookCopy size={16} className="mr-2"/> Níveis de Criatividade
              </a>
            </div>

            {activeModalTab === 'prompts' && (
              <>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="form-control">
                    <label className="label"><span className="label-text">Título *</span></label>
                    <input type="text" value={formData.title} onChange={(e) => setFormData({...formData, title: e.target.value})} className="input input-bordered w-full" />
                  </div>
                  <div className="form-control">
                    <label className="label"><span className="label-text">Plataforma *</span></label>
                    <select value={formData.streaming_platform} onChange={(e) => setFormData({...formData, streaming_platform: e.target.value})} className="select select-bordered w-full">
                      <option value="netflix">Netflix</option>
                      <option value="disney">Disney+</option>
                      <option value="amazon">Amazon Prime</option>
                    </select>
                  </div>
                </div>

                <div className="form-control mt-4">
                  <label className="label"><span className="label-text">Nível de Criatividade Padrão</span></label>
                   <select 
                      value={formData.default_creativity_level_id || ''} 
                      onChange={(e) => setFormData({...formData, default_creativity_level_id: e.target.value})} 
                      className="select select-bordered w-full"
                      disabled={creativityLevels.length === 0}
                    >
                      <option value="" disabled>Selecione um nível...</option>
                      {creativityLevels.map(level => (
                        <option key={level.id} value={level.id}>{level.name}</option>
                      ))}
                  </select>
                </div>

                <div className="mt-6">
                    <div className="flex justify-end gap-2 mb-2">
                        <button onClick={() => setAiModal({ isOpen: true, instruction: '', selectedModel: 'gpt-4o', isGenerating: false })} className="btn btn-sm btn-primary">
                          <Brain size={16} className="mr-1" /> Refazer com IA
                        </button>
                        <button onClick={exportPrompts} className="btn btn-sm btn-success">
                          <Download size={16} className="mr-1" /> Exportar
                        </button>
                        <label className="btn btn-sm btn-info">
                          <Upload size={16} className="mr-1" /> Importar
                          <input type="file" accept=".json" onChange={importPrompts} className="hidden" />
                        </label>
                    </div>

                    <div className="form-control">
                      <label className="label flex justify-between">
                        <span className="label-text">Prompt Principal *</span>
                      </label>
                      <textarea
                        value={formData.base_prompt}
                        onChange={(e) => handleBasePromptChange(e.target.value)}
                        className="textarea textarea-bordered w-full h-32"
                        placeholder="Descreva o pôster aqui..."
                      ></textarea>
                      
                      <div className="form-control mt-2">
                          <label className="label cursor-pointer justify-start gap-2">
                              <input 
                                  type="checkbox" 
                                  checked={formData.auto_generate} 
                                  onChange={(e) => setFormData(prev => ({...prev, auto_generate: e.target.checked}))}
                                  className="checkbox checkbox-sm" 
                              />
                              <span className="label-text">Gerar prompts específicos automaticamente a partir do Principal?</span>
                          </label>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                      <div className="form-control">
                          <label className="label"><span className="label-text">Safe Prompt</span></label>
                          <textarea 
                            value={formData.safe_prompt} 
                            onChange={(e) => setFormData({...formData, safe_prompt: e.target.value})} 
                            className="textarea textarea-bordered w-full h-24"
                            placeholder="Versão mais segura do prompt principal"
                            disabled={formData.auto_generate}
                          ></textarea>
                      </div>
                      <div className="form-control">
                          <label className="label"><span className="label-text">Prompt Masculino</span></label>
                          <textarea 
                            value={formData.gender_male_prompt} 
                            onChange={(e) => setFormData({...formData, gender_male_prompt: e.target.value})} 
                            className="textarea textarea-bordered w-full h-24"
                            placeholder="Versão específica para personagens masculinos"
                            disabled={formData.auto_generate}
                          ></textarea>
                      </div>
                      <div className="form-control">
                          <label className="label"><span className="label-text">Prompt Feminino</span></label>
                          <textarea 
                            value={formData.gender_female_prompt} 
                            onChange={(e) => setFormData({...formData, gender_female_prompt: e.target.value})} 
                            className="textarea textarea-bordered w-full h-24"
                            placeholder="Versão específica para personagens femininos"
                            disabled={formData.auto_generate}
                          ></textarea>
                      </div>
                      <div className="form-control">
                          <label className="label"><span className="label-text">Prompt para Casal</span></label>
                          <textarea 
                            value={formData.couple_prompt} 
                            onChange={(e) => setFormData({...formData, couple_prompt: e.target.value})} 
                            className="textarea textarea-bordered w-full h-24"
                            placeholder="Versão específica para casais/múltiplas pessoas"
                            disabled={formData.auto_generate}
                          ></textarea>
                      </div>
                    </div>
                    
                    <div className="alert alert-info mt-4">
                      <AlertCircle size={24} />
                      <div>
                        <h3 className="font-bold">Como funciona:</h3>
                        <ul className="list-disc list-inside text-xs">
                          <li>O sistema salva e lê os 5 prompts diretamente.</li>
                          <li>O sistema SEMPRE anexa o "Nível de Criatividade" antes do prompt final.</li>
                          <li>O sistema usa o prompt específico conforme o tipo de foto (base/safe/masculino/etc).</li>
                          <li><strong>Auto-gerar:</strong> Se marcado, os prompts específicos são preenchidos (e desabilitados) automaticamente. Desmarque para editar cada um individualmente.</li>
                        </ul>
                      </div>
                    </div>
                </div>
                <div className="modal-action mt-6">
                    <button onClick={closeModal} className="btn btn-ghost" disabled={saving}>Cancelar</button>
                    <button onClick={handleSave} className="btn btn-primary" disabled={saving}>
                        {saving ? <Loader2 className="animate-spin" /> : <Save />}
                        {saving ? 'Salvando...' : (editingMovie ? 'Salvar Alterações' : 'Criar Filme')}
                    </button>
                </div>
              </>
            )}

            {activeModalTab === 'creativity' && (
              <CreativityLevelManager levels={creativityLevels} onLevelsUpdate={loadCreativityLevels} />
            )}

          </div>
        </div>
      )}

      {aiModal.isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 overflow-y-auto">
          <div className="bg-white rounded-lg shadow-xl w-11/12 max-w-lg p-6 relative">
            <h3 className="font-bold text-lg mb-3">🧠 Refazer Todos os Prompts com IA</h3>
            
            <div className="bg-blue-50 border-l-4 border-blue-400 p-3 mb-4">
              <p className="text-sm font-semibold text-blue-800">A IA irá reescrever todos os 5 tipos de prompts:</p>
              <ul className="text-xs text-blue-700 mt-1 list-disc list-inside">
                <li>Prompt Principal (base)</li>
                <li>Safe Prompt (versão segura)</li>
                <li>Prompt Masculino</li>
                <li>Prompt Feminino</li>
                <li>Prompt para Casal</li>
              </ul>
            </div>
            
            <div className="form-control">
              <label className="label"><span className="label-text font-semibold">Instrução para a IA *</span></label>
              <textarea 
                className="textarea textarea-bordered h-28" 
                placeholder="Exemplos:&#10;• Transforme em um poster de terror com elementos sombrios&#10;• Mude o estilo para algo mais gótico e misterioso&#10;• Adicione elementos de ficção científica&#10;• Torne mais colorido e vibrante..."
                value={aiModal.instruction}
                onChange={(e) => setAiModal(prev => ({...prev, instruction: e.target.value}))}
              ></textarea>
            </div>

            <div className="form-control mt-4">
                <label className="label"><span className="label-text font-semibold">Modelo de IA</span></label>
                <select 
                    className="select select-bordered w-full"
                    value={aiModal.selectedModel}
                    onChange={(e) => setAiModal(prev => ({...prev, selectedModel: e.target.value}))}
                >
                    {openAIModels.map(model => (
                        <option key={model.id} value={model.id} title={model.description}>{model.name}</option>
                    ))}
                </select>
                <label className="label">
                  <span className="label-text-alt text-gray-500">
                    {openAIModels.find(m => m.id === aiModal.selectedModel)?.description}
                  </span>
                </label>
            </div>

            <div className="modal-action mt-6">
              <button onClick={() => setAiModal(prev => ({...prev, isOpen: false}))} className="btn btn-ghost" disabled={aiModal.isGenerating}>Cancelar</button>
              <button onClick={regenerateWithAI} className="btn btn-primary" disabled={!aiModal.instruction.trim() || aiModal.isGenerating}>
                {aiModal.isGenerating ? <Loader2 className="animate-spin mr-2" size={16}/> : <Wand2 className="mr-2" size={16}/>}
                {aiModal.isGenerating ? "Regenerando..." : "Regenerar Todos"}
              </button>
            </div>
          </div>
        </div>
      )}

    </div>
  );
} 