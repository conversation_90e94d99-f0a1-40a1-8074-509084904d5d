import React, { useState, useEffect } from 'react';
import { supabase } from '../lib/supabase';
import { CanvaGenerationRecord } from '../types/canva';
import { Loader2, RefreshCw, ExternalLink } from 'lucide-react';
import { useTranslation } from 'react-i18next';

const CanvaPosterHistory: React.FC = () => {
  const { t } = useTranslation();
  const [generations, setGenerations] = useState<CanvaGenerationRecord[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchGenerations = async () => {
    try {
      setLoading(true);
      setError(null);

      const { data, error } = await supabase
        .from('canva_generations')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) {
        throw error;
      }

      setGenerations(data as CanvaGenerationRecord[]);
    } catch (err: any) {
      setError(`${t('canvaHistory.errorLoading')}: ${err.message}`);
      console.error('Error fetching generation history:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchGenerations();
  }, []);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString(i18n.language, {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'processing':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            <div className="animate-spin h-3 w-3 border-2 border-b-0 border-blue-800 rounded-full mr-1"></div>
            {t('admin.processing')}
          </span>
        );
      case 'completed':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
            ✓ {t('admin.completed')}
          </span>
        );
      case 'failed':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
            ✗ {t('admin.failed')}
          </span>
        );
      default:
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
            {status}
          </span>
        );
    }
  };

  const { i18n } = useTranslation();

  return (
    <div className="container mx-auto py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-gray-800">{t('canvaHistory.title')}</h1>
        <button 
          onClick={fetchGenerations}
          className="flex items-center space-x-2 px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
        >
          <RefreshCw className="h-4 w-4" />
          <span>{t('canvaHistory.update')}</span>
        </button>
      </div>

      {loading && (
        <div className="flex justify-center items-center py-10">
          <Loader2 className="h-8 w-8 text-blue-500 animate-spin mr-2" />
          <span className="text-lg text-gray-600">{t('canvaHistory.loading')}</span>
        </div>
      )}

      {error && (
        <div className="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6" role="alert">
          <p>{error}</p>
        </div>
      )}

      {!loading && generations.length === 0 && !error && (
        <div className="bg-gray-100 rounded-lg p-8 text-center">
          <p className="text-lg text-gray-600">{t('canvaHistory.noPosters')}</p>
          <p className="mt-2">{t('canvaHistory.goToGenerate')}</p>
        </div>
      )}

      {generations.length > 0 && (
        <div className="bg-white shadow overflow-hidden rounded-lg">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t('admin.date')}
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t('admin.title')}
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t('admin.status')}
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t('admin.actions')}
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {generations.map((generation) => (
                <tr key={generation.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {formatDate(generation.created_at)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">
                      {generation.request_data.TITLE || t('admin.noTitle')}
                    </div>
                    <div className="text-sm text-gray-500">
                      {generation.request_data.SUBTITLE || ''}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {getStatusBadge(generation.status)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    {generation.design_url && (
                      <a 
                        href={generation.design_url} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="flex items-center text-blue-600 hover:text-blue-900"
                      >
                        <ExternalLink className="w-4 h-4 mr-1" /> 
                        {t('canvaHistory.openInCanva')}
                      </a>
                    )}
                    {generation.status === 'failed' && generation.error_message && (
                      <div className="text-xs text-red-600 mt-1">
                        {t('admin.error')}: {generation.error_message}
                      </div>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};

export default CanvaPosterHistory;
