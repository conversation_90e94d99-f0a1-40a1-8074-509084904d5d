import React from 'react'
import { useTranslation } from 'react-i18next'
import { Globe } from 'lucide-react'
import { motion } from 'framer-motion'
import { Language } from '../types'

interface LanguageSelectorProps {
  selectedLanguage: Language | null
  onSelect: (language: Language) => void
}

export default function LanguageSelector({ selectedLanguage, onSelect }: LanguageSelectorProps) {
  const { t } = useTranslation()

  const languages = [
    {
      id: 'english' as Language,
      name: t('languageSelector.english'),
      code: 'US',
      flag: '🇺🇸',
      description: t('languageSelector.englishDesc')
    },
    {
      id: 'portuguese' as Language,
      name: t('languageSelector.portuguese'),
      code: 'BR',
      flag: '🇧🇷',
      description: t('languageSelector.portugueseDesc')
    }
  ]

  return (
    <div className="w-full max-w-2xl mx-auto">
      <div className="text-center mb-10">
        <div className="w-20 h-20 bg-brand-white rounded-xl flex items-center justify-center mx-auto mb-6 border-2 border-brand-black shadow-brutal">
          <Globe className="w-10 h-10 text-brand-black" />
        </div>
        <h3 className="text-3xl font-black text-brand-text mb-2">{t('languageSelector.title')}</h3>
        <p className="text-lg text-brand-text/80">{t('languageSelector.subtitle')}</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {languages.map((language, index) => {
          const isSelected = selectedLanguage === language.id
          
          return (
            <button
              key={language.id}
              onClick={() => onSelect(language.id)}
              className={`
                p-8 rounded-xl transition-all duration-200 text-center border-2 border-brand-black
                ${isSelected 
                  ? 'bg-brand-primary shadow-brutal-pressed' 
                  : 'bg-brand-white shadow-brutal hover:shadow-brutal-hover active:shadow-brutal-pressed'
                }
              `}
            >
              <div className="w-20 h-20 rounded-lg flex items-center justify-center mx-auto mb-5 text-5xl border-2 border-brand-black bg-white">
                {language.flag}
              </div>
              
              <div className="flex items-center justify-center space-x-2 mb-2">
                <h4 className={`font-bold text-xl ${isSelected ? 'text-brand-black' : 'text-brand-text'}`}>
                  {language.name}
                </h4>
                <span className={`
                  px-2 py-1 rounded-md text-xs font-bold border-2 border-brand-black
                  ${isSelected 
                    ? 'bg-brand-secondary text-brand-black' 
                    : 'bg-gray-200 text-brand-text'
                  }
                `}>
                  {language.code}
                </span>
              </div>
              
              <p className={`text-sm ${isSelected ? 'text-brand-black/80' : 'text-brand-text/70'}`}>
                {language.description}
              </p>
            </button>
          )
        })}
      </div>
    </div>
  )
}