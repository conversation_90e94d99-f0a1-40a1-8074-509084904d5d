#!/usr/bin/env node

/**
 * 🔧 Setup Automático do OAuth Canva
 * 
 * Este script faz o OAuth dance uma única vez e configura
 * todos os tokens necessários automaticamente.
 * 
 * De<PERSON><PERSON> disso, a Edge Function funciona 100% automaticamente.
 */

import { randomBytes, createHash } from 'crypto';
import { exec } from 'child_process';
import open from 'open';
import readline from 'readline';

// Configuração
const CONFIG = {
  clientId: 'OC-AZdfZOcE7mgD',
  clientSecret: 'cnvca9UbcRNeju-iX-YioJQNkDTYTxpbzcfrpZ_AGkA_vYEc50913078',
  redirectUri: 'https://automate.felvieira.com.br/rest/oauth2-credential/callback', // Usar o mesmo do N8N
  scope: 'app:read app:write asset:read asset:write brandtemplate:content:read brandtemplate:meta:read comment:read comment:write design:content:read design:content:write design:meta:read design:permission:read design:permission:write folder:read folder:write folder:permission:read folder:permission:write profile:read'
};

// Gerar code_verifier e code_challenge para PKCE
function generatePKCE() {
  const codeVerifier = randomBytes(96).toString('base64url');
  const codeChallenge = createHash('sha256')
    .update(codeVerifier)
    .digest('base64url');
  
  return { codeVerifier, codeChallenge };
}

// Configurar tokens no Supabase
function configureSupabaseSecrets(accessToken, refreshToken) {
  console.log('🔧 Configurando secrets no Supabase...');
  
  const commands = [
    `supabase secrets set CANVA_ACCESS_TOKEN="${accessToken}"`,
    `supabase secrets set CANVA_REFRESH_TOKEN="${refreshToken}"`,
    `supabase secrets set CANVA_CLIENT_ID="${CONFIG.clientId}"`,
    `supabase secrets set CANVA_CLIENT_SECRET="${CONFIG.clientSecret}"`
  ];

  commands.forEach((cmd, index) => {
    exec(cmd, (error, stdout, stderr) => {
      if (error) {
        console.error(`❌ Erro ao configurar secret ${index + 1}:`, error);
      } else {
        console.log(`✅ Secret ${index + 1}/4 configurado`);
      }
    });
  });
}

// Trocar authorization code por tokens
async function exchangeCodeForTokens(code, codeVerifier) {
  console.log('🔄 Trocando authorization code por tokens...');
  
  const credentials = Buffer.from(`${CONFIG.clientId}:${CONFIG.clientSecret}`).toString('base64');
  
  const response = await fetch('https://api.canva.com/rest/v1/oauth/token', {
    method: 'POST',
    headers: {
      'Authorization': `Basic ${credentials}`,
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    body: new URLSearchParams({
      grant_type: 'authorization_code',
      code: code,
      code_verifier: codeVerifier,
      redirect_uri: CONFIG.redirectUri
    })
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`Erro ao trocar code por tokens: ${errorText}`);
  }

  const tokens = await response.json();
  console.log('✅ Tokens obtidos com sucesso!');
  console.log(`⏰ Access token expira em: ${tokens.expires_in} segundos (${Math.round(tokens.expires_in/3600)}h)`);
  
  return tokens;
}

// Capturar authorization code via input manual
function getAuthorizationCodeFromUser() {
  return new Promise((resolve) => {
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });

    console.log('\n📋 INSTRUÇÕES:');
    console.log('1. 🌐 Seu navegador vai abrir automaticamente');
    console.log('2. 🔐 Faça login no Canva se necessário');
    console.log('3. ✅ Clique "Authorize" para autorizar a aplicação');
    console.log('4. 📄 Você será redirecionado para uma página de erro (normal!)');
    console.log('5. 📋 COPIE o valor do parâmetro "code" da URL');
    console.log('\n💡 Exemplo da URL de erro:');
    console.log('https://automate.felvieira.com.br/rest/oauth2-credential/callback?code=ABC123...');
    console.log('                                                                     ^^^^^^^^');
    console.log('                                                                (copie essa parte)');
    
    rl.question('\n🔑 Cole o authorization code aqui: ', (code) => {
      rl.close();
      resolve(code.trim());
    });
  });
}

// Função principal
async function main() {
  console.log('🚀 Iniciando setup automático do OAuth Canva...\n');

  try {
    // 1. Gerar PKCE
    const { codeVerifier, codeChallenge } = generatePKCE();
    console.log('🔐 PKCE gerado');

    // 2. Criar URL de autorização
    const authUrl = new URL('https://www.canva.com/api/oauth/authorize');
    authUrl.searchParams.set('code_challenge', codeChallenge);
    authUrl.searchParams.set('code_challenge_method', 'S256');
    authUrl.searchParams.set('scope', CONFIG.scope);
    authUrl.searchParams.set('response_type', 'code');
    authUrl.searchParams.set('client_id', CONFIG.clientId);
    authUrl.searchParams.set('redirect_uri', CONFIG.redirectUri);

    console.log('🔗 URL de autorização criada');

    // 3. Abrir navegador
    console.log('🌐 Abrindo navegador para autorização...');
    await open(authUrl.toString());

    // 4. Capturar authorization code manualmente
    const authCode = await getAuthorizationCodeFromUser();

    if (!authCode) {
      throw new Error('Authorization code não fornecido');
    }

    // 5. Trocar por tokens
    const tokens = await exchangeCodeForTokens(authCode, codeVerifier);

    // 6. Configurar Supabase
    configureSupabaseSecrets(`Bearer ${tokens.access_token}`, tokens.refresh_token);

    // 7. Sucesso!
    console.log('\n🎉 SETUP CONCLUÍDO COM SUCESSO!');
    console.log('✅ Tokens configurados no Supabase');
    console.log('✅ Edge Function pronta para usar');
    console.log('✅ Renovação automática configurada');
    console.log('\n🧪 Agora você pode testar a Edge Function sem configuração manual!');

  } catch (error) {
    console.error('\n❌ Erro no setup:', error.message);
    process.exit(1);
  }
}

// Executar automaticamente (ES module)
main();

export { main }; 