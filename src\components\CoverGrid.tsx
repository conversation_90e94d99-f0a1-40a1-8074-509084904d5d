import React, { useState, useEffect, useCallback } from 'react'
import { Download, Loader2, RotateCcw, X, Printer, Check, Plus, Image as ImageIcon, FolderOpen, RefreshCw, Trash2, GalleryHorizontal, Wand2, ExternalLink, Eye, Film } from 'lucide-react'
import { motion, AnimatePresence } from 'framer-motion'
import { toast as showToast } from 'sonner'
import { supabase } from '../lib/supabase'
import PosterStatus from './PosterStatus'
import MyGenerations from './MyGenerations'
import BrutalistModal from './BrutalistModal'
import { PhotoType, StreamingPlatform } from '../types'
import GenerationProgressBar from './GenerationProgressBar'
import { movieDatabase } from '../../supabase/functions/_shared/movieDatabase'
import { getCoverQuantity, getTemplateId } from '../../supabase/functions/_shared/platformConfig'
import { useTranslation } from 'react-i18next'

interface GenerationProgress {
  total: number
  completed: number
  failed: number
  inProgress: number
}

interface CoverGridProps {
  covers: string[]
  isLoading: boolean
  onRegenerateIndividual?: (index: number, addCoverCallback?: (url: string) => void) => void
  onRegenerateCoverImage?: () => void;
  regeneratingIndex?: number | null
  regeneratingIndices?: Set<number>
  expectedQuantity?: number | null
  streamingPlatform?: string
  photoType?: string
  originalImageUrl?: string
  userName?: string
  failedIndices?: Set<number>
  onRetryFailed?: (index: number) => void
  progress?: GenerationProgress
  currentMovie?: string | null
  currentGenerationId?: string | null
  forceLoadCovers?: () => void
  refreshCovers?: () => void  // 🔥 FUNÇÃO para atualizar capas automaticamente
  refreshCurrentGeneration?: (generationId?: string) => void  // 🔥 NOVA FUNÇÃO específica para geração atual
  // 🔥 NOVAS PROPS para manter configurações
  gender?: 'male' | 'female' | null
  language?: 'english' | 'portuguese'
  generateTitles?: boolean
  creativityLevel?: 'criativo' | 'rostoFiel' | 'estrito'
}

export default function CoverGrid({ 
  covers, 
  isLoading, 
  onRegenerateIndividual,
  onRegenerateCoverImage,
  regeneratingIndex,
  regeneratingIndices = new Set(),
  expectedQuantity,
  streamingPlatform,
  photoType,
  originalImageUrl,
  userName,
  failedIndices = new Set(),
  onRetryFailed,
  progress,
  currentMovie,
  currentGenerationId,
  forceLoadCovers,
  refreshCovers,  // 🔥 FUNÇÃO para atualizar capas automaticamente
  refreshCurrentGeneration,  // 🔥 NOVA FUNÇÃO específica para geração atual
  // 🔥 NOVAS PROPS
  gender,
  language = 'portuguese',
  generateTitles = false,
  creativityLevel = 'rostoFiel'
}: CoverGridProps) {
  const { t } = useTranslation()
  const [selectedCover, setSelectedCover] = useState<string | null>(null)
  const [selectedIndex, setSelectedIndex] = useState<number | null>(null)
  const [isGeneratingPoster, setIsGeneratingPoster] = useState(false)
  const [showPosterStatus, setShowPosterStatus] = useState(false)
  const [finalPoster, setFinalPoster] = useState<string | null>(null)
  const [generatedCover, setGeneratedCover] = useState<string | null>(null)
  const [allCovers, setAllCovers] = useState<string[]>([])
  const [generatedCovers, setGeneratedCovers] = useState<any[]>([]) // Novo estado para detalhes das capas
  const [coverTitles, setCoverTitles] = useState<string[]>([]) // 🔥 NOVO: Títulos dos filmes
  const [selectedCovers, setSelectedCovers] = useState<Set<number>>(new Set())
  const [showGenerateButton, setShowGenerateButton] = useState(true)
  const [showGallery, setShowGallery] = useState(false)
  const [isGeneratingCover, setIsGeneratingCover] = useState(false)
  const [generatedMovies, setGeneratedMovies] = useState<Set<string>>(new Set())
  
  // Estado local para regenerações individuais
  const [localRegeneratingIndices, setLocalRegeneratingIndices] = useState<Set<number>>(new Set())
  // NOVO: Key para forçar re-render
  const [renderKey, setRenderKey] = useState(0)

  // Modal de visualização
  const [modalImage, setModalImage] = useState<string | null>(null)
  const [selectedMainCover, setSelectedMainCover] = useState<string | null>(null)
  
  // Estado para modal de visualização do poster final
  const [selectedFinalPoster, setSelectedFinalPoster] = useState<string | null>(null)
  const [modalIndex, setModalIndex] = useState<number>(-1)

  // NOVO: Estado para modal de erro na geração
  const [showErrorModal, setShowErrorModal] = useState(false)
  const [errorModalIndex, setErrorModalIndex] = useState<number>(-1)
  
  // NOVO: Estado para modal de regeneração
  const [showRegenerateModal, setShowRegenerateModal] = useState(false)
  const [regenerateModalIndex, setRegenerateModalIndex] = useState<number>(-1)
  const [availableMovies, setAvailableMovies] = useState<string[]>([])
  const [selectedMovie, setSelectedMovie] = useState<string>('')
  const [customPrompt, setCustomPrompt] = useState<string>('')
  const [customTitle, setCustomTitle] = useState<string>('')
  const [useCustomPrompt, setUseCustomPrompt] = useState(false)
  const [isGeneratingFromModal, setIsGeneratingFromModal] = useState(false)
  const [currentRegeneratingTitle, setCurrentRegeneratingTitle] = useState<string>('')

  // Estados para modal de gerar capa principal
  const [showMainCoverModal, setShowMainCoverModal] = useState(false)
  const [useCustomPromptMain, setUseCustomPromptMain] = useState(false)
  const [selectedMovieMain, setSelectedMovieMain] = useState('')
  const [customPromptMain, setCustomPromptMain] = useState('')
  const [customTitleMain, setCustomTitleMain] = useState('')
  const [isGeneratingMainFromModal, setIsGeneratingMainFromModal] = useState(false)

  // NOVA FUNÇÃO: Definir uma capa individual como a capa principal
  const handleSetAsMainCover = (coverUrl: string) => {
    if (coverUrl) {
      setGeneratedCover(coverUrl);
      showToast.success('Capa principal definida com sucesso!');
      console.log(' Nova capa principal definida:', coverUrl);
    }
  };

  // 🔥 REMOVIDO: AUTO-GERAÇÃO DA CAPA PRINCIPAL QUE CAUSAVA LOOP
  // O usuário deve gerar a capa principal manualmente clicando no botão
  // useEffect(() => {
  //   const requiredQty = getRequiredQuantity();
  //   if (allCovers.length >= requiredQty && !generatedCover && !isGeneratingCover) {
  //     console.log(` Todas as ${requiredQty} capas foram geradas. Gerando capa principal automaticamente...`);
  //     handleGenerateCover();
  //   }
  // }, [allCovers.length, generatedCover, isGeneratingCover]);

  // Atualiza a lista de todas as capas quando novas capas são geradas
  useEffect(() => {
    console.log(' CoverGrid: useEffect triggered, covers.length:', covers.length, 'covers:', covers)
    
    // Se não há capas, limpar TODOS os estados relacionados
    if (covers.length === 0) {
      setAllCovers([])
      setSelectedCovers(new Set())
      setSelectedCover(null)
      setSelectedIndex(null)
      setGeneratedCover(null)
      setFinalPoster(null)
      setCoverTitles([])
      setGeneratedCovers([])
      setGeneratedMovies(new Set())
      setModalImage(null)
      setModalIndex(-1)
      setRenderKey(prev => prev + 1)
      console.log('🧹 CoverGrid: Todos os estados limpos')
      return
    }

    // ATUALIZAÇÃO COMPLETA: substitui o estado atual com as capas recebidas
    setAllCovers(covers)
    
    // FORÇA RE-RENDER
    setRenderKey(prev => prev + 1)
    
    console.log(' CoverGrid: allCovers updated to:', covers.length, 'items')
  }, [covers])

  // FORÇA ATUALIZAÇÃO: useEffect adicional para garantir que mudanças sejam detectadas
  useEffect(() => {
    if (covers.length > 0) {
      console.log(' Force update: setting allCovers with', covers.length, 'covers')
      console.log(' Covers recebidos:', covers)
      setAllCovers([...covers]) // Força nova referência
      
      // FORÇA RE-RENDER DOS COMPONENTES
      setTimeout(() => {
        console.log(' Segundo force update...')
        setAllCovers(prev => [...prev]) // Força nova referência novamente
      }, 100)
    }
  }, [covers.length, JSON.stringify(covers)]) // Detecta mudanças no conteúdo das capas

  // NOVO: useEffect para mudanças específicas de capa individual
  useEffect(() => {
    console.log(' CoverGrid state update: allCovers.length =', allCovers.length)
    console.log(' Current allCovers:', allCovers)
  }, [allCovers])

  // 🔥 NOVA FUNÇÃO: Buscar títulos dos filmes e popular generatedCovers
  const fetchCoverTitles = useCallback(async () => {
    console.log('🔍 fetchCoverTitles chamada:', {
      streamingPlatform,
      coversLength: covers.length,
      currentGenerationId
    })
    
    if (!streamingPlatform || covers.length === 0) {
      console.log('🔍 fetchCoverTitles: Condições não atendidas, limpando estados')
      setCoverTitles([])
      setGeneratedCovers([])
      return
    }

    try {
      const { data: { session } } = await supabase.auth.getSession()
      if (!session) {
        console.log('🔍 fetchCoverTitles: Usuário não autenticado')
        return
      }

      // 🔥 MÉTODO 1: Buscar da geração específica (se currentGenerationId existe) ou última geração
      let generationsQuery = supabase
        .from('cover_generations')
        .select('generated_covers')
        .eq('user_id', session.user.id)
      
      // Se temos um ID de geração específico, buscar apenas essa geração
      if (currentGenerationId) {
        generationsQuery = generationsQuery.eq('id', currentGenerationId)
        console.log('🎯 Buscando títulos da geração específica:', currentGenerationId)
      } else {
        // Caso contrário, buscar a última geração
        generationsQuery = generationsQuery.order('created_at', { ascending: false }).limit(1)
        console.log('🎯 Buscando títulos da última geração')
      }
      
      const { data: generations } = await generationsQuery

      if (generations && generations.length > 0) {
        const generation = generations[0]
        console.log('🔍 MÉTODO 1 - Geração encontrada:', generation)
        
        if (generation.generated_covers && Array.isArray(generation.generated_covers)) {
          console.log('🔍 MÉTODO 1 - generated_covers encontrado:', generation.generated_covers)
          
          // Se é formato novo (objetos com movie_title)
          if (generation.generated_covers[0] && typeof generation.generated_covers[0] === 'object') {
            const titles = generation.generated_covers.map((cover: any) => cover.movie_title || 'Filme Desconhecido')
            setCoverTitles(titles)
            
            // Popular generatedCovers com dados da geração
            const generatedCoversData = generation.generated_covers.map((cover: any, index: number) => ({
              movieTitle: cover.movie_title || 'Filme Desconhecido',
              imageUrl: covers[index] || '',
              position: index
            }))
            setGeneratedCovers(generatedCoversData)
            
            console.log('🎯 MÉTODO 1 - Títulos encontrados na geração:', titles)
            console.log('🎯 MÉTODO 1 - GeneratedCovers populado:', generatedCoversData)
            return
          } else {
            console.log('🔍 MÉTODO 1 - Formato antigo detectado, passando para MÉTODO 2')
          }
        } else {
          console.log('🔍 MÉTODO 1 - generated_covers não encontrado ou não é array')
        }
      } else {
        console.log('🔍 MÉTODO 1 - Nenhuma geração encontrada')
      }

      // 🔥 MÉTODO 2: Buscar do banco de dados unified_movies_series
      console.log('🔍 MÉTODO 2 - Buscando em unified_movies_series para plataforma:', streamingPlatform)
      const { data: movies, error: moviesError } = await supabase
        .from('unified_movies_series')
        .select('title')
        .eq('streaming_platform', streamingPlatform)
        .eq('is_active', true)
        .order('created_at', { ascending: true }) // Ordem consistente

      if (!moviesError && movies) {
        console.log('🔍 MÉTODO 2 - Filmes encontrados:', movies)
        const titles = covers.map((_, index) => {
          return movies[index]?.title || `Filme ${index + 1}`
        })
        setCoverTitles(titles)
        
        // Popular generatedCovers com dados dos filmes
        const generatedCoversData = covers.map((coverUrl, index) => ({
          movieTitle: movies[index]?.title || `Filme ${index + 1}`,
          imageUrl: coverUrl,
          position: index
        }))
        setGeneratedCovers(generatedCoversData)
        
        console.log('🎯 MÉTODO 2 - Títulos do banco unified_movies_series:', titles)
        console.log('🎯 MÉTODO 2 - GeneratedCovers populado:', generatedCoversData)
        return
      } else {
        console.log('🔍 MÉTODO 2 - Erro ou nenhum filme encontrado:', moviesError)
      }

      // 🔥 MÉTODO 3: Buscar na tabela cover_images (fallback)
      const { data: coverImages } = await supabase
        .from('cover_images')
        .select('image_url, movie_title')
        .eq('user_id', session.user.id)
        .eq('streaming_platform', streamingPlatform)
        .order('created_at', { ascending: false })

      if (coverImages && coverImages.length > 0) {
        const titles = covers.map(coverUrl => {
          const found = coverImages.find(img => img.image_url === coverUrl)
          return found?.movie_title || 'Filme Desconhecido'
        })
        setCoverTitles(titles)
        
        // Popular generatedCovers com dados das cover_images
        const generatedCoversData = covers.map((coverUrl, index) => {
          const found = coverImages.find(img => img.image_url === coverUrl)
          return {
            movieTitle: found?.movie_title || 'Filme Desconhecido',
            imageUrl: coverUrl,
            position: index
          }
        })
        setGeneratedCovers(generatedCoversData)
        
        console.log('🔍 Títulos encontrados na cover_images:', titles)
        console.log('🔍 GeneratedCovers populado:', generatedCoversData)
        return
      }

      // Fallback: títulos genéricos
      const fallbackTitles = covers.map((_, index) => `Filme ${index + 1}`)
      setCoverTitles(fallbackTitles)
      
      // Popular generatedCovers com dados genéricos
      const fallbackGeneratedCovers = covers.map((coverUrl, index) => ({
        movieTitle: `Filme ${index + 1}`,
        imageUrl: coverUrl,
        position: index
      }))
      setGeneratedCovers(fallbackGeneratedCovers)
      
      console.log('🔄 Usando títulos genéricos:', fallbackTitles)
      console.log('🔄 GeneratedCovers populado com dados genéricos:', fallbackGeneratedCovers)

    } catch (error) {
      console.error('Erro ao buscar títulos:', error)
      const errorTitles = covers.map((_, index) => `Filme ${index + 1}`)
      setCoverTitles(errorTitles)
      
      // Popular generatedCovers mesmo em caso de erro
      const errorGeneratedCovers = covers.map((coverUrl, index) => ({
        movieTitle: `Filme ${index + 1}`,
        imageUrl: coverUrl,
        position: index
      }))
      setGeneratedCovers(errorGeneratedCovers)
    }
  }, [covers, streamingPlatform, currentGenerationId])

  // 🔥 Buscar títulos quando as capas mudarem
  useEffect(() => {
    fetchCoverTitles()
  }, [fetchCoverTitles])

  // 🔥 Buscar títulos quando currentGenerationId mudar
  useEffect(() => {
    if (currentGenerationId) {
      console.log('🔄 currentGenerationId mudou, buscando títulos:', currentGenerationId)
      fetchCoverTitles()
    }
  }, [currentGenerationId, fetchCoverTitles])

  // Função para adicionar nova capa regenerada
  const addRegeneratedCover = (newCoverUrl: string, targetIndex?: number) => {
    console.log(' Adding regenerated cover:', newCoverUrl, 'at index:', targetIndex)
    setAllCovers(prevCovers => {
      const newCovers = [...prevCovers]
      if (targetIndex !== undefined && targetIndex >= 0 && targetIndex < newCovers.length) {
        // Substitui na posição específica
        newCovers[targetIndex] = newCoverUrl
        console.log(` Replaced cover at index ${targetIndex}:`, newCoverUrl)
      } else {
        // Se não tem índice específico, adiciona no final (comportamento antigo)
        newCovers.push(newCoverUrl)
        console.log(' Added cover at end:', newCoverUrl)
      }
      console.log(' Updated covers array:', newCovers)
      return newCovers
    })
    
    // FORÇA RE-RENDER IMEDIATO
    setRenderKey(prev => prev + 1)
    console.log(' Forced re-render after adding cover')
    
    // 🔥 Atualizar títulos após adicionar nova capa
    setTimeout(() => {
      fetchCoverTitles()
    }, 500)
  }

  // Seleciona automaticamente as primeiras capas baseado na plataforma
  useEffect(() => {
    if (allCovers.length > 0 && selectedCovers.size === 0) {
      const requiredQuantity = getRequiredQuantity()
      const initialSelection = new Set<number>()
      for (let i = 0; i < Math.min(requiredQuantity, allCovers.length); i++) {
        initialSelection.add(i)
      }
      setSelectedCovers(initialSelection)
    }
  }, [allCovers, streamingPlatform])

  const getRequiredQuantity = () => {
    return streamingPlatform ? getCoverQuantity(streamingPlatform) : 12
  }

  const toggleCoverSelection = (index: number) => {
    const newSelection = new Set(selectedCovers)
    const requiredQuantity = getRequiredQuantity()
    
    if (newSelection.has(index)) {
      // Remove da seleção
      newSelection.delete(index)
    } else {
      // 🔥 VERIFICAR LIMITE: Não pode selecionar mais que o necessário
      if (newSelection.size >= requiredQuantity) {
        showToast.warning(`Você só pode selecionar até ${requiredQuantity} capas para ${streamingPlatform?.toUpperCase()}. Deselecione outras primeiro.`)
        return
      }
      // Adiciona à seleção
      newSelection.add(index)
    }
    
    setSelectedCovers(newSelection)
  }

  // 🔥 NOVO: Funções para selecionar/deselecionar todas as capas
  const selectAllCovers = () => {
    const requiredQuantity = getRequiredQuantity()
    const newSelection = new Set<number>()
    
    // Seleciona até o limite do streaming
    for (let i = 0; i < Math.min(allCovers.length, requiredQuantity); i++) {
      newSelection.add(i)
    }
    
    setSelectedCovers(newSelection)
  }

  const deselectAllCovers = () => {
    setSelectedCovers(new Set())
  }

  const isAllSelected = () => {
    const requiredQuantity = getRequiredQuantity()
    const maxSelectable = Math.min(allCovers.length, requiredQuantity)
    return selectedCovers.size === maxSelectable && maxSelectable > 0
  }

  const handleRegenerateIndividual = (index: number) => {
    if (onRegenerateIndividual) {
      // Criar uma função wrapper que passa o índice correto
      const addRegeneratedCoverAtIndex = (newCoverUrl: string) => {
        addRegeneratedCover(newCoverUrl, index)
      }
      onRegenerateIndividual(index, addRegeneratedCoverAtIndex)
      // Toast que desaparece automaticamente em 3 segundos
      showToast.info('Regenerando capa... A capa será substituída na posição correta!', {
        duration: 3000
      })
    }
  }

  const getSelectedCoversArray = () => {
    return Array.from(selectedCovers)
      .sort((a, b) => a - b)
      .map(index => allCovers[index])
      .filter(Boolean)
  }

  // 🔥 NOVA FUNÇÃO: Retorna apenas as capas necessárias para o poster
  const getSelectedCoversForPoster = () => {
    const requiredQuantity = getRequiredQuantity()
    return Array.from(selectedCovers)
      .sort((a, b) => a - b)
      .slice(0, requiredQuantity) // Pega apenas as primeiras N necessárias
      .map(index => allCovers[index])
      .filter(Boolean)
  }

  // Nova função para buscar URLs do Supabase para o poster
  const getSupabaseCoversForPoster = async () => {
    try {
      const { data: { session } } = await supabase.auth.getSession()
      if (!session) {
        console.warn('Usuário não autenticado - usando URLs da sessão atual')
        return getSelectedCoversArray()
      }

      // Primeiro, tenta buscar as capas específicas da geração atual se existirem
      const selectedUrls = getSelectedCoversForPoster() // 🔥 USA NOVA FUNÇÃO QUE LIMITA
      const replicateUrls: string[] = []
      const validSupabaseUrls: string[] = []

      // Verifica quais das URLs selecionadas são do Supabase vs Replicate
      for (const url of selectedUrls) {
        if (url && url.includes('supabase.co/storage')) {
          validSupabaseUrls.push(url)
          console.log(' URL do Supabase encontrada:', url)
        } else if (url && url.includes('replicate.delivery')) {
          replicateUrls.push(url)
          console.warn(' URL do Replicate encontrada (será convertida):', url)
        }
      }

      // NOVA FUNCIONALIDADE: Converter URLs do Replicate para Supabase automaticamente
      if (replicateUrls.length > 0) {
        console.log(` Convertendo ${replicateUrls.length} URLs do Replicate para Supabase...`)
        
        try {
          const convertResponse = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/convert-replicate-urls`, {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${session.access_token}`,
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              urls: replicateUrls
            })
          })

          if (convertResponse.ok) {
            const convertData = await convertResponse.json()
            if (convertData.success && convertData.convertedUrls) {
              console.log(` ${convertData.stats.successful} URLs convertidas com sucesso!`)
              validSupabaseUrls.push(...convertData.convertedUrls)
            } else {
              console.warn(' Falha na conversão, usando URLs originais')
              validSupabaseUrls.push(...replicateUrls)
            }
          } else {
            console.warn(' Erro na API de conversão, usando URLs originais')
            validSupabaseUrls.push(...replicateUrls)
          }
        } catch (convertError) {
          console.error(' Erro ao converter URLs do Replicate:', convertError)
          validSupabaseUrls.push(...replicateUrls)
        }
      }

      // Se tem URLs válidas (agora todas do Supabase), usa elas
      if (validSupabaseUrls.length > 0) {
        console.log(` Usando ${validSupabaseUrls.length} URLs do Supabase para o poster`)
        
        // Completa com URLs da base de dados se necessário
        const requiredQuantity = getRequiredQuantity()
        if (validSupabaseUrls.length < requiredQuantity) {
          const { data: additionalCovers, error } = await supabase
            .from('cover_images')
            .select('image_url')
            .eq('user_id', session.user.id)
            .eq('streaming_platform', streamingPlatform)
            .order('created_at', { ascending: false })
            .limit(requiredQuantity - validSupabaseUrls.length)

          if (!error && additionalCovers) {
            const additionalUrls = additionalCovers.map(cover => cover.image_url)
            validSupabaseUrls.push(...additionalUrls)
            console.log(` Adicionadas ${additionalUrls.length} URLs adicionais do banco`)
          }
        }

        return validSupabaseUrls
      }

      // Se não tem URLs válidas selecionadas, busca as mais recentes do usuário
      console.log(' Buscando capas mais recentes do Supabase...')
      const { data: covers, error } = await supabase
        .from('cover_images')
        .select('image_url')
        .eq('user_id', session.user.id)
        .eq('streaming_platform', streamingPlatform)
        .order('created_at', { ascending: false })
        .limit(getRequiredQuantity())

      if (error) {
        console.error('Erro ao buscar capas do Supabase:', error)
        return getSelectedCoversArray()
      }

      if (!covers || covers.length === 0) {
        console.warn('Nenhuma capa encontrada no Supabase - usando URLs da sessão atual')
        return getSelectedCoversArray()
      }

      console.log(` Encontradas ${covers.length} capas no Supabase para o poster`)
      return covers.map(cover => cover.image_url)

    } catch (error) {
      console.error('Erro ao buscar capas do Supabase:', error)
      return getSelectedCoversArray()
    }
  }

  const canGeneratePoster = () => {
    const requiredQuantity = getRequiredQuantity()
    const currentCoversCount = allCovers.length
    const selectedCount = selectedCovers.size
    
    // 🔥 NOVA LÓGICA: Precisa ter pelo menos a quantidade necessária
    // O usuário pode escolher mais capas e o sistema usa as primeiras N necessárias
    return selectedCount >= requiredQuantity && 
           currentCoversCount >= requiredQuantity &&
           streamingPlatform && 
           originalImageUrl && 
           userName?.trim()
  }

  const handleDownloadCover = async (url: string, filename: string) => {
    try {
      const response = await fetch(url)
      const blob = await response.blob()
      const downloadUrl = window.URL.createObjectURL(blob)
      
      const link = document.createElement('a')
      link.href = downloadUrl
      link.download = `${filename}.jpg`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      
      window.URL.revokeObjectURL(downloadUrl)
      showToast.success('Download iniciado!')
    } catch (error) {
      console.error('Erro no download:', error)
      showToast.error('Erro ao fazer download')
    }
  }

  const handleDownload = async (url: string, index: number) => {
    const downloadToast = showToast.loading('Baixando imagem...')
    try {
      const response = await fetch(url)
      const blob = await response.blob()
      const downloadUrl = window.URL.createObjectURL(blob)
      
      const link = document.createElement('a')
      link.href = downloadUrl
      link.download = `movie-cover-${index + 1}.jpg`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      
      window.URL.revokeObjectURL(downloadUrl)
      showToast.dismiss(downloadToast)
      showToast.success('Imagem baixada com sucesso! ')
    } catch (error) {
      console.error('Error downloading image:', error)
      showToast.dismiss(downloadToast)
      showToast.error('Erro ao baixar imagem')
    }
  }

  const handleDownloadPoster = async () => {
    if (!finalPoster) return
    
    const downloadToast = showToast.loading('Baixando poster final...')
    try {
      // VERIFICAR SE É URL DE DOWNLOAD DIRETO
      if (finalPoster.includes('export-download.canva.com') || finalPoster.match(/\.(png|jpg|jpeg|webp)(\?|$)/i)) {
        // URL de download direto - fazer download normal
        const response = await fetch(finalPoster)
        const blob = await response.blob()
        const downloadUrl = window.URL.createObjectURL(blob)
        
        const link = document.createElement('a')
        link.href = downloadUrl
        link.download = `poster-final-${userName || 'usuario'}.png`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        
        window.URL.revokeObjectURL(downloadUrl)
        showToast.dismiss(downloadToast)
        showToast.success('Poster final baixado com sucesso! ')
      } else {
        // Link do Canva - abrir em nova aba
        showToast.dismiss(downloadToast)
        window.open(finalPoster, '_blank')
        showToast.success('Poster aberto no Canva! Use Ctrl+S ou o botão de download do Canva.')
      }
    } catch (error) {
      console.error('Error downloading poster:', error)
      showToast.dismiss(downloadToast)
      showToast.error('Erro ao baixar poster final')
    }
  }

  const clearPoster = () => {
    setFinalPoster(null)
    setGeneratedCover(null)
    showToast.success('Poster removido!')
  }

  const handleDownloadAll = async () => {
    const downloadToast = showToast.loading(`Baixando ${allCovers.length} imagens...`)
    
    try {
      for (let i = 0; i < allCovers.length; i++) {
        await handleDownload(allCovers[i], i)
        // Small delay between downloads
        await new Promise(resolve => setTimeout(resolve, 500))
      }
      showToast.dismiss(downloadToast)
      showToast.success(`Todas as ${allCovers.length} imagens foram baixadas! `)
    } catch (error) {
      showToast.dismiss(downloadToast)
      showToast.error('Erro ao baixar algumas imagens')
    }
  }

  // Função para buscar filmes disponíveis que ainda não foram usados
  const fetchAvailableMovies = async () => {
    if (!streamingPlatform) return []

    // Buscar todos os filmes da plataforma
    const { data, error } = await supabase
      .from('unified_movies_series')
      .select('*')
      .eq('streaming_platform', streamingPlatform)

    if (error) {
      console.error('Error fetching movies:', error)
      return []
    }
    
    if (data) {
      // Extrair títulos únicos de todos os filmes
      const allMovieTitles = data.map(movie => movie.title || movie.name).filter(Boolean)
      const uniqueAllTitles = [...new Set(allMovieTitles)]
      
      // Obter títulos que já foram usados nas capas geradas
      const usedTitles = new Set()
      
      // Adicionar títulos das capas já geradas
      generatedCovers.forEach(cover => {
        if (cover.movie_title) {
          usedTitles.add(cover.movie_title)
        }
      })
      
      // Adicionar títulos dos filmes já usados no coverTitles
      coverTitles.forEach(title => {
        if (title) {
          usedTitles.add(title)
        }
      })
      
      // Filtrar apenas títulos que ainda não foram usados
      const availableTitles = uniqueAllTitles.filter(title => !usedTitles.has(title))
      
      console.log('🎬 Títulos totais:', uniqueAllTitles.length)
      console.log('🚫 Títulos já usados:', Array.from(usedTitles))
      console.log('✅ Títulos disponíveis:', availableTitles.length)
      
      return availableTitles
    }
    
    return []
  }

  // Função para abrir modal de gerar capa principal
  const handleOpenMainCoverModal = async () => {
    console.log('🎯 handleOpenMainCoverModal chamada!')
    try {
      const movies = await fetchAvailableMovies()
      console.log('🎬 Filmes encontrados:', movies.length)
      setAvailableMovies(movies)
      setShowMainCoverModal(true)
      console.log('✅ Modal deve estar aberto agora')
    } catch (error) {
      console.error('❌ Erro ao abrir modal:', error)
      showToast.error('Erro ao carregar filmes disponíveis')
    }
  }

  // Função para gerar capa principal a partir do modal
  const handleGenerateMainFromModal = async () => {
    if (!useCustomPromptMain && !selectedMovieMain) {
      showToast.error('Selecione um filme ou use um prompt customizado')
      return
    }
    
    if (useCustomPromptMain && !customPromptMain.trim()) {
      showToast.error('Digite um prompt customizado')
      return
    }

    setIsGeneratingMainFromModal(true)
    let toastId: string | number | undefined
    
    try {
      toastId = showToast.loading('Gerando capa principal...')
      
      let movieTitle = ''
      
      if (useCustomPromptMain) {
        movieTitle = customTitleMain || 'Filme Customizado'
        // Para prompt customizado, usar generateCoverImage diretamente
        const coverUrl = await generateCoverImage(false, 1, movieTitle)
        
        if (coverUrl) {
          setGeneratedCover(coverUrl)
          await saveCoverImageToDatabase(coverUrl)
          
          // FORÇAR RE-RENDER PARA MOSTRAR A CAPA NA INTERFACE
          setRenderKey(prev => prev + 1)
          
          // DISPARAR EVENTO PARA ATUALIZAR OUTRAS PARTES DA INTERFACE
          window.dispatchEvent(new CustomEvent('coverGenerated', {
            detail: { coverUrl, type: 'main' }
          }))
          
          // 🔥 DISPARAR EVENTO PARA INVALIDAR CACHE DE CRÉDITOS
          window.dispatchEvent(new CustomEvent('creditConsumed'))
          
          showToast.dismiss(toastId)
          showToast.success('Capa principal gerada com sucesso!')
          
          // Fechar modal e limpar estados
          setShowMainCoverModal(false)
          setUseCustomPromptMain(false)
          setSelectedMovieMain('')
          setCustomPromptMain('')
          setCustomTitleMain('')
          
          console.log('🎯 Capa principal definida no estado:', coverUrl)
          return
        } else {
          throw new Error('Não foi possível gerar a capa')
        }
      } else {
        movieTitle = selectedMovieMain
        // Para título selecionado, usar generateCoverImage diretamente
        const coverUrl = await generateCoverImage(false, 1, movieTitle)
        
        if (coverUrl) {
          setGeneratedCover(coverUrl)
          await saveCoverImageToDatabase(coverUrl)
          
          // FORÇAR RE-RENDER PARA MOSTRAR A CAPA NA INTERFACE
          setRenderKey(prev => prev + 1)
          
          // DISPARAR EVENTO PARA ATUALIZAR OUTRAS PARTES DA INTERFACE
          window.dispatchEvent(new CustomEvent('coverGenerated', {
            detail: { coverUrl, type: 'main' }
          }))
          
          // 🔥 DISPARAR EVENTO PARA INVALIDAR CACHE DE CRÉDITOS
          window.dispatchEvent(new CustomEvent('creditConsumed'))
          
          showToast.dismiss(toastId)
          showToast.success('Capa principal gerada com sucesso!')
          
          // Fechar modal e limpar estados
          setShowMainCoverModal(false)
          setUseCustomPromptMain(false)
          setSelectedMovieMain('')
          setCustomPromptMain('')
          setCustomTitleMain('')
          
          console.log('🎯 Capa principal definida no estado:', coverUrl)
          return
        } else {
          throw new Error('Não foi possível gerar a capa')
        }
      }
    } catch (error) {
      console.error('Erro ao gerar capa:', error)
      if (toastId) showToast.dismiss(toastId)
      const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido'
      if (!errorMessage.includes('Erro na geração de capa')) {
        showToast.error(`Erro ao gerar capa: ${errorMessage}`)
      }
    } finally {
      setIsGeneratingMainFromModal(false)
    }
  }

  const handleGenerateCover = async () => {
    if (!originalImageUrl || !streamingPlatform || !photoType) {
      showToast.error('Informações necessárias não encontradas')
      return
    }

    setIsGeneratingCover(true)
    let toastId: string | number | undefined
    
    try {
      toastId = showToast.loading('Gerando capa principal...')
      
      // USAR NOVO SISTEMA DE FALLBACK PARA E005
      const coverUrl = await generateCoverWithFallback()
      
      if (coverUrl) {
        setGeneratedCover(coverUrl)
        await saveCoverImageToDatabase(coverUrl)
        
        // FORÇAR RE-RENDER PARA MOSTRAR A CAPA NA INTERFACE
        setRenderKey(prev => prev + 1)
        
        // DISPARAR EVENTO PARA ATUALIZAR OUTRAS PARTES DA INTERFACE
        window.dispatchEvent(new CustomEvent('coverGenerated', {
          detail: { coverUrl, type: 'main' }
        }))
        
        // 🔥 DISPARAR EVENTO PARA INVALIDAR CACHE DE CRÉDITOS
        window.dispatchEvent(new CustomEvent('creditConsumed'))
        
        showToast.dismiss(toastId)
        showToast.success('Capa principal gerada com sucesso!')
        
        // LOG PARA DEBUG
        console.log('🎯 Capa principal definida no estado:', coverUrl)
        console.log('🎯 Estado generatedCover atualizado para:', coverUrl)
      } else {
        throw new Error('Não foi possível gerar a capa')
      }
    } catch (error) {
      console.error('Erro ao gerar capa:', error)
      if (toastId) showToast.dismiss(toastId)
      const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido'
      if (!errorMessage.includes('Erro na geração de capa')) {
        showToast.error(`Erro ao gerar capa: ${errorMessage}`)
      }
    } finally {
      setIsGeneratingCover(false)
    }
  }

  const handleGenerateFinalPoster = async () => {
    if (!canGeneratePoster()) {
      showToast.warning(`Selecione pelo menos ${getRequiredQuantity()} capas para gerar o poster do ${streamingPlatform?.toUpperCase()}.`);
      return;
    }
    if (!generatedCover) {
      showToast.error('Gere a capa principal primeiro antes de criar o poster final.');
      return;
    }

    setIsGeneratingPoster(true);
    setShowPosterStatus(true);

    const generationMode = import.meta.env.VITE_POSTER_GENERATION_MODE || 'BACKEND';

    try {
      let finalPosterUrl = null;

      const templateId = streamingPlatform ? getTemplateId(streamingPlatform) : 'EAGqDjHn_M4';

      const isCouple = photoType === 'casal';
      const verb = isCouple ? 'são transportados' : 'é transportado';
      const pronoun = isCouple ? 'eles vivem' : 'vive';
      const platformName = streamingPlatform ? streamingPlatform.charAt(0).toUpperCase() + streamingPlatform.slice(1) : 'Disney';
      const customDescription = `${userName || 'Caio'} ${verb} para diferentes séries da ${platformName}, onde ${pronoun} como o protagonista, enfrentando aventuras únicas em cada episódio enquanto tenta voltar para sua realidade.`;

      // CORREÇÃO: Busca URLs do Supabase em vez de usar URLs temporárias do Replicate
      const supabaseCovers = await getSupabaseCoversForPoster();
      
      console.log(' URLs do Supabase para o poster:', supabaseCovers);

      // PAYLOAD ADAPTATIVO BASEADO NA PLATAFORMA
      const payload: any = {
        ID: templateId,
        CAPA: generatedCover,
        AVATAR: originalImageUrl,
        PHOTO_01: supabaseCovers[0] || "https://placehold.co/600x400",
        PHOTO_02: supabaseCovers[1] || "https://placehold.co/600x400",
        PHOTO_03: supabaseCovers[2] || "https://placehold.co/600x400",
        PHOTO_04: supabaseCovers[3] || "https://placehold.co/600x400",
        PHOTO_05: supabaseCovers[4] || "https://placehold.co/600x400",
        PHOTO_06: supabaseCovers[5] || "https://placehold.co/600x400",
        PHOTO_07: supabaseCovers[6] || "https://placehold.co/600x400",
        PHOTO_08: supabaseCovers[7] || "https://placehold.co/600x400",
        PHOTO_09: supabaseCovers[8] || "https://placehold.co/600x400",
        TITLE: `Poster ${streamingPlatform?.toUpperCase() || 'STREAMING'} - ${userName || 'Personalizado'}`,
        DESCRIPTION: customDescription,
        CONTINUE: 'Clique para continuar'
      };

      // ADICIONAR FOTOS EXTRAS APENAS PARA NETFLIX E AMAZON (12 e 11 capas respectivamente)
      if (streamingPlatform === 'netflix') {
        // Netflix precisa de 12 fotos (PHOTO_01 até PHOTO_12)
        payload.PHOTO_10 = supabaseCovers[9] || "https://placehold.co/600x400";
        payload.PHOTO_11 = supabaseCovers[10] || "https://placehold.co/600x400";
        payload.PHOTO_12 = supabaseCovers[11] || "https://placehold.co/600x400";
      } else if (streamingPlatform === 'amazon') {
        // Amazon precisa de 11 fotos (PHOTO_01 até PHOTO_11)
        payload.PHOTO_10 = supabaseCovers[9] || "https://placehold.co/600x400";
        payload.PHOTO_11 = supabaseCovers[10] || "https://placehold.co/600x400";
      }
      // Disney só precisa de 9 fotos (PHOTO_01 até PHOTO_09) - não adiciona mais nada

      console.log(' Payload final para o Canva:', payload);

      if (generationMode === 'BACKEND') {
        const { data: { session } } = await supabase.auth.getSession();
        const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/generate-print-poster`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${session?.access_token}`
          },
          body: JSON.stringify(payload)
        });
        const result = await response.json();
        if (!response.ok) throw new Error(result.error || 'Erro no backend');
        
        // TRATAMENTO CORRETO DA RESPOSTA DO BACKEND
        console.log(' Resposta do backend:', result);
        
        if (result.success && result.webhookResult?.job?.urls?.length > 0) {
          // Resposta com estrutura webhookResult
          finalPosterUrl = result.webhookResult.job.urls[0];
          console.log(' URL do poster extraída do webhookResult:', finalPosterUrl);
        } else if (result.posterUrl) {
          // Resposta direta com posterUrl (fallback)
          finalPosterUrl = result.posterUrl;
          console.log(' URL do poster extraída diretamente:', finalPosterUrl);
        } else {
          throw new Error('Resposta do backend não contém URL válida do poster');
        }

      } else { // FRONTEND (fallback)
        const webhookResponse = await fetch('https://automate.felvieira.com.br/webhook/criar-design-canva', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json', 'key': 'n8ncanvaapicall' },
          body: JSON.stringify(payload)
        });
        const result = await webhookResponse.json();
        if (!webhookResponse.ok) throw new Error('Erro no webhook do Canva');
        finalPosterUrl = result.design_url || result.urls?.[0];
      }

      if (finalPosterUrl) {
        setFinalPoster(finalPosterUrl);
        await savePosterToDatabase(finalPosterUrl);
        
        // MOSTRAR MODAL COM O POSTER PARA DOWNLOAD
        showToast.success(' Poster criado com sucesso! Clique no botão de download para baixar.');
        
        // OPCIONAL: Abrir automaticamente em nova aba se for URL do Canva
        if (finalPosterUrl.includes('canva.com') || finalPosterUrl.includes('export-download.canva.com')) {
          // Para URLs de download do Canva, não abrir automaticamente
          console.log(' Poster pronto para download:', finalPosterUrl);
        }
      } else {
        throw new Error('Não foi possível obter a URL do poster final.');
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Ocorreu um erro desconhecido.';
      console.error("Erro ao gerar poster final:", error);
      showToast.error(errorMessage);
    } finally {
      setIsGeneratingPoster(false);
      setShowPosterStatus(false);
    }
  };

  const savePosterToDatabase = async (posterUrl: string) => {
    try {
      console.log(' Iniciando salvamento do poster...')
      
      const { data: { session } } = await supabase.auth.getSession()
      if (!session) {
        console.warn(' Usuário não autenticado - não salvando poster')
        return
      }

      console.log(' Usuário autenticado:', session.user.id)

      const insertData = {
        user_id: session.user.id,
        poster_url: posterUrl,
        cover_url: generatedCover,
        avatar_url: originalImageUrl,
        streaming_platform: streamingPlatform || 'netflix',
        photo_type: photoType || 'individual',
        user_name: userName || 'Usuário',
        template_id: streamingPlatform ? getTemplateId(streamingPlatform) : 'EAGqDjHn_M4',
        canva_design_url: posterUrl,
        created_at: new Date().toISOString()
      }

      console.log(' Dados do poster para inserção:', insertData)

      const { data, error } = await supabase
        .from('poster_images')
        .insert(insertData)
        .select()

      if (error) {
        console.error('Erro ao salvar poster no banco:', error)
        throw error
      }

      console.log(' Poster salvo no banco com sucesso:', data)
      
      // MENSAGEM ADAPTATIVA BASEADA NO TIPO DE URL
      const isDirectDownload = posterUrl.includes('export-download.canva.com') || posterUrl.match(/\.(png|jpg|jpeg|webp)(\?|$)/i)
      if (isDirectDownload) {
        showToast.success(t('coverGrid.posterSaved'))
      } else {
        showToast.success(t('coverGrid.posterSavedCanva'))
      }
      
    } catch (error) {
      console.error('Erro ao salvar poster no banco:', error)
      showToast.warning('Poster criado, mas não foi salvo no dashboard')
      // Não falha o processo todo, só loga o erro
    }
  }

  const saveCoverImageToDatabase = async (coverUrl: string) => {
    try {
      console.log(' Iniciando salvamento da cover image...')
      
      const { data: { session } } = await supabase.auth.getSession()
      if (!session) {
        console.warn(' Usuário não autenticado - não salvando cover image')
        return
      }

      console.log(' Usuário autenticado:', session.user.id)

      // CORRIGIDO: A capa principal DEVE ser salva para aparecer em "Minhas Gerações"
      const insertData = {
        user_id: session.user.id,
        image_url: coverUrl,
        movie_title: `Capa ${streamingPlatform?.toUpperCase() || 'STREAMING'}`,
        streaming_platform: streamingPlatform || 'netflix',
        photo_type: photoType || 'individual',
        aspect_ratio: '21:9', // CORRIGIDO: 16:9 não é aceito pelo Replicate, usar 21:9 para banner
        created_at: new Date().toISOString()
      }

      console.log(' Salvando capa principal em cover_images:', insertData)

      const { data, error } = await supabase
        .from('cover_images')
        .insert(insertData)
        .select()

      if (error) {
        console.error('Erro ao salvar cover image no banco:', error)
        throw error
      }

      console.log(' Capa principal salva no banco com sucesso:', data)
      showToast.success('Capa principal salva no dashboard! ')
      
    } catch (error) {
      console.error('Erro ao salvar cover image no banco:', error)
      showToast.warning('Cover image criada, mas não foi salva no dashboard')
      // Não falha o processo todo, só loga o erro
    }
  }

  const generateCoverImage = async (useBackupPrompt = false, attemptNumber = 1, specificMovieTitle?: string) => {
    try {
      const { data: { session } } = await supabase.auth.getSession()
      const authToken = session?.access_token || import.meta.env.VITE_SUPABASE_ANON_KEY
      
      const apiUrl = `${import.meta.env.VITE_SUPABASE_URL}/functions/v1/generate-single-cover`

      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          movieTitle: specificMovieTitle || `Capa Principal ${streamingPlatform}`,
          originalImageUrl,
          streamingPlatform,
          photoType: attemptNumber > 0 ? 'cover' : photoType, // Forçar tipo 'cover' para capa principal
          aspectRatio: '21:9', // CORRIGIDO: 16:9 não é aceito pelo Replicate, usar 21:9 para banner
          userId: session?.user?.id,
          generationId: currentGenerationId || crypto.randomUUID(),
          position: attemptNumber || 1,
          gender,
          language,
          generateTitles,
          creativityLevelId: creativityLevel, // Enviar como ID conforme esperado
          useBackup: useBackupPrompt, // Corrigido nome do parâmetro
          testMode: false // Não é teste, é geração real
        })
      })

      const data = await response.json()
      console.log(' Resposta da API generate-single-cover:', data) // LOG PARA DEBUG

      if (!response.ok) {
        const errorMessage = data.error || `Erro na geração de capa: ${response.status}`
        
        // CORREÇÃO: Verificar se details é array ou string
        let details = ''
        if (data.details) {
          if (Array.isArray(data.details)) {
            details = data.details.join(', ')
          } else if (typeof data.details === 'string') {
            details = data.details
          }
        }
        
        const fullError = details ? `${errorMessage} - ${details}` : errorMessage
        
        console.error('Erro detalhado na geração de capa:', data)
        
        // DETECTAR ERRO E005 E IMPLEMENTAR FALLBACK
        if (data.error && data.error.includes('E005')) {
          console.log(' Erro E005 detectado - conteúdo flagado como sensível')
          throw new Error('E005_SENSITIVE_CONTENT')
        }
        
        showToast.error(fullError)
        throw new Error(fullError)
      }
      
      if (data.image_url) {
        // ATUALIZAR ESTADO E FORÇAR RE-RENDER
        setGeneratedCover(data.image_url)
        console.log(' Estado generatedCover atualizado para:', data.image_url)
        
        // FORÇAR RE-RENDER IMEDIATO
        setRenderKey(prev => prev + 1)
        
        // ADICIONAR DADOS DA CAPA GERADA AO ESTADO
        const newGeneratedCover = {
          movieTitle: data.movie_title,
          imageUrl: data.image_url,
          position: attemptNumber
        }
        setGeneratedCovers(prev => [...prev, newGeneratedCover])
        
        console.log(' Dados da capa gerada adicionados ao estado:', newGeneratedCover)
      }
      
      console.log(' generateCoverImage retornando URL:', data.image_url)
      return data.image_url

    } catch (error) {
      console.error('Erro ao gerar imagem de capa:', error)
      
      // PROPAGAR ERRO E005 PARA O FALLBACK
      if (error instanceof Error && error.message === 'E005_SENSITIVE_CONTENT') {
        throw error
      }
      
      const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido'
      if (!errorMessage.includes('Erro na geração de capa')) {
        showToast.error(`Erro ao gerar capa: ${errorMessage}`)
      }
      return null
    }
  }

  // NOVA FUNÇÃO: Gerar capa com sistema de fallback para E005
  const generateCoverWithFallback = async (): Promise<string | null> => {
    const maxAttempts = 20 // Máximo de tentativas (10 filmes x 2 tentativas cada)
    let attempt = 1
    let currentMovieAttempt = 1 // Qual tentativa para o filme atual (1=normal, 2=safe)
    
    while (attempt <= maxAttempts) {
      try {
        console.log(` Tentativa ${attempt}/${maxAttempts} de geração de capa`)
        
        // Determinar se é prompt normal ou safe
        const isPromptSafe = currentMovieAttempt === 2
        
        // USAR FILMES SOBRA (não usados nas Series & Films) para capa principal
        const backupMovies = getBackupMovies()
        if (backupMovies.length === 0) {
          console.error(' Nenhum filme de backup disponível')
          return null
        }

        const movieIndex = Math.floor((attempt - 1) / 2) // Qual filme usar (0, 1, 2...)
        if (movieIndex >= backupMovies.length) break
        
        const movie = backupMovies[movieIndex]
        
        if (isPromptSafe) {
          console.log(` Tentativa ${attempt}: ${movie.title} com prompt SAFE`)
        } else {
          console.log(` Tentativa ${attempt}: ${movie.title} com prompt NORMAL`)
        }
        
        const result = await generateCoverImage(isPromptSafe, attempt, movie.title)
        
        if (result) {
          if (attempt > 1) {
            const strategy = isPromptSafe ? 
              `${movie.title} (prompt safe)` : 
              `${movie.title} (prompt normal)`
            showToast.success(` Capa gerada com sucesso usando ${strategy}!`)
          }
          return result
        }
        
        throw new Error('Resultado vazio')
        
      } catch (error) {
        console.error(` Tentativa ${attempt} falhou:`, error)
        
        if (error instanceof Error && error.message === 'E005_SENSITIVE_CONTENT') {
          if (attempt < maxAttempts) {
            // Determinar próxima estratégia
            if (currentMovieAttempt === 1) {
              // Próxima: mesmo filme com prompt safe
              currentMovieAttempt = 2
              const backupMovies = getBackupMovies()
              const movieIndex = Math.floor((attempt - 1) / 2)
              const currentMovie = backupMovies[movieIndex]?.title || 'filme atual'
              showToast.info(` Gerando capa para ${currentMovie} com prompt safe...`)
            } else {
              // Próxima: novo filme com prompt normal
              currentMovieAttempt = 1
              const backupMovies = getBackupMovies()
              const nextMovieIndex = Math.floor(attempt / 2)
              const nextMovie = backupMovies[nextMovieIndex]?.title || 'próximo filme'
              showToast.info(` Tentando novo filme: ${nextMovie}...`)
            }
            
            attempt++
            continue
          } else {
            showToast.error(' Não foi possível gerar a capa após múltiplas tentativas com diferentes filmes. Tente com outra foto.')
            return null
          }
        } else {
          // Erro diferente de E005 - não tentar novamente
          throw error
        }
      }
    }
    
    return null
  }

  const generateAvatarImage = async () => {
    try {
      const { data: { session } } = await supabase.auth.getSession()
      const authToken = session?.access_token || import.meta.env.VITE_SUPABASE_ANON_KEY
      
      const apiUrl = `${import.meta.env.VITE_SUPABASE_URL}/functions/v1/generate-avatar-image`

      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          imageUrl: originalImageUrl,
          photoType,
          aspectRatio: '1:1'
        })
      })

      const data = await response.json()

      if (!response.ok) {
        const errorMessage = data.error || `Erro na geração de avatar: ${response.status}`
        
        // CORREÇÃO: Verificar se details é array ou string
        let details = ''
        if (data.details) {
          if (Array.isArray(data.details)) {
            details = data.details.join(', ')
          } else if (typeof data.details === 'string') {
            details = data.details
          }
        }
        
        const fullError = details ? `${errorMessage} - ${details}` : errorMessage
        
        console.error('Erro detalhado na geração de avatar:', data)
        showToast.error(fullError)
        throw new Error(fullError)
      }

      return data.avatarUrl

    } catch (error) {
      console.error('Erro ao gerar imagem de avatar:', error)
      const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido'
      if (!errorMessage.includes('Erro na geração de avatar')) {
        showToast.error(`Erro ao gerar avatar: ${errorMessage}`)
      }
      return null
    }
  }

  const openModal = (coverUrl: string, index: number) => {
    setSelectedCover(coverUrl)
    setSelectedIndex(index)
  }

  const closeModal = () => {
    setSelectedCover(null)
  }

  const [movies, setMovies] = useState<any[]>([])

  useEffect(() => {
    const fetchMovies = async () => {
      if (!streamingPlatform) return

      const { data, error } = await supabase
        .from('unified_movies_series')
        .select('*')
        .eq('streaming_platform', streamingPlatform)

      if (error) {
        console.error('Error fetching movies for backup:', error)
      } else if (data) {
        setMovies(data)
        console.log(`[useEffect] Carregados ${data.length} filmes para a plataforma ${streamingPlatform}`)
      }
    }

    fetchMovies()
  }, [streamingPlatform])

  const getBackupMovies = () => {
    if (!movies || movies.length === 0 || !streamingPlatform) {
      console.log('[getBackupMovies] Condições iniciais não atendidas (movies, streamingPlatform).');
      return [];
    }

    // Filmes da plataforma selecionada
    const allPlatformMovies = movies.filter((m: any) => m.streaming_platform === streamingPlatform);

    // Títulos dos filmes já usados nos 9 covers gerados
    const usedMovieTitles = new Set(generatedCovers.map((c: any) => c.movieTitle));

    // Retorna os filmes da plataforma que NÃO estão na lista de usados
    const backupMovies = allPlatformMovies.filter((m: any) => !usedMovieTitles.has(m.title));

    console.log(`[getBackupMovies] Filmes de backup para ${streamingPlatform}:`, backupMovies.map((m: any) => m.title));
    return backupMovies;
  };

  const handleShowGallery = () => {
    setShowGallery(true)
  }

  const handleSelectFromGallery = (selectedUrls: string[]) => {
    const requiredQuantity = getRequiredQuantity()
    const currentCoversCount = allCovers.length
    
    // 🔥 NOVA LÓGICA: Usuário pode adicionar quantas imagens quiser
    // Simplesmente adiciona as imagens selecionadas à lista atual
    
    console.log(`📋 Adicionando ${selectedUrls.length} imagem(ns) da galeria às ${currentCoversCount} existentes`)
    
    // Adiciona as imagens da galeria às capas existentes (nunca sobrescreve)
    const newAllCovers = [...allCovers, ...selectedUrls]
    setAllCovers(newAllCovers)
    
    // 🔥 MELHORIA: Não seleciona automaticamente - deixa o usuário escolher
    // Apenas mostra quantas foram adicionadas
    showToast.success(`✅ ${selectedUrls.length} imagem(ns) adicionada(s) da galeria! Total: ${newAllCovers.length} capas disponíveis. Agora escolha ${requiredQuantity} capas para criar seu poster ${streamingPlatform?.toUpperCase()}.`)
    
    setShowGallery(false)
    
    // Força re-render para mostrar as novas capas
    setRenderKey(prev => prev + 1)
  }

  // Função para escolher da galeria para um slot específico
  const handleSelectFromGalleryForSlot = (slotIndex: number) => {
    // TODO: Implementar modal de galeria específico para um slot
    // Por enquanto, usar a funcionalidade existente
    setShowGallery(true)
    showToast.info(`Selecione uma capa da galeria para o slot ${slotIndex + 1}`)
  }

  // NOVA FUNÇÃO: Buscar filmes disponíveis (não gerados)
  const getAvailableMovies = async (includeCurrentTitle?: string) => {
    console.log('🎬 GET AVAILABLE MOVIES DEBUG - includeCurrentTitle:', includeCurrentTitle)
    console.log('🎬 GET AVAILABLE MOVIES DEBUG - streamingPlatform:', streamingPlatform)
    
    if (!streamingPlatform) return []
    
    try {
      const { data: { session } } = await supabase.auth.getSession()
      if (!session) return []

      // Buscar filmes da plataforma
      const { data: movies, error } = await supabase
        .from('unified_movies_series')
        .select('title')
        .eq('streaming_platform', streamingPlatform)

      if (error) {
        console.error('Error fetching movies:', error)
        return []
      }

      console.log('🎬 GET AVAILABLE MOVIES DEBUG - Total filmes na plataforma:', movies?.length)

      // 🔥 BUSCAR TÍTULOS JÁ GERADOS PARA FILTRAR CORRETAMENTE
      const { data: generation, error: genError } = await supabase
        .from('cover_generations')
        .select('generated_covers')
        .eq('id', currentGenerationId)
        .single()

      let generatedTitles: string[] = []
      if (!genError && generation && generation.generated_covers) {
        generatedTitles = generation.generated_covers.map((c: any) => c.movie_title).filter(Boolean)
      }

      console.log('🎬 GET AVAILABLE MOVIES DEBUG - Títulos já gerados:', generatedTitles)

      // 🔥 CORREÇÃO: Para regeneração, mostrar apenas filmes NÃO gerados + o atual
      if (includeCurrentTitle) {
        console.log('🔥 MODO REGENERAÇÃO: Filtrando filmes não gerados + atual')
        
        // Identificar o título real sendo regenerado
        let actualCurrentTitle = includeCurrentTitle
        
        // Se o título é genérico "Posição X", tentar encontrar o título real
        if (includeCurrentTitle.startsWith('Posição ')) {
          const positionMatch = includeCurrentTitle.match(/Posição (\d+)/)
          if (positionMatch) {
            const position = parseInt(positionMatch[1]) - 1 // Converter para índice 0-based
            if (coverTitles[position]) {
              actualCurrentTitle = coverTitles[position]
              console.log(`🎯 Título real encontrado para ${includeCurrentTitle}: ${actualCurrentTitle}`)
            } else if (generatedCovers[position]) {
              actualCurrentTitle = generatedCovers[position].movieTitle
              console.log(`� Título real encontrado em generatedCovers para ${includeCurrentTitle}: ${actualCurrentTitle}`)
            }
          }
        }

        // Filtrar filmes não gerados
        let availableMovies = movies
          ?.filter(m => !generatedTitles.includes(m.title))
          ?.map(m => m.title) || []

        // Adicionar o título atual no início (se existir na base ou se for customizado)
        if (actualCurrentTitle && actualCurrentTitle !== includeCurrentTitle) {
          // Se o título real existe na base de dados
          const titleExistsInDB = movies?.some(m => m.title === actualCurrentTitle)
          if (titleExistsInDB) {
            // Remover da lista se já estiver lá e adicionar no início
            availableMovies = availableMovies.filter(title => title !== actualCurrentTitle)
            availableMovies.unshift(actualCurrentTitle)
            console.log('🎬 GET AVAILABLE MOVIES DEBUG - Título atual (real) adicionado no início:', actualCurrentTitle)
          }
        } else if (actualCurrentTitle) {
          // Se é um título customizado ou não encontrado na base
          const titleExistsInDB = movies?.some(m => m.title === actualCurrentTitle)
          if (titleExistsInDB) {
            // Remover da lista se já estiver lá e adicionar no início
            availableMovies = availableMovies.filter(title => title !== actualCurrentTitle)
            availableMovies.unshift(actualCurrentTitle)
            console.log('🎬 GET AVAILABLE MOVIES DEBUG - Título atual adicionado no início:', actualCurrentTitle)
          } else if (!actualCurrentTitle.startsWith('Posição ')) {
            // Só adiciona se não for um título genérico
            availableMovies.unshift(actualCurrentTitle)
            console.log('🎬 GET AVAILABLE MOVIES DEBUG - Título customizado adicionado:', actualCurrentTitle)
          }
        }

        console.log('🎬 GET AVAILABLE MOVIES DEBUG - Lista final de filmes (modo regeneração):', availableMovies.length)
        console.log('🎬 GET AVAILABLE MOVIES DEBUG - Primeiros 10 filmes:', availableMovies.slice(0, 10))
        return availableMovies
      }

      // 🔥 MODO NORMAL: Filtrar apenas filmes não gerados
      let availableMovies = movies
        ?.filter(m => !generatedTitles.includes(m.title))
        ?.map(m => m.title) || []

      console.log('🎬 GET AVAILABLE MOVIES DEBUG - Lista final de filmes disponíveis (modo normal):', availableMovies.length)
      console.log('🎬 GET AVAILABLE MOVIES DEBUG - Primeiros 5 filmes:', availableMovies.slice(0, 5))

      return availableMovies
    } catch (error) {
      console.error('Error fetching available movies:', error)
      return []
    }
  }



  // NOVA FUNÇÃO: Abrir modal de erro
  const handleOpenErrorModal = async (index: number) => {
    setErrorModalIndex(index)
    setSelectedMovie('')
    setCustomPrompt('')
    setCustomTitle('')
    setUseCustomPrompt(false)
    
    const movies = await getAvailableMovies()
    setAvailableMovies(movies)
    setShowErrorModal(true)
  }
  
  // NOVA FUNÇÃO: Abrir modal de regeneração
  const handleOpenRegenerateModal = async (index: number) => {
    console.log(`🔄 REGENERATE MODAL DEBUG - Clicou no item visual ${index + 1} (índice ${index})`)
    console.log('🔄 REGENERATE MODAL DEBUG - generatedCovers:', generatedCovers)
    console.log('🔄 REGENERATE MODAL DEBUG - coverTitles:', coverTitles)
    console.log('🔄 REGENERATE MODAL DEBUG - allCovers length:', allCovers.length)
    
    setRegenerateModalIndex(index)
    setSelectedMovie('')
    setCustomPrompt('')
    setCustomTitle('')
    setUseCustomPrompt(false)
    
    // 🔥 IDENTIFICAR O TÍTULO REAL DA CAPA SENDO REGENERADA
    let currentTitle = ''
    
    // Método 1: Buscar em coverTitles (mais confiável)
    if (coverTitles && coverTitles[index]) {
      currentTitle = coverTitles[index]
      setCurrentRegeneratingTitle(currentTitle)
      console.log(`🔄 REGENERATE MODAL DEBUG - Título encontrado em coverTitles[${index}]:`, currentTitle)
    } 
    // Método 2: Buscar em generatedCovers
    else if (generatedCovers && generatedCovers[index]) {
      currentTitle = generatedCovers[index].movieTitle || `Posição ${index + 1}`
      setCurrentRegeneratingTitle(currentTitle)
      console.log(`🔄 REGENERATE MODAL DEBUG - Título encontrado em generatedCovers[${index}]:`, currentTitle)
    } 
    // Método 3: Buscar na última geração do banco de dados
    else {
      try {
        const { data: { session } } = await supabase.auth.getSession()
        if (session && currentGenerationId) {
          const { data: generation } = await supabase
            .from('cover_generations')
            .select('generated_covers')
            .eq('id', currentGenerationId)
            .single()
          
          if (generation?.generated_covers && generation.generated_covers[index]) {
            currentTitle = generation.generated_covers[index].movie_title || `Posição ${index + 1}`
            setCurrentRegeneratingTitle(currentTitle)
            console.log(`🔄 REGENERATE MODAL DEBUG - Título encontrado no banco para [${index}]:`, currentTitle)
          } else {
            currentTitle = `Posição ${index + 1}`
            setCurrentRegeneratingTitle(currentTitle)
            console.log('🔄 REGENERATE MODAL DEBUG - Usando posição como título (banco vazio):', currentTitle)
          }
        } else {
          currentTitle = `Posição ${index + 1}`
          setCurrentRegeneratingTitle(currentTitle)
          console.log('🔄 REGENERATE MODAL DEBUG - Usando posição como título (sem sessão/geração):', currentTitle)
        }
      } catch (error) {
        console.error('Erro ao buscar título no banco:', error)
        currentTitle = `Posição ${index + 1}`
        setCurrentRegeneratingTitle(currentTitle)
        console.log('🔄 REGENERATE MODAL DEBUG - Usando posição como título (erro):', currentTitle)
      }
    }
    
    // Buscar filmes disponíveis (incluindo o atual)
    console.log('🔄 REGENERATE MODAL DEBUG - Chamando getAvailableMovies com:', currentTitle)
    const movies = await getAvailableMovies(currentTitle)
    console.log('🔄 REGENERATE MODAL DEBUG - Filmes retornados:', movies)
    console.log('🔄 REGENERATE MODAL DEBUG - Número de filmes:', movies.length)
    
    setAvailableMovies(movies)
    setShowRegenerateModal(true)
  }

  // NOVA FUNÇÃO: Gerar com escolha do modal de regeneração
  const handleRegenerateFromModal = async () => {
    if (!originalImageUrl || !streamingPlatform) return
    
    setIsGeneratingFromModal(true)
    
    try {
      const { data: { session } } = await supabase.auth.getSession()
      if (!session) throw new Error('Não autenticado')

      // CRIAR GENERATION ID SE NÃO EXISTIR
      let effectiveGenerationId = currentGenerationId
      if (!effectiveGenerationId) {
        effectiveGenerationId = crypto.randomUUID()
        console.log(`🆕 Created new generation ID for regenerate modal: ${effectiveGenerationId}`)
        // 🔥 Salvar o ID no estado através de props ou context se necessário
      }

      const position = regenerateModalIndex + 1
      const aspectRatio = getCoverAspectRatio(streamingPlatform, regenerateModalIndex)
      
      console.log(`🔄 REGENERATE DEBUG - Regenerando posição ${regenerateModalIndex} (visual ${position})`)
      console.log(`🔄 REGENERATE DEBUG - Generation ID: ${effectiveGenerationId}`)
      
      const requestBody = {
        movieTitle: useCustomPrompt ? (customTitle || 'Custom Movie') : selectedMovie,
        originalImageUrl,
        streamingPlatform,
        photoType,
        aspectRatio,
        userId: session.user.id,
        generationId: effectiveGenerationId,
        position,
        gender,
        language,
        generateTitles,
        creativityLevel,
        userName,
        useBackup: true,
        testMode: false,
        isManualRegeneration: true,
        customPrompt: useCustomPrompt ? customPrompt : undefined
      }

      console.log('🔄 REGENERATE DEBUG - Request body:', requestBody)

      const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/generate-single-cover`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody)
      })

      if (!response.ok) {
        const errorText = await response.text()
        throw new Error(`API Error: ${errorText}`)
      }

      const result = await response.json()
      console.log('🔄 REGENERATE DEBUG - API Response:', result)

      if (result.success) {
        // Adicionar nova capa na posição correta
        const newCovers = [...allCovers]
        newCovers[regenerateModalIndex] = result.image_url
        setAllCovers(newCovers)

        // Atualizar coverTitles também
        const newCoverTitles = [...coverTitles]
        newCoverTitles[regenerateModalIndex] = result.movie_title
        setCoverTitles(newCoverTitles)

        // Adiciona os detalhes da capa gerada ao estado
        const newGeneratedCover = {
          movieTitle: result.movie_title,
          imageUrl: result.image_url,
          position: regenerateModalIndex
        }
        setGeneratedCovers(prev => [...prev.filter(c => c.position !== regenerateModalIndex), newGeneratedCover])
        
        console.log(`✅ REGENERATE SUCCESS - ${result.movie_title} salva na posição ${regenerateModalIndex}`)
        showToast.success(`✅ ${result.movie_title} regenerada com sucesso!`)
        setShowRegenerateModal(false)
        
        // 🔥 FORÇAR ATUALIZAÇÃO IMEDIATA DO COMPONENTE MyGenerations
        console.log('🔄 Disparando evento generationUpdated para atualizar MyGenerations')
        window.dispatchEvent(new CustomEvent('generationUpdated', {
          detail: { 
            type: 'regenerate',
            generationId: effectiveGenerationId,
            movieTitle: result.movie_title,
            imageUrl: result.image_url,
            position: regenerateModalIndex,
            streamingPlatform: streamingPlatform,
            timestamp: new Date().toISOString()
          }
        }))
        
        // 🔥 AGUARDAR UM POUCO E DISPARAR NOVAMENTE PARA GARANTIR ATUALIZAÇÃO
        setTimeout(() => {
          console.log('🔄 Disparando segundo evento generationUpdated (delayed)')
          window.dispatchEvent(new CustomEvent('generationUpdated', {
            detail: { 
              type: 'regenerate_delayed',
              generationId: effectiveGenerationId,
              movieTitle: result.movie_title,
              imageUrl: result.image_url,
              position: regenerateModalIndex,
              streamingPlatform: streamingPlatform,
              timestamp: new Date().toISOString()
            }
          }))
        }, 1500)
        
        // DISPARAR EVENTO PARA ATUALIZAR CRÉDITOS NO HEADER
        window.dispatchEvent(new CustomEvent('creditConsumed', {
          detail: { movieTitle: result.movie_title, creditsUsed: 1 }
        }))
      } else {
        throw new Error(result.error || 'Erro na regeneração')
      }
    } catch (error) {
      console.error('Error regenerating from modal:', error)
      showToast.error(`Erro: ${error instanceof Error ? error.message : 'Erro desconhecido'}`)
    } finally {
      setIsGeneratingFromModal(false)
    }
  }
  
  // NOVA FUNÇÃO: Gerar com escolha do modal
  const handleGenerateFromModal = async () => {
    if (!originalImageUrl || !streamingPlatform) return
    
    setIsGeneratingFromModal(true)
    
    try {
      const { data: { session } } = await supabase.auth.getSession()
      if (!session) throw new Error('Não autenticado')

      // CRIAR GENERATION ID SE NÃO EXISTIR
      let effectiveGenerationId = currentGenerationId
      if (!effectiveGenerationId) {
        effectiveGenerationId = crypto.randomUUID()
        console.log(` Created new generation ID for modal: ${effectiveGenerationId}`)
      }

      const position = errorModalIndex + 1
      const aspectRatio = getCoverAspectRatio(streamingPlatform, errorModalIndex)
      
      const requestBody = {
        movieTitle: useCustomPrompt ? (customTitle || 'Custom Movie') : selectedMovie,
        originalImageUrl,
        streamingPlatform,
        photoType,
        aspectRatio,
        userId: session.user.id,
        generationId: effectiveGenerationId, // USAR ID VÁLIDO
        position,
        gender,
        language,
        generateTitles,
        creativityLevel,
        userName, // INCLUIR NOME DO USUÁRIO
        useBackup: true,
        testMode: false,
        isManualRegeneration: true, // Permitir duplicatas
        customPrompt: useCustomPrompt ? customPrompt : undefined
      }

      const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/generate-single-cover`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody)
      })

      if (!response.ok) {
        const errorText = await response.text()
        throw new Error(`API Error: ${errorText}`)
      }

      const result = await response.json()

      if (result.success) {
        // Adicionar nova capa
        const newCovers = [...allCovers]
        newCovers[errorModalIndex] = result.image_url
        setAllCovers(newCovers)

        // Adiciona os detalhes da capa gerada ao estado
        const newGeneratedCover = {
          movieTitle: result.movie_title,
          imageUrl: result.image_url,
          position: errorModalIndex
        }
        setGeneratedCovers(prev => [...prev.filter(c => c.position !== errorModalIndex), newGeneratedCover])
        
        showToast.success(`✅ ${result.movie_title} gerada com sucesso!`)
        setShowErrorModal(false) // FECHAR MODAL AUTOMATICAMENTE
        
        // DISPARAR EVENTO PARA ATUALIZAR CRÉDITOS NO HEADER
        window.dispatchEvent(new CustomEvent('creditConsumed', {
          detail: { movieTitle: result.movie_title, creditsUsed: 1 }
        }))
        
        // Atualizar progresso se a função existir
        if (progress) {
          // Progresso será atualizado pelo componente pai
          console.log('✅ Progresso atualizado: +1 completado, -1 falha')
        }
        
        // Remover da lista de falhas se for prop controlada
        if (failedIndices.has(errorModalIndex)) {
          console.log(`✅ Removendo índice ${errorModalIndex} da lista de falhas`)
          // A atualização será feita pelo componente pai
        }
      } else {
        throw new Error(result.error || 'Erro na geração')
      }
    } catch (error) {
      console.error('Error generating from modal:', error)
      showToast.error(`Erro: ${error instanceof Error ? error.message : 'Erro desconhecido'}`)
    } finally {
      setIsGeneratingFromModal(false)
    }
  }

  const handleGenerateNewMovie = async (slotIndex: number) => {
    if (!onRegenerateIndividual) {
      // Se não há função de regeneração, abrir modal de erro
      handleOpenErrorModal(slotIndex)
      return
    }

    // Obter próximo filme disponível, excluindo os que já falharam
    const nextMovie = getNextAvailableMovie()
    if (!nextMovie) {
      showToast.error('Todos os filmes já foram gerados ou falharam!')
      return
    }

    try {
      const { data: { session } } = await supabase.auth.getSession()
      if (!session) {
        showToast.error('Você precisa estar logado')
        return
      }

      // 🔥 CRIAR GENERATION ID SE NÃO EXISTIR
      let effectiveGenerationId = currentGenerationId
      if (!effectiveGenerationId) {
        effectiveGenerationId = crypto.randomUUID()
        console.log(`🆕 Created new generation ID: ${effectiveGenerationId}`)
      }

      // Adicionar ao estado de regeneração
      setLocalRegeneratingIndices((prev: Set<number>) => new Set(prev).add(slotIndex))
      showToast.info(`Gerando capa para: ${nextMovie}`)

      // Chamar a edge function para gerar uma nova capa individual com filme específico
      const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/generate-single-cover`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          movieTitle: nextMovie, // Especificar qual filme gerar
          originalImageUrl,
          streamingPlatform,
          photoType,
          aspectRatio: getCoverAspectRatio(streamingPlatform, slotIndex),
          userId: session.user.id,
          generationId: effectiveGenerationId, // 🔥 USAR ID VÁLIDO
          position: slotIndex + 1, // Position é 1-indexed
          // 🔥 INCLUIR CONFIGURAÇÕES DA GERAÇÃO ORIGINAL
          gender,
          language,
          generateTitles,
          creativityLevel,
          userName, // 🔥 INCLUIR NOME DO USUÁRIO
          useBackup: true, // 🔥 ATIVAR SISTEMA DE FALLBACK E005
          testMode: false // 🔥 EXPLICITAMENTE DEFINIR COMO MODO NORMAL (consome créditos)
        }),
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: `HTTP ${response.status}` }))
        
        // 🔥 TRATAMENTO ESPECÍFICO POR STATUS HTTP
        if (response.status === 402) {
          // Pagamento necessário = Sem créditos
          showToast.error(`💳 Sem créditos! ${errorData.message || 'Compre mais créditos para continuar'}`)
          setTimeout(() => {
            window.dispatchEvent(new CustomEvent('openCreditsModal'))
          }, 2000)
        } else if (response.status === 401) {
          showToast.error('🔒 Sessão expirada. Faça login novamente.')
        } else {
          showToast.error(`❌ Erro ${response.status}: ${errorData.error || 'Erro desconhecido'}`)
        }
        
        return
      }

      const result = await response.json()
      console.log('📥 Resposta da API:', result)

      // 🔥 VERIFICAR SE REALMENTE TEM SUCESSO E URL VÁLIDA
      if (result.success && result.image_url) {
        // Validar se a URL é válida
        const isValidUrl = (url: string) => {
          try {
            new URL(url)
            return !url.includes('example.com') && !url.includes('placehold.co')
          } catch {
            return false
          }
        }

        if (!isValidUrl(result.image_url)) {
          console.error('❌ URL inválida retornada:', result.image_url)
          showToast.error('Erro: URL de imagem inválida retornada')
          return
        }

        // 🔥 NOVA CAPA GERADA COM SUCESSO - Atualizar interface
        let successMessage = `🎬 ${result.movie_title || nextMovie} gerada com sucesso!`
        
        // 🔥 DISPARAR EVENTO PARA ATUALIZAR CRÉDITOS NO HEADER
        window.dispatchEvent(new CustomEvent('creditConsumed', {
          detail: { movieTitle: result.movie_title || nextMovie, creditsUsed: 1 }
        }))
        
        // 🔥 FEEDBACK ESPECÍFICO SOBRE FALLBACK
        if (result.fallback_used && result.fallback_movie) {
          successMessage = `🎲 ${result.fallback_movie} gerada com sucesso! (${nextMovie} teve erro E005)`
        } else if (result.safe_prompt_used) {
          successMessage = `🛡️ ${result.movie_title || nextMovie} gerada com prompt seguro!`
        }
        
        showToast.success(successMessage)
        
        // Adicionar o filme REALMENTE gerado ao estado (pode ser diferente do solicitado)
        const actualMovie = result.movie_title || nextMovie
        setGeneratedMovies(prev => new Set(prev).add(actualMovie))
        
        // 🔥 ATUALIZAR APENAS LOCALMENTE - NÃO FAZER REFRESH COMPLETO
        // Adicionar a nova capa diretamente ao array de capas
        const newCoverUrl = result.image_url
        const updatedCovers = [...allCovers]
        
        // Se a posição (slotIndex) está dentro do array existente, substitui
        if (slotIndex < updatedCovers.length) {
          updatedCovers[slotIndex] = newCoverUrl
        } else {
          // Caso contrário, adiciona no final
          updatedCovers.push(newCoverUrl)
        }
        
        // Emitir evento para atualizar o estado pai
        window.dispatchEvent(new CustomEvent('coversGenerated', {
          detail: { covers: updatedCovers }
        }))
        
        console.log(`✅ Capa adicionada localmente na posição ${slotIndex}`)
        
        // REMOVIDO: Não chamar refreshCurrentGeneration para evitar poluir com capas antigas
        // if (refreshCovers || refreshCurrentGeneration) {
        //   setTimeout(() => {
        //     console.log('🔄 Calling refresh function...')
        //     if (refreshCurrentGeneration && currentGenerationId) {
        //       console.log('🎯 Using refreshCurrentGeneration for current generation')
        //       refreshCurrentGeneration(currentGenerationId)
        //     } else if (refreshCovers) {
        //       console.log('🔄 Using refreshCovers fallback')
        //       refreshCovers()
        //     }
        //   }, 500)
        // }
      } else {
        // 🔥 FEEDBACK ESPECÍFICO SOBRE FALHAS
        let errorMessage = `❌ Erro ao gerar ${nextMovie}`
        
        // 🔥 VERIFICAR SE É ERRO DE CRÉDITOS
        if (result.error?.includes('Créditos insuficientes') || result.availableCredits !== undefined) {
          errorMessage = `💳 Sem créditos! Você tem ${result.availableCredits || 0} créditos. Necessário: ${result.requiredCredits || 1}`
          showToast.error(errorMessage)
          
          // Abrir modal de compra de créditos após 2 segundos
          setTimeout(() => {
            window.dispatchEvent(new CustomEvent('openCreditsModal'))
          }, 2000)
          
        } else if (result.error?.includes('E005')) {
          errorMessage = `🚫 ${nextMovie} foi bloqueado (E005). Tentando próximo filme...`
          showToast.warning(errorMessage)
          
          // Tentar próximo filme automaticamente, adicionando o filme atual à lista de exclusão
          setTimeout(() => {
            handleGenerateNewMovie(slotIndex)
          }, 1000)
          
        } else {
          errorMessage = `❌ Erro ao gerar ${nextMovie}: ${result.error || 'Erro desconhecido'}`
          showToast.error(errorMessage)
        }
      }

    } catch (error) {
      console.error('❌ Erro ao gerar nova capa:', error)
      const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido'
      
      // 🔥 NÃO MOSTRAR MENSAGEM GENÉRICA SE JÁ FOI TRATADO ACIMA
      if (!errorMessage.includes('HTTP')) {
        showToast.error(`Erro ao gerar nova capa: ${errorMessage}`)
      }
    } finally {
      // Remover do estado de regeneração após um tempo
      setTimeout(() => {
        setLocalRegeneratingIndices((prev: Set<number>) => {
          const newSet = new Set(prev)
          newSet.delete(slotIndex)
          return newSet
        })
      }, 3000)
    }
  }

  const handleRegenerateFailedCover = async (index: number) => {
    if (!onRegenerateIndividual) {
      // Se não há função de regeneração, abrir modal de erro
      handleOpenErrorModal(index)
      return
    }

    // Call the regeneration function
    const addRegeneratedCoverAtIndex = (newCoverUrl: string) => {
      addRegeneratedCover(newCoverUrl, index)
    }
    onRegenerateIndividual(index, addRegeneratedCoverAtIndex)
    showToast.info('Regenerando capa... A capa será substituída na posição correta!', {
      duration: 3000
    })
  }

  const generatePosterOnly = async () => {
    if (!canGeneratePoster()) {
      showToast.warning(`Selecione pelo menos ${getRequiredQuantity()} capas para gerar o poster do ${streamingPlatform?.toUpperCase()}.`)
      return
    }

    if (!generatedCover) {
      showToast.error('Capa principal não encontrada. Gere a capa primeiro.')
      return
    }

    setIsGeneratingPoster(true)
    setShowPosterStatus(true)

    try {
      // Define o ID do template baseado no streaming usando configuração centralizada
      const templateId = streamingPlatform ? getTemplateId(streamingPlatform) : 'EAGqDjHn_M4'

      // Texto personalizado com concordância verbal
      const isCouple = photoType === 'casal'
      const verb = isCouple ? 'são transportados' : 'é transportado'
      const pronoun = isCouple ? 'eles vivem' : 'vive'
      
      const platformName = streamingPlatform ? streamingPlatform.charAt(0).toUpperCase() + streamingPlatform.slice(1) : 'Disney'
      const customDescription = `${userName || 'Caio'} ${verb} para diferentes séries da ${platformName}, onde ${pronoun} como o protagonista, enfrentando aventuras únicas em cada episódio enquanto tenta voltar para sua realidade.`

      // 🔥 CORREÇÃO: Busca URLs do Supabase em vez de usar URLs temporárias do Replicate
      const supabaseCovers = await getSupabaseCoversForPoster();
      console.log('🔍 URLs do Supabase para poster (modo rápido):', supabaseCovers);

      // 🔥 PAYLOAD ADAPTATIVO BASEADO NA PLATAFORMA (generatePosterOnly)
      const payload: any = {
        ID: templateId,
        CAPA: generatedCover, // Usa a capa já gerada
        AVATAR: originalImageUrl,
        PHOTO_01: supabaseCovers[0] || "https://placehold.co/600x400",
        PHOTO_02: supabaseCovers[1] || "https://placehold.co/600x400",
        PHOTO_03: supabaseCovers[2] || "https://placehold.co/600x400",
        PHOTO_04: supabaseCovers[3] || "https://placehold.co/600x400",
        PHOTO_05: supabaseCovers[4] || "https://placehold.co/600x400",
        PHOTO_06: supabaseCovers[5] || "https://placehold.co/600x400",
        PHOTO_07: supabaseCovers[6] || "https://placehold.co/600x400",
        PHOTO_08: supabaseCovers[7] || "https://placehold.co/600x400",
        PHOTO_09: supabaseCovers[8] || "https://placehold.co/600x400",
        TITLE: `Poster ${streamingPlatform?.toUpperCase() || 'STREAMING'} - ${userName || 'Personalizado'}`,
        DESCRIPTION: customDescription,
        CONTINUE: 'Clique para continuar'
      };

      // 🔥 ADICIONAR FOTOS EXTRAS APENAS PARA NETFLIX E AMAZON (generatePosterOnly)
      if (streamingPlatform === 'netflix') {
        // Netflix precisa de 12 fotos (PHOTO_01 até PHOTO_12)
        payload.PHOTO_10 = supabaseCovers[9] || "https://placehold.co/600x400";
        payload.PHOTO_11 = supabaseCovers[10] || "https://placehold.co/600x400";
        payload.PHOTO_12 = supabaseCovers[11] || "https://placehold.co/600x400";
      } else if (streamingPlatform === 'amazon') {
        // Amazon precisa de 11 fotos (PHOTO_01 até PHOTO_11)
        payload.PHOTO_10 = supabaseCovers[9] || "https://placehold.co/600x400";
        payload.PHOTO_11 = supabaseCovers[10] || "https://placehold.co/600x400";
      }
      // Disney só precisa de 9 fotos (PHOTO_01 até PHOTO_09) - não adiciona mais nada

      console.log('🚀 Gerando poster apenas (URLs do Supabase):', payload)

      // Chama o webhook direto do frontend
      const webhookResponse = await fetch('https://automate.felvieira.com.br/webhook/criar-design-canva', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'key': 'n8ncanvaapicall'
        },
        body: JSON.stringify(payload)
      })

      if (!webhookResponse.ok) {
        throw new Error(`Erro no webhook: ${webhookResponse.status}`)
      }

      const webhookResult = await webhookResponse.json()
      console.log('✅ Resposta do webhook (poster apenas):', webhookResult)
      
      // Fecha o modal de status
      setShowPosterStatus(false)
      
      // Define a URL do poster final
      const posterUrl = webhookResult.design_url || webhookResult.url || null
      
      if (posterUrl) {
        setFinalPoster(posterUrl)
        await savePosterToDatabase(posterUrl)
        showToast.success('Poster criado com sucesso! 🎉 Clique para abrir no Canva.')
        window.open(posterUrl, '_blank')
      } else {
        showToast.success('Poster enviado para processamento! 🎨 Verifique seu Canva.')
      }

    } catch (error) {
      console.error('Erro ao gerar poster apenas:', error)
      
      // Mostra erro
      showToast.error('Falha ao gerar poster. Tente novamente.')
      setShowPosterStatus(false)
    } finally {
      setIsGeneratingPoster(false)
    }
  }

  // Função auxiliar para determinar a proporção da capa
  const getCoverAspectRatio = (platform: string | undefined, index: number): string => {
    if (platform === 'disney') {
      // 🔥 DISNEY: 6 imagens em 16:9 e 3 imagens em 5:4
      // Primeiras 6: 16:9 (widescreen)
      // Próximas 3: 5:4 (quase quadrado)
      return index < 6 ? '16:9' : '5:4';
    }
    return '3:4'; // Netflix e Amazon usam 3:4 (retrato)
  };

  // Função para obter lista de filmes do streaming atual
  const getMovieList = (): string[] => {
    if (!streamingPlatform) return []
    
    const platformMovies = movieDatabase[streamingPlatform as keyof typeof movieDatabase]
    if (!platformMovies) return []
    
    // Extrair apenas os títulos dos filmes
    return platformMovies.map(movie => movie.title)
  }

  // Função para obter próximo filme disponível
  const getNextAvailableMovie = (moviesToExclude: string[] = []): string | null => {
    const allMovies = getMovieList()
    const moviesToAttempt = allMovies.filter(
      (movie: string) => !generatedMovies.has(movie) && !moviesToExclude.includes(movie)
    )
    
    if (moviesToAttempt.length === 0) {
      return null // Todos os filmes já foram gerados ou tentados
    }
    
    // Retorna o primeiro filme disponível
    return moviesToAttempt[0]
  }

  // Atualizar lista de filmes gerados quando allCovers muda
  useEffect(() => {
    // Extrair títulos dos filmes das URLs das capas geradas
    const movieTitles = new Set<string>()
    
    allCovers.forEach((coverUrl: string) => {
      // Extrair título do filme da URL (assumindo formato: /covers/timestamp-movie-title-platform.jpg)
      const urlParts = coverUrl.split('/')
      const filename = urlParts[urlParts.length - 1]
      const titlePart = filename.split('-').slice(1, -1).join('-') // Remove timestamp e platform
      
      // Converter de slug para título (exemplo: "round-6-squid-game" -> "Round 6 (Squid Game)")
      const allMovies = getMovieList()
      const matchedMovie = allMovies.find((movie: string) => 
        movie.toLowerCase().replace(/[^a-z0-9]/g, '-') === titlePart ||
        movie.toLowerCase().includes(titlePart.replace(/-/g, ' '))
      )
      
      if (matchedMovie) {
        movieTitles.add(matchedMovie)
      }
    })
    
    setGeneratedMovies(movieTitles)
    console.log('🎬 Filmes já gerados:', Array.from(movieTitles))
  }, [allCovers, streamingPlatform])

  // 🔥 LISTENER PARA ATUALIZAÇÃO AUTOMÁTICA DE CAPAS
  useEffect(() => {
    const handleCoversGenerated = (event: CustomEvent) => {
      console.log('🔔 CoverGrid: Received coversGenerated event', event.detail)
      const { covers: newCovers } = event.detail
      
      if (newCovers && newCovers.length > 0) {
        console.log('🔄 Updating covers from event:', newCovers.length)
        setAllCovers([...newCovers]) // Força nova referência
        
        // REMOVIDO: Não chamar refresh para evitar trazer capas antigas
        // O evento já contém as capas atualizadas, não precisamos buscar do banco
        // if (refreshCovers || refreshCurrentGeneration) {
        //   setTimeout(() => {
        //     console.log('🔄 Calling refresh function...')
        //     if (refreshCurrentGeneration && currentGenerationId) {
        //       console.log('🎯 Using refreshCurrentGeneration for current generation')
        //       refreshCurrentGeneration(currentGenerationId)
        //     } else if (refreshCovers) {
        //       console.log('🔄 Using refreshCovers fallback')
        //       refreshCovers()
        //     }
        //   }, 500)
        // }
      }
    }

    window.addEventListener('coversGenerated', handleCoversGenerated as EventListener)
    
    return () => {
      window.removeEventListener('coversGenerated', handleCoversGenerated as EventListener)
    }
  }, [refreshCovers, refreshCurrentGeneration, currentGenerationId])

  // 🔥 AUTO-SELECIONAR CAPAS QUANDO TODAS FOREM GERADAS
  useEffect(() => {
    if (!isLoading && allCovers.length > 0 && progress && progress.completed === progress.total && selectedCovers.size === 0) {
      // Auto-selecionar todas as capas disponíveis até o limite necessário
      const requiredQty = getRequiredQuantity()
      const newSelection = new Set<number>()
      
      for (let i = 0; i < Math.min(allCovers.length, requiredQty); i++) {
        if (allCovers[i]) {
          newSelection.add(i)
        }
      }
      
      setSelectedCovers(newSelection)
      console.log(`🔥 Auto-selecionadas ${newSelection.size} capas`)
    }
  }, [isLoading, allCovers.length, progress])

  // 🔥 LISTENER PARA ATUALIZAR CAPAS AUTOMATICAMENTE
  useEffect(() => {
    const handleCoversUpdate = (event: CustomEvent) => {
      console.log('🔥 Evento de atualização de capas recebido:', event.detail)
      if (event.detail?.covers && Array.isArray(event.detail.covers)) {
        setAllCovers(event.detail.covers)
        setRenderKey(prev => prev + 1) // Força re-render
      }
    }

    const handleCoverGenerated = (event: CustomEvent) => {
      console.log('🔥 Evento de capa individual gerada:', event.detail)
      if (event.detail?.coverUrl && event.detail?.type === 'main') {
        setGeneratedCover(event.detail.coverUrl)
        setRenderKey(prev => prev + 1) // Força re-render
        console.log('🔍 Capa principal atualizada via evento:', event.detail.coverUrl)
      }
    }

    window.addEventListener('coversGenerated', handleCoversUpdate as EventListener)
    window.addEventListener('coverGenerated', handleCoverGenerated as EventListener)
    
    return () => {
      window.removeEventListener('coversGenerated', handleCoversUpdate as EventListener)
      window.removeEventListener('coverGenerated', handleCoverGenerated as EventListener)
    }
  }, [])

  // 🔥 REMOVIDO: useEffect duplicado que causava loop infinito

  if (isLoading) {
    const loadingQuantity = expectedQuantity || 2
    
    return (
      <div className="w-full py-12">
        {/* Progress Bar */}
        {progress && (
          <GenerationProgressBar 
            progress={progress} 
            isGenerating={isLoading}
            currentMovie={currentMovie}
          />
        )}
        
        <div className="text-center mb-8 max-w-2xl mx-auto">
          <div className="w-20 h-20 bg-brand-primary mx-auto rounded-2xl flex items-center justify-center border-2 border-brand-black mb-6 shadow-brutal">
            <Loader2 className="w-10 h-10 animate-spin text-brand-black" />
          </div>
          <h3 className="text-3xl font-black text-brand-text mb-2">
            Gerando Seus Covers de Filme
          </h3>
          <p className="text-brand-text/80 mb-6">
            Estamos criando {loadingQuantity} capas em paralelo. Isso pode levar alguns minutos.
          </p>
        </div>
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
          {Array.from({ length: loadingQuantity }).map((_, index) => {
            const isCompleted = progress && index < progress.completed
            const isFailed = progress && failedIndices.has(index)
            const isInProgress = progress && index < (progress.completed + progress.inProgress) && !isCompleted && !isFailed
            const coverUrl = covers[index] || null // Pegar a URL da capa se existir
            
            return (
              <div 
                key={index} 
                className={`
                  aspect-[2/3] rounded-2xl border-2 border-brand-black overflow-hidden transition-all duration-300
                  ${isCompleted && !coverUrl ? 'bg-green-200' : 
                    isFailed ? 'bg-red-200' :
                    isInProgress ? 'bg-blue-200 animate-pulse' :
                    'bg-gray-200 animate-pulse'
                  }
                `}
              >
                {/* Se tiver a imagem da capa, mostrar ela */}
                {isCompleted && coverUrl ? (
                  <img
                    src={coverUrl}
                    alt={`Capa ${index + 1}`}
                    className="w-full h-full object-cover"
                    loading="lazy"
                  />
                ) : isCompleted && !coverUrl ? (
                  // Se completou mas não tem URL, mostrar check
                  <div className="w-full h-full flex items-center justify-center">
                    <Check className="w-10 h-10 text-green-700" />
                  </div>
                ) : isFailed ? (
                  <div className="w-full h-full flex items-center justify-center">
                    <X className="w-10 h-10 text-red-700" />
                  </div>
                ) : isInProgress ? (
                  <div className="w-full h-full flex items-center justify-center">
                    <Loader2 className="w-10 h-10 animate-spin text-blue-700" />
                  </div>
                ) : (
                  <div className="w-full h-full flex items-center justify-center">
                    <div className="text-gray-500 font-bold text-lg">#{index + 1}</div>
                  </div>
                )}
              </div>
            )
          })}
        </div>
        

      </div>
    )
  }

  if (allCovers.length === 0 && !isLoading) {
    // Mostrar placeholders vazios se expectedQuantity for definido (modo galeria)
    if (expectedQuantity && expectedQuantity > 0) {
      const requiredQuantity = getRequiredQuantity()
      
      return (
        <div className="w-full">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 space-y-4 sm:space-y-0">
            <div>
              <h3 className="text-2xl font-bold text-brand-text">
                Adicione Suas Fotos da Galeria
              </h3>
              <p className="text-brand-text/70 text-sm mt-1">
                Clique nos espaços abaixo para adicionar fotos da galeria ou gerar com IA
              </p>
            </div>
            
            <div className="flex space-x-3">
              <button
                onClick={handleShowGallery}
                className="bg-brand-secondary text-brand-black px-4 py-2 rounded-md font-bold border-2 border-brand-black shadow-brutal-sm hover:shadow-brutal-hover active:shadow-brutal-pressed transition-all flex items-center gap-2"
              >
                <GalleryHorizontal size={16} />
                Escolher da Galeria
              </button>
            </div>
          </div>
          
          {/* Grid de placeholders vazios */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            {Array.from({ length: requiredQuantity }).map((_, index) => (
              <motion.div 
                key={`placeholder-${index}`} 
                className="group relative"
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: index * 0.1 }}
              >
                <div className="aspect-[2/3] bg-gray-100 rounded-2xl overflow-hidden cursor-pointer transition-all duration-300 relative border-2 border-dashed border-gray-400 hover:border-brand-primary hover:bg-gray-50">
                  <div className="w-full h-full flex flex-col items-center justify-center p-4">
                    <ImageIcon className="w-12 h-12 text-gray-400 mb-2" />
                    <p className="text-sm text-gray-500 mb-3 text-center">
                      Slot {index + 1}
                    </p>
                    <div className="flex flex-col space-y-2 w-full">
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          handleOpenErrorModal(index)
                        }}
                        className="w-full px-3 py-2 rounded-md text-xs transition-colors flex items-center justify-center space-x-1 border-2 border-brand-black font-bold bg-brand-primary text-brand-black hover:bg-brand-accent"
                      >
                        <RotateCcw size={14} />
                        <span>Gerar com IA</span>
                      </button>
                      
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          handleSelectFromGalleryForSlot(index)
                        }}
                        className="w-full px-3 py-2 rounded-md text-xs transition-colors flex items-center justify-center space-x-1 border-2 border-brand-black font-bold bg-brand-white text-brand-black hover:bg-gray-50"
                      >
                        <GalleryHorizontal size={14} />
                        <span>Da Galeria</span>
                      </button>
                    </div>
                  </div>
                  
                  <div className="absolute top-2 left-2 w-8 h-8 bg-black/70 rounded-full flex items-center justify-center border-2 border-white/50 text-xs font-bold text-white pointer-events-none">
                    {index + 1}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      )
    }
    
    return null
  }

  const requiredQuantity = getRequiredQuantity()

  // 🔥 DEBUG: Log do estado atual da capa principal
  console.log('🔍 CoverGrid renderizando - generatedCover:', generatedCover)
  console.log('🔍 CoverGrid renderizando - finalPoster:', finalPoster)
  console.log('🔍 CoverGrid renderizando - renderKey:', renderKey)

  return (
    <>
      <div className="w-full">
        {/* Seção de Imagens Criadas - Movido para o topo */}
        {(finalPoster || generatedCover) && (
          <div className="mb-8 p-6 bg-brand-white rounded-lg border-2 border-brand-black shadow-brutal-lg">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h3 className="text-xl font-bold text-brand-text">🎉 Imagens Criadas!</h3>
                <p className="text-sm text-brand-text/70">Suas imagens personalizadas estão prontas</p>
              </div>
              <button
                onClick={() => {
                  setFinalPoster(null)
                  setGeneratedCover(null)
                }}
                className="p-2 bg-brand-white rounded-lg border-2 border-brand-black shadow-brutal-sm hover:shadow-brutal-hover transition-all duration-200"
              >
                <X size={20} className="text-brand-text" />
              </button>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Cover Image */}
              {generatedCover && (
                <div className="space-y-4">
                  <div className="text-center">
                    <h4 className="text-lg font-bold text-brand-text mb-2">Capa Principal</h4>
                    <div className="relative group">
                      <img
                        src={generatedCover}
                        alt="Capa Principal"
                        className="w-full max-h-64 object-cover rounded-lg border-2 border-brand-black cursor-pointer hover:shadow-brutal-hover transition-all duration-200"
                        onClick={() => setSelectedMainCover(generatedCover)}
                      />
                      <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100">
                        <span className="text-white font-bold">Clique para ampliar</span>
                      </div>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="flex space-x-2">
                      <button
                        onClick={() => window.open(generatedCover, '_blank')}
                        className="flex-1 bg-brand-secondary text-brand-black py-2 px-3 rounded-lg font-bold border-2 border-brand-black shadow-brutal-sm hover:shadow-brutal-hover transition-all duration-200 flex items-center justify-center space-x-2"
                      >
                        <ExternalLink size={16} />
                        <span>Visualizar</span>
                      </button>
                      <button
                        onClick={() => handleDownloadCover(generatedCover, 'capa-principal')}
                        className="bg-brand-white text-brand-text py-2 px-3 rounded-lg font-bold border-2 border-brand-black shadow-brutal-sm hover:shadow-brutal-hover transition-all duration-200 flex items-center justify-center"
                        title="Download"
                      >
                        <Download size={16} />
                      </button>
                    </div>
                    
                    {/* Botão para regenerar apenas a capa principal */}
                    <button
                      onClick={async () => {
                        try {
                          setIsGeneratingCover(true)
                          const newCover = await generateCoverWithFallback()
                          if (newCover) {
                            setGeneratedCover(newCover)
                            showToast.success('Capa principal regenerada com sucesso!')
                          } else {
                            showToast.error('Erro ao regenerar capa principal')
                          }
                        } catch (error) {
                          console.error('Error regenerating main cover:', error)
                          showToast.error('Erro ao regenerar capa principal')
                        } finally {
                          setIsGeneratingCover(false)
                        }
                      }}
                      disabled={isGeneratingCover}
                      className={`
                        w-full py-2 px-3 rounded-lg font-bold border-2 border-brand-black shadow-brutal-sm hover:shadow-brutal-hover transition-all duration-200 flex items-center justify-center space-x-2
                        ${isGeneratingCover ? 'bg-gray-300 text-gray-500 cursor-not-allowed' : 'bg-brand-accent text-brand-black'}
                      `}
                      title="Regenera a capa principal usando as mesmas configurações"
                    >
                      {isGeneratingCover ? (
                        <>
                          <Loader2 size={16} className="animate-spin" />
                          <span>Regenerando...</span>
                        </>
                      ) : (
                        <>
                          <RefreshCw size={16} />
                          <span>Regenerar Capa Principal</span>
                        </>
                      )}
                    </button>
                  </div>
                </div>
              )}

              {/* Poster Final */}
              {finalPoster && (
                <div className="space-y-4">
                  <div className="text-center">
                    <h4 className="text-lg font-bold text-brand-text mb-2">Poster Final</h4>
                    <div className="relative group">
                      {/* 🔥 VERIFICAR SE É URL DE DOWNLOAD DIRETO (PNG/JPG) OU LINK DO CANVA */}
                      {finalPoster.includes('export-download.canva.com') || finalPoster.match(/\.(png|jpg|jpeg|webp)(\?|$)/i) ? (
                        <img
                          src={finalPoster}
                          alt="Poster Final"
                          className="w-full max-h-64 object-cover rounded-lg border-2 border-brand-black cursor-pointer hover:shadow-brutal-hover transition-all duration-200"
                          onClick={() => setSelectedFinalPoster(finalPoster)}
                          onError={(e) => {
                            // Se a imagem falhar, mostrar o placeholder do Canva
                            e.currentTarget.style.display = 'none';
                            const nextElement = e.currentTarget.nextElementSibling as HTMLElement;
                            if (nextElement) {
                              nextElement.style.display = 'flex';
                            }
                          }}
                        />
                      ) : null}
                      <div 
                        className={`w-full h-64 bg-brand-secondary rounded-lg border-2 border-brand-black flex items-center justify-center cursor-pointer hover:shadow-brutal-hover transition-all duration-200 ${
                          finalPoster.includes('export-download.canva.com') || finalPoster.match(/\.(png|jpg|jpeg|webp)(\?|$)/i) ? 'hidden' : ''
                        }`}
                        onClick={() => window.open(finalPoster, '_blank')}
                      >
                        <div className="text-center">
                          <Printer size={48} className="text-brand-black mx-auto mb-2" />
                          <p className="font-bold text-brand-black">Poster no Canva</p>
                          <p className="text-sm text-brand-black/70">Clique para abrir</p>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="flex space-x-2">
                      {/* 🔥 BOTÕES ADAPTATIVOS BASEADOS NO TIPO DE URL */}
                      {finalPoster.includes('export-download.canva.com') || finalPoster.match(/\.(png|jpg|jpeg|webp)(\?|$)/i) ? (
                        // URLs de download direto
                        <>
                          <button
                            onClick={handleDownloadPoster}
                            className="flex-1 bg-brand-primary text-brand-black py-2 px-3 rounded-lg font-bold border-2 border-brand-black shadow-brutal-sm hover:shadow-brutal-hover transition-all duration-200 flex items-center justify-center space-x-2"
                          >
                            <Download size={16} />
                            <span>Baixar Poster</span>
                          </button>
                          <button
                            onClick={() => window.open(finalPoster, '_blank')}
                            className="bg-brand-white text-brand-text py-2 px-3 rounded-lg font-bold border-2 border-brand-black shadow-brutal-sm hover:shadow-brutal-hover transition-all duration-200 flex items-center justify-center"
                            title="Visualizar"
                          >
                            <ExternalLink size={16} />
                          </button>
                        </>
                      ) : (
                        // Links do Canva
                        <>
                          <button
                            onClick={() => window.open(finalPoster, '_blank')}
                            className="flex-1 bg-brand-primary text-brand-black py-2 px-3 rounded-lg font-bold border-2 border-brand-black shadow-brutal-sm hover:shadow-brutal-hover transition-all duration-200 flex items-center justify-center space-x-2"
                          >
                            <ExternalLink size={16} />
                            <span>Abrir no Canva</span>
                          </button>
                          <button
                            onClick={handleDownloadPoster}
                            className="bg-brand-white text-brand-text py-2 px-3 rounded-lg font-bold border-2 border-brand-black shadow-brutal-sm hover:shadow-brutal-hover transition-all duration-200 flex items-center justify-center"
                            title="Download"
                          >
                            <Download size={16} />
                          </button>
                        </>
                      )}
                    </div>
                    
                    {/* Botão para regenerar apenas o poster usando a capa existente */}
                    {generatedCover && canGeneratePoster() && (
                      <button
                        onClick={generatePosterOnly}
                        disabled={isGeneratingPoster}
                        className={`
                          w-full py-2 px-3 rounded-lg font-bold border-2 border-brand-black shadow-brutal-sm hover:shadow-brutal-hover transition-all duration-200
                          ${isGeneratingPoster ? 'bg-gray-300 text-gray-500 cursor-not-allowed' : 'bg-brand-accent text-brand-black'}
                        `}
                        title="Regenera o poster usando a mesma capa"
                      >
                        {isGeneratingPoster ? (
                          <>
                            <Loader2 size={16} className="animate-spin" />
                            <span>Regenerando...</span>
                          </>
                        ) : (
                          <>
                            <RefreshCw size={16} />
                            <span>Regenerar Poster</span>
                          </>
                        )}
                      </button>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Seção de Geração do Poster */}
        {allCovers.length > 0 && (
          <div className="mb-8 p-6 bg-brand-white rounded-2xl border-2 border-brand-black shadow-brutal">
            <div className="flex items-center justify-between mb-4">
                          <h3 className="text-xl font-bold text-brand-text">
              Selecione os Covers para o Poster
            </h3>
              <div className="text-sm text-brand-text">
                {(() => {
                  const requiredQuantity = getRequiredQuantity()
                  const currentCoversCount = allCovers.length
                  const selectedCount = selectedCovers.size
                  const missingCovers = Math.max(0, requiredQuantity - currentCoversCount)
                  
                  if (missingCovers > 0) {
                    return (
                      <span className="text-orange-600 font-bold">
                        {selectedCount} selecionadas • Faltam {missingCovers} capas
                      </span>
                    )
                  } else {
                    return (
                      <span className={selectedCount === requiredQuantity ? "text-green-600 font-bold" : "text-brand-text"}>
                        {selectedCount} de {requiredQuantity} selecionadas
                      </span>
                    )
                  }
                })()}
              </div>
            </div>
            
            {(() => {
              const requiredQuantity = getRequiredQuantity()
              const currentCoversCount = allCovers.length
              const missingCovers = Math.max(0, requiredQuantity - currentCoversCount)
              
              if (missingCovers > 0) {
                return (
                  <div className="bg-orange-100 rounded-lg p-4 border-2 border-brand-black mb-4 shadow-brutal-sm">
                    <p className="text-sm text-orange-900 font-semibold">
                      <strong>⚠️ Atenção:</strong> Você tem apenas <strong>{currentCoversCount} de {requiredQuantity}</strong> covers necessários. 
                      <strong> Faltam {missingCovers} covers</strong> para gerar o poster completo.
                      <br />
                      <span className="text-orange-700">
                        💡 Use o botão "Escolher da Galeria" para adicionar mais {missingCovers} imagem(ns) ou regenere covers individuais.
                      </span>
                    </p>
                  </div>
                )
              } else {
                return (
                  <div className="bg-blue-100 rounded-lg p-4 border-2 border-brand-black mb-4 shadow-brutal-sm">
                    <p className="text-sm text-blue-900 font-semibold">
                      <strong>📋 Instruções:</strong> Selecione exatamente <strong>{requiredQuantity} capas</strong> para criar seu poster do {streamingPlatform?.toUpperCase() || 'streaming'}. 
                      Você pode adicionar quantas capas quiser da galeria e depois escolher as {requiredQuantity} melhores para o poster.
                    </p>
                  </div>
                )
              }
            })()}

            <div className="w-full bg-brand-white rounded-full h-4 p-1 border-2 border-brand-black shadow-brutal-sm mb-6">
              <div 
                className="bg-brand-primary h-full rounded-full transition-all duration-300 border border-brand-black"
                style={{ width: `${(selectedCovers.size / getRequiredQuantity()) * 100}%` }}
              />
            </div>
            
            {/* 🔥 BOTÕES DE AÇÃO */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              {/* Botão Gerar Capa */}
              {(() => {
                console.log('🔍 Debug botão:', {
                  selectedCoversSize: selectedCovers.size,
                  generatedCover: !!generatedCover,
                  shouldShow: selectedCovers.size > 0 && !generatedCover
                })
                return selectedCovers.size > 0 && !generatedCover
              })() && (
                <button
                  onClick={async (e) => {
                    e.preventDefault()
                    e.stopPropagation()
                    console.log('💆 Botão Gerar Capa Principal clicado!')
                    console.log('💆 Elemento clicado:', e.target)
                    console.log('💆 Chamando handleOpenMainCoverModal...')
                    await handleOpenMainCoverModal()
                    console.log('💆 handleOpenMainCoverModal finalizada')
                  }}
                  disabled={isGeneratingCover || isGeneratingMainFromModal}
                  className={`
                    px-6 py-3 rounded-lg font-bold border-2 border-brand-black shadow-brutal-sm 
                    hover:shadow-brutal-hover active:shadow-brutal-pressed transition-all duration-200
                    flex items-center justify-center space-x-2
                    ${(isGeneratingCover || isGeneratingMainFromModal) 
                      ? 'bg-gray-300 text-gray-500 cursor-not-allowed' 
                      : 'bg-brand-secondary text-brand-black'
                    }
                  `}
                >
                  {(isGeneratingCover || isGeneratingMainFromModal) ? (
                    <>
                      <Loader2 className="w-5 h-5 animate-spin" />
                      <span>Gerando Capa...</span>
                    </>
                  ) : (
                    <>
                      <ImageIcon className="w-5 h-5" />
                      <span>Gerar Capa Principal</span>
                    </>
                  )}
                </button>
              )}
              
              {/* Botão Gerar Poster */}
              {canGeneratePoster() && generatedCover && (
                <button
                  onClick={handleGenerateFinalPoster}
                  disabled={isGeneratingPoster}
                  className={`
                    px-6 py-3 rounded-lg font-bold border-2 border-brand-black shadow-brutal-sm 
                    hover:shadow-brutal-hover active:shadow-brutal-pressed transition-all duration-200
                    flex items-center justify-center space-x-2
                    ${isGeneratingPoster 
                      ? 'bg-gray-300 text-gray-500 cursor-not-allowed' 
                      : 'bg-brand-primary text-brand-black'
                    }
                  `}
                >
                  {isGeneratingPoster ? (
                    <>
                      <Loader2 className="w-5 h-5 animate-spin" />
                      <span>Gerando Poster...</span>
                    </>
                  ) : (
                    <>
                      <Printer className="w-5 h-5" />
                      <span>Gerar Poster Final</span>
                    </>
                  )}
                </button>
              )}
            </div>
          </div>
        )}

        {allCovers.length > 0 && (
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 space-y-4 sm:space-y-0">
            <div>
              <h3 className="text-2xl font-bold text-brand-text">
                Seus Covers Gerados ({allCovers.length})
              </h3>
              {(() => {
                const requiredQuantity = getRequiredQuantity()
                const currentCoversCount = allCovers.length
                const selectedCount = selectedCovers.size
                const missingCovers = Math.max(0, requiredQuantity - currentCoversCount)
                
                if (missingCovers > 0) {
                  return (
                    <p className="text-orange-600 font-bold text-sm mt-1">
                      ⚠️ Faltam {missingCovers} covers para completar o template {streamingPlatform?.toUpperCase()}
                    </p>
                  )
                } else {
                  return (
                    <p className="text-green-600 font-bold text-sm mt-1">
                      ✅ Template {streamingPlatform?.toUpperCase()} completo!
                    </p>
                  )
                }
              })()}
            </div>
            
            <div className="flex space-x-3">
              {/* 🔥 NOVO: Botão Selecionar/Deselecionar Todas */}
              {allCovers.length > 0 && (
                <button
                  onClick={isAllSelected() ? deselectAllCovers : selectAllCovers}
                  className="bg-brand-primary text-brand-black px-4 py-2 rounded-md font-bold border-2 border-brand-black shadow-brutal-sm hover:shadow-brutal-hover active:shadow-brutal-pressed transition-all flex items-center gap-2"
                  title={isAllSelected() ? 'Deselecionar todas as capas' : `Selecionar até ${getRequiredQuantity()} capas`}
                >
                  {isAllSelected() ? (
                    <>
                      <X size={16} />
                      Deselecionar Todas
                    </>
                  ) : (
                    <>
                      <Check size={16} />
                      Selecionar Todas ({Math.min(allCovers.length, getRequiredQuantity())})
                    </>
                  )}
                </button>
              )}
              
              <button
                onClick={handleDownloadAll}
                disabled={allCovers.length === 0}
                className="bg-brand-secondary text-brand-black px-4 py-2 rounded-md font-bold border-2 border-brand-black shadow-brutal-sm hover:shadow-brutal-hover active:shadow-brutal-pressed transition-all flex items-center gap-2"
              >
                <Download size={16} />
                {t('coverGrid.downloadAll')} ({allCovers.length})
              </button>
              
              <button
                onClick={handleShowGallery}
                className="bg-brand-secondary text-brand-black px-4 py-2 rounded-md font-bold border-2 border-brand-black shadow-brutal-sm hover:shadow-brutal-hover active:shadow-brutal-pressed transition-all flex items-center gap-2"
              >
                <GalleryHorizontal size={16} />
                {t('coverGrid.chooseFromGallery')}
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Grid das Capas */}
      {allCovers.length > 0 && (
        <div 
          key={`cover-grid-${renderKey}`} // 🔥 KEY DINÂMICO para forçar re-render
          className="grid grid-cols-2 md:grid-cols-4 gap-6"
        >
          {Array.from({ length: Math.max(getRequiredQuantity(), allCovers.length) }).map((_, index) => {
            const coverUrl = allCovers[index] || null
            const isSelected = selectedCovers.has(index)
            const isRegenerating = regeneratingIndex === index || regeneratingIndices.has(index) || localRegeneratingIndices.has(index)
            const isMissing = !coverUrl && !isRegenerating && index < getRequiredQuantity()
            const aspectRatio = getCoverAspectRatio(streamingPlatform, index)
            
            return (
              <motion.div 
                key={`slot-${index}`} 
                className="group relative"
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: index * 0.1 }}
              >
                <div className={`
                  aspect-[2/3] bg-brand-white rounded-2xl overflow-hidden cursor-pointer transition-all duration-300 relative border-2 border-brand-black
                  ${isSelected 
                    ? 'shadow-brutal-pressed scale-[0.98] ring-4 ring-brand-primary ring-offset-2' 
                    : 'shadow-brutal hover:shadow-brutal-lg'
                  }
                `}>
                  {isRegenerating ? (
                    <div className="w-full h-full bg-brand-white flex items-center justify-center">
                      <div className="text-center">
                        <Loader2 className="w-8 h-8 animate-spin text-brand-primary mx-auto mb-2" />
                        <p className="text-sm text-brand-text">Regenerando...</p>
                      </div>
                    </div>
                  ) : coverUrl ? (
                    <div className="relative w-full h-full">
                      <img
                        src={coverUrl}
                        alt={`Capa gerada ${index + 1}`}
                        className="w-full h-full object-cover"
                        loading="lazy"
                        onClick={() => toggleCoverSelection(index)}
                      />
                      {/* 🔥 OVERLAY COM NOME DO FILME */}
                      {coverTitles[index] && (
                        <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-2">
                          <p className="text-white text-xs font-bold text-center truncate">
                            {coverTitles[index]}
                          </p>
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="w-full h-full bg-gray-100 flex flex-col items-center justify-center border-2 border-dashed border-gray-400 rounded-lg m-2">
                      <ImageIcon className="w-12 h-12 text-gray-400 mb-2" />
                      <p className="text-sm text-gray-500 mb-3 text-center px-2">
                        {isMissing ? 'Capa não gerada' : 'Slot vazio'}
                      </p>
                      <div className="flex flex-col space-y-2 w-full px-2">
                        <button
                          onClick={(e) => {
                            e.stopPropagation()
                            handleOpenErrorModal(index)
                          }}
                          disabled={regeneratingIndices.has(index) || localRegeneratingIndices.has(index)}
                          className={`w-full px-3 py-2 rounded-md text-xs transition-colors flex items-center justify-center space-x-1 border-2 border-brand-black font-bold ${
                            regeneratingIndices.has(index) || localRegeneratingIndices.has(index)
                              ? 'bg-gray-400 text-gray-200 cursor-not-allowed' 
                              : 'bg-brand-primary text-brand-black hover:bg-brand-accent'
                          }`}
                        >
                          <RotateCcw size={14} />
                          <span>{regeneratingIndices.has(index) || localRegeneratingIndices.has(index) ? 'Gerando...' : 'Gerar Filme'}</span>
                        </button>
                        
                        <button
                          onClick={(e) => {
                            e.stopPropagation()
                            handleSelectFromGalleryForSlot(index)
                          }}
                          className="w-full px-3 py-2 rounded-md text-xs transition-colors flex items-center justify-center space-x-1 border-2 border-brand-black font-bold bg-brand-white text-brand-black hover:bg-gray-50"
                        >
                          <GalleryHorizontal size={14} />
                          <span>Da Galeria</span>
                        </button>
                      </div>
                    </div>
                  )}
                  
                  {isSelected && !isRegenerating && coverUrl && (
                    <div className="absolute top-2 right-2 w-8 h-8 bg-brand-primary rounded-full flex items-center justify-center border-2 border-brand-black pointer-events-none shadow-brutal-sm">
                      <Check size={20} className="text-white" />
                    </div>
                  )}
                  
                  <div className="absolute top-2 left-2 w-8 h-8 bg-black/70 rounded-full flex items-center justify-center border-2 border-white/50 text-xs font-bold text-white pointer-events-none">
                    {index + 1}
                  </div>
                </div>
                
                {!isRegenerating && coverUrl && (
                  <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-70 transition-all duration-300 rounded-xl flex items-center justify-center opacity-0 group-hover:opacity-100 p-2">
                    <div className="flex flex-col space-y-3 w-full">
                      <button
                          onClick={(e) => {
                            e.stopPropagation();
                            toggleCoverSelection(index);
                          }}
                          className={`
                            w-full py-2 px-3 rounded-lg text-sm font-bold transition-all duration-200 flex items-center justify-center space-x-2 border-2 border-brand-black
                            ${isSelected 
                              ? 'bg-brand-primary text-brand-black shadow-brutal-sm' 
                              : 'bg-brand-white text-brand-text shadow-brutal-sm hover:shadow-brutal-pressed'
                            }
                          `}
                        >
                          <Check size={16} />
                          <span>{isSelected ? 'Selecionada' : 'Selecionar'}</span>
                        </button>
                      <div className="flex space-x-2">
                        <button
                          onClick={(e) => { e.stopPropagation(); openModal(coverUrl, index); }}
                          className="w-full h-12 bg-brand-white rounded-lg border-2 border-brand-black shadow-brutal-sm flex items-center justify-center" title="Visualizar">
                          <ImageIcon size={20} className="text-brand-black" />
                        </button>
                        
                        <button
                          onClick={(e) => { e.stopPropagation(); handleDownloadCover(coverUrl, `capa-${index + 1}`); }}
                          className="w-full h-12 bg-brand-white rounded-lg border-2 border-brand-black shadow-brutal-sm flex items-center justify-center" title="Baixar">
                          <Download size={20} className="text-brand-black" />
                        </button>
                        
                        <button
                          onClick={(e) => { e.stopPropagation(); if (!regeneratingIndices.has(index)) handleOpenRegenerateModal(index); }}
                          disabled={regeneratingIndices.has(index)}
                          className={`w-full h-12 rounded-lg border-2 border-brand-black shadow-brutal-sm flex items-center justify-center ${regeneratingIndices.has(index) ? 'bg-gray-400' : 'bg-brand-white'}`} title={regeneratingIndices.has(index) ? 'Regenerando...' : 'Regenerar capa'}>
                          <RotateCcw size={20} className="text-brand-black" />
                        </button>
                      </div>
                      
                      {/* 🔥 NOVO BOTÃO: Definir como Capa Principal */}
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleSetAsMainCover(coverUrl);
                        }}
                        className="w-full py-2 px-3 rounded-lg text-sm font-bold bg-green-500 text-white border-2 border-brand-black shadow-brutal-sm hover:shadow-brutal-hover active:shadow-brutal-pressed transition-all flex items-center justify-center space-x-2"
                        title="Definir como Capa Principal"
                      >
                        <Wand2 size={16} />
                        <span>Definir como Capa</span>
                      </button>
                    </div>
                  </div>
                )}
              </motion.div>
            )
          })}
        </div>
      )}

      {/* Modal Melhorado com estilo do Dashboard */}
      <AnimatePresence>
        {selectedCover && selectedIndex !== null && (
          <motion.div 
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4"
            onClick={closeModal}
          >
            <motion.div 
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="relative bg-brand-white rounded-lg border-2 border-brand-black shadow-brutal-lg max-w-2xl w-full"
              onClick={(e) => e.stopPropagation()}
            >
              <button
                onClick={closeModal}
                className="absolute -top-4 -right-4 w-12 h-12 bg-brand-accent text-white rounded-full border-2 border-brand-black hover:bg-brand-accent-dark transition-colors z-10 shadow-brutal-sm flex items-center justify-center"
              >
                <X size={24} />
              </button>
              
              <div className="p-4">
                <img
                  src={selectedCover}
                  alt={`Capa gerada ${selectedIndex + 1}`}
                  className="w-full max-h-[70vh] object-contain rounded-md border-2 border-brand-black"
                />
              </div>
              
              <div className="p-4 border-t-2 border-brand-black flex justify-between items-center bg-gray-50">
                <div>
                  <h3 className="text-xl font-bold text-brand-text">
                    {coverTitles[selectedIndex] || `Capa #${selectedIndex + 1}`}
                  </h3>
                  <div className="flex items-center space-x-2 text-sm text-brand-text mt-1">
                    <span className="px-3 py-1 bg-brand-secondary text-brand-black rounded-md font-bold border-2 border-brand-black">
                      {streamingPlatform?.toUpperCase() || 'STREAMING'}
                    </span>
                    <span className="capitalize font-semibold">{photoType}</span>
                    {coverTitles[selectedIndex] && (
                      <span className="text-xs text-brand-text/60">#{selectedIndex + 1}</span>
                    )}
                  </div>
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={() => handleDownloadCover(selectedCover, `capa-${selectedIndex + 1}`)}
                    className="p-3 bg-brand-primary rounded-lg hover:bg-brand-accent-dark transition-colors shadow-brutal-sm border-2 border-brand-black flex items-center space-x-2 font-bold text-brand-black"
                    title="Baixar HD"
                  >
                    <Download size={20} />
                    <span>Baixar</span>
                  </button>
                  <button
                    onClick={() => {
                      toggleCoverSelection(selectedIndex)
                      closeModal()
                    }}
                    className={`p-3 rounded-lg transition-colors shadow-brutal-sm border-2 border-brand-black flex items-center space-x-2 font-bold ${
                      selectedCovers.has(selectedIndex)
                        ? 'bg-red-400 text-brand-black'
                        : 'bg-brand-white text-brand-black'
                    }`}
                    title={selectedCovers.has(selectedIndex) ? 'Remover Seleção' : 'Selecionar'}
                  >
                    <Check size={20} />
                    <span>{selectedCovers.has(selectedIndex) ? 'Selecionado' : 'Selecionar'}</span>
                  </button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* 🔥 NOVO MODAL: Ampliar Capa Principal */}
      <AnimatePresence>
        {selectedMainCover && (
          <motion.div 
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/90 backdrop-blur-sm flex items-center justify-center z-50 p-4"
            onClick={() => setSelectedMainCover(null)}
          >
            <motion.div 
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="relative max-w-4xl max-h-[90vh] overflow-hidden"
              onClick={(e) => e.stopPropagation()}
            >
              {/* Botão fechar */}
              <button
                onClick={() => setSelectedMainCover(null)}
                className="absolute -top-4 -right-4 w-12 h-12 bg-brand-white text-brand-black rounded-full border-2 border-brand-black hover:bg-gray-100 transition-colors z-10 shadow-lg flex items-center justify-center"
              >
                <X size={24} />
              </button>
              
              {/* Imagem ampliada */}
              <img
                src={selectedMainCover}
                alt="Capa Principal Ampliada"
                className="w-full h-full object-contain rounded-lg border-2 border-brand-black"
                style={{ maxHeight: '90vh' }}
              />
              
              {/* Botões de ação */}
              <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-3">
                <button
                  onClick={() => window.open(selectedMainCover, '_blank')}
                  className="px-4 py-2 bg-brand-white text-brand-black rounded-lg border-2 border-brand-black shadow-lg hover:bg-gray-100 transition-colors font-bold flex items-center space-x-2"
                >
                  <Eye className="w-4 h-4" />
                  <span>Abrir Original</span>
                </button>
                <button
                  onClick={() => {
                    handleDownloadCover(selectedMainCover, 'capa-principal')
                    showToast.success('Download iniciado!')
                  }}
                  className="px-4 py-2 bg-brand-primary text-brand-black rounded-lg border-2 border-brand-black shadow-lg hover:bg-brand-accent transition-colors font-bold flex items-center space-x-2"
                >
                  <Download className="w-4 h-4" />
                  <span>Baixar</span>
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Poster Status Modal */}
      <PosterStatus 
        isOpen={showPosterStatus}
        onClose={() => setShowPosterStatus(false)}
        totalImages={1}
        generatedImages={finalPoster ? 1 : 0}
        isGenerating={isGeneratingPoster}
                  templateName={t('coverGrid.printPosterTemplate')}
      />

      {/* Gallery Modal */}
      <AnimatePresence>
        {showGallery && (
          <motion.div 
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
            onClick={() => setShowGallery(false)}
          >
            <motion.div 
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-brand-white rounded-2xl p-6 max-w-6xl max-h-[90vh] overflow-y-auto shadow-brutal-lg border-2 border-brand-black w-full"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-2xl font-bold text-brand-text">Escolher Fotos da Galeria</h3>
                <button
                  onClick={() => setShowGallery(false)}
                  className="p-2 bg-brand-white rounded-lg border-2 border-brand-black hover:shadow-brutal-hover active:shadow-brutal-pressed transition-all duration-200"
                >
                  <X size={20} className="text-brand-text" />
                </button>
              </div>
              
              <div className="mb-4 p-4 bg-blue-100 rounded-lg border-2 border-brand-black">
                <p className="text-sm text-blue-900">
                  <strong>💡 Instruções:</strong> 
                  {(() => {
                    const requiredQuantity = getRequiredQuantity()
                    const currentCoversCount = allCovers.length
                    
                    return (
                      <>
                        Você tem <strong>{currentCoversCount}</strong> capas disponíveis. 
                        Selecione <strong>quantas imagens quiser</strong> da galeria para adicionar mais opções. 
                        Depois, escolha <strong>{requiredQuantity} capas</strong> para criar seu poster {streamingPlatform?.toUpperCase() || 'streaming'}.
                      </>
                    )
                  })()}
                </p>
              </div>

              <MyGenerations 
                selectionMode={true}
                maxSelection={undefined} // 🔥 REMOVIDO LIMITE: Permite selecionar quantas quiser
                onSelectImages={handleSelectFromGallery}
                onCancel={() => setShowGallery(false)}
                // 🔥 FILTROS REMOVIDOS TEMPORARIAMENTE PARA DEBUG
                // filterByStreaming={streamingPlatform}
                // filterByAspectRatio={true}
                showAspectRatio={true}
              />
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* 🔥 NOVO: Modal de Erro na Geração */}
      <BrutalistModal
        isOpen={showErrorModal}
        onClose={() => setShowErrorModal(false)}
        title="Gerar Filme - Escolha uma Opção"
        icon={<Film className="w-4 h-4 text-brand-black" />}
        maxWidth="max-w-md"
        maxHeight="max-h-[80vh]"
        footerActions={
          <div className="flex space-x-3">
            <button
              onClick={() => setShowErrorModal(false)}
              className="flex-1 px-4 py-2 bg-brand-white text-brand-black rounded-lg border-2 border-brand-black shadow-brutal-sm hover:shadow-brutal-hover active:shadow-brutal-pressed transition-all font-bold"
            >
              Cancelar
            </button>
            <button
              onClick={handleGenerateFromModal}
              disabled={isGeneratingFromModal || (!useCustomPrompt && !selectedMovie) || (useCustomPrompt && !customPrompt.trim())}
              className="flex-1 px-4 py-2 bg-red-500 text-white rounded-lg border-2 border-brand-black shadow-brutal-sm hover:shadow-brutal-hover active:shadow-brutal-pressed transition-all font-bold disabled:bg-gray-300 disabled:text-gray-500 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
            >
              {isGeneratingFromModal ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin" />
                  <span>Gerando...</span>
                </>
              ) : (
                <span>Gerar</span>
              )}
            </button>
          </div>
        }
      >
        <p className="text-sm text-brand-black/70 mb-6">
          Escolha um título disponível da plataforma ou digite um prompt customizado:
        </p>

        {/* Opções de escolha */}
        <div className="space-y-4 mb-6">
          <label className="flex items-center space-x-3 cursor-pointer">
            <input
              type="radio"
              name="promptType"
              checked={!useCustomPrompt}
              onChange={() => setUseCustomPrompt(false)}
              className="w-4 h-4 text-brand-primary"
            />
            <span className="text-sm font-bold text-brand-black">
              Escolher título disponível
            </span>
          </label>

          <label className="flex items-center space-x-3 cursor-pointer">
            <input
              type="radio"
              name="promptType"
              checked={useCustomPrompt}
              onChange={() => setUseCustomPrompt(true)}
              className="w-4 h-4 text-brand-primary"
            />
            <span className="text-sm font-bold text-brand-black">
              Usar prompt customizado
            </span>
          </label>
        </div>

        {/* Seleção de título */}
        {!useCustomPrompt && (
          <div className="mb-6">
            <label className="block text-sm font-bold text-brand-black mb-2">
              Títulos Disponíveis ({availableMovies.length})
            </label>
            <select
              value={selectedMovie}
              onChange={(e) => setSelectedMovie(e.target.value)}
              className="w-full px-3 py-2 border-2 border-brand-black rounded-lg shadow-brutal-sm focus:shadow-brutal-hover transition-all font-medium"
            >
              <option value="">Selecione um título...</option>
              {availableMovies.map((movie, index) => {
                const movieTitle = movie; // availableMovies agora é sempre string[]
                return (
                  <option key={`movie-${index}-${movieTitle}`} value={movieTitle}>
                    {movieTitle}
                  </option>
                );
              })}
            </select>
            {availableMovies.length === 0 && (
              <p className="text-xs text-brand-accent mt-1 font-medium">
                Todos os títulos já foram gerados. Use um prompt customizado.
              </p>
            )}
          </div>
        )}

        {/* Prompt customizado */}
        {useCustomPrompt && (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-bold text-brand-black mb-2">
                Título da Série/Filme
              </label>
              <input
                type="text"
                value={customTitle}
                onChange={(e) => setCustomTitle(e.target.value)}
                className="w-full px-3 py-2 border-2 border-brand-black rounded-lg shadow-brutal-sm focus:shadow-brutal-hover transition-all font-medium"
                placeholder="Ex: Breaking Bad, Game of Thrones, etc."
              />
            </div>
            <div>
              <label className="block text-sm font-bold text-brand-black mb-2">
                Prompt Customizado
              </label>
              <textarea
                value={customPrompt}
                onChange={(e) => setCustomPrompt(e.target.value)}
                rows={4}
                className="w-full px-3 py-2 border-2 border-brand-black rounded-lg shadow-brutal-sm focus:shadow-brutal-hover transition-all font-medium"
                placeholder="Digite seu prompt aqui... (será usado exatamente como digitado, sem concatenações)"
              />
              <p className="text-xs text-brand-black/60 mt-1">
                O prompt será enviado exatamente como você escreveu, sem modificações.
              </p>
            </div>
          </div>
        )}
      </BrutalistModal>
      
      {/* 🔥 NOVO: Modal de Regeneração */}
      <BrutalistModal
        isOpen={showRegenerateModal}
        onClose={() => setShowRegenerateModal(false)}
        title="Regenerar Capa - Escolha uma Opção"
        icon={<RefreshCw className="w-4 h-4 text-brand-black" />}
        maxWidth="max-w-2xl"
        maxHeight="max-h-[90vh]"
        footerActions={
          <div className="flex space-x-3">
            <button
              onClick={() => setShowRegenerateModal(false)}
              className="flex-1 px-4 py-2 bg-brand-white text-brand-black rounded-lg border-2 border-brand-black shadow-brutal-sm hover:shadow-brutal-hover active:shadow-brutal-pressed transition-all font-bold"
            >
              Cancelar
            </button>
            <button
              onClick={handleRegenerateFromModal}
              disabled={isGeneratingFromModal || (!useCustomPrompt && !selectedMovie) || (useCustomPrompt && !customPrompt.trim())}
              className="flex-1 px-4 py-2 bg-red-500 text-white rounded-lg border-2 border-brand-black shadow-brutal-sm hover:shadow-brutal-hover active:shadow-brutal-pressed transition-all font-bold disabled:bg-gray-300 disabled:text-gray-500 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
            >
              {isGeneratingFromModal ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin" />
                  <span>Regenerando...</span>
                </>
              ) : (
                <span>Regenerar</span>
              )}
            </button>
          </div>
        }
      >
        {/* Mostrar título da capa sendo regenerada */}
        {currentRegeneratingTitle && (
          <div className="mb-6 p-4 bg-brand-secondary border-2 border-brand-black rounded-lg shadow-brutal-sm">
            <p className="text-sm text-brand-black font-bold">
              🎬 Regenerando: {currentRegeneratingTitle}
            </p>
          </div>
        )}

        <p className="text-sm text-brand-black/70 mb-6">
          Escolha um título disponível da plataforma ou digite um prompt customizado para regenerar esta capa:
        </p>

        {/* Opções de escolha */}
        <div className="space-y-4 mb-6">
          <label className="flex items-center space-x-3 cursor-pointer">
            <input
              type="radio"
              name="regeneratePromptType"
              checked={!useCustomPrompt}
              onChange={() => setUseCustomPrompt(false)}
              className="w-4 h-4 text-brand-primary"
            />
            <span className="text-sm font-bold text-brand-black">
              Escolher título disponível
            </span>
          </label>

          <label className="flex items-center space-x-3 cursor-pointer">
            <input
              type="radio"
              name="regeneratePromptType"
              checked={useCustomPrompt}
              onChange={() => setUseCustomPrompt(true)}
              className="w-4 h-4 text-brand-primary"
            />
            <span className="text-sm font-bold text-brand-black">
              Usar prompt customizado
            </span>
          </label>
        </div>

        {/* Seleção de título */}
        {!useCustomPrompt && (
          <div className="mb-6">
            <label className="block text-sm font-bold text-brand-black mb-2">
              Títulos Disponíveis ({availableMovies.length})
            </label>
            <select
              value={selectedMovie}
              onChange={(e) => setSelectedMovie(e.target.value)}
              className="w-full px-3 py-2 border-2 border-brand-black rounded-lg shadow-brutal-sm focus:shadow-brutal-hover transition-all font-medium"
            >
              <option value="">Selecione um título...</option>
              {availableMovies.map((movie, index) => {
                const movieTitle = movie; // availableMovies agora é sempre string[]
                return (
                  <option key={`regenerate-movie-${index}-${movieTitle}`} value={movieTitle}>
                    {movieTitle}
                  </option>
                );
              })}
            </select>
            {availableMovies.length === 0 && (
              <p className="text-xs text-brand-accent mt-1 font-medium">
                Todos os títulos já foram gerados. Use um prompt customizado.
              </p>
            )}
          </div>
        )}

        {/* Prompt customizado */}
        {useCustomPrompt && (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-bold text-brand-black mb-2">
                Título da Série/Filme
              </label>
              <input
                type="text"
                value={customTitle}
                onChange={(e) => setCustomTitle(e.target.value)}
                className="w-full px-3 py-2 border-2 border-brand-black rounded-lg shadow-brutal-sm focus:shadow-brutal-hover transition-all font-medium"
                placeholder="Ex: Breaking Bad, Game of Thrones, etc."
              />
            </div>
            <div>
              <label className="block text-sm font-bold text-brand-black mb-2">
                Prompt Customizado
              </label>
              <textarea
                value={customPrompt}
                onChange={(e) => setCustomPrompt(e.target.value)}
                rows={4}
                className="w-full px-3 py-2 border-2 border-brand-black rounded-lg shadow-brutal-sm focus:shadow-brutal-hover transition-all font-medium"
                placeholder="Digite seu prompt aqui... (será usado exatamente como digitado, sem concatenações)"
              />
              <p className="text-xs text-brand-black/60 mt-1">
                O prompt será enviado exatamente como você escreveu, sem modificações.
              </p>
            </div>
          </div>
        )}
      </BrutalistModal>
      
      {/* 🔥 NOVO: Modal de Gerar Capa Principal */}
      <BrutalistModal
        isOpen={showMainCoverModal}
        onClose={() => setShowMainCoverModal(false)}
        title="Gerar Capa Principal - Escolha uma Opção"
        icon={<ImageIcon className="w-4 h-4 text-brand-black" />}
        maxWidth="max-w-2xl"
        maxHeight="max-h-[90vh]"
        footerActions={
          <div className="flex space-x-3">
            <button
              onClick={() => setShowMainCoverModal(false)}
              className="flex-1 px-4 py-2 bg-brand-white text-brand-black rounded-lg border-2 border-brand-black shadow-brutal-sm hover:shadow-brutal-hover active:shadow-brutal-pressed transition-all font-bold"
            >
              Cancelar
            </button>
            <button
              onClick={handleGenerateMainFromModal}
              disabled={isGeneratingMainFromModal || (!useCustomPromptMain && !selectedMovieMain) || (useCustomPromptMain && !customPromptMain.trim())}
              className="flex-1 px-4 py-2 bg-brand-secondary text-brand-black rounded-lg border-2 border-brand-black shadow-brutal-sm hover:shadow-brutal-hover active:shadow-brutal-pressed transition-all font-bold disabled:bg-gray-300 disabled:text-gray-500 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
            >
              {isGeneratingMainFromModal ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin" />
                  <span>Gerando...</span>
                </>
              ) : (
                <span>Gerar Capa</span>
              )}
            </button>
          </div>
        }
      >
        <p className="text-sm text-brand-black/70 mb-6">
          Escolha um título disponível da plataforma ou digite um prompt customizado para gerar a capa principal:
        </p>

        {/* Opções de escolha */}
        <div className="space-y-4 mb-6">
          <label className="flex items-center space-x-3 cursor-pointer">
            <input
              type="radio"
              name="mainCoverPromptType"
              checked={!useCustomPromptMain}
              onChange={() => setUseCustomPromptMain(false)}
              className="w-4 h-4 text-brand-primary"
            />
            <span className="text-sm font-bold text-brand-black">
              Escolher título disponível
            </span>
          </label>

          <label className="flex items-center space-x-3 cursor-pointer">
            <input
              type="radio"
              name="mainCoverPromptType"
              checked={useCustomPromptMain}
              onChange={() => setUseCustomPromptMain(true)}
              className="w-4 h-4 text-brand-primary"
            />
            <span className="text-sm font-bold text-brand-black">
              Usar prompt customizado
            </span>
          </label>
        </div>

        {/* Seleção de título */}
        {!useCustomPromptMain && (
          <div className="mb-6">
            <label className="block text-sm font-bold text-brand-black mb-2">
              Títulos Disponíveis ({availableMovies.length})
            </label>
            <select
              value={selectedMovieMain}
              onChange={(e) => setSelectedMovieMain(e.target.value)}
              className="w-full px-3 py-2 border-2 border-brand-black rounded-lg shadow-brutal-sm focus:shadow-brutal-hover transition-all font-medium"
            >
              <option value="">Selecione um título...</option>
              {availableMovies.map((movie, index) => (
                <option key={`main-movie-${index}-${movie}`} value={movie}>
                  {movie}
                </option>
              ))}
            </select>
            {availableMovies.length === 0 && (
              <p className="text-xs text-brand-accent mt-1 font-medium">
                Nenhum título encontrado. Use um prompt customizado.
              </p>
            )}
          </div>
        )}

        {/* Prompt customizado */}
        {useCustomPromptMain && (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-bold text-brand-black mb-2">
                Título da Série/Filme
              </label>
              <input
                type="text"
                value={customTitleMain}
                onChange={(e) => setCustomTitleMain(e.target.value)}
                className="w-full px-3 py-2 border-2 border-brand-black rounded-lg shadow-brutal-sm focus:shadow-brutal-hover transition-all font-medium"
                placeholder="Ex: Breaking Bad, Game of Thrones, etc."
              />
            </div>
            <div>
              <label className="block text-sm font-bold text-brand-black mb-2">
                Prompt Customizado
              </label>
              <textarea
                value={customPromptMain}
                onChange={(e) => setCustomPromptMain(e.target.value)}
                rows={4}
                className="w-full px-3 py-2 border-2 border-brand-black rounded-lg shadow-brutal-sm focus:shadow-brutal-hover transition-all font-medium"
                placeholder="Digite seu prompt aqui... (será usado exatamente como digitado, sem concatenações)"
              />
              <p className="text-xs text-brand-black/60 mt-1">
                O prompt será enviado exatamente como você escreveu, sem modificações.
              </p>
            </div>
          </div>
        )}
      </BrutalistModal>
      
      {/* Modal de Visualização da Capa Principal */}
      <AnimatePresence>
        {selectedMainCover && (
          <motion.div 
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4"
            onClick={() => setSelectedMainCover(null)}
          >
            <motion.div 
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="relative bg-brand-white rounded-lg border-2 border-brand-black shadow-brutal-lg max-w-4xl w-full"
              onClick={(e) => e.stopPropagation()}
            >
              <button
                onClick={() => setSelectedMainCover(null)}
                className="absolute -top-4 -right-4 w-12 h-12 bg-brand-accent text-white rounded-full border-2 border-brand-black hover:bg-brand-accent-dark transition-colors z-10 shadow-brutal-sm flex items-center justify-center"
              >
                <X size={24} />
              </button>
              
              <div className="p-4">
                <img
                  src={selectedMainCover}
                  alt="Capa Principal"
                  className="w-full max-h-[70vh] object-contain rounded-md border-2 border-brand-black"
                />
              </div>
              
              <div className="p-4 border-t-2 border-brand-black bg-brand-white">
                <div className="flex space-x-2">
                  <button
                    onClick={() => handleDownloadCover(selectedMainCover, 'capa-principal')}
                    className="p-3 bg-brand-primary rounded-lg hover:bg-brand-accent-dark transition-colors shadow-brutal-sm border-2 border-brand-black flex items-center space-x-2 font-bold text-brand-black"
                    title="Baixar HD"
                  >
                    <Download size={20} />
                    <span>Baixar</span>
                  </button>
                  <button
                    onClick={() => window.open(selectedMainCover, '_blank')}
                    className="p-3 bg-brand-white rounded-lg hover:bg-gray-50 transition-colors shadow-brutal-sm border-2 border-brand-black flex items-center space-x-2 font-bold text-brand-black"
                    title="Abrir em Nova Aba"
                  >
                    <ExternalLink size={20} />
                    <span>Visualizar</span>
                  </button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
      
      {/* Modal de Visualização do Poster Final */}
      <AnimatePresence>
        {selectedFinalPoster && (
          <motion.div 
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4"
            onClick={() => setSelectedFinalPoster(null)}
          >
            <motion.div 
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="relative bg-brand-white rounded-lg border-2 border-brand-black shadow-brutal-lg max-w-4xl w-full"
              onClick={(e) => e.stopPropagation()}
            >
              <button
                onClick={() => setSelectedFinalPoster(null)}
                className="absolute -top-4 -right-4 w-12 h-12 bg-brand-accent text-white rounded-full border-2 border-brand-black hover:bg-brand-accent-dark transition-colors z-10 shadow-brutal-sm flex items-center justify-center"
              >
                <X size={24} />
              </button>
              
              <div className="p-4">
                <img
                  src={selectedFinalPoster}
                  alt="Poster Final"
                  className="w-full max-h-[70vh] object-contain rounded-md border-2 border-brand-black"
                />
              </div>
              
              <div className="p-4 border-t-2 border-brand-black bg-brand-white">
                <div className="flex space-x-2">
                  <button
                    onClick={() => handleDownloadCover(selectedFinalPoster, 'poster-final')}
                    className="p-3 bg-brand-primary rounded-lg hover:bg-brand-accent-dark transition-colors shadow-brutal-sm border-2 border-brand-black flex items-center space-x-2 font-bold text-brand-black"
                    title="Baixar HD"
                  >
                    <Download size={20} />
                    <span>Baixar</span>
                  </button>
                  <button
                    onClick={() => window.open(selectedFinalPoster, '_blank')}
                    className="p-3 bg-brand-white rounded-lg hover:bg-gray-50 transition-colors shadow-brutal-sm border-2 border-brand-black flex items-center space-x-2 font-bold text-brand-black"
                    title="Abrir em Nova Aba"
                  >
                    <ExternalLink size={20} />
                    <span>Visualizar</span>
                  </button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  )
}