// Configurações centralizadas para todas as plataformas de streaming
export interface PlatformConfig {
  quantity: number
  aspectRatios: string[] | ((index: number) => string)
  templateId: string
  name: string
}

export const PLATFORM_CONFIGS: Record<string, PlatformConfig> = {
  netflix: {
    quantity: 12,
    aspectRatios: ['3:4'], // Todas as 12 capas usam 3:4
    templateId: 'EAGqDjHn_M4',
    name: 'Netflix'
  },
  disney: {
    quantity: 9,
    aspectRatios: (index: number) => {
      // 🔥 DISNEY: 6 imagens em 16:9 e 3 imagens em 5:4
      // Primeiras 6: 16:9 (widescreen)
      // Próximas 3: 5:4 (quase quadrado)
      return index < 6 ? '16:9' : '5:4'
    },
    templateId: 'EAGqWLwQR0Q',
    name: 'Disney+'
  },
  amazon: {
    quantity: 11,
    aspectRatios: ['3:4'], // Todas as 11 capas usam 3:4
    templateId: 'EAGqV5bYqfQ',
    name: 'Amazon Prime'
  }
}

// Função utilitária para obter a proporção correta
export function getAspectRatio(platform: string, index: number): string {
  const config = PLATFORM_CONFIGS[platform]
  if (!config) return '3:4' // fallback padrão
  
  if (typeof config.aspectRatios === 'function') {
    return config.aspectRatios(index)
  } else {
    return config.aspectRatios[0] || '3:4'
  }
}

// Função utilitária para obter quantidade de capas
export function getCoverQuantity(platform: string): number {
  return PLATFORM_CONFIGS[platform]?.quantity || 12
}

// Função utilitária para obter template ID
export function getTemplateId(platform: string): string {
  return PLATFORM_CONFIGS[platform]?.templateId || 'EAGqDjHn_M4'
}

// Função utilitária para obter nome da plataforma
export function getPlatformName(platform: string): string {
  return PLATFORM_CONFIGS[platform]?.name || platform.charAt(0).toUpperCase() + platform.slice(1)
}

// Validar se uma plataforma é suportada
export function isValidPlatform(platform: string): boolean {
  return platform in PLATFORM_CONFIGS
} 