# 🧪 Teste da Edge Function Canva

## 🚀 **NOVO: Setup Automático (Recomendado)**

Agora você pode configurar tudo automaticamente em 2 minutos:

```bash
npm run setup-canva
```

📖 **Veja:** `README-SETUP-AUTOMATICO-CANVA.md` para instruções completas

Depois do setup automático, **pule direto para a seção de teste** abaixo!

---

## 📋 **Objetivo**

Testar a Edge Function `canva-poster` que substituirá o fluxo N8N para geração de posters no Canva, validando se a integração direta com a API do Canva está funcionando corretamente.

## 🚀 **Como Testar**

### 1. **Acessar o Teste**
1. Abra a aplicação em `http://localhost:5175/`
2. Faça login
3. Abra o Dashboard (botão do usuário no header)
4. Clique na aba **"Test"** 🧪

### 2. **Executar o Teste**
1. Na aba Test, você verá o componente de teste
2. Clique no botão **"🧪 Testar Edge Function"**
3. Aguarde o processamento (pode levar 1-2 minutos)
4. Verifique o resultado

## 📊 **O que o Teste Faz**

### **Payload de Teste**
```json
{
  "ID": "EAGqDjHn_M4",  // Template ID do Canva (mesmo do N8N)
  "streaming_platform": "netflix",  // Campo obrigatório
  "photo_type": "individual",       // Campo obrigatório
  "user_name": "Teste Edge Function", // Nome do usuário
  "TITLE": "Posters NETFLIX - Teste Edge Function",
  "DESCRIPTION": "Teste da integração direta com a API do Canva...",
  "CONTINUE": "Continue testando a nova função",
  "CAPA": "URL_da_capa_principal",
  "AVATAR": "URL_do_avatar_do_usuário", 
  "PHOTO_01": "URL_cover_Wednesday",
  "PHOTO_02": "URL_cover_Stranger_Things",
  "PHOTO_03": "URL_cover_Elite",
  // ... até PHOTO_09
}
```

### **Processo Executado**
1. 🔄 **Upload de Imagens** - Baixa e envia imagens para o Canva
2. 🎨 **Criação do Design** - Usa o template e preenche com os dados
3. ⏳ **Polling de Status** - Verifica se o design foi criado
4. ✅ **Resultado Final** - Retorna URL do design criado
5. 💾 **Persistência** - Salva na tabela `poster_images` existente

## 📈 **Resultados Esperados**

### ✅ **Sucesso**
- Status: `completed`
- ID da geração gerado automaticamente
- URL do design no Canva funcionando
- Registro salvo na tabela `poster_images` (tabela existente)

### ❌ **Possíveis Erros**
- **Erro de autenticação**: Verificar credenciais do Canva
- **Erro de template**: Template ID pode estar inválido
- **Erro de upload**: URLs das imagens podem estar inacessíveis
- **Timeout**: Processo pode demorar mais que o esperado

## 🔍 **Verificações Importantes**

### **1. Credenciais do Canva**
```env
CANVA_CLIENT_ID=OC-AZdfZOcE7mgD
CANVA_CLIENT_SECRET=cnvca9UbcRNeju-iX-YioJQNkDTYTxpbzcfrpZ_AGkA_vYEc50913078
```

### **2. Template ID Válido**
- Template usado: `EAGqDjHn_M4`
- Deve ser o mesmo que funciona no fluxo N8N

### **3. URLs das Imagens**
- Todas as URLs devem estar acessíveis publicamente
- Formato suportado: JPG, PNG
- Tamanho adequado para upload no Canva

## 📱 **Interface do Teste**

### **Componente Inclui:**
- 🟣 **Botão de Teste** - Inicia o processo
- 📄 **Payload Visualizado** - Mostra dados que serão enviados
- 🔄 **Status em Tempo Real** - Loading, erro ou sucesso
- 🎯 **Resultado Detalhado** - ID, URL do design, etc.
- 🔗 **Link para Visualizar** - Abre o poster criado no Canva

## 🎯 **Próximos Passos**

### **Se o Teste Funcionar ✅**
1. Implementar a Edge Function no lugar do fluxo N8N
2. Adaptar o componente de geração de posters
3. Remover dependência do webhook N8N

### **Se o Teste Falhar ❌**
1. Verificar logs da Edge Function
2. Confirmar credenciais do Canva
3. Testar template ID manualmente
4. Validar URLs das imagens

## 🛠️ **Debugging**

### **Logs para Verificar**
```bash
# Logs da Edge Function
supabase functions logs canva-poster

# Console do navegador
# Abrir DevTools → Console
```

### **Tabela para Monitorar**
```sql
SELECT * FROM poster_images 
WHERE template_id = 'EAGqDjHn_M4'
ORDER BY created_at DESC 
LIMIT 5;
```

## 📝 **Comparison com N8N**

| **Aspecto** | **N8N Flow** | **Edge Function** |
|-------------|--------------|-------------------|
| **Dependência** | Externa (N8N) | Interna (Supabase) |
| **Velocidade** | ~2-3 min | ~1-2 min |
| **Confiabilidade** | Webhook pode falhar | Mais robusto |
| **Monitoramento** | N8N dashboard | Logs do Supabase |
| **Manutenção** | Via N8N interface | Via código |

---

**🎉 Teste concluído com sucesso significa que podemos substituir o fluxo N8N!**

## ℹ️ **Sobre este Teste**

- • **Template:** Usa o mesmo template do fluxo N8N (EAGqDjHn_M4)
- • **Imagens:** URLs reais de covers já gerados no sistema  
- • **Processo:** Upload → Canva API → Design → URL final
- • **Banco:** Salva na tabela `poster_images` (mesma estrutura atual)

---

**🎉 Teste concluído com sucesso significa que podemos substituir o fluxo N8N!** 