import React from 'react';
import { Twitter, Instagram, Facebook } from 'lucide-react';

const Footer: React.FC = () => {
  const socialLinks = [
    { icon: <Twitter />, href: '#' },
    { icon: <Instagram />, href: '#' },
    { icon: <Facebook />, href: '#' },
  ];

  return (
    <footer className="py-12 px-8 bg-black border-t-4 border-black">
      <div className="max-w-6xl mx-auto flex flex-col md:flex-row justify-between items-center text-center md:text-left">
        <div className="mb-8 md:mb-0">
          <h3 className="text-2xl font-bold uppercase text-yellow-400">AI Streaming Poster</h3>
          <p className="text-gray-500 mt-2">Transforme-se no protagonista.</p>
        </div>
        <div className="flex flex-col items-center md:items-end">
          <div className="flex space-x-4 mb-4">
            {socialLinks.map((link, index) => (
              <a 
                key={index} 
                href={link.href} 
                className="w-12 h-12 flex items-center justify-center bg-[#1a1a1a] border-2 border-black shadow-[4px_4px_0px_#000000] hover:shadow-none hover:translate-x-0.5 hover:translate-y-0.5 transition-all duration-200"
              >
                {link.icon}
              </a>
            ))}
          </div>
          <p className="text-gray-600">&copy; {new Date().getFullYear()} AI Streaming Poster. Todos os direitos reservados.</p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
