import React, { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { User, ImageIcon, Zap, LogOut, ChevronDown, Plus, FolderOpen, Mail, Shield, Settings, Home, Key } from 'lucide-react'
import { Link } from 'react-router-dom'
import LanguageSwitcher from './LanguageSwitcher'

interface AppHeaderProps {
  user: { id: string; email?: string; user_metadata?: { role?: string } } | null
  onLogout: () => void
  availableCredits?: number
  onOpenDashboard?: () => void
  onBuyCredits?: () => void
  onOpenGenerations?: () => void
  onOpenContact?: () => void
  onSetPassword?: () => void
}

export default function AppHeader({ 
  user, 
  onLogout, 
  availableCredits = 0,
  onOpenDashboard, 
  onBuyCredits,
  onOpenGenerations,
  onOpenContact,
  onSetPassword
}: AppHeaderProps) {
  const { t } = useTranslation();
  const [isMenuO<PERSON>, setIsMenuOpen] = useState(false)

  if (!user) return null

  const getInitials = (email: string) => {
    return email.split('@')[0].slice(0, 2).toUpperCase()
  }

  // 🔥 Determinar se está com poucos créditos
  const isLowOnCredits = availableCredits < 10

  return (
    <header className="bg-brand-white border-b-2 border-brand-black sticky top-0 z-40">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <div className="flex items-center space-x-3">
            <img 
              src="/img/logo-final-svg.png" 
              alt="PosterFlix Online" 
              className="h-10 w-auto"
            />
          </div>

          {/* Center - Buy More Button (when low on credits) */}
          {isLowOnCredits && onBuyCredits && (
            <button
              onClick={onBuyCredits}
              className="flex items-center space-x-2 bg-brand-accent text-white px-6 py-2 rounded-lg border-2 border-brand-black shadow-brutal-sm hover:shadow-brutal-hover active:shadow-brutal-pressed transition-all"
            >
              <Plus className="w-4 h-4" />
              <span className="font-medium">Comprar Créditos</span>
            </button>
          )}

          {/* Right Side */}
          <div className="flex items-center space-x-4">
            <LanguageSwitcher />
            
            {/* Credit Balance */}
            <div className={`
              flex items-center space-x-2 bg-brand-white rounded-lg px-3 py-1.5 border-2 border-brand-black shadow-brutal-sm
              ${isLowOnCredits ? 'bg-brand-accent/20 border-brand-accent' : ''}
            `}>
              <div className={`
                w-8 h-8 rounded-md flex items-center justify-center border-2 border-brand-black
                ${isLowOnCredits ? 'bg-brand-accent/30' : 'bg-brand-primary/30'}
              `}>
                <Zap className={`w-4 h-4 ${isLowOnCredits ? 'text-brand-accent' : 'text-yellow-600'}`} />
              </div>
              <div className="text-left">
                <p className="text-xs text-brand-text/70">{t('header.credits')}</p>
                <p className={`text-sm font-semibold ${isLowOnCredits ? 'text-brand-accent' : 'text-brand-text'}`}>
                  {availableCredits}
                </p>
              </div>
              {onBuyCredits && (
                <button
                  onClick={onBuyCredits}
                  className="ml-2 w-7 h-7 bg-brand-secondary hover:bg-brand-accent rounded-lg flex items-center justify-center transition-colors duration-200 border-2 border-brand-black"
                  title={t('header.buyCredits')}
                >
                  <Plus className="w-4 h-4 text-brand-black" />
                </button>
              )}
            </div>

            {/* User Menu */}
            <div className="relative">
              <button
                onClick={() => setIsMenuOpen(!isMenuOpen)}
                className="flex items-center space-x-3 bg-brand-white rounded-lg px-3 py-1.5 border-2 border-brand-black shadow-brutal-sm hover:shadow-brutal-hover active:shadow-brutal-pressed transition-all"
              >
                {/* User Avatar */}
                <div className="flex items-center space-x-2">
                  <div className="w-8 h-8 bg-brand-secondary rounded-md flex items-center justify-center border-2 border-brand-black">
                    <span className="text-sm font-semibold text-brand-black">
                      {getInitials(user.email || 'U')}
                    </span>
                  </div>
                  <div className="text-left hidden sm:block">
                    <p className="text-sm font-medium text-brand-text">{user.email?.split('@')[0]}</p>
                    <p className="text-xs text-brand-text/70">Sistema de Créditos</p>
                  </div>
                </div>
                <ChevronDown className={`w-4 h-4 text-brand-text/70 transition-transform duration-200 ${isMenuOpen ? 'rotate-180' : ''}`} />
              </button>

              {/* Dropdown Menu */}
              {isMenuOpen && (
                <div className="absolute right-0 mt-2 w-64 bg-brand-white rounded-lg shadow-brutal-lg border-2 border-brand-black py-2 z-50">
                  <div className="px-4 py-3 border-b-2 border-brand-black">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-brand-secondary rounded-md flex items-center justify-center border-2 border-brand-black">
                        <span className="text-base font-semibold text-brand-black">
                          {getInitials(user.email || 'U')}
                        </span>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-brand-text">{user.email}</p>
                        <p className="text-xs text-brand-text/70">Sistema de Créditos</p>
                        {/* 🔥 Mostrar saldo no dropdown */}
                        <div className="mt-1 flex items-center space-x-1">
                          <Zap className="w-3 h-3 text-yellow-500" />
                          <span className="text-xs font-semibold text-yellow-600">
                            {availableCredits} créditos
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="py-2">
                    {/* Landing Page Button */}
                    <Link
                      to="/"
                      onClick={() => setIsMenuOpen(false)}
                      className="w-full text-left px-4 py-2 text-sm text-brand-text hover:bg-brand-secondary flex items-center space-x-3 transition-colors duration-150"
                    >
                      <div className="w-8 h-8 bg-yellow-200 rounded-lg flex items-center justify-center border-2 border-brand-black">
                        <Home className="w-4 h-4 text-brand-black" />
                      </div>
                      <div>
                        <p className="font-medium">Landing Page</p>
                        <p className="text-xs text-brand-text/70">Voltar à página inicial</p>
                      </div>
                    </Link>

                    {onOpenDashboard && (
                      <button
                        onClick={() => {
                          onOpenDashboard()
                          setIsMenuOpen(false)
                        }}
                        className="w-full text-left px-4 py-2 text-sm text-brand-text hover:bg-brand-secondary flex items-center space-x-3 transition-colors duration-150"
                      >
                        <div className="w-8 h-8 bg-brand-primary/30 rounded-lg flex items-center justify-center border-2 border-brand-black">
                          <User className="w-4 h-4 text-brand-black" />
                        </div>
                        <div>
                          <p className="font-medium">{t('header.dashboard')}</p>
                          <p className="text-xs text-brand-text/70">{t('header.manageAccount')}</p>
                        </div>
                      </button>
                    )}

                    {onOpenGenerations && (
                      <button
                        onClick={() => {
                          onOpenGenerations()
                          setIsMenuOpen(false)
                        }}
                        className="w-full text-left px-4 py-2 text-sm text-brand-text hover:bg-brand-secondary flex items-center space-x-3 transition-colors duration-150"
                      >
                        <div className="w-8 h-8 bg-blue-200 rounded-lg flex items-center justify-center border-2 border-brand-black">
                          <FolderOpen className="w-4 h-4 text-brand-black" />
                        </div>
                        <div>
                          <p className="font-medium">{t('header.myGenerations')}</p>
                          <p className="text-xs text-brand-text/70">{t('header.viewGeneratedPhotos')}</p>
                        </div>
                      </button>
                    )}

                    {/* 🔥 Botão para comprar créditos */}
                    {onBuyCredits && (
                      <button
                        onClick={() => {
                          onBuyCredits()
                          setIsMenuOpen(false)
                        }}
                        className="w-full text-left px-4 py-2 text-sm text-brand-text hover:bg-brand-secondary flex items-center space-x-3 transition-colors duration-150"
                      >
                        <div className="w-8 h-8 bg-purple-200 rounded-lg flex items-center justify-center border-2 border-brand-black">
                          <Zap className="w-4 h-4 text-brand-black" />
                        </div>
                        <div>
                          <p className="font-medium">{t('header.buyCredits')}</p>
                          <p className="text-xs text-brand-text/70">Adquirir mais créditos para gerações</p>
                        </div>
                      </button>
                    )}

                    {onOpenContact && (
                      <button
                        onClick={() => {
                          onOpenContact()
                          setIsMenuOpen(false)
                        }}
                        className="w-full text-left px-4 py-2 text-sm text-brand-text hover:bg-brand-secondary flex items-center space-x-3 transition-colors duration-150"
                      >
                        <div className="w-8 h-8 bg-green-200 rounded-lg flex items-center justify-center border-2 border-brand-black">
                          <Mail className="w-4 h-4 text-brand-black" />
                        </div>
                        <div>
                          <p className="font-medium">{t('header.contact')}</p>
                          <p className="text-xs text-brand-text/70">{t('header.getInTouch')}</p>
                        </div>
                      </button>
                    )}

                    {/* 🔐 Botão para definir senha */}
                    {onSetPassword && (
                      <button
                        onClick={() => {
                          onSetPassword()
                          setIsMenuOpen(false)
                        }}
                        className="w-full text-left px-4 py-2 text-sm text-brand-text hover:bg-brand-secondary flex items-center space-x-3 transition-colors duration-150"
                      >
                        <div className="w-8 h-8 bg-orange-200 rounded-lg flex items-center justify-center border-2 border-brand-black">
                          <Key className="w-4 h-4 text-brand-black" />
                        </div>
                        <div>
                          <p className="font-medium">Definir Senha</p>
                          <p className="text-xs text-brand-text/70">Criar senha para login com email</p>
                        </div>
                      </button>
                    )}

                    {/* Admin Links - Only for admins */}
                    {user.user_metadata?.role === 'admin' && (
                      <>
                        <Link
                          to="/admin"
                          onClick={() => setIsMenuOpen(false)}
                          className="w-full text-left px-4 py-2 text-sm text-brand-text hover:bg-brand-secondary flex items-center space-x-3 transition-colors duration-150"
                        >
                          <div className="w-8 h-8 bg-red-200 rounded-lg flex items-center justify-center border-2 border-brand-black">
                            <Shield className="w-4 h-4 text-brand-black" />
                          </div>
                          <div>
                            <p className="font-medium">{t('header.admin')}</p>
                            <p className="text-xs text-brand-text/70">{t('header.adminPanel')}</p>
                          </div>
                        </Link>

                        <Link
                          to="/canva-poster"
                          onClick={() => setIsMenuOpen(false)}
                          className="w-full text-left px-4 py-2 text-sm text-brand-text hover:bg-brand-secondary flex items-center space-x-3 transition-colors duration-150"
                        >
                          <div className="w-8 h-8 bg-orange-200 rounded-lg flex items-center justify-center border-2 border-brand-black">
                            <Settings className="w-4 h-4 text-brand-black" />
                          </div>
                          <div>
                            <p className="font-medium">Test</p>
                            <p className="text-xs text-brand-text/70">Teste de funcionalidades</p>
                          </div>
                        </Link>
                      </>
                    )}

                    <div className="border-t-2 border-brand-black mt-2 pt-2">
                      <button
                        onClick={() => {
                          onLogout()
                          setIsMenuOpen(false)
                        }}
                        className="w-full text-left px-4 py-2 text-sm text-brand-accent hover:bg-brand-accent/20 flex items-center space-x-3 transition-colors duration-150"
                      >
                        <div className="w-8 h-8 bg-red-200 rounded-lg flex items-center justify-center border-2 border-brand-black">
                          <LogOut className="w-4 h-4 text-brand-accent" />
                        </div>
                        <div>
                          <p className="font-medium">{t('header.signOut')}</p>
                          <p className="text-xs text-brand-accent/70">{t('header.logout')}</p>
                        </div>
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Click outside to close */}
      {isMenuOpen && (
        <div
          className="fixed inset-0 z-30"
          onClick={() => setIsMenuOpen(false)}
        />
      )}
    </header>
  )
}
