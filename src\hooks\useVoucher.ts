import { useState } from 'react';
import { supabase } from '../lib/supabase';

interface VoucherRedemptionResult {
  success: boolean;
  error?: string;
  message: string;
  data?: {
    credits: number;
    voucherCode: string;
  };
}

export const useVoucher = () => {
  const [loading, setLoading] = useState(false);

  const redeemVoucher = async (voucherCode: string, userId: string, userEmail: string): Promise<VoucherRedemptionResult> => {
    setLoading(true);
    
    try {
      const { data: { session } } = await supabase.auth.getSession();
      
      if (!session?.access_token) {
        throw new Error('Usuário não autenticado');
      }

      const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/redeem-voucher`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          voucherCode: voucherCode.toUpperCase().trim(),
          userId,
          userEmail
        }),
      });

      const result = await response.json();
      
      if (!response.ok) {
        return {
          success: false,
          error: result.error || 'UNKNOWN_ERROR',
          message: result.message || 'Erro ao resgatar voucher'
        };
      }

      return result;
    } catch (error) {
      console.error('Error redeeming voucher:', error);
      return {
        success: false,
        error: 'NETWORK_ERROR',
        message: 'Erro de conexão. Tente novamente.'
      };
    } finally {
      setLoading(false);
    }
  };

  const validateVoucherCode = (code: string): boolean => {
    // Basic validation: 6-12 characters, alphanumeric
    const trimmedCode = code.trim();
    return /^[A-Z0-9]{6,12}$/i.test(trimmedCode);
  };

  return {
    redeemVoucher,
    validateVoucherCode,
    loading
  };
}; 