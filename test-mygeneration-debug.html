<!DOCTYPE html>
<html>
<head>
    <title>Debug MyGenerations</title>
</head>
<body>
    <h1>Debug MyGenerations</h1>
    <div id="output"></div>
    
    <script>
        // Simular dados do Supabase
        const mockCoverGenerations = [
            {
                id: "ea9929c2-aa8d-425b-b401-a27eb1963a0b",
                user_id: "a3379e9d-579b-4333-946a-f42584b93af6",
                status: "completed",
                streaming_platform: "amazon",
                photo_type: "individual",
                created_at: "2025-07-16T15:39:59.288853+00:00",
                generated_covers: [
                    {
                        position: 1,
                        image_url: "https://replicate.delivery/xezq/zCZntURGii4NHt2LftxfEUuTINeZRJ41G42ydxPE7SGSOvCqA/tmpl58t5n5w.jpg",
                        created_at: "2025-07-16T15:39:59.256Z",
                        photo_type: "individual",
                        movie_title: "The Boys",
                        aspect_ratio: "3:4",
                        streaming_platform: "amazon"
                    },
                    {
                        position: 2,
                        image_url: "https://replicate.delivery/xezq/f0H4x97wLIUMQyj4cw6W2y6ZdYYxRyhahAemBaKzeqJzOvCqA/tmpj6nxwm0d.jpg",
                        created_at: "2025-07-16T15:40:12.788Z",
                        photo_type: "individual",
                        movie_title: "The Marvelous Mrs. Maisel",
                        aspect_ratio: "3:4",
                        streaming_platform: "amazon"
                    }
                ]
            }
        ];

        // Função getAspectRatio correta
        const getAspectRatio = (platform, index) => {
            if (platform === 'disney') {
                return index < 6 ? '16:9' : '5:4';
            }
            return '3:4'; // Netflix e Amazon usam 3:4 (retrato)
        };

        // Processar dados como no MyGenerations
        const series = [];
        mockCoverGenerations.forEach(gen => {
            console.log('Processing generation:', gen);
            
            if (gen.generated_covers && Array.isArray(gen.generated_covers)) {
                console.log('Generated covers found:', gen.generated_covers.length);
                
                gen.generated_covers.forEach((cover, index) => {
                    console.log('Processing cover:', cover);
                    
                    let url = typeof cover === 'string' ? cover : cover.image_url;
                    let title = typeof cover === 'string' ? `Capa ${index + 1}` : (cover.movie_title || `Capa ${index + 1}`);
                    
                    console.log('URL:', url, 'Title:', title);
                    
                    if (url) {
                        const aspectRatio = gen.streaming_platform ? 
                            getAspectRatio(gen.streaming_platform, index) : 
                            '3:4';
                        
                        console.log('Aspect ratio:', aspectRatio);
                        
                        series.push({
                            id: `${gen.id}-${index}`,
                            url,
                            title: `${gen.streaming_platform?.toUpperCase()} - ${title}`,
                            createdAt: gen.created_at,
                            type: 'series',
                            streamingPlatform: gen.streaming_platform,
                            photoType: gen.photo_type,
                            aspectRatio
                        });
                    }
                });
            } else {
                console.log('No generated_covers or not an array');
            }
        });

        console.log('Final series array:', series);
        document.getElementById('output').innerHTML = `
            <h2>Results:</h2>
            <p>Total series found: ${series.length}</p>
            <pre>${JSON.stringify(series, null, 2)}</pre>
        `;
    </script>
</body>
</html>
