// Webhook handler local para desenvolvimento
import { supabase } from '../lib/supabase'

export async function handleStripeWebhook(event: any) {
  console.log('🔥 Webhook local recebido:', event.type)
  
  try {
    switch (event.type) {
      case 'payment_intent.succeeded':
        const paymentIntent = event.data.object
        const userId = paymentIntent.metadata.userId
        const planId = paymentIntent.metadata.planId
        const planType = paymentIntent.metadata.planType
        const credits = parseInt(paymentIntent.metadata.credits) || 0

        console.log(`💰 Pagamento confirmado localmente:`, {
          paymentIntentId: paymentIntent.id,
          userId,
          planId,
          credits,
          amount: paymentIntent.amount
        })

        if (userId && planId && planType === 'credits') {
          // Atualizar status da compra
          const { error: updateError } = await supabase
            .from('credit_purchases')
            .update({ 
              status: 'completed',
              completed_at: new Date().toISOString()
            })
            .eq('stripe_payment_intent_id', paymentIntent.id)

          if (updateError) {
            console.error('❌ Erro ao atualizar compra:', updateError)
            return { success: false, error: updateError.message }
          }

          // Verificar se usuário tem registro de créditos
          const { data: existingCredits, error: checkError } = await supabase
            .from('user_credits')
            .select('*')
            .eq('user_id', userId)
            .single()

          if (checkError && checkError.code === 'PGRST116') {
            // Criar registro se não existir
            const { error: insertError } = await supabase
              .from('user_credits')
              .insert({
                user_id: userId,
                total_credits: credits,
                available_credits: credits,
                used_credits: 0
              })

            if (insertError) {
              console.error('❌ Erro ao criar registro de créditos:', insertError)
              return { success: false, error: insertError.message }
            }

            console.log(`✅ Registro de créditos criado com ${credits} créditos`)
          } else if (!checkError) {
            // Atualizar registro existente
            const { error: updateCreditsError } = await supabase
              .from('user_credits')
              .update({
                total_credits: (existingCredits.total_credits || 0) + credits,
                available_credits: (existingCredits.available_credits || 0) + credits,
                updated_at: new Date().toISOString()
              })
              .eq('user_id', userId)

            if (updateCreditsError) {
              console.error('❌ Erro ao atualizar créditos:', updateCreditsError)
              return { success: false, error: updateCreditsError.message }
            }

            console.log(`✅ ${credits} créditos adicionados ao usuário ${userId}`)
          }

          return { success: true, message: `Pagamento processado: ${credits} créditos adicionados` }
        }
        break

      case 'payment_intent.payment_failed':
        const failedPayment = event.data.object
        console.log(`❌ Pagamento falhou:`, failedPayment.id)
        
        // Atualizar status para failed
        const { error: failError } = await supabase
          .from('credit_purchases')
          .update({ status: 'failed' })
          .eq('stripe_payment_intent_id', failedPayment.id)

        if (failError) {
          console.error('❌ Erro ao marcar pagamento como falho:', failError)
        }
        
        return { success: true, message: 'Pagamento falho processado' }

      default:
        console.log(`ℹ️ Evento não tratado: ${event.type}`)
        return { success: true, message: 'Evento não tratado' }
    }
  } catch (error) {
    console.error('💥 Erro no webhook local:', error)
    return { success: false, error: error.message }
  }
}