import { useState, useEffect } from 'react';
import { Loader2, X, Wand2, FolderOpen } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { Toaster, toast } from 'sonner';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { <PERSON>rowserRouter } from 'react-router-dom';
import CoverGenerationWizard from './components/CoverGenerationWizard';
import CoverGrid from './components/CoverGrid';
import AppHeader from './components/AppHeader';
import UserDashboard from './components/UserDashboard';
import MyGenerations from './components/MyGenerations';
import NewGenerationModal from './components/NewGenerationModal';
import GenerationSelector from './components/GenerationSelector';
import ContactForm from './components/ContactForm';
import ErrorBoundary from './components/ErrorBoundary';
import SetPasswordModal from './components/SetPasswordModal';

import { useCoverGeneration } from './hooks/useCoverGeneration';
import { PhotoType, StreamingPlatform, Language } from './types';
import AuthForm from './components/AuthForm';
import { supabase, signOut } from './lib/supabase';
import PricingModal from './components/PricingModal';
import CheckoutForm from './components/CheckoutForm';
import { usePayment } from './hooks/usePayment';
import { stripePromise, checkUserCreditBalance, consumeCredits, calculateCreditsNeeded } from './lib/stripe';
import { useCreditBalance } from './hooks/useCreditBalance';
import './utils/envCheck'; // Auto-check environment variables

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 30 * 1000, // 30 segundos
      gcTime: 5 * 60 * 1000, // 5 minutos
      retry: 3,
    },
  },
})

function AppContent() {
  // Estados para o wizard
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [photoType, setPhotoType] = useState<PhotoType | null>(null)
  const [gender, setGender] = useState<'male' | 'female' | null>(null)
  const [streamingPlatform, setStreamingPlatform] = useState<StreamingPlatform | null>(null)
  const [language, setLanguage] = useState<Language | null>(null)
  const [generateTitles, setGenerateTitles] = useState(true)
  const [creativityLevel, setCreativityLevel] = useState<'criativo' | 'rostoFiel' | 'estrito'>('rostoFiel')
  const [creativityLevelSelected, setCreativityLevelSelected] = useState(false)
  const [userName, setUserName] = useState('')
  const [quantity, setQuantity] = useState<number | null>(null)
  const [user, setUser] = useState<{ id: string; email?: string } | null>(null)
  const [showPricingModal, setShowPricingModal] = useState(false)
  const [showDashboard, setShowDashboard] = useState(false)
  const [dashboardRefreshTrigger, setDashboardRefreshTrigger] = useState(0)
  const [showGenerations, setShowGenerations] = useState(false)
  const [showContact, setShowContact] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [showWizard, setShowWizard] = useState(true) // Iniciar com o wizard aberto
  const [wizardInitialStep, setWizardInitialStep] = useState(0) // Novo estado para o step inicial
  const [showNewGenerationModal, setShowNewGenerationModal] = useState(false)
  const [showGenerationSelector, setShowGenerationSelector] = useState(false)
  const [showSetPasswordModal, setShowSetPasswordModal] = useState(false)
  const [showGalleryMode, setShowGalleryMode] = useState(false)
  
  // 🔥 Credit system with React Query
  const {
    creditBalance,
    isLoading: loadingCredits,
    consumeCredits: consumeCreditsQuery,
    invalidateAfterPayment,
    updateAfterPayment
  } = useCreditBalance(user?.id || null)
  
  // Auto-set quantity based on template
  useEffect(() => {
    if (streamingPlatform) {
      const quantities = { netflix: 12, disney: 9, amazon: 11 }
      setQuantity(quantities[streamingPlatform] || 12)
    }
  }, [streamingPlatform])

  // Hooks
  const { 
    generateCoversDirectly,
    regenerateIndividualCover,
    regenerateCoverImage,
    retryFailedCover,
    isGenerating, 
    generatedCovers, 
    failedIndices,
    error: _,
    regeneratingIndex,
    regeneratingIndices,
    progress,
    originalImageUrl,
    currentMovie,
    currentGenerationId,
    lastGenerationParams,
    hasExistingGenerations,
    loadPreviousGeneration,
    loadSpecificGeneration,
    checkExistingGenerations,
    forceLoadCovers,
    refreshCovers,
    refreshCurrentGeneration,
    clearGeneration
  } = useCoverGeneration()

  // 🔥 Payment integration - apenas créditos
  const {
    loading: paymentLoading,
    error: paymentError,
    createPaymentIntent,
    checkBalance,
    processPaymentSuccess,
    clearError
  } = usePayment()

  const [paymentIntent, setPaymentIntent] = useState<{
    clientSecret: string
    planId: string
    paymentIntentId: string
  } | null>(null)

  // Check auth state on mount
  useEffect(() => {
    const checkAuth = async () => {
      try {
        const { data: { session } } = await supabase.auth.getSession();
        if (session?.user) {
          setUser(session.user);
        }
      } catch (error) {
        console.error('Error checking auth:', error);
        toast.error('Failed to check authentication');
      } finally {
        setIsLoading(false);
      }
    };
    checkAuth();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange((_, session) => {
      if (session?.user) {
        setUser(session.user);
      } else {
        setUser(null);
      }
    });

    return () => subscription.unsubscribe();
  }, []);

  // 🔥 Load existing generations when user logs in
  useEffect(() => {
    if (user?.id) {
      const timer = setTimeout(() => {
        checkExistingGenerations()
      }, 500)

      return () => clearTimeout(timer)
    }
  }, [user?.id, checkExistingGenerations])

  // 🔥 Listener para invalidar cache de créditos quando capas são geradas
  useEffect(() => {
    const handleCoversGenerated = () => {
      console.log('🔄 Covers generated, invalidating credits cache...')
      invalidateAfterPayment() // Forçar recarregamento dos créditos
    }

    const handleCreditConsumed = (event?: CustomEvent) => {
      console.log('💳 Credits consumed, invalidating credits cache...')
      
      // 🔥 Atualização otimista: reduzir 1 crédito imediatamente na interface
      const creditsUsed = event?.detail?.creditsUsed || 1
      updateAfterPayment(-creditsUsed) // Usar valor negativo para reduzir
      
      // 🔥 Invalidar cache para buscar dados atualizados do servidor
      invalidateAfterPayment() // Forçar recarregamento dos créditos
    }

    window.addEventListener('coversGenerated', handleCoversGenerated)
    window.addEventListener('creditConsumed', handleCreditConsumed)
    
    return () => {
      window.removeEventListener('coversGenerated', handleCoversGenerated)
      window.removeEventListener('creditConsumed', handleCreditConsumed)
    }
  }, [invalidateAfterPayment])

  // Handle logout
  const handleLogout = async () => {
    try {
      await signOut();
      setUser(null);
      // React Query will automatically clear cache when user changes
      toast.success('Logged out successfully');
    } catch (error) {
      console.error('Error logging out:', error);
      toast.error('Failed to log out');
    }
  };

  // 🔥 Check if user can generate based on credits
  const canGenerateWithCredits = (creditsNeeded: number) => {
    return creditBalance.availableCredits >= creditsNeeded
  }

  // 🔥 Calculate credits needed for current generation
  const getCreditsNeeded = () => {
    if (!streamingPlatform) return 0
    return calculateCreditsNeeded(streamingPlatform, true) // Always include poster
  }

  const handleGenerate = async () => {
    if (!canGenerate) {
      toast.warning('Please fill in all required fields')
      return
    }

    // 🔥 VALIDAÇÃO DO TIPO DE ARQUIVO
    const allowedTypes = ['image/jpeg', 'image/png'];
    if (selectedFile && !allowedTypes.includes(selectedFile.type)) {
      toast.error('Formato de arquivo inválido. Por favor, envie apenas imagens JPEG ou PNG.');
      return;
    }

    const creditsNeeded = getCreditsNeeded()
    
    // 🔥 Check if user has enough credits
    if (!canGenerateWithCredits(creditsNeeded)) {
      toast.warning(`Você precisa de ${creditsNeeded} créditos para esta geração. Você tem ${creditBalance.availableCredits} créditos.`)
      setShowPricingModal(true)
      return
    }

    try {
      setShowWizard(false)
      setShowGalleryMode(false) // Resetar modo galeria ao gerar com IA
      
      // Generate covers (créditos serão consumidos apenas em caso de sucesso)
      await generateCoversDirectly(selectedFile!, photoType!, gender, streamingPlatform!, language!, quantity!, generateTitles, creativityLevel, false) // testMode: false para sistema normal
      
      toast.success(`Geração iniciada! Créditos serão consumidos apenas para capas geradas com sucesso.`)
    } catch (error) {
      console.error('Error generating covers:', error)
      toast.error('Erro ao gerar capas. Seus créditos não foram consumidos.')
      setShowWizard(true)
    }
  }

  const canGenerate: boolean = !!(selectedFile && photoType && streamingPlatform && language && quantity && userName.trim() && !isGenerating)

  // 🔥 Create payment intent for credits
  const handleCreatePayment = async (planId: string) => {
    if (!user?.id) return

    try {
      const result = await createPaymentIntent(planId, user.id)
      
      if (result.clientSecret) {
        setPaymentIntent({
          clientSecret: result.clientSecret,
          planId,
          paymentIntentId: result.paymentIntentId
        })
      }
    } catch (error) {
      console.error('Error creating payment:', error)
      toast.error('Erro ao processar pagamento')
    }
  }

  // 🔥 Cancel payment
  const handleCancelPayment = () => {
    setPaymentIntent(null)
    clearError()
  }

  // 🔥 Payment success - aguardar confirmação automática
  const handlePaymentSuccess = async () => {
    if (!user?.id || !paymentIntent) return

    try {
      // Mostrar loading enquanto aguarda confirmação
      toast.loading('💳 Pagamento confirmado! Aguardando processamento...', { 
        id: 'payment-processing',
        duration: Infinity 
      })

      // Aguardar webhook processar automaticamente (máximo 30 segundos)
      let attempts = 0
      const maxAttempts = 30 // 30 tentativas de 1 segundo cada

      const checkPaymentCompletion = async (): Promise<boolean> => {
        try {
          // Verificar se a compra foi completada no banco
          const { data: purchase } = await supabase
            .from('credit_purchases')
            .select('status, credits')
            .eq('stripe_payment_intent_id', paymentIntent.clientSecret.split('_secret')[0])
            .eq('user_id', user.id)
            .single()

          if (purchase?.status === 'completed') {
            return true
          }

          // Se ainda está pending, tentar forçar com webhook
          if (purchase?.status === 'pending') {
            await supabase.functions.invoke('test-webhook', {
              body: {
                paymentIntentId: paymentIntent.clientSecret.split('_secret')[0],
                userId: user.id
              }
            })
          }

          return false
        } catch (error) {
          console.warn('Erro ao verificar status do pagamento:', error)
          return false
        }
      }

      // Loop para aguardar confirmação
      while (attempts < maxAttempts) {
        const isCompleted = await checkPaymentCompletion()
        
        if (isCompleted) {
          // Pagamento confirmado!
          toast.dismiss('payment-processing')
          
          // Invalidar cache para recarregar saldo atualizado
          invalidateAfterPayment()
          
          // Forçar refresh do dashboard
          setDashboardRefreshTrigger(prev => prev + 1)
          
          // Limpar estados do pagamento
          setPaymentIntent(null)
          setShowPricingModal(false)
          
          // Mostrar mensagem de sucesso
          toast.success('🎉 Pagamento confirmado! Créditos adicionados à sua conta.')
          
          return
        }

        // Aguardar 1 segundo antes da próxima tentativa
        await new Promise(resolve => setTimeout(resolve, 1000))
        attempts++
      }

      // Se chegou aqui, o pagamento não foi confirmado em 30 segundos
      toast.dismiss('payment-processing')
      toast.warning('⏱️ Pagamento realizado mas ainda processando. Seus créditos aparecerão em breve.')
      
      // Mesmo assim, tentar atualizar
      invalidateAfterPayment()
      setDashboardRefreshTrigger(prev => prev + 1)
      setPaymentIntent(null)
      setShowPricingModal(false)
      
    } catch (error) {
      console.error('Error processing payment success:', error)
      toast.dismiss('payment-processing')
      toast.error('❌ Erro ao processar pagamento. Entre em contato se os créditos não aparecerem.')
      
      // Tentar recarregar o saldo mesmo em caso de erro
      invalidateAfterPayment()
      setDashboardRefreshTrigger(prev => prev + 1)
      setPaymentIntent(null)
      setShowPricingModal(false)
    }
  }

  // 🔥 Função para lidar com Nova Geração
  const handleNewGeneration = () => {
    // Garantir que o wizard está em um estado limpo quando o modal for aberto
    setWizardInitialStep(0)
    setShowNewGenerationModal(true)
  }

  // 🔥 Função wrapper para setCreativityLevel
  const handleSetCreativityLevel = (level: 'criativo' | 'rostoFiel' | 'estrito') => {
    setCreativityLevel(level)
    setCreativityLevelSelected(true)
  }
  
  // 🔥 Função para criar do zero (LÓGICA NOVA E SIMPLIFICADA)
  const handleCreateFromScratch = () => {
    // 1. Fechar o modal
    setShowNewGenerationModal(false)
    
    // 2. Limpar TUDO do hook de geração
    if (clearGeneration) {
      clearGeneration(false) // Limpar tudo, sem manter configurações
    }
    
    // 3. Resetar TODOS os estados do wizard no App.tsx
    setSelectedFile(null)
    setPhotoType(null)
    setGender(null)
    setStreamingPlatform(null)
    setLanguage(null)
    setGenerateTitles(true)
    setCreativityLevel('rostoFiel')
    setCreativityLevelSelected(false)
    setUserName('')
    setQuantity(null)
    setShowGalleryMode(false) // Resetar modo galeria
    
    // 4. Definir que o wizard deve começar no step 1 (upload) e mostrá-lo
    setWizardInitialStep(1) // Ir direto para o passo de upload
    setShowWizard(true)
    
    // 5. Notificar o usuário
    toast.success('Novo wizard iniciado! Faça o upload da sua foto.')
  }

  // 🔥 Função para usar mesmos itens
  const handleUseSameItems = () => {
    clearGeneration(true) // Manter configurações
    // Manter o estado de seleção do creativity level se já foi selecionado
    setWizardInitialStep(0) // Usar a lógica padrão do wizard
    setShowWizard(true)
    toast.success('Configurações mantidas! Você pode editá-las antes de gerar.')
  }

  // 🔥 Função para carregar geração específica
  const handleLoadSpecificGeneration = async (generationData: any) => {
    console.log('🔄 Carregando geração específica:', generationData)
    
    const result = await loadSpecificGeneration(generationData)
    
    if (result) {
      console.log('📋 Dados da geração carregados:', result)
      
      // Preencher TODOS os campos do wizard com os dados da geração
      setPhotoType(result.photo_type || 'individual')
      setStreamingPlatform(result.streaming_platform || 'netflix')
      setLanguage(result.language || 'portuguese')
      setGenerateTitles(result.generate_titles !== false)
      setUserName(result.user_name || user?.email?.split('@')[0] || '')
      setGender(result.gender || null)
      setCreativityLevel(result.creativity_level || 'rostoFiel')
      setCreativityLevelSelected(!!result.creativity_level)
      
      // Definir quantidade baseada na plataforma
      const quantities = { netflix: 12, disney: 9, amazon: 11 }
      setQuantity(quantities[result.streaming_platform as keyof typeof quantities] || 12)
      
      // Se houver imagem original, tentar buscar ela
      if (generationData.original_image_url) {
        console.log('🖼️ Imagem original encontrada:', generationData.original_image_url)
        // Note: Não podemos definir selectedFile diretamente para uma URL
        // O usuário verá as capas carregadas e pode gerar novas se quiser
      }
      
      setShowWizard(true)
      console.log('✅ Wizard preenchido com todos os dados da geração')
      toast.success(`Geração carregada com sucesso! ${result.covers?.length || 0} capas e todas as configurações foram restauradas.`)
    } else {
      toast.error('Não foi possível carregar os dados da geração selecionada.')
    }
  }

  // 🔥 Load previous generation
  const handleLoadPreviousGeneration = async () => {
    console.log('🔍 Verificando gerações existentes...')
    const hasGenerations = await checkExistingGenerations()
    
    if (hasGenerations) {
      console.log('✅ Gerações encontradas, abrindo seletor...')
      setShowGenerationSelector(true)
    } else {
      console.log('❌ Nenhuma geração encontrada')
      toast.info('Você ainda não tem gerações anteriores.')
    }
  }

  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-brand-white">
        <Loader2 className="w-12 h-12 text-brand-primary animate-spin" />
      </div>
    );
  }

  // Auth form
  if (!user) {
    return (
      <div className="min-h-screen">
        <Toaster
          position="top-center"
          toastOptions={{
            classNames: {
              toast: 'bg-brand-white text-brand-text border-2 border-brand-black shadow-brutal-sm font-semibold',
              success: 'bg-green-200 border-green-500',
              error: 'bg-red-200 border-red-500',
              warning: 'bg-yellow-200 border-yellow-500',
            },
          }}
        />
        <AuthForm onAuthSuccess={setUser} />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 text-brand-text relative">
      <Toaster
        position="top-right"
        toastOptions={{
          classNames: {
            toast: 'bg-brand-white text-brand-text border-2 border-brand-black shadow-brutal-sm font-semibold rounded-lg p-4',
            success: '!bg-green-200 !border-green-500',
            error: '!bg-red-200 !border-red-500',
            warning: '!bg-yellow-200 !border-yellow-500',
          },
        }}
      />
      
      <div className="relative z-10">
        {/* Header */}
        <AppHeader 
          user={user}
          onLogout={handleLogout}
          availableCredits={creditBalance.availableCredits}
          onOpenDashboard={() => setShowDashboard(true)}
          onBuyCredits={() => setShowPricingModal(true)}
          onOpenGenerations={() => setShowGenerations(true)}
          onOpenContact={() => setShowContact(true)}
          onSetPassword={() => setShowSetPasswordModal(true)}
        />

        {/* Payment Intent Warning */}
        {paymentIntent && (
          <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-yellow-700">Payment in progress...</p>
              </div>
            </div>
          </div>
        )}

          {/* Main Content */}
        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Generation Wizard */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-16"
          >
          {showWizard ? (
            <div className="wizard-container" data-testid="wizard-container">
              <CoverGenerationWizard
                key={`wizard-${wizardInitialStep}-${showWizard}`}
                user={user}
                onGenerate={handleGenerate}
                onOpenGallery={() => {
                  // Definir configurações padrão para o modo galeria
                  if (!streamingPlatform) setStreamingPlatform('netflix')
                  if (!photoType) setPhotoType('individual')
                  if (!language) setLanguage('portuguese')
                  if (!quantity) setQuantity(2)
                  
                  // Ativar o modo galeria e fechar o wizard
                  setShowGalleryMode(true)
                  setShowWizard(false)
                  setWizardInitialStep(0)
                }}
                selectedFile={selectedFile}
                setSelectedFile={setSelectedFile}
                photoType={photoType}
                setPhotoType={setPhotoType}
                gender={gender}
                setGender={setGender}
                streamingPlatform={streamingPlatform}
                setStreamingPlatform={setStreamingPlatform}
                language={language}
                setLanguage={setLanguage}
                generateTitles={generateTitles}
                setGenerateTitles={setGenerateTitles}
                userName={userName}
                setUserName={setUserName}
                canGenerate={canGenerate}
                isGenerating={isGenerating}
                onClose={() => { 
                  setShowWizard(false) 
                  setWizardInitialStep(0)
                }}
                loadPreviousGeneration={handleLoadPreviousGeneration}
                hasExistingGenerations={hasExistingGenerations}
                hasAvailableCredits={creditBalance.availableCredits > 0}
                availableCredits={creditBalance.availableCredits}
                creditsNeeded={getCreditsNeeded()}
                onSelectPlan={handleCreatePayment}
                paymentLoading={paymentLoading}
                onStepChange={() => {}}
                creativityLevel={creativityLevel}
                setCreativityLevel={handleSetCreativityLevel}
                creativityLevelSelected={creativityLevelSelected}
                initialStep={wizardInitialStep}
                onStartFromScratch={() => clearGeneration && clearGeneration(false)}
              />
            </div>
          ) : (
            <div className="text-center mb-8">
              <div className="flex flex-col sm:flex-row gap-4 justify-center max-w-md mx-auto">
                <button
                  onClick={handleNewGeneration}
                  className="flex-1 bg-brand-primary text-brand-black font-bold py-4 px-6 rounded-lg border-2 border-brand-black shadow-brutal-sm hover:shadow-brutal-hover active:shadow-brutal-pressed transition-all flex items-center justify-center space-x-2"
                >
                  <Wand2 className="w-5 h-5" />
                  <span>Gerar Cover com IA</span>
                </button>
                
                <button
                  onClick={() => {
                    setShowGalleryMode(true)
                    // Definir configurações padrão para o modo galeria
                    if (!streamingPlatform) setStreamingPlatform('netflix')
                    if (!photoType) setPhotoType('individual')
                    if (!language) setLanguage('portuguese')
                    if (!quantity) setQuantity(2)
                  }}
                  className="flex-1 bg-brand-white text-brand-black font-bold py-4 px-6 rounded-lg border-2 border-brand-black shadow-brutal-sm hover:shadow-brutal-hover active:shadow-brutal-pressed transition-all flex items-center justify-center space-x-2"
                >
                  <FolderOpen className="w-5 h-5" />
                  <span>Gerar Cover com fotos da galeria</span>
                </button>
              </div>
            </div>
          )}
        </motion.div>

        {/* Generated Covers */}
        <AnimatePresence>
          {(generatedCovers.length > 0 || isGenerating || (progress.total > 0 && progress.completed === 0 && progress.failed > 0) || showGalleryMode) && (
            <motion.div
              key="cover-grid"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-brand-white rounded-2xl shadow-brutal-lg border-2 border-brand-black p-6 md:p-8"
            >
              {/* Error State - All generations failed */}
              {!isGenerating && progress.total > 0 && progress.completed === 0 && progress.failed > 0 && generatedCovers.length === 0 && (
                <div className="text-center py-12">
                  <div className="w-20 h-20 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6 border-2 border-red-300">
                    <X className="w-10 h-10 text-red-500" />
                  </div>
                  <h3 className="text-2xl font-bold text-red-600 mb-4">
                    Erro na Geração
                  </h3>
                  <p className="text-gray-600 mb-2">
                    Todas as {progress.failed} capas falharam ao serem geradas.
                  </p>
                  <p className="text-gray-500 text-sm mb-8">
                    Erro: "Cannot read properties of undefined (reading 'max_tokens')"
                  </p>
                  
                  <div className="flex flex-col sm:flex-row gap-4 justify-center max-w-md mx-auto">
                    <button
                      onClick={() => {
                        // Tentar novamente com os mesmos parâmetros
                        if (selectedFile && photoType && streamingPlatform && language && quantity && userName.trim()) {
                          handleGenerate()
                        }
                      }}
                      className="flex-1 bg-brand-accent text-white font-bold py-3 px-6 rounded-lg border-2 border-brand-black shadow-brutal-sm hover:shadow-brutal-hover active:shadow-brutal-pressed transition-all"
                    >
                      🔄 Tentar Novamente
                    </button>
                    
                    <button
                      onClick={handleNewGeneration}
                      className="flex-1 bg-brand-primary text-brand-black font-bold py-3 px-6 rounded-lg border-2 border-brand-black shadow-brutal-sm hover:shadow-brutal-hover active:shadow-brutal-pressed transition-all"
                    >
                      🎬 Nova Geração
                    </button>
                  </div>
                </div>
              )}

              {/* Normal CoverGrid */}
              {(generatedCovers.length > 0 || isGenerating || showGalleryMode) && (
                <CoverGrid
                  covers={showGalleryMode && generatedCovers.length === 0 ? [] : generatedCovers}
                  isLoading={isGenerating}
                  onRegenerateIndividual={(index, addCallback) => regenerateIndividualCover(index, addCallback)}
                  onRegenerateCoverImage={regenerateCoverImage}
                  regeneratingIndex={regeneratingIndex}
                  regeneratingIndices={regeneratingIndices}
                  expectedQuantity={quantity}
                  streamingPlatform={streamingPlatform || undefined}
                  photoType={photoType || undefined}
                  originalImageUrl={originalImageUrl || undefined}
                  userName={userName || undefined}
                  failedIndices={failedIndices}
                  onRetryFailed={retryFailedCover}
                  progress={progress}
                  currentMovie={currentMovie}
                  currentGenerationId={currentGenerationId}
                  forceLoadCovers={forceLoadCovers}
                  refreshCovers={refreshCovers}
                  refreshCurrentGeneration={refreshCurrentGeneration}
                  gender={gender}
                  language={language || 'portuguese'}
                  generateTitles={generateTitles}
                  creativityLevel={creativityLevel}
                />
              )}
            </motion.div>
          )}
        </AnimatePresence>

    
        </main>
      </div>

      {/* Modals */}
      <PricingModal
        isOpen={showPricingModal}
        onClose={() => setShowPricingModal(false)}
        onSelectPlan={handleCreatePayment}
        loading={paymentLoading}
        currentCredits={creditBalance.availableCredits}
        user={user}
        onVoucherRedeemed={() => {
          // Recarregar créditos após resgatar voucher
          invalidateAfterPayment();
          setDashboardRefreshTrigger(prev => prev + 1);
        }}
      />

      {paymentIntent && paymentIntent.clientSecret && paymentIntent.planId && (
        <CheckoutForm
          stripePromise={stripePromise}
          clientSecret={paymentIntent.clientSecret}
          planId={paymentIntent.planId}
          paymentIntentId={paymentIntent.paymentIntentId}
          onSuccess={handlePaymentSuccess}
          onCancel={handleCancelPayment}
        />
      )}

      {/* User Dashboard Modal */}
      <AnimatePresence>
        {showDashboard && user && (
          <UserDashboard 
            user={user} 
            onClose={() => setShowDashboard(false)}
            refreshTrigger={dashboardRefreshTrigger}
          />
        )}
      </AnimatePresence>

      {/* My Generations Modal */}
      {showGenerations && (
        <motion.div 
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
          onClick={() => setShowGenerations(false)}
        >
          <motion.div 
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            className="bg-white rounded-3xl p-6 max-w-7xl max-h-[90vh] overflow-y-auto shadow-2xl w-full"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-bold text-gray-800">Minhas Gerações</h2>
              <button
                onClick={() => setShowGenerations(false)}
                className="p-2 bg-neu-base rounded-xl hover:shadow-neu-pressed transition-all duration-200"
              >
                <X size={20} className="text-gray-600" />
              </button>
            </div>
            
            <MyGenerations />
          </motion.div>
        </motion.div>
      )}

      {/* 🔥 Modal para Nova Geração */}
      <NewGenerationModal
        isOpen={showNewGenerationModal}
        onClose={() => setShowNewGenerationModal(false)}
        onCreateFromScratch={handleCreateFromScratch}
        onUseSameItems={handleUseSameItems}
        hasExistingData={
          !!lastGenerationParams || 
          generatedCovers.length > 0 || 
          !!selectedFile || 
          !!streamingPlatform ||
          !!photoType
        }
      />

      {/* 🔥 Modal para Seleção de Geração */}
      <GenerationSelector
        isOpen={showGenerationSelector}
        onClose={() => setShowGenerationSelector(false)}
        onSelectGeneration={handleLoadSpecificGeneration}
      />

      {/* Contact Form Modal */}
      {showContact && (
        <motion.div 
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
          onClick={() => setShowContact(false)}
        >
          <motion.div 
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            className="bg-white rounded-3xl p-6 max-w-5xl max-h-[90vh] overflow-y-auto shadow-2xl w-full"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-bold text-gray-800">Contato</h2>
              <button
                onClick={() => setShowContact(false)}
                className="p-2 bg-neu-base rounded-xl hover:shadow-neu-pressed transition-all duration-200"
              >
                <X size={20} className="text-gray-600" />
              </button>
            </div>
            
            <ContactForm />
          </motion.div>
        </motion.div>
      )}

      {/* Set Password Modal */}
      <SetPasswordModal
        isOpen={showSetPasswordModal}
        onClose={() => setShowSetPasswordModal(false)}
        userEmail={user?.email || ''}
      />
    </div>
  );
}

// Main App component with QueryClient provider
function App() {
  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <AppContent />
      </QueryClientProvider>
    </ErrorBoundary>
  )
}

export default App;
