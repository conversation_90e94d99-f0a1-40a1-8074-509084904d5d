import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    console.log('🔍 Starting upload test...');
    
    const supabaseUrl = Deno.env.get('SUPABASE_URL') || '';
    const supabaseServiceRoleKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || '';
    
    console.log(`📊 Environment check:`);
    console.log(`- SUPABASE_URL: ${supabaseUrl ? 'SET' : 'MISSING'}`);
    console.log(`- SUPABASE_SERVICE_ROLE_KEY: ${supabaseServiceRoleKey ? 'SET' : 'MISSING'}`);
    
    if (!supabaseUrl || !supabaseServiceRoleKey) {
      throw new Error('Missing Supabase credentials');
    }
    
    const supabase = createClient(supabaseUrl, supabaseServiceRoleKey);
    
    // Test URL - uma imagem pequena do Replicate para teste
    const testImageUrl = 'https://replicate.delivery/xezq/vhBa3wAzqJLKP1Je9yvlh4nvhwC5DaJNBnf1GJcxQDhfEWCqA/tmp007zemat.jpg';
    
    console.log(`📥 Testing download from: ${testImageUrl}`);
    
    const imageResponse = await fetch(testImageUrl);
    if (!imageResponse.ok) {
      throw new Error(`Failed to download test image: ${imageResponse.status} ${imageResponse.statusText}`);
    }
    
    console.log(`✅ Download successful`);
    console.log(`- Status: ${imageResponse.status}`);
    console.log(`- Content-Type: ${imageResponse.headers.get('content-type')}`);
    console.log(`- Content-Length: ${imageResponse.headers.get('content-length')}`);
    
    const imageBlob = await imageResponse.blob();
    const imageBuffer = await imageBlob.arrayBuffer();
    
    console.log(`📦 Buffer created: ${imageBuffer.byteLength} bytes`);
    
    const filename = `test-uploads/test-${Date.now()}.jpg`;
    
    console.log(`📤 Uploading to: ${filename}`);
    
    const { data, error } = await supabase.storage.from('generated-images').upload(filename, imageBuffer, {
      contentType: 'image/jpeg',
      upsert: false
    });
    
    if (error) {
      console.error(`❌ Upload error:`, error);
      throw new Error(`Upload failed: ${JSON.stringify(error)}`);
    }
    
    console.log(`✅ Upload successful:`, data);
    
    const { data: { publicUrl } } = supabase.storage.from('generated-images').getPublicUrl(filename);
    
    console.log(`🌐 Public URL: ${publicUrl}`);
    
    return new Response(JSON.stringify({
      success: true,
      message: 'Upload test successful',
      filename,
      publicUrl,
      fileSize: imageBuffer.byteLength
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200
    });

  } catch (error) {
    console.error('❌ Test failed:', error);
    return new Response(JSON.stringify({
      success: false,
      error: error.message
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 500
    });
  }
});
