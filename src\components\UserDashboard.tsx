import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  User, 
  ImageIcon, 
  Package, 
  CreditCard,
  FileText,
  TestTube,
  RefreshCw
} from 'lucide-react'
import { useQueryClient } from '@tanstack/react-query'
import { useTranslation } from 'react-i18next'

import { usePayment } from '../hooks/usePayment'
import { useCreditBalance } from '../hooks/useCreditBalance'
import MyGenerations from './MyGenerations'
import Dashboard from './Dashboard'
import GenerationLogs from './GenerationLogs'
import CanvaTestComponent from './CanvaTestComponent'
import { formatDateTime } from '../utils/dateUtils'
import { supabase } from '../lib/supabase'
import { showToast } from '../utils/toast'

interface UserDashboardProps {
  user: { id: string; email?: string; user_metadata?: { role?: string } } | null
  onClose: () => void
  refreshTrigger?: number // Para forçar refresh quando pagamento for concluído
}

type Tab = 'profile' | 'generations' | 'logs' | 'packs' | 'payments' | 'test'

export default function UserDashboard({ user, onClose, refreshTrigger }: UserDashboardProps) {
  const { t } = useTranslation()
  const [activeTab, setActiveTab] = useState<Tab>('profile')
  const queryClient = useQueryClient()
  
  // 🔥 Usar o mesmo hook que o header para garantir consistência
  const { creditBalance, isLoading: loadingCredits, refetch: refetchCredits } = useCreditBalance(user?.id || null)
  
  // Estados para dados adicionais (histórico e compras)
  const [creditUsage, setCreditUsage] = useState<any[]>([])
  const [paymentHistory, setPaymentHistory] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)

  // Carregar dados reais quando o componente montar ou quando refreshTrigger mudar
  useEffect(() => {
    if (user?.id) {
      loadDashboardData()
    }
  }, [user?.id, refreshTrigger])

  // Escutar mudanças na tabela de compras em tempo real
  useEffect(() => {
    if (!user?.id) return

    const channel = supabase
      .channel('credit_purchases_changes')
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'credit_purchases',
          filter: `user_id=eq.${user.id}`
        },
        (payload) => {
          console.log('💰 Compra atualizada:', payload)
          // Recarregar dados quando uma compra for atualizada
          loadDashboardData()
          // Também invalidar cache do React Query
          if (user?.id) {
            queryClient.invalidateQueries({ queryKey: ['credits', 'balance', user.id] })
          }
        }
      )
      .subscribe()

    return () => {
      supabase.removeChannel(channel)
    }
  }, [user?.id])

  const loadDashboardData = async () => {
    try {
      setIsLoading(true)

      // Buscar compras de créditos
      const { data: purchases, error: purchasesError } = await supabase
        .from('credit_purchases')
        .select('*')
        .eq('user_id', user?.id)
        .order('created_at', { ascending: false })

      if (purchasesError) throw purchasesError

      // Buscar uso de créditos
      const { data: usage, error: usageError } = await supabase
        .from('credit_usage')
        .select('*')
        .eq('user_id', user?.id)
        .order('created_at', { ascending: false })

      if (usageError) throw usageError

      setPaymentHistory(purchases || [])
      setCreditUsage(usage || [])

      showToast.success(t('notifications.dataUpdated'))

      // 🔥 INVALIDAR CACHE DO REACT QUERY PARA ATUALIZAR O HEADER E RECARREGAR CRÉDITOS
      if (user?.id) {
        queryClient.invalidateQueries({ queryKey: ['credits', 'balance', user.id] })
        refetchCredits() // Forçar recarregamento dos créditos
      }

      // 🔥 REMOVIDO: Lógica que completava automaticamente compras pendentes
      // Isso estava causando adição incorreta de créditos apenas por abrir o dashboard

    } catch (error) {
      console.error('Erro ao carregar dados do dashboard:', error)
      showToast.error(t('notifications.errorLoadingData'))
    } finally {
      setIsLoading(false)
    }
  }

  // 🔥 Usar função utilitária para formatação consistente com hora
  const formatDate = formatDateTime

  const tabs = [
    { id: 'profile', label: 'dashboard_tabs.dashboard', icon: User },
    { id: 'generations', label: 'dashboard_tabs.myGenerations', icon: ImageIcon },
    { id: 'logs', label: 'dashboard_tabs.logs', icon: FileText },
    { id: 'packs', label: 'dashboard_tabs.credits', icon: Package },
    { id: 'payments', label: 'dashboard_tabs.purchases', icon: CreditCard },
    // Aba Test apenas para admins
    ...(user?.user_metadata?.role === 'admin' ? [{ id: 'test', label: 'dashboard_tabs.test', icon: TestTube }] : [])
  ]

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        className="bg-brand-white rounded-xl shadow-brutal-lg border-2 border-brand-black w-full max-w-6xl max-h-[90vh] flex flex-col overflow-hidden"
      >
        {/* Header */}
        <div className="bg-brand-white p-5 border-b-2 border-brand-black flex-shrink-0">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-brand-primary rounded-lg flex items-center justify-center border-2 border-brand-black">
                <User className="w-6 h-6 text-brand-black" />
              </div>
              <div>
                <h2 className="text-2xl font-bold text-brand-text">{t('dashboard.title')}</h2>
                {user && <p className="text-brand-text/70">{user.email}</p>}
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => {
                  loadDashboardData()
                  refetchCredits()
                }}
                disabled={isLoading || loadingCredits}
                className="p-2 bg-brand-secondary rounded-lg border-2 border-brand-black shadow-brutal-sm hover:shadow-brutal-hover active:shadow-brutal-pressed transition-all disabled:opacity-50"
                title={t('dashboard.refresh')}
              >
                <svg className={`w-5 h-5 text-brand-black ${(isLoading || loadingCredits) ? 'animate-spin' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
              </button>
              <button
                onClick={onClose}
                className="p-2 bg-brand-white rounded-lg border-2 border-brand-black shadow-brutal-sm hover:shadow-brutal-hover active:shadow-brutal-pressed transition-all"
              >
                <svg className="w-6 h-6 text-brand-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="border-b-2 border-brand-black flex-shrink-0">
          <nav className="flex space-x-2 p-2 bg-gray-100">
            {tabs.map((tab) => {
              const Icon = tab.icon
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`
                    flex items-center space-x-2 py-2 px-4 rounded-lg border-2 border-brand-black font-semibold transition-all
                    ${activeTab === tab.id
                      ? 'bg-brand-primary shadow-brutal-pressed'
                      : 'bg-brand-white shadow-brutal-sm hover:shadow-brutal-hover'
                    }
                  `}
                >
                  <Icon className="w-4 h-4 text-brand-black" />
                  <span className="text-brand-black">{t(tab.label)}</span>
                </button>
              )
            })}
          </nav>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto bg-gray-50 flex-grow">
          {(isLoading || loadingCredits) && (
            <div className="bg-brand-accent/20 border-2 border-brand-accent text-brand-accent rounded-lg p-4 mb-6">
              <h3 className="font-bold">{t('notifications.loadingDashboardData')}</h3>
            </div>
          )}
          
          {activeTab === 'profile' && user && (
            <Dashboard user={user} setActiveTab={setActiveTab} />
          )}

          {activeTab === 'generations' && (
            <div className="mt-4">
              <MyGenerations />
            </div>
          )}

          {activeTab === 'logs' && user && (
            <div className="mt-4">
              <GenerationLogs userId={user.id} />
            </div>
          )}

          {activeTab === 'packs' && (
            <div className="space-y-8">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="bg-brand-primary rounded-xl p-6 border-2 border-brand-black shadow-brutal">
                    <p className="text-brand-black font-semibold">{t('dashboard.availableCredits')}</p>
                    <p className="text-4xl font-black text-brand-black mt-1">{creditBalance.availableCredits}</p>
                </div>
                <div className="bg-brand-secondary rounded-xl p-6 border-2 border-brand-black shadow-brutal">
                    <p className="text-brand-black font-semibold">{t('dashboard.usedCredits')}</p>
                    <p className="text-4xl font-black text-brand-black mt-1">{creditBalance.usedCredits}</p>
                </div>
                <div className="bg-brand-accent rounded-xl p-6 border-2 border-brand-black shadow-brutal">
                    <p className="text-white font-semibold">{t('dashboard.totalCredits')}</p>
                    <p className="text-4xl font-black text-white mt-1">{creditBalance.totalCredits}</p>
                </div>
              </div>

              <div className="bg-brand-white border-2 border-brand-black rounded-xl overflow-hidden shadow-brutal">
                <div className="px-6 py-4 border-b-2 border-brand-black">
                  <h3 className="text-lg font-bold text-brand-text">{t('dashboard.creditHistory')}</h3>
                </div>
                <div className="overflow-x-auto">
                  <table className="w-full text-left">
                    <thead className="border-b-2 border-brand-black">
                      <tr>
                        <th className="px-6 py-3 text-sm font-bold text-brand-text">{t('dashboard.date')}</th>
                        <th className="px-6 py-3 text-sm font-bold text-brand-text">{t('dashboard.description')}</th>
                        <th className="px-6 py-3 text-sm font-bold text-brand-text">{t('dashboard.creditsUsed')}</th>
                      </tr>
                    </thead>
                    <tbody>
                      {creditUsage.map((usage) => (
                        <tr key={usage.id} className="border-b border-gray-200 last:border-b-0">
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-brand-text">
                            {formatDate(usage.created_at)}
                          </td>
                          <td className="px-6 py-4 text-sm font-semibold text-brand-text">
                            {usage.description}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-brand-text">
                            {usage.credits_used}
                          </td>
                        </tr>
                      ))}
                      {creditUsage.length === 0 && (
                        <tr>
                          <td colSpan={3} className="px-6 py-8 text-center text-brand-text/60">
                            {t('dashboard.noCreditUsageFound')}
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'payments' && (
            <div className="space-y-6">
              <div className="bg-brand-white border-2 border-brand-black rounded-xl overflow-hidden shadow-brutal">
                <div className="px-6 py-4 border-b-2 border-brand-black">
                  <h3 className="text-lg font-bold text-brand-text">{t('dashboard.purchaseHistory')}</h3>
                </div>
                <div className="overflow-x-auto">
                  <table className="w-full text-left">
                    <thead className="border-b-2 border-brand-black">
                      <tr>
                        <th className="px-6 py-3 text-sm font-bold text-brand-text">{t('dashboard.date')}</th>
                        <th className="px-6 py-3 text-sm font-bold text-brand-text">{t('dashboard.credits')}</th>
                        <th className="px-6 py-3 text-sm font-bold text-brand-text">{t('dashboard.amount')}</th>
                        <th className="px-6 py-3 text-sm font-bold text-brand-text">{t('creditBalance.status')}</th>
                      </tr>
                    </thead>
                    <tbody>
                      {paymentHistory.map((payment) => (
                        <tr key={payment.id} className="border-b border-gray-200 last:border-b-0">
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-brand-text">
                            {formatDate(payment.created_at)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-semibold text-brand-text">
                            {payment.credits}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-brand-text">
                            R$ {(payment.amount / 100).toFixed(2)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`px-3 py-1 text-xs font-bold rounded-lg border-2 border-brand-black ${
                              payment.status === 'completed' ? 'bg-green-300 text-brand-black' :
                              payment.status === 'pending' ? 'bg-yellow-300 text-brand-black' :
                              'bg-red-300 text-brand-black'
                            }`}>
                              {payment.status}
                            </span>
                          </td>
                        </tr>
                      ))}
                      {paymentHistory.length === 0 && (
                        <tr>
                          <td colSpan={4} className="px-6 py-8 text-center text-brand-text/60">
                            {t('dashboard.noPurchaseFound')}
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'test' && (
            <div className="mt-4">
              <CanvaTestComponent />
            </div>
          )}
        </div>
      </motion.div>
    </div>
  )
} 