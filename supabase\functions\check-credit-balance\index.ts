import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient, SupabaseClient } from "https://esm.sh/@supabase/supabase-js@2"

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST',
}

const supabaseUrl = Deno.env.get('SUPABASE_URL')!
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Create a Supabase client with the user's auth token
    const authHeader = req.headers.get('Authorization')!
    const supabase = createClient(supabaseUrl, supabaseServiceKey, {
      global: {
        headers: { Authorization: authHeader }
      }
    })

    // Get the user from the token
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError) {
      return new Response(JSON.stringify({ error: 'Falha na autenticação' }), { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } })
    }
    const userId = user.id

    // Use a service role client for database operations from now on
    const serviceClient = createClient(supabaseUrl, supabaseServiceKey)

    // 🔥 Primeiro, garantir que o usuário tem um registro na tabela user_credits
    const { data: existingCredits, error: checkError } = await serviceClient
      .from('user_credits')
      .select('*')
      .eq('user_id', userId)
      .single()

    if (checkError && checkError.code === 'PGRST116') {
      // Usuário não tem registro, vamos criar um
      console.log('🔥 Criando registro de créditos para usuário:', userId)
      const { error: insertError } = await serviceClient
        .from('user_credits')
        .insert({
          user_id: userId,
          total_credits: 0,
          available_credits: 0,
          used_credits: 0
        })

      if (insertError) {
        console.error('❌ Erro ao criar registro de créditos:', insertError)
        return new Response(
          JSON.stringify({ error: 'Erro ao inicializar créditos do usuário' }),
          { 
            status: 500, 
            headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
          }
        )
      }
    }

    // Buscar saldo atual de créditos
    const { data: userCredits, error: creditsError } = await serviceClient
      .from('user_credits')
      .select('total_credits, available_credits, used_credits')
      .eq('user_id', userId)
      .single()

    if (creditsError) {
      console.error('Erro ao buscar créditos do usuário:', creditsError)
      return new Response(
        JSON.stringify({ error: 'Erro ao buscar créditos do usuário' }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Buscar histórico de compras para o histórico
    const { data: purchases, error: purchasesError } = await serviceClient
      .from('credit_purchases')
      .select('credits, created_at, plan_name')
      .eq('user_id', userId)
      .eq('status', 'completed')
      .order('created_at', { ascending: false })
      .limit(10)

    // Buscar histórico de uso para o histórico
    const { data: usage, error: usageError } = await serviceClient
      .from('credit_usage')
      .select('credits_used, description, created_at')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(10)

    // Preparar histórico de créditos (compras e uso combinados)
    const creditHistory = [
      ...(purchases?.map(p => ({
        id: `purchase_${p.created_at}`,
        amount: p.credits,
        type: 'purchase' as const,
        description: `Compra: ${p.plan_name || 'Créditos'} - ${p.credits} créditos`,
        created_at: p.created_at
      })) || []),
      ...(usage?.map(u => ({
        id: `usage_${u.created_at}`,
        amount: -u.credits_used,
        type: 'usage' as const,
        description: u.description,
        created_at: u.created_at
      })) || [])
    ].sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())

    // Últimos 10 usos
    const recentUsage = usage?.map(u => ({
      id: u.created_at,
      amount: u.credits_used,
      description: u.description,
      created_at: u.created_at
    })) || []

    const totalCredits = userCredits?.total_credits || 0
    const availableCredits = userCredits?.available_credits || 0
    const usedCredits = userCredits?.used_credits || 0

    return new Response(
      JSON.stringify({
        totalCredits,
        availableCredits,
        usedCredits,
        creditHistory: creditHistory.slice(0, 20), // Últimos 20 itens
        recentUsage
      }),
      { 
        status: 200, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )

  } catch (error) {
    console.error('Erro na função check-credit-balance:', error)
    return new Response(
      JSON.stringify({ error: 'Erro interno do servidor' }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
}) 