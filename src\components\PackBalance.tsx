import React from 'react'
import { useTranslation } from 'react-i18next'
import { Package, Plus, Clock, CheckCircle } from 'lucide-react'

interface PackBalanceProps {
  balance: {
    totalPacks: number
    availablePacks: number
    usedPacks: number
    packs: any[]
    recentUsage: any[]
  }
  onBuyMore: () => void
  loading?: boolean
}

export default function PackBalance({ balance, onBuyMore, loading }: PackBalanceProps) {
  const { t, i18n } = useTranslation()

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6 animate-pulse">
        <div className="h-6 bg-gray-200 rounded mb-4"></div>
        <div className="h-4 bg-gray-200 rounded mb-2"></div>
        <div className="h-4 bg-gray-200 rounded"></div>
      </div>
    )
  }

  const formatDate = (date: string) => new Date(date).toLocaleDateString(i18n.language)

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <Package className="w-6 h-6 text-blue-600" />
          <h3 className="text-lg font-semibold text-gray-800">{t('packBalance.title')}</h3>
        </div>
        <button
          onClick={onBuyMore}
          className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <Plus size={16} />
          <span>{t('packBalance.buyMore')}</span>
        </button>
      </div>

      {/* Pack Statistics */}
      <div className="grid grid-cols-3 gap-4 mb-6">
        <div className="text-center p-4 bg-blue-50 rounded-lg">
          <div className="text-2xl font-bold text-blue-600">{balance.availablePacks}</div>
          <div className="text-sm text-blue-700">{t('packBalance.available')}</div>
        </div>
        <div className="text-center p-4 bg-green-50 rounded-lg">
          <div className="text-2xl font-bold text-green-600">{balance.usedPacks}</div>
          <div className="text-sm text-green-700">{t('packBalance.used')}</div>
        </div>
        <div className="text-center p-4 bg-gray-50 rounded-lg">
          <div className="text-2xl font-bold text-gray-600">{balance.totalPacks}</div>
          <div className="text-sm text-gray-700">{t('packBalance.total')}</div>
        </div>
      </div>

      {/* Pack Details */}
      {balance.packs && balance.packs.length > 0 && (
        <div className="mb-6">
          <h4 className="font-semibold text-gray-800 mb-3">{t('packBalance.purchasedPacks')}</h4>
          <div className="space-y-2">
            {balance.packs.map((pack: any, index: number) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div>
                  <span className="font-medium text-gray-800">{t(pack.pack_type)}</span>
                  <div className="text-sm text-gray-600">{t('packBalance.purchasedOn', { date: formatDate(pack.purchased_at) })}</div>
                </div>
                <div className="text-right">
                  <div className="text-sm font-medium text-gray-800">{t('packBalance.packsAvailable', { available: pack.available_packs, total: pack.total_packs })}</div>
                  <div className="text-xs text-gray-600">{t('packBalance.packsUsed', { used: pack.used_packs })}</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Recent Usage */}
      {balance.recentUsage && balance.recentUsage.length > 0 && (
        <div>
          <h4 className="font-semibold text-gray-800 mb-3 flex items-center space-x-2">
            <Clock size={16} />
            <span>{t('packBalance.recentUsage')}</span>
          </h4>
          <div className="space-y-2">
            {balance.recentUsage.slice(0, 5).map((usage: any, index: number) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <CheckCircle className="w-4 h-4 text-green-600" />
                  <div>
                    <div className="text-sm font-medium text-gray-800">
                      {usage.streaming_platform.charAt(0).toUpperCase() + usage.streaming_platform.slice(1)}
                    </div>
                    <div className="text-xs text-gray-600">
                      {usage.photo_type === 'individual' ? 'Individual' : 'Casal'}
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-xs text-gray-600">
                    {new Date(usage.created_at).toLocaleDateString('pt-BR')}
                  </div>
                  <div className="text-xs text-gray-500">
                    {new Date(usage.created_at).toLocaleTimeString('pt-BR', { 
                      hour: '2-digit', 
                      minute: '2-digit' 
                    })}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Empty State */}
      {balance.totalPacks === 0 && (
        <div className="text-center py-8">
          <Package className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h4 className="text-lg font-medium text-gray-600 mb-2">{t('packBalance.noPacks')}</h4>
          <p className="text-gray-500 mb-4">{t('packBalance.buyFirstPack')}</p>
          <button
            onClick={onBuyMore}
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            {t('packBalance.buyFirstPackButton')}
          </button>
        </div>
      )}

      {/* Low Balance Warning */}
      {balance.availablePacks > 0 && balance.availablePacks <= 2 && (
        <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
            <span className="text-sm text-yellow-800">{t('packBalance.lowBalanceWarning', { count: balance.availablePacks })} {t('packBalance.considerBuying')}</span>
          </div>
        </div>
      )}
    </div>
  )
} 