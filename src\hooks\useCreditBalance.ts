import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { checkUserCreditBalance, consumeCredits } from '../lib/stripe'
import { showToast } from '../utils/toast'

interface CreditBalance {
  totalCredits: number
  availableCredits: number
  usedCredits: number
  creditHistory: any[]
  recentUsage: any[]
}

// Query key factory
const creditKeys = {
  all: ['credits'] as const,
  balance: (userId: string) => [...creditKeys.all, 'balance', userId] as const,
}

export function useCreditBalance(userId: string | null) {
  const queryClient = useQueryClient()

  // Query para buscar saldo de créditos
  const {
    data: creditBalance,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: creditKeys.balance(userId || ''),
    queryFn: () => checkUserCreditBalance(userId!),
    enabled: !!userId,
    staleTime: 0, // 🔥 Sem cache para sempre buscar dados frescos
    gcTime: 5 * 60 * 1000, // 5 minutos (novo nome para cacheTime)
    retry: 3,
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
  })

  // Mutation para consumir créditos
  const consumeCreditsMutation = useMutation({
    mutationFn: ({ amount, description }: { amount: number; description: string }) =>
      consumeCredits(userId!, amount, description),
    onSuccess: (data, variables) => {
      // Invalidar e recarregar o saldo
      queryClient.invalidateQueries({ queryKey: creditKeys.balance(userId!) })
      
      // Atualizar optimisticamente o cache local
      queryClient.setQueryData(creditKeys.balance(userId!), (old: CreditBalance | undefined) => {
        if (!old) return old
        return {
          ...old,
          availableCredits: old.availableCredits - variables.amount,
          usedCredits: old.usedCredits + variables.amount
        }
      })
      
      showToast.success(`${variables.amount} créditos consumidos`)
    },
    onError: (error) => {
      console.error('Erro ao consumir créditos:', error)
      showToast.error('Erro ao consumir créditos')
    }
  })

  // Função para invalidar o cache após pagamento
  const invalidateAfterPayment = () => {
    console.log('🔄 Invalidating credit balance cache...')
    queryClient.invalidateQueries({ queryKey: creditKeys.balance(userId!) })
    // 🔥 Forçar refetch imediato para garantir atualização
    setTimeout(() => {
      refetch()
    }, 100)
  }

  // Função para atualizar optimisticamente após pagamento ou consumo
  const updateAfterPayment = (creditChange: number) => {
    console.log(`🔄 Updating credits optimistically: ${creditChange > 0 ? '+' : ''}${creditChange}`)
    queryClient.setQueryData(creditKeys.balance(userId!), (old: CreditBalance | undefined) => {
      if (!old) return old
      
      const newAvailableCredits = Math.max(0, old.availableCredits + creditChange)
      const newUsedCredits = creditChange < 0 ? old.usedCredits + Math.abs(creditChange) : old.usedCredits
      const newTotalCredits = creditChange > 0 ? old.totalCredits + creditChange : old.totalCredits
      
      return {
        ...old,
        totalCredits: newTotalCredits,
        availableCredits: newAvailableCredits,
        usedCredits: newUsedCredits
      }
    })
  }

  return {
    creditBalance: creditBalance || {
      totalCredits: 0,
      availableCredits: 0,
      usedCredits: 0,
      creditHistory: [],
      recentUsage: []
    },
    isLoading,
    error,
    refetch,
    consumeCredits: consumeCreditsMutation.mutate,
    isConsumingCredits: consumeCreditsMutation.isPending,
    invalidateAfterPayment,
    updateAfterPayment
  }
}