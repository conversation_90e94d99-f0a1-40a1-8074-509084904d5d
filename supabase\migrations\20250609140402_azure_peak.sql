/*
  # Movie Cover Generator Database Schema

  1. New Tables
    - `movies_series`
      - `id` (uuid, primary key)
      - `title` (text, movie/series title)
      - `streaming_platform` (text, netflix/disney/amazon)
      - `type` (text, movie or series)
      - `year` (integer, release year)
      - `genre` (text, optional genre)
      - `poster_style_prompt` (text, AI prompt for poster style)
      - `created_at` (timestamp)

    - `cover_generations`
      - `id` (uuid, primary key)
      - `user_id` (uuid, references auth.users)
      - `original_image_url` (text, uploaded image URL)
      - `photo_type` (text, individual or casal)
      - `streaming_platform` (text, selected platform)
      - `generated_covers` (jsonb, array of generated cover URLs)
      - `status` (text, processing/completed/failed)
      - `created_at` (timestamp)
      - `completed_at` (timestamp, optional)

  2. Storage
    - `cover-images` bucket for storing uploaded and generated images

  3. Security
    - Enable RLS on all tables
    - Public read access for movies_series
    - User-specific access for cover_generations
    - Storage policies for image upload and access
*/

-- Create movies_series table
CREATE TABLE IF NOT EXISTS movies_series (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  title text NOT NULL,
  streaming_platform text NOT NULL CHECK (streaming_platform IN ('netflix', 'disney', 'amazon')),
  type text NOT NULL CHECK (type IN ('movie', 'series')),
  year integer NOT NULL,
  genre text,
  poster_style_prompt text NOT NULL,
  created_at timestamptz DEFAULT now()
);

-- Create cover_generations table
CREATE TABLE IF NOT EXISTS cover_generations (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id),
  original_image_url text NOT NULL,
  photo_type text NOT NULL CHECK (photo_type IN ('individual', 'casal')),
  streaming_platform text NOT NULL CHECK (streaming_platform IN ('netflix', 'disney', 'amazon')),
  generated_covers jsonb NOT NULL DEFAULT '[]'::jsonb,
  status text DEFAULT 'processing' CHECK (status IN ('processing', 'completed', 'failed')),
  created_at timestamptz DEFAULT now(),
  completed_at timestamptz
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_movies_streaming ON movies_series(streaming_platform);
CREATE INDEX IF NOT EXISTS idx_movies_year ON movies_series(year);
CREATE INDEX IF NOT EXISTS idx_generations_user ON cover_generations(user_id);
CREATE INDEX IF NOT EXISTS idx_generations_status ON cover_generations(status);

-- Enable RLS
ALTER TABLE movies_series ENABLE ROW LEVEL SECURITY;
ALTER TABLE cover_generations ENABLE ROW LEVEL SECURITY;

-- RLS Policies for movies_series (public read)
CREATE POLICY "movies_series_select_policy" ON movies_series
  FOR SELECT USING (true);

-- RLS Policies for cover_generations (user-specific)
CREATE POLICY "cover_generations_select_policy" ON cover_generations
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "cover_generations_insert_policy" ON cover_generations
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "cover_generations_update_policy" ON cover_generations
  FOR UPDATE USING (auth.uid() = user_id);

-- Insert sample movie data
INSERT INTO movies_series (title, streaming_platform, type, year, genre, poster_style_prompt) VALUES
-- Netflix
('Stranger Things 5', 'netflix', 'series', 2024, 'Sci-Fi Horror', 'Dark supernatural thriller poster with neon lights and 80s retro aesthetic'),
('Wednesday Addams', 'netflix', 'series', 2023, 'Comedy Horror', 'Gothic dark comedy poster with Victorian mansion and ravens'),
('The Crown', 'netflix', 'series', 2023, 'Drama', 'Royal drama poster with golden crown and elegant palace background'),
('Squid Game 2', 'netflix', 'series', 2024, 'Thriller', 'Dystopian survival game poster with geometric shapes and bright colors'),
('Ozark', 'netflix', 'series', 2022, 'Crime Drama', 'Crime drama poster with dark lake and money laundering theme'),
('The Witcher', 'netflix', 'series', 2023, 'Fantasy', 'Fantasy adventure poster with medieval sword and magical creatures'),

-- Disney+
('The Mandalorian', 'disney', 'series', 2023, 'Sci-Fi', 'Space western poster with desert planet and futuristic armor'),
('Encanto', 'disney', 'movie', 2022, 'Animation', 'Magical family adventure poster with colorful Colombian house and butterflies'),
('Loki', 'disney', 'series', 2023, 'Action', 'Time-travel adventure poster with mystical green magic and cosmic background'),
('WandaVision', 'disney', 'series', 2022, 'Superhero', 'Supernatural sitcom poster with retro TV aesthetic and red energy'),
('Frozen 3', 'disney', 'movie', 2024, 'Animation', 'Animated musical poster with ice castle and magical snow effects'),
('The Lion King', 'disney', 'movie', 2023, 'Animation', 'African savanna adventure poster with golden sunset and pride rock'),

-- Amazon Prime
('The Boys', 'amazon', 'series', 2024, 'Superhero', 'Dark superhero satire poster with gritty urban setting and blood splatter'),
('The Marvelous Mrs. Maisel', 'amazon', 'series', 2023, 'Comedy', '1950s comedy poster with vintage microphone and stage lights'),
('Jack Ryan', 'amazon', 'series', 2023, 'Action', 'Political thriller poster with CIA badge and international espionage theme'),
('The Rings of Power', 'amazon', 'series', 2022, 'Fantasy', 'Epic fantasy poster with Middle-earth landscapes and elven architecture'),
('Invincible', 'amazon', 'series', 2024, 'Animation', 'Animated superhero poster with comic book style and intense action'),
('The Terminal List', 'amazon', 'series', 2022, 'Thriller', 'Military thriller poster with Navy SEAL and conspiracy theme')
ON CONFLICT DO NOTHING;

-- Create storage bucket for cover images
INSERT INTO storage.buckets (id, name, public) 
VALUES ('cover-images', 'cover-images', true)
ON CONFLICT DO NOTHING;

-- Storage policies
CREATE POLICY "Users can upload images" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'cover-images' AND 
    (auth.uid()::text = (storage.foldername(name))[1] OR (storage.foldername(name))[1] = 'uploads')
  );

CREATE POLICY "Images are publicly accessible" ON storage.objects
  FOR SELECT USING (bucket_id = 'cover-images');

CREATE POLICY "Users can update their own images" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'cover-images' AND 
    auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can delete their own images" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'cover-images' AND 
    auth.uid()::text = (storage.foldername(name))[1]
  );