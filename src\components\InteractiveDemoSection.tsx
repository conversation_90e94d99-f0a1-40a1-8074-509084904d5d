import React, { useState, useRef } from 'react';
import { Upload, Film } from 'lucide-react';

const InteractiveDemoSection: React.FC = () => {
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleUploadClick = () => {
    fileInputRef.current?.click();
  };

  return (
    <section className="py-20 px-8 bg-[#1a1a1a] border-t-4 border-black">
      <div className="max-w-6xl mx-auto text-center">
        <h2 className="text-4xl md:text-5xl font-bold mb-4 uppercase text-white">Veja a Magia Acontecer</h2>
        <p className="text-lg text-gray-400 mb-12 max-w-3xl mx-auto">
          Faça o upload de uma foto e veja uma pré-visualização instantânea de como poderia ficar o seu póster.
        </p>

        <div className="grid md:grid-cols-2 gap-8 items-center bg-black p-8 border-2 border-black shadow-[8px_8px_0px_#FBBF24]">
          {/* Upload Area */}
          <div 
            className="bg-[#1a1a1a] p-8 border-2 border-dashed border-gray-600 h-96 flex flex-col items-center justify-center cursor-pointer transition-colors hover:bg-gray-800"
            onClick={handleUploadClick}
          >
            <input 
              type="file" 
              ref={fileInputRef} 
              onChange={handleFileChange} 
              className="hidden" 
              accept="image/*"
            />
            <Upload className="w-16 h-16 text-gray-500 mb-4" />
            <h3 className="text-2xl font-bold text-white">Clique para Carregar</h3>
            <p className="text-gray-400">Ou arraste e solte a sua imagem aqui</p>
          </div>

          {/* Preview Area */}
          <div className="bg-[#1a1a1a] p-2 border-2 border-black h-96 flex items-center justify-center">
            {imagePreview ? (
              <img src={imagePreview} alt="Pré-visualização" className="max-h-full max-w-full object-contain" />
            ) : (
              <div className="text-center text-gray-500">
                <Film className="w-16 h-16 mx-auto mb-4" />
                <h3 className="text-2xl font-bold">A sua pré-visualização</h3>
                <p>Aparecerá aqui</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </section>
  );
};

export default InteractiveDemoSection;
