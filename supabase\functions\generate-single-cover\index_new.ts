import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

// Declaração de tipo para o Deno global
declare const Deno: {
  env: {
    get(key: string): string | undefined;
  };
};

// Função para fazer polling do resultado da predição
async function pollForResult(predictionId: string, replicateToken: string): Promise<{ status: string, output?: string[], error?: string }> {
  const maxAttempts = 30;
  const delayMs = 10000;

  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    await new Promise(resolve => setTimeout(resolve, delayMs));

    try {
      const response = await fetch(`https://api.replicate.com/v1/predictions/${predictionId}`, {
        headers: {
          'Authorization': `Token ${replicateToken}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        console.error(`Poll attempt ${attempt}: HTTP ${response.status}`);
        continue;
      }

      const result = await response.json();
      console.log(`Poll attempt ${attempt}: ${result.status}`);

      if (result.status === 'succeeded' || result.status === 'failed' || result.status === 'canceled') {
        return result;
      }
    } catch (error) {
      console.error(`Poll attempt ${attempt} failed:`, error);
    }
  }

  return { status: 'failed', error: 'Polling timeout' };
}

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
}

interface GeneratedCover {
  movie_title: string;
  image_url?: string;
  success: boolean;
  error?: string;
  fallback_used?: boolean;
  fallback_movie?: string;
  no_title_used?: boolean;
  safe_prompt_used?: boolean;
  generated_at: string;
  promptInfo?: {
    promptUsed: string;
    attempts: number;
    fallbackUsed: boolean;
    safePromptUsed: boolean;
    lastPromptTried?: string;
  };
}

// Função auxiliar para gerar uma tentativa de capa
async function generateSingleCoverAttempt(
  supabase: any,
  movie: any,
  originalImageUrl: string,
  streamingPlatform: string,
  photoType: string,
  aspectRatio: string,
  creativityLevelId: number,
  customPrompt?: string,
  gender?: string,
  language: string = 'portuguese',
  generateTitles: boolean = true,
  skipCreativity: boolean = false
): Promise<{
  success: boolean;
  image_url?: string;
  error?: string;
  promptUsed?: string;
  creativityLevel?: any;
}> {
  try {
    const replicateToken = Deno.env.get('REPLICATE_API_TOKEN');
    if (!replicateToken) {
      throw new Error('REPLICATE_API_TOKEN não configurado');
    }

    // Buscar nível de criatividade
    const { data: creativityLevel, error: creativityError } = await supabase
      .from('creativity_levels')
      .select('*')
      .eq('id', creativityLevelId)
      .single();

    if (creativityError || !creativityLevel) {
      console.error('Erro ao buscar nível de criatividade:', creativityError);
      throw new Error('Nível de criatividade não encontrado');
    }

    // Construir prompt
    let basePrompt = '';
    let promptSource = '';
    let finalPrompt = '';

    if (customPrompt) {
      console.log('📝 Using custom prompt...');
      basePrompt = customPrompt;
      promptSource = 'custom_prompt';
    } else {
      console.log('📝 Building prompt from database...');
      basePrompt = movie.base_prompt;
      
      // Usar prompts específicos resolvidos do banco de dados
      if (photoType === 'cover') {
        basePrompt = `${movie.base_prompt} Position the person on the right side of the image. Wide cinematic shot with balanced composition. Make sure the person is not centered but positioned to the right third of the image. Place the title '${movie.title}' in the bottom right corner of the image with an elegant, readable gold font.`;
        promptSource = 'cover_prompt';
      } else if (photoType === 'casal' && movie.couple_prompt) {
        basePrompt = movie.couple_prompt;
        promptSource = 'couple_prompt';
      } else if (photoType === 'individual' && gender === 'male' && movie.gender_male_prompt) {
        basePrompt = movie.gender_male_prompt;
        promptSource = 'gender_male_prompt';
      } else if (photoType === 'individual' && gender === 'female' && movie.gender_female_prompt) {
        basePrompt = movie.gender_female_prompt;
        promptSource = 'gender_female_prompt';
      } else {
        promptSource = 'base_prompt';
      }
    }

    // Instrução de título
    const titleInstruction = generateTitles 
      ? `IMPORTANTE: Adicione o título "${movie.title}" na parte inferior da imagem com fonte elegante e legível. `
      : '';

    // Montagem final do prompt
    if (skipCreativity) {
      finalPrompt = movie.title;
    } else {
      finalPrompt = (creativityLevel.prompt_enhancement || '') + titleInstruction + basePrompt;
    }

    console.log(`📝 Prompt source: ${promptSource}`);
    console.log(`📝 Final prompt (${finalPrompt.length} chars): ${finalPrompt.substring(0, 200)}...`);

    const replicatePayload = {
      input: {
        input_image: originalImageUrl,
        prompt: finalPrompt,
        aspect_ratio: aspectRatio || '3:4',
        output_format: 'jpg',
        safety_tolerance: 2,
        num_inference_steps: 28,
        guidance_scale: 2,
      }
    };

    const prediction = await fetch('https://api.replicate.com/v1/models/black-forest-labs/flux-kontext-max/predictions', {
      method: 'POST',
      headers: {
        'Authorization': `Token ${replicateToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(replicatePayload)
    });

    if (!prediction.ok) {
      const errorText = await prediction.text();
      console.log(`❌ Replicate API error: ${errorText}`);
      throw new Error(`Replicate API error: ${errorText}`);
    }

    const predictionData = await prediction.json();
    console.log(`🚀 Started generation for ${movie.title}: ${predictionData.status}`);

    // Polling para resultado
    const pollResult = await pollForResult(predictionData.id, replicateToken);
    
    if (pollResult.status === 'succeeded' && pollResult.output) {
      const imageUrl = Array.isArray(pollResult.output) ? pollResult.output[0] : pollResult.output;

      if (imageUrl) {
        console.log(`✅ Generation successful for ${movie.title}`);
        return {
          success: true,
          image_url: imageUrl,
          promptUsed: finalPrompt,
          creativityLevel
        };
      }
    }

    const errorMessage = pollResult.error || `Generation failed with status: ${pollResult.status}`;
    console.log(`❌ Generation failed for ${movie.title}: ${errorMessage}`);
    
    return {
      success: false,
      error: errorMessage,
      promptUsed: finalPrompt,
      creativityLevel
    };

  } catch (error) {
    console.error(`❌ Error in generateSingleCoverAttempt for ${movie.title}:`, error);
    return {
      success: false,
      error: error.message,
      promptUsed: customPrompt || movie.base_prompt
    };
  }
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    const { 
      movieTitle, 
      originalImageUrl, 
      streamingPlatform, 
      photoType, 
      aspectRatio, 
      position, 
      creativityLevel,
      customPrompt,
      gender,
      language = 'portuguese',
      generateTitles = true,
      testMode = false,
      generationId,
      userId,
      userName
    } = await req.json();

    console.log(`🎬 Generating cover for: ${movieTitle} (${streamingPlatform})`);

    // Mapear nível de criatividade para ID
    const creativityMap: { [key: string]: number } = {
      'estrito': 1,
      'rostoFiel': 2, 
      'rostofiel': 2,
      'criativo': 3,
      'default': 2
    };

    const normalizedCreativityId = creativityLevel?.toLowerCase() || 'rostofiel';
    const finalCreativityLevelId = creativityMap[normalizedCreativityId] || creativityMap['rostoFiel'];

    console.log(`🎨 Using creativity level ID: ${finalCreativityLevelId}`);

    // Buscar filme no banco de dados
    const { data: movies, error: moviesError } = await supabase
      .from('movies_with_prompts')
      .select('*, default_creativity_level')
      .eq('streaming_platform', streamingPlatform)
      .eq('is_active', true);

    if (moviesError) {
      console.error('Error loading movies from database:', moviesError);
      throw new Error(`Failed to load movies: ${moviesError.message}`);
    }

    if (!movies || movies.length === 0) {
      throw new Error(`No active movies found for platform: ${streamingPlatform}`);
    }

    let targetMovie = movies.find((m: any) => m.title === movieTitle);
    
    if (!targetMovie && !customPrompt) {
      throw new Error(`Movie "${movieTitle}" not found for platform: ${streamingPlatform}`);
    }

    // Fallback para filme genérico se não encontrado
    if (!targetMovie) {
      targetMovie = {
        title: movieTitle,
        base_prompt: customPrompt || 'A cinematic poster',
        safe_prompt: customPrompt || 'A cinematic poster',
        gender_male_prompt: customPrompt || 'A cinematic poster',
        gender_female_prompt: customPrompt || 'A cinematic poster',
        couple_prompt: customPrompt || 'A cinematic poster',
        default_creativity_level: 'rostoFiel'
      };
    }

    console.log(`🎬 Target movie: ${targetMovie.title}`);
    console.log(`📝 Available prompts: base=${!!targetMovie.base_prompt}, safe=${!!targetMovie.safe_prompt}, male=${!!targetMovie.gender_male_prompt}, female=${!!targetMovie.gender_female_prompt}, couple=${!!targetMovie.couple_prompt}`);

    // 🔥 SISTEMA DE FALLBACK REORGANIZADO CONFORME ESPECIFICAÇÃO
    let attemptCount = 0;
    let lastError = '';

    const result: GeneratedCover = {
      movie_title: movieTitle,
      success: false,
      generated_at: new Date().toISOString(),
      promptInfo: {
        promptUsed: '',
        attempts: 0,
        fallbackUsed: false,
        safePromptUsed: false
      }
    };

    // 🎯 DEFINIR SEQUÊNCIA DE TENTATIVAS BASEADA NO TIPO DE FOTO E GÊNERO
    const attempts = [];

    if (photoType === 'cover') {
      // COVER/BANNER: 4 tentativas
      attempts.push(
        { prompt: targetMovie.base_prompt, withTitle: false, withPositioning: true, description: 'base_prompt + criatividade + instruções de posicionamento' },
        { prompt: gender === 'male' ? targetMovie.gender_male_prompt : gender === 'female' ? targetMovie.gender_female_prompt : targetMovie.couple_prompt, withTitle: false, withPositioning: true, description: `${gender || 'couple'}_prompt + criatividade + instruções de posicionamento` },
        { prompt: `Inspired in a series ${targetMovie.title} make a poster film about it`, withTitle: false, withPositioning: true, description: 'generic prompt + criatividade + instruções de posicionamento' },
        { prompt: targetMovie.safe_prompt, withTitle: false, withPositioning: true, description: 'safe_prompt + criatividade + instruções de posicionamento' }
      );
    } else if (photoType === 'individual' && gender === 'male') {
      // HOMEM INDIVIDUAL: 8 tentativas
      attempts.push(
        { prompt: targetMovie.base_prompt, withTitle: true, withPositioning: false, description: 'base_prompt + criatividade + instrução do título' },
        { prompt: targetMovie.base_prompt, withTitle: false, withPositioning: false, description: 'base_prompt + criatividade' },
        { prompt: targetMovie.gender_male_prompt, withTitle: true, withPositioning: false, description: 'gender_male_prompt + criatividade + instrução do título' },
        { prompt: targetMovie.gender_male_prompt, withTitle: false, withPositioning: false, description: 'gender_male_prompt + criatividade' },
        { prompt: `Inspired in a series ${targetMovie.title} make a poster film about it`, withTitle: true, withPositioning: false, description: 'generic prompt + criatividade + instrução do título' },
        { prompt: `Inspired in a series ${targetMovie.title} make a poster film about it`, withTitle: false, withPositioning: false, description: 'generic prompt + criatividade' },
        { prompt: targetMovie.safe_prompt, withTitle: true, withPositioning: false, description: 'safe_prompt + criatividade + instrução do título' },
        { prompt: targetMovie.safe_prompt, withTitle: false, withPositioning: false, description: 'safe_prompt + criatividade' }
      );
    } else if (photoType === 'individual' && gender === 'female') {
      // MULHER INDIVIDUAL: 8 tentativas
      attempts.push(
        { prompt: targetMovie.base_prompt, withTitle: true, withPositioning: false, description: 'base_prompt + criatividade + instrução do título' },
        { prompt: targetMovie.base_prompt, withTitle: false, withPositioning: false, description: 'base_prompt + criatividade' },
        { prompt: targetMovie.gender_female_prompt, withTitle: true, withPositioning: false, description: 'gender_female_prompt + criatividade + instrução do título' },
        { prompt: targetMovie.gender_female_prompt, withTitle: false, withPositioning: false, description: 'gender_female_prompt + criatividade' },
        { prompt: `Inspired in a series ${targetMovie.title} make a poster film about it`, withTitle: true, withPositioning: false, description: 'generic prompt + criatividade + instrução do título' },
        { prompt: `Inspired in a series ${targetMovie.title} make a poster film about it`, withTitle: false, withPositioning: false, description: 'generic prompt + criatividade' },
        { prompt: targetMovie.safe_prompt, withTitle: true, withPositioning: false, description: 'safe_prompt + criatividade + instrução do título' },
        { prompt: targetMovie.safe_prompt, withTitle: false, withPositioning: false, description: 'safe_prompt + criatividade' }
      );
    } else if (photoType === 'casal') {
      // CASAL: 8 tentativas
      attempts.push(
        { prompt: targetMovie.base_prompt, withTitle: true, withPositioning: false, description: 'base_prompt + criatividade + instrução do título' },
        { prompt: targetMovie.base_prompt, withTitle: false, withPositioning: false, description: 'base_prompt + criatividade' },
        { prompt: targetMovie.couple_prompt, withTitle: true, withPositioning: false, description: 'couple_prompt + criatividade + instrução do título' },
        { prompt: targetMovie.couple_prompt, withTitle: false, withPositioning: false, description: 'couple_prompt + criatividade' },
        { prompt: `Inspired in a series ${targetMovie.title} make a poster film about it`, withTitle: true, withPositioning: false, description: 'generic prompt + criatividade + instrução do título' },
        { prompt: `Inspired in a series ${targetMovie.title} make a poster film about it`, withTitle: false, withPositioning: false, description: 'generic prompt + criatividade' },
        { prompt: targetMovie.safe_prompt, withTitle: true, withPositioning: false, description: 'safe_prompt + criatividade + instrução do título' },
        { prompt: targetMovie.safe_prompt, withTitle: false, withPositioning: false, description: 'safe_prompt + criatividade' }
      );
    } else {
      // FALLBACK PADRÃO: individual sem gênero definido
      attempts.push(
        { prompt: targetMovie.base_prompt, withTitle: true, withPositioning: false, description: 'base_prompt + criatividade + instrução do título' },
        { prompt: targetMovie.base_prompt, withTitle: false, withPositioning: false, description: 'base_prompt + criatividade' },
        { prompt: `Inspired in a series ${targetMovie.title} make a poster film about it`, withTitle: true, withPositioning: false, description: 'generic prompt + criatividade + instrução do título' },
        { prompt: `Inspired in a series ${targetMovie.title} make a poster film about it`, withTitle: false, withPositioning: false, description: 'generic prompt + criatividade' },
        { prompt: targetMovie.safe_prompt, withTitle: true, withPositioning: false, description: 'safe_prompt + criatividade + instrução do título' },
        { prompt: targetMovie.safe_prompt, withTitle: false, withPositioning: false, description: 'safe_prompt + criatividade' }
      );
    }

    // Filtrar tentativas com prompts válidos
    const validAttempts = attempts.filter(attempt => attempt.prompt && attempt.prompt.trim().length > 0);
    
    console.log(`🎯 Prepared ${validAttempts.length} fallback attempts for ${photoType} ${gender || 'unspecified'}`);

    // 🔄 EXECUTAR TENTATIVAS SEQUENCIALMENTE
    for (const attempt of validAttempts) {
      attemptCount++;
      console.log(`🚀 Attempt ${attemptCount}: ${attempt.description}`);
      
      // Preparar prompt customizado para esta tentativa
      let customPromptForAttempt = attempt.prompt;
      
      // Adicionar instruções de posicionamento para cover/banner
      if (attempt.withPositioning) {
        customPromptForAttempt += ` Position the person on the right side of the image. Wide cinematic shot with balanced composition. Make sure the person is not centered but positioned to the right third of the image. Place the title '${targetMovie.title}' in the bottom right corner of the image with an elegant, readable gold font.`;
      }

      const attemptResult = await generateSingleCoverAttempt(
        supabase,
        { ...targetMovie, base_prompt: customPromptForAttempt },
        originalImageUrl,
        streamingPlatform,
        photoType,
        aspectRatio,
        finalCreativityLevelId, // OBSERVAÇÃO OBRIGATÓRIA: sempre usar nível de criatividade
        undefined,
        gender,
        language,
        attempt.withTitle, // Usar título conforme definido na tentativa
        false // Nunca pular criatividade (OBSERVAÇÃO OBRIGATÓRIA)
      );

      result.promptInfo!.attempts = attemptCount;
      result.promptInfo!.promptUsed = attemptResult.promptUsed || '';

      if (attemptResult.success) {
        console.log(`✅ Attempt ${attemptCount} successful: ${attempt.description}`);
        result.success = true;
        result.image_url = attemptResult.image_url;
        result.movie_title = targetMovie.title;
        result.promptInfo!.fallbackUsed = attemptCount > 1;

        return new Response(JSON.stringify(result), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        });
      }

      lastError = attemptResult.error || `Attempt ${attemptCount} failed`;
      console.log(`❌ Attempt ${attemptCount} failed: ${lastError}`);
    }

    // 🔥 SE TODAS AS TENTATIVAS FALHARAM
    console.log(`❌ All ${attemptCount} attempts failed for ${movieTitle}`);
    result.promptInfo!.attempts = attemptCount;
    result.error = `All ${attemptCount} attempts failed. Last error: ${lastError}`;

    return new Response(JSON.stringify(result), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('❌ Error in generate-single-cover:', error);
    return new Response(
      JSON.stringify({ 
        error: error.message,
        movie_title: 'Unknown',
        success: false,
        generated_at: new Date().toISOString()
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );
  }
});