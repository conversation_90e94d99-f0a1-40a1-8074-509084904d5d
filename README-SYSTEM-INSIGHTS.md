# README do Sistema - Insights e Padrões

## 🧠 Visão Geral do Sistema

Este documento centraliza todos os insights técnicos, padrões de desenvolvimento e boas práticas do sistema AI Streaming Poster.

## 🔄 Uso do MCP Supabase

### Como usar SEMPRE o MCP para atualizar Edge Functions

1. **Listagem de funções**:
```bash
# Use para ver todas as Edge Functions
mcp_supabase_list_edge_functions
```

2. **Deploy de função**:
```bash
# SEMPRE use para atualizar funções
mcp_supabase_deploy_edge_function
```

3. **Verificar logs**:
```bash
# Para debug e monitoramento
mcp_supabase_get_logs service="edge-function"
```

### ⚠️ IMPORTANTE: Nunca edite Edge Functions localmente
- Sempre use o MCP do Supabase para garantir sincronia
- As funções locais podem estar desatualizadas
- O MCP garante que a função no Supabase seja atualizada

## 🎬 Sistema de Filmes - Fluxo Correto Implementado

### 📋 **Configuração das Plataformas**
```typescript
const platformConfig = {
  netflix: { aspectRatio: '5:4', quantity: 12 },
  amazon: { aspectRatio: '5:4', quantity: 11 }, 
  disney: { 
    aspectRatio: '3:5', 
    quantity: 6,
    secondaryRatio: '6:5', 
    secondaryQuantity: 3,
    totalQuantity: 9 
  }
}
```

### 🎯 **Fluxo de Processamento Individual**

**1. Netflix pega 12 filmes do `movieDatabase.ts`**
- Filmes principais: primeiros 12 do array
- Filmes backup: restantes do array (13+)

**2. Para cada filme (processamento sequencial):**

```
Filme 1 → Tenta gerar
├─ ✅ Sucesso → Salva na cover_images
├─ ❌ Erro E005 → Tenta PROMPT SAFE
│   ├─ ✅ Sucesso com safe → Salva (marca safe_prompt_used: true)
│   └─ ❌ Ainda falhou → Vai para FALLBACK
└─ 🔄 FALLBACK → Pega filme não usado (filme 13, 14, 15...)
    ├─ ✅ Sucesso → Salva (marca fallback_used: true)
    └─ ❌ Falhou → Próximo filme backup
```

**3. Progresso em tempo real:**
- Cada resultado é salvo imediatamente em `cover_generations.generated_covers`
- Interface pode mostrar progresso filme por filme
- Usuário vê: "Processando Elite... ✅ Sucesso"

### 🔄 **Regeneração de Filme Específico**

```typescript
// Usuário clica "Regenerar Elite"
{
  "regenerateMovie": "Elite",
  "originalImageUrl": "...",
  "streamingPlatform": "netflix"
}
```

**Comportamento:**
1. Encontra filme "Elite" no movieDatabase
2. Cria **variação do prompt** (não prompt safe)
3. Gera nova versão com prompt diferente
4. Marca versão anterior como `is_regenerated: true`
5. Salva nova versão como atual

### 📊 **Sistema de Banco de Dados**

**`cover_generations`** - Controla processo:
```json
{
  "generated_covers": [
    {
      "movie_title": "Elite",
      "success": true,
      "image_url": "https://...",
      "generated_at": "2025-01-24T10:30:00Z"
    },
    {
      "movie_title": "Round 6", 
      "success": false,
      "error": "E005 - Content flagged",
      "safe_prompt_used": true,
      "fallback_used": true,
      "fallback_movie": "Ozark"
    }
  ]
}
```

**`cover_images`** - Histórico de sucessos:
- Cada imagem gerada com sucesso
- Permite regeneração seletiva
- Filtro por plataforma, usuário, data

### 🛡️ **Sistema de Prompt Safe**

**Palavras removidas automaticamente:**
```typescript
const safePrompt = basePrompt
  .replace(/distópico|dystopian/gi, 'futurista')
  .replace(/sobrevivência|survival/gi, 'aventura')
  .replace(/jogo.*morte|death.*game/gi, 'competição amigável')
  .replace(/violência|violence/gi, 'ação')
  .replace(/sangue|blood/gi, 'energia')
  .replace(/arma|weapon|gun/gi, 'acessório')
```

### 🎲 **Sistema de Variação para Regeneração**

**Variações aleatórias aplicadas:**
- "com iluminação mais dramática"
- "com ângulo cinematográfico diferente"
- "com paleta de cores mais vibrante"
- "com composição mais dinâmica"
- "com atmosfera mais intensa"
- "com estilo visual alternativo"

## 📈 **Vantagens do Sistema Implementado**

### ✅ **Fallback Inteligente**
- Round 6 falha → automaticamente tenta Ozark
- Evita filmes já gerados pelo usuário
- Sempre garante 12 capas (ou máximo possível)

### ✅ **Progresso Individual**
- Interface mostra filme por filme
- Usuário vê progresso em tempo real
- Não precisa esperar todas as 12 para ver resultados

### ✅ **Regeneração Seletiva**
- "Elite ficou ruim? Regenere só ele"
- Prompt com variação, não repetição
- Histórico completo mantido

### ✅ **Sistema Robusto**
- E005 → Prompt Safe → Fallback → Próximo filme
- Múltiplas camadas de proteção
- Logs detalhados para debug

## 📊 **Histórico de Logs no Dashboard**

### 🗂️ **Nova Aba: "Histórico de Logs"**
- **Localização**: Dashboard → Histórico de Logs
- **Funcionalidades**:
  - Lista todas as gerações do usuário
  - Filtros por status (concluída, falhou, processando)
  - Filtros por plataforma (Netflix, Disney+, Prime Video)
  - Detalhes completos de cada geração
  - Progresso filme por filme
  - Informações sobre fallbacks e prompts safe

### 🔍 **Detalhes Exibidos**:
- **Status geral**: ✅ Concluída, ❌ Falhou, 🔄 Processando
- **Duração**: Tempo total de processamento
- **Estatísticas**: X sucessos, Y falhas de Z filmes
- **Detalhes por filme**:
  - Nome do filme/série
  - Status individual (sucesso/falha)
  - Uso de prompt safe
  - Uso de fallback (qual filme foi usado)
  - Mensagem de erro (se houver)
  - Miniatura da capa gerada

## 🔄 **Sistema de Regeneração Atualizado**

### ✨ **Nova Lógica: Adicionar vs Substituir**
```typescript
// Regeneração SEMPRE adiciona nova capa
{
  "addToCurrent": true,  // Padrão: adiciona nova versão
  "movieTitle": "Elite",
  "streamingPlatform": "netflix"
}
```

### 📈 **Comportamento Atualizado**:
1. **12 capas geradas** → Usuário regenera 3 → **15 capas totais**
2. **Cada regeneração** cria nova entrada na galeria
3. **Usuário escolhe** qual versão prefere
4. **Histórico completo** de todas as versões

### 🎯 **Componente RegenerateCover**:
- Botão "Nova Versão" em vez de "Regenerar"
- Mostra que adiciona à galeria
- Feedback visual de sucesso/erro
- Explicação clara do funcionamento

## 🔧 **Atualizações Técnicas Implementadas**

### ✅ **Função regenerate-cover**:
- Importa `movieDatabase` do arquivo compartilhado
- Usa sistema de variação de prompt
- Cria registro em `cover_generations`
- Adiciona nova entrada em `cover_images`
- Não marca anteriores como `is_regenerated` (mantém todas)

### ✅ **Componente GenerationLogs**:
- Interface completa para visualizar logs
- Modal com detalhes expandidos
- Filtros e ordenação
- Atualização em tempo real

### ✅ **UserDashboard atualizado**:
- Nova aba "Histórico de Logs"
- Integração com GenerationLogs
- Navegação intuitiva

## 🎯 **Fluxo Completo Atual**

1. **Geração Inicial**: 12 filmes Netflix → 10 sucessos, 2 falhas
2. **Logs Salvos**: Tudo registrado em `cover_generations`
3. **Dashboard**: Usuário vê histórico completo
4. **Regeneração**: Clica "Nova Versão" em filme específico
5. **Nova Capa**: Adicionada à galeria (total: 11 capas)
6. **Escolha**: Usuário decide qual versão usar

O sistema agora oferece **controle total** e **transparência completa** para o usuário! 🚀

## 📁 Estrutura de Arquivos Separados

### MovieDatabase Separado
- **Arquivo**: `supabase/functions/_shared/movieDatabase.ts`
- **Benefícios**: Melhor escalabilidade, reutilização entre funções
- **Importação**: `import { movieDatabase } from '../_shared/movieDatabase.ts'`

### Configuração de Plataformas
```typescript
const platformConfig = {
  netflix: { aspectRatio: '5:4', quantity: 12 },
  amazon: { aspectRatio: '5:4', quantity: 11 }, 
  disney: { 
    aspectRatio: '3:5', 
    quantity: 9,
    totalQuantity: 9 
  }
}
```

## 🎯 Sistema de Fallback Inteligente

### Como Funciona
1. **Primeira tentativa**: Filmes do movieDatabase local
2. **Segunda tentativa**: Prompt mais seguro se der erro E005
3. **Terceira tentativa**: Busca filmes alternativos no banco de dados
4. **Quarta tentativa**: Continua até atingir o número configurado

### Banco de Dados de Backup
- **Tabela**: `movies_series`
- **Campos**: `title`, `streaming_platform`, `poster_style_prompt`
- **Query**: Busca filmes não utilizados da mesma plataforma

### Rastreamento de Gerações
- **Tabela**: `generation_details`
- **Campos**: `generation_id`, `movie_title`, `success`, `image_url`, `error_message`
- **Benefício**: Permite regenerar filmes específicos que falharam

## 🔍 Processamento Individual

### Características
- **Uma imagem por vez**: Retorno em tempo real
- **Fallback automático**: Se um filme falha, tenta outro
- **Tracking completo**: Salva sucesso/falha de cada filme
- **Interface amigável**: Mostra progresso individual

### Estrutura de Resposta
```json
{
  "covers": ["url1", "url2", "url3"],
  "generationId": 123,
  "totalGenerated": 10,
  "totalRequested": 12,
  "details": [
    {
      "movie_title": "Elite",
      "image_url": "https://...",
      "success": true
    },
    {
      "movie_title": "Round 6",
      "image_url": "",
      "success": false,
      "error": "Content flagged as sensitive"
    }
  ]
}
```

## 🛠 Regeneração Inteligente

### Dashboard de Gerações
- **Visualização**: Todas as gerações do usuário
- **Status**: Sucesso/falha por filme
- **Ações**: Regenerar filmes específicos que falharam

### Regeneração Seletiva
- **Filme específico**: Regenerar apenas um filme que falhou
- **Prompt alternativo**: Usar versão mais segura automaticamente
- **Histórico**: Manter registro de todas as tentativas

## 📊 Métricas e Monitoramento

### Logs Importantes
```bash
# Ver logs de geração
mcp_supabase_get_logs service="edge-function"

# Verificar status das funções
mcp_supabase_list_edge_functions
```

### Tabelas de Controle
1. **cover_generations**: Geração principal
2. **generation_details**: Detalhes por filme
3. **cover_images**: Imagens individuais salvas
4. **movies_series**: Banco de filmes para fallback

## 🚨 Tratamento de Erros

### Erro E005 (Conteúdo Sensível)
- **Primeira tentativa**: safety_tolerance: 2
- **Segunda tentativa**: safety_tolerance: 5 + prompt mais seguro
- **Terceira tentativa**: Filme alternativo do banco

### Timeout de Edge Functions
- **Limite**: 150-400 segundos
- **Solução**: Processamento individual em vez de batch
- **Fallback**: Salvar progresso parcial

### Filmes Problemáticos
- **Round 6**: Frequentemente bloqueado por violência
- **The Boys**: Pode ser bloqueado por conteúdo adulto
- **Solução**: Prompts mais seguros automáticos

## 💡 Boas Práticas

### Desenvolvimento
1. **Sempre testar localmente primeiro**
2. **Usar MCP para deploy das funções**
3. **Verificar logs após cada deploy**
4. **Manter movieDatabase atualizado**

### Escalabilidade
1. **Separar arquivos por responsabilidade**
2. **Usar banco de dados para filmes extras**
3. **Implementar cache quando possível**
4. **Monitorar performance das APIs**

### Usuário
1. **Mostrar progresso em tempo real**
2. **Permitir regeneração seletiva**
3. **Salvar histórico de gerações**
4. **Feedback claro sobre falhas**

## 🔧 Comandos Úteis

```bash
# Verificar status das funções
mcp_supabase_list_edge_functions

# Ver logs recentes
mcp_supabase_get_logs service="edge-function"

# Fazer deploy de função atualizada
mcp_supabase_deploy_edge_function name="generate-covers" files=[...]

# Verificar banco de dados
mcp_supabase_execute_sql query="SELECT * FROM cover_generations ORDER BY created_at DESC LIMIT 10"
```

## 📈 Próximos Passos

1. **Interface de Regeneração**: Botões para regenerar filmes específicos
2. **Cache Inteligente**: Evitar regenerar filmes já bem-sucedidos
3. **Análise de Falhas**: Dashboard com estatísticas de erro por filme
4. **Otimização de Prompts**: Melhorar prompts baseado em taxa de sucesso

## 🏗️ Arquitetura do Sistema

### Frontend (React + TypeScript)
```
src/
├── components/         # Componentes reutilizáveis
├── hooks/             # Custom hooks (useAuth, usePackBalance, etc)
├── lib/               # Configurações (Supabase, Stripe)
├── pages/             # Páginas principais
├── types/             # Types TypeScript
└── utils/             # Utilitários
```

### Backend (Supabase Edge Functions)
```
supabase/functions/
├── generate-covers/        # Geração de capas com IA
├── canva-poster/          # Integração com Canva
├── check-pack-balance/    # Verificação de saldo
├── consume-pack/          # Consumo de pack
├── create-payment-intent/ # Pagamento Stripe
└── stripe-webhook/        # Webhook de pagamentos
```

## 🚨 PROBLEMA CRÍTICO: Netflix gerando apenas 3 capas em vez de 12

### Causa Raiz: Timeout da Edge Function

A função está configurada para gerar 12 capas da Netflix, mas está parando após 3-4 devido ao **limite de tempo das Edge Functions**:

- **Limite de duração**: 150s (plano gratuito) / 400s (plano pago)
- **Tempo por capa**: ~30-60s (geração + polling + upload)
- **Tempo total para 12 capas**: ~360-720s (EXCEDE O LIMITE!)

### Por que apenas 3 capas?
1. Round 6 (Squid Game) falha por conteúdo sensível (E005)
2. Wednesday, Stranger Things 5, Elite são geradas com sucesso
3. A função atinge o timeout antes de processar os outros 8 filmes

### Soluções Propostas

#### 1. **Dividir em batches** (RECOMENDADO)
```typescript
// Processar em grupos de 4 filmes por vez
const BATCH_SIZE = 4;
const batches = [];
for (let i = 0; i < movies.length; i += BATCH_SIZE) {
  batches.push(movies.slice(i, i + BATCH_SIZE));
}
```

#### 2. **Usar Background Tasks**
```typescript
// Processar de forma assíncrona
EdgeRuntime.waitUntil(processRemainingMovies());
```

#### 3. **Implementar fila de processamento**
- Criar função separada para polling
- Usar webhooks para notificar conclusão
- Processar em paralelo quando possível

## 🔍 Problema Identificado: Falha em Round 6 (Squid Game)

### Análise do Erro
O erro `"The input or output was flagged as sensitive. Please try again with different inputs. (E005)"` indica que o conteúdo foi bloqueado pelo modelo de IA por ser considerado sensível.

### Solução Implementada
A função já tem um mecanismo de retry com prompt mais seguro:

```typescript
if (isSensitiveError) {
  const saferPrompt = buildSaferPrompt(finalPrompt, language)
  // Retry com safety_tolerance: 5
}
```

### Por que Round 6 ainda falha?
1. **Conteúdo violento**: Squid Game tem temas de violência
2. **Safety tolerance insuficiente**: Mesmo com valor 5, o conteúdo pode ser bloqueado
3. **Palavras-chave problemáticas**: "jogo de sobrevivência", "distópico"

### Recomendações
1. Ajustar o prompt para ser ainda mais genérico
2. Remover referências a "sobrevivência" e "distópico"
3. Focar em elementos visuais neutros (cores, geometria)

## 📦 Sistema de Packs

### Fluxo de Consumo
1. **Verificação**: `check-pack-balance` → verifica saldo
2. **Consumo**: `consume-pack` → debita 1 pack
3. **Atualização**: `update-pack-usage` → registra uso

### Regras de Negócio
- 1 pack = 1 plataforma completa (até 12 capas)
- Regeneração ilimitada de capas individuais
- Poster final: 1 geração por pack

## 🔐 Segurança e RLS

### Políticas Implementadas
```sql
-- Usuários só veem seus próprios dados
CREATE POLICY "Users can only see own data" ON public.user_packs
FOR SELECT USING (auth.uid() = user_id);

-- Apenas sistema pode inserir
CREATE POLICY "Only system can insert" ON public.user_packs
FOR INSERT WITH CHECK (false);
```

### Padrão de Segurança
1. **Frontend**: Validação básica
2. **Edge Functions**: Validação completa + auth
3. **Database**: RLS como última linha de defesa

## 🚀 Deploy e Manutenção

### Checklist de Deploy

1. **Edge Functions**:
   ```bash
   # SEMPRE via MCP
   mcp_supabase_deploy_edge_function
   ```

2. **Migrações**:
   ```bash
   # Aplicar via MCP
   mcp_supabase_apply_migration
   ```

3. **Verificações**:
   ```bash
   # Logs
   mcp_supabase_get_logs
   
   # Advisors de segurança
   mcp_supabase_get_advisors type="security"
   ```

## 🐛 Debug e Troubleshooting

### Logs Estruturados
```typescript
console.log('Starting cover generation with:', { 
  imageUrl, 
  photoType, 
  gender, 
  streamingPlatform, 
  language, 
  generateTitles 
})
```

### Padrões de Error Handling
```typescript
try {
  // Operação
} catch (error) {
  console.error(`Error processing ${movieTitle}:`, error)
  // Não para a execução, continua com próximo filme
  continue
}
```

## 📊 Métricas e Monitoramento

### KPIs Importantes
1. **Taxa de sucesso de geração**: Ideal > 90%
2. **Tempo médio de geração**: < 30s por capa
3. **Taxa de erro E005**: Monitorar filmes problemáticos

### Queries Úteis
```sql
-- Capas geradas por plataforma
SELECT streaming_platform, COUNT(*) 
FROM cover_images 
GROUP BY streaming_platform;

-- Taxa de sucesso
SELECT 
  COUNT(CASE WHEN status = 'completed' THEN 1 END)::float / COUNT(*) as success_rate
FROM cover_generations;
```

## 🔄 Integração com Canva

### Fluxo de Autenticação
1. Usuário autoriza no Canva
2. Recebe `auth_code`
3. Sistema troca por `access_token`
4. Token usado para gerar posters

### Limitações
- API do Canva tem rate limits
- Templates devem ser pré-criados
- Imagens devem estar em URLs públicas

## 🛠️ Ferramentas de Desenvolvimento

### VS Code Extensions Recomendadas
- Supabase (oficial)
- Tailwind CSS IntelliSense
- TypeScript Error Lens

### Scripts Úteis
```bash
# Desenvolvimento local
npm run dev

# Type checking
npm run type-check

# Lint
npm run lint
```

## 📝 Padrões de Código

### Nomenclatura
- **Componentes**: PascalCase (`PackBalance.tsx`)
- **Hooks**: camelCase com `use` (`usePackBalance.ts`)
- **Funções**: camelCase (`generateCovers()`)
- **Types**: PascalCase com sufixo (`UserPackType`)

### Estrutura de Componentes
```typescript
// 1. Imports
import { useState } from 'react'

// 2. Types
interface Props {
  // ...
}

// 3. Component
export function Component({ prop }: Props) {
  // 4. Hooks
  const [state, setState] = useState()
  
  // 5. Handlers
  const handleClick = () => {}
  
  // 6. Effects
  useEffect(() => {}, [])
  
  // 7. Render
  return <div>...</div>
}
```

## 🚨 Alertas e Cuidados

### ⚠️ NUNCA
1. Editar Edge Functions localmente sem MCP
2. Commitar chaves de API
3. Ignorar erros de RLS
4. Fazer deploy sem testar

### ✅ SEMPRE
1. Usar MCP para Edge Functions
2. Validar dados no frontend E backend
3. Logar erros com contexto
4. Verificar advisors de segurança

## 📚 Documentação Relacionada

- [Sistema de Packs](./README-PACK-SYSTEM.md)
- [Dashboard](./README-DASHBOARD.md)
- [Stripe Integration](./README-STRIPE.md)
- [Environment Setup](./ENVIRONMENT-SETUP.md)

## 🔮 Melhorias Futuras

1. **Cache de Imagens**: Implementar CDN próprio
2. **Queue System**: Para processar gerações em fila
3. **Webhooks**: Notificar quando geração completar
4. **Analytics**: Dashboard com métricas em tempo real

---

**Última atualização**: Janeiro 2025
**Mantenedor**: Sistema AI Assistant 