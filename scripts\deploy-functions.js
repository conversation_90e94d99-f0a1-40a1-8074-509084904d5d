const { spawn } = require('child_process');
const path = require('path');
require('dotenv').config();

console.log('🚀 Implantando funções Supabase...');

const functions = [
  'canva-poster',
  'apply-migrations'
];

// Função auxiliar para executar comandos
function runCommand(command, args, options = {}) {
  return new Promise((resolve, reject) => {
    console.log(`Executando: ${command} ${args.join(' ')}`);
    
    const childProcess = spawn(command, args, {
      stdio: 'inherit',
      shell: true,
      ...options
    });

    childProcess.on('close', (code) => {
      if (code === 0) {
        resolve();
      } else {
        reject(new Error(`Comando falhou com código ${code}`));
      }
    });
  });
}

// Implantar funções sequencialmente
async function deployFunctions() {
  try {
    // Verificar se supabase CLI está instalado
    try {
      await runCommand('supabase', ['--version']);
    } catch (error) {
      console.error('❌ Supabase CLI não encontrado. Por favor instale usando:');
      console.error('npm install -g supabase');
      process.exit(1);
    }

    for (const func of functions) {
      console.log(`\n📦 Implantando função: ${func}`);
      try {
        await runCommand('supabase', ['functions', 'deploy', func]);
        console.log(`✅ Função ${func} implantada com sucesso!`);
      } catch (error) {
        console.error(`❌ Erro ao implantar função ${func}:`, error);
        // Continue com as outras funções
      }
    }

    console.log('\n🔍 Listando funções implantadas:');
    await runCommand('supabase', ['functions', 'list']);
    
    console.log('\n🚀 Aplicando migrações...');
    try {
      await runCommand('supabase', ['functions', 'invoke', 'apply-migrations']);
      console.log('✅ Migrações aplicadas com sucesso!');
    } catch (error) {
      console.error('❌ Erro ao aplicar migrações:', error);
    }

    console.log('\n🎉 Implantação concluída! Você pode iniciar o servidor de desenvolvimento com:');
    console.log('npm run dev');
    
  } catch (error) {
    console.error('❌ Erro durante implantação:', error);
    process.exit(1);
  }
}

deployFunctions();
