-- Create the creativity_levels table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.creativity_levels (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL UNIQUE,
    max_tokens INTEGER NOT NULL,
    temperature NUMERIC NOT NULL,
    prompt_enhancement TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Only insert default values if the table is empty
DO $$
BEGIN
    -- Check if table is empty
    IF NOT EXISTS (SELECT 1 FROM public.creativity_levels LIMIT 1) THEN
        -- Insert default creativity levels based on movieDatabase.ts
        INSERT INTO public.creativity_levels (name, max_tokens, temperature, prompt_enhancement)
        VALUES
            ('criativo', 2500, 0.7, 'Inspired by the person in the photo, create an artistic version where they are depicted as part of the poster''s universe.'),
            ('rostoFiel', 2000, 0.5, 'Keeping the person''s face in the photo as faithful as possible, but with creative freedom for the hair and pose and body'),
            ('estrito', 1500, 0.3, 'Keeping the person''s exact facial features, hair, and pose from the photo,');
    END IF;
END $$;

-- Add a default_creativity_level column to movies table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'movies' 
        AND column_name = 'default_creativity_level'
    ) THEN
        ALTER TABLE public.movies
        ADD COLUMN default_creativity_level TEXT DEFAULT 'rostoFiel';
    END IF;
END $$;

-- Create an RLS policy for the creativity_levels table
DO $$
BEGIN
    -- Enable RLS on the table
    ALTER TABLE public.creativity_levels ENABLE ROW LEVEL SECURITY;
    
    -- Create select policy if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE tablename = 'creativity_levels' 
        AND policyname = 'select_creativity_levels'
    ) THEN
        CREATE POLICY select_creativity_levels ON public.creativity_levels
            FOR SELECT
            TO authenticated
            USING (true);
    END IF;
    
    -- Create admin policy if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE tablename = 'creativity_levels' 
        AND policyname = 'admin_all_creativity_levels'
    ) THEN
        CREATE POLICY admin_all_creativity_levels ON public.creativity_levels
            FOR ALL
            TO authenticated
            USING (
                EXISTS (
                    SELECT 1 FROM public.profiles
                    WHERE profiles.id = auth.uid() AND profiles.role = 'admin'
                )
            );
    END IF;
END $$; 