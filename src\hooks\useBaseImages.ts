import { useState, useEffect } from 'react'
import { supabase } from '../lib/supabase'

interface BaseImage {
  id: string
  original_image_url: string
  file_name: string
  file_size: number
  content_type: string
  photo_type: 'individual' | 'casal'
  streaming_platform: 'netflix' | 'disney' | 'amazon'
  created_at: string
  user_id: string
}

export function useBaseImages(userId: string | null) {
  const [baseImages, setBaseImages] = useState<BaseImage[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const loadBaseImages = async () => {
    if (!userId) {
      setBaseImages([])
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      const { data, error } = await supabase
        .from('base_images')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })

      if (error) throw error
      
      setBaseImages(data || [])
    } catch (err) {
      console.error('Error loading base images:', err)
      setError(err instanceof Error ? err.message : 'Erro ao carregar imagens base')
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    loadBaseImages()
  }, [userId])

  return {
    baseImages,
    isLoading,
    error,
    refetch: loadBaseImages
  }
} 