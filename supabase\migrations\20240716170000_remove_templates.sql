-- Step 1: Create a helper function to replace variables in a text template.
CREATE OR REPLACE FUNCTION public.flatten_prompt_variables(template_text TEXT, variables JSONB)
RETURNS TEXT AS $$
DECLARE
    var_record RECORD;
    result_text TEXT;
BEGIN
    result_text := template_text;
    -- Return original text if there are no variables or text to process
    IF template_text IS NULL OR variables IS NULL OR jsonb_typeof(variables) = 'null' OR variables::text = '{}'::text THEN
        RETURN template_text;
    END IF;

    -- Loop through each key-value pair in the JSONB object and replace the placeholder
    FOR var_record IN SELECT * FROM jsonb_each_text(variables) LOOP
        result_text := replace(result_text, '{' || var_record.key || '}', var_record.value);
    END LOOP;

    RETURN result_text;
END;
$$ LANGUAGE plpgsql;

-- Step 2: Update the 'movies' table to flatten the templates into the override fields.
UPDATE public.movies m SET base_prompt_override = COALESCE(m.base_prompt_override, flatten_prompt_variables((SELECT pt.template_text FROM public.prompt_templates pt WHERE pt.id = m.base_prompt_template_id), m.base_prompt_variables)) WHERE m.base_prompt_template_id IS NOT NULL;
UPDATE public.movies m SET safe_prompt_override = COALESCE(m.safe_prompt_override, flatten_prompt_variables((SELECT pt.template_text FROM public.prompt_templates pt WHERE pt.id = m.safe_prompt_template_id), m.safe_prompt_variables)) WHERE m.safe_prompt_template_id IS NOT NULL;
UPDATE public.movies m SET gender_male_prompt_override = COALESCE(m.gender_male_prompt_override, flatten_prompt_variables((SELECT pt.template_text FROM public.prompt_templates pt WHERE pt.id = m.gender_male_prompt_template_id), m.gender_male_prompt_variables)) WHERE m.gender_male_prompt_template_id IS NOT NULL;
UPDATE public.movies m SET gender_female_prompt_override = COALESCE(m.gender_female_prompt_override, flatten_prompt_variables((SELECT pt.template_text FROM public.prompt_templates pt WHERE pt.id = m.gender_female_prompt_template_id), m.gender_female_prompt_variables)) WHERE m.gender_female_prompt_template_id IS NOT NULL;
UPDATE public.movies m SET couple_prompt_override = COALESCE(m.couple_prompt_override, flatten_prompt_variables((SELECT pt.template_text FROM public.prompt_templates pt WHERE pt.id = m.couple_prompt_template_id), m.couple_prompt_variables)) WHERE m.couple_prompt_template_id IS NOT NULL;

-- Step 3: Drop the dependent view FIRST.
DROP VIEW IF EXISTS public.movies_with_prompts;

-- Step 4: NOW drop the columns.
ALTER TABLE public.movies
DROP COLUMN IF EXISTS base_prompt_template_id,
DROP COLUMN IF EXISTS base_prompt_variables,
DROP COLUMN IF EXISTS safe_prompt_template_id,
DROP COLUMN IF EXISTS safe_prompt_variables,
DROP COLUMN IF EXISTS gender_male_prompt_template_id,
DROP COLUMN IF EXISTS gender_male_prompt_variables,
DROP COLUMN IF EXISTS gender_female_prompt_template_id,
DROP COLUMN IF EXISTS gender_female_prompt_variables,
DROP COLUMN IF EXISTS couple_prompt_template_id,
DROP COLUMN IF EXISTS couple_prompt_variables;

-- Step 5: Recreate the view with the new simplified structure.
CREATE OR REPLACE VIEW public.movies_with_prompts AS
SELECT
    m.id,
    m.title,
    m.streaming_platform,
    m.is_active,
    m.created_at,
    m.updated_at,
    COALESCE(m.default_creativity_level, 'Equilibrado') AS default_creativity_level,
    
    m.base_prompt_override AS base_prompt,
    m.safe_prompt_override AS safe_prompt,
    m.gender_male_prompt_override AS gender_male_prompt,
    m.gender_female_prompt_override AS gender_female_prompt,
    m.couple_prompt_override AS couple_prompt,
    
    (
        SELECT json_agg(stats_agg)
        FROM (
            SELECT
                s.successful_attempts,
                s.failed_attempts
            FROM movie_generation_stats s
            WHERE s.movie_id = m.id
        ) stats_agg
    ) AS movie_generation_stats
FROM
    movies m;

-- Step 6: Drop the now-obsolete prompt_templates table.
DROP TABLE IF EXISTS public.prompt_templates;

-- Step 7: Drop the helper function as it's no longer needed.
DROP FUNCTION IF EXISTS public.flatten_prompt_variables(TEXT, JSONB); 