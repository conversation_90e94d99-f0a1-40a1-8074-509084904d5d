import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { amount, currency, planId, userId, credits, productId, planType, metadata } = await req.json()

    console.log('🔥 Payment intent request:', { amount, currency, planId, userId, credits, productId, planType })

    // 🔥 Validate required fields - only credit system
    if (!amount || !currency || !planId || !userId || !credits || planType !== 'credits') {
      console.error('❌ Missing required fields:', { amount, currency, planId, userId, credits, planType })
      return new Response(
        JSON.stringify({ error: 'Missing required fields or invalid plan type' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // 🔥 Detectar ambiente e usar chaves apropriadas
    const isLocal = req.headers.get('origin')?.includes('localhost') || req.headers.get('referer')?.includes('localhost')
    
    // Usar chaves de teste para local, produção para deploy
    const stripeSecretKey = isLocal 
      ? Deno.env.get('STRIPE_TEST_SECRET_KEY')  // Variável de ambiente para teste
      : Deno.env.get('STRIPE_SECRET_KEY')       // Variável de ambiente para produção
    
    if (!stripeSecretKey) {
      console.error('❌ Stripe secret key not found')
      return new Response(
        JSON.stringify({ error: 'Stripe configuration error' }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }
    
    console.log('🔑 Environment:', isLocal ? 'LOCAL (test mode)' : 'PRODUCTION')
    console.log('🔑 Using Stripe key:', stripeSecretKey.substring(0, 15) + '...')
    
    // 🔥 Prepare metadata for Stripe - only credits
    const stripeMetadata: Record<string, string> = {
      planId,
      userId,
      productId: productId || '',
      planName: metadata?.planName || '',
      planType: 'credits',
      credits: credits.toString()
    }

    // 🔥 Create description for credits
    const description = `${metadata?.planName || planId} - ${credits} créditos para usuário ${userId}`

    console.log('📝 Creating payment intent with:', { amount, currency, description, metadata: stripeMetadata })

    // Create payment intent with Stripe API
    const stripeBody = new URLSearchParams({
      amount: amount.toString(),
      currency: currency,
      description: description,
      'automatic_payment_methods[enabled]': 'true',
      'automatic_payment_methods[allow_redirects]': 'never'
    })

    // Add metadata to Stripe request
    Object.entries(stripeMetadata).forEach(([key, value]) => {
      stripeBody.append(`metadata[${key}]`, value)
    })

    console.log('🚀 Sending request to Stripe API...')

    const stripeResponse = await fetch('https://api.stripe.com/v1/payment_intents', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${stripeSecretKey}`,
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: stripeBody,
    })

    console.log('📡 Stripe response status:', stripeResponse.status)

    if (!stripeResponse.ok) {
      const errorData = await stripeResponse.text()
      console.error('❌ Stripe API error:', errorData)
      return new Response(
        JSON.stringify({ error: 'Failed to create payment intent', details: errorData }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    const paymentIntent = await stripeResponse.json()
    console.log('✅ Payment intent created:', { 
      id: paymentIntent.id, 
      client_secret: paymentIntent.client_secret?.substring(0, 30) + '...',
      amount: paymentIntent.amount,
      currency: paymentIntent.currency,
      status: paymentIntent.status
    })

    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // 🔥 Store in credit_purchases table only
    const { error: dbError } = await supabase
      .from('credit_purchases')
      .insert({
        stripe_payment_intent_id: paymentIntent.id,
        user_id: userId,
        credits: credits,
        amount: amount,
        currency: currency,
        status: 'pending',
        created_at: new Date().toISOString()
      })

    if (dbError) {
      console.error('❌ Database error (credits):', dbError)
      return new Response(
        JSON.stringify({ error: 'Failed to store payment record' }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    console.log('✅ Payment record stored successfully')

    const response = {
      clientSecret: paymentIntent.client_secret,
      paymentIntentId: paymentIntent.id,
      productId: productId,
      planType: 'credits',
      credits: credits
    }

    console.log('📤 Returning response:', { 
      ...response, 
      clientSecret: response.clientSecret?.substring(0, 30) + '...'
    })

    return new Response(
      JSON.stringify(response),
      { 
        status: 200, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )

  } catch (error) {
    console.error('❌ Error creating payment intent:', error)
    return new Response(
      JSON.stringify({ error: 'Internal server error', details: error.message }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
}) 