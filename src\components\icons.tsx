import * as React from 'react';

export const BrazilFlag = (props: React.SVGProps<SVGSVGElement>) => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 700" {...props}>
    <rect width="1000" height="700" fill="#009c3b"/>
    <path d="M500 85L890 350L500 615L110 350L500 85Z" fill="#ffdf00"/>
    <circle cx="500" cy="350" r="195" fill="#002776"/>
  </svg>
);

export const USFlag = (props: React.SVGProps<SVGSVGElement>) => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1235 650" {...props}>
    <path fill="#B22234" d="M0 0h1235v650H0z"/>
    <path fill="#FFF" d="M0 50h1235v50H0zm0 100h1235v50H0zm0 100h1235v50H0zm0 100h1235v50H0zm0 100h1235v50H0zm0 100h1235v50H0z"/>
    <path fill="#3C3B6E" d="M0 0h494v350H0z"/>
  </svg>
); 