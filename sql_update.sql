-- Atualiza a stored procedure para aceitar todos os campos de prompt
CREATE OR REPLACE FUNCTION update_movies_bulk(movies_data jsonb)
RETURNS void AS $$
DECLARE
  movie_record record;
BEGIN
  FOR movie_record IN SELECT * FROM jsonb_to_recordset(movies_data)
    AS x(
      id uuid, 
      title text, 
      base_prompt text, 
      safe_prompt text, 
      gender_male_prompt text, 
      gender_female_prompt text, 
      couple_prompt text, 
      prompt_pt text, 
      prompt_en text
    )
  LOOP
    UPDATE public.movies_series
    SET
      base_prompt = COALESCE(movie_record.base_prompt, base_prompt),
      safe_prompt = COALESCE(movie_record.safe_prompt, safe_prompt),
      gender_male_prompt = COALESCE(movie_record.gender_male_prompt, gender_male_prompt),
      gender_female_prompt = COALESCE(movie_record.gender_female_prompt, gender_female_prompt),
      couple_prompt = COALESCE(movie_record.couple_prompt, couple_prompt),
      prompt_pt = COALESCE(movie_record.prompt_pt, prompt_pt),
      prompt_en = COALESCE(movie_record.prompt_en, prompt_en)
    WHERE
      public.movies_series.id = movie_record.id;
  END LOOP;
END;
$$ LANGUAGE plpgsql;
