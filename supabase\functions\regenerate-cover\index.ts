import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { movieDatabase } from '../_shared/movieDatabase.ts'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
}

interface RegenerateCoverRequest {
  movieTitle?: string
  coverIndex?: number
  originalImageUrl: string
  streamingPlatform: string
  photoType: string
  aspectRatio?: string
  userId: string
  gender?: string
  language?: string
  generateTitles?: boolean
  creativityLevel?: 'criativo' | 'rostoFiel' | 'estrito'
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  // 1. AUTHENTICATION
  const authHeader = req.headers.get('Authorization')
  if (!authHeader) {
    return new Response(JSON.stringify({ error: 'Falta o cabeçalho de autenticação' }), { status: 401, headers: corsHeaders })
  }
  
  const supabase = createClient(
    Deno.env.get('SUPABASE_URL')!,
    Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!,
  )
  
  const { data: { user } } = await supabase.auth.getUser(authHeader.replace('Bearer ', ''));
  if (!user) {
    return new Response(JSON.stringify({ error: 'Token inválido ou expirado' }), { status: 401, headers: corsHeaders })
  }

  try {
    const {
      movieTitle,
      coverIndex,
      originalImageUrl,
      streamingPlatform,
      photoType,
      aspectRatio = '2:3',
      gender,
      language = 'portuguese',
      generateTitles = false,
      creativityLevel = 'rostoFiel'
    }: RegenerateCoverRequest = await req.json()

    // 🔥 Se não tem movieTitle mas tem coverIndex, usar movieDatabase diretamente
    let finalMovieTitle = movieTitle
    if (!finalMovieTitle && coverIndex !== undefined && streamingPlatform) {
      // 🔥 MÉTODO DIRETO: Usar o mesmo mapeamento da geração original
      const movies = movieDatabase[streamingPlatform as keyof typeof movieDatabase]
      if (movies && movies[coverIndex]) {
        finalMovieTitle = movies[coverIndex].title
        console.log(`🎯 Filme encontrado via movieDatabase para coverIndex ${coverIndex}: ${finalMovieTitle}`)
      } else {
        console.log(`❌ Filme não encontrado no movieDatabase para platform: ${streamingPlatform}, index: ${coverIndex}`)
        
        // 🔥 FALLBACK: Buscar no banco como antes (para casos especiais)
        const { data: generations } = await supabase
          .from('cover_generations')
          .select('id, generated_covers')
          .eq('user_id', user.id)
          .order('created_at', { ascending: false })
          .limit(1)

        if (generations && generations.length > 0) {
          const generation = generations[0]
          
          if (generation.generated_covers && Array.isArray(generation.generated_covers)) {
            const coverAtIndex = generation.generated_covers[coverIndex]
            
            // Se é um objeto com movie_title
            if (coverAtIndex && typeof coverAtIndex === 'object' && coverAtIndex.movie_title) {
              finalMovieTitle = coverAtIndex.movie_title
              console.log(`🔍 Filme encontrado no formato novo para coverIndex ${coverIndex}: ${finalMovieTitle}`)
            }
            // Se é formato antigo (apenas URLs), buscar na tabela cover_images
            else if (coverAtIndex && typeof coverAtIndex === 'string') {
              console.log(`🔍 Formato antigo detectado, buscando na tabela cover_images...`)
              
              const { data: coverImage } = await supabase
                .from('cover_images')
                .select('movie_title')
                .eq('image_url', coverAtIndex)
                .limit(1)
                .single()
              
              if (coverImage && coverImage.movie_title) {
                finalMovieTitle = coverImage.movie_title
                console.log(`🔍 Filme encontrado na tabela cover_images para coverIndex ${coverIndex}: ${finalMovieTitle}`)
              }
            }
          }
        }
      }
    }

    if (!finalMovieTitle) {
      return new Response(
        JSON.stringify({ error: 'Título do filme não encontrado' }),
        { status: 400, headers: corsHeaders }
      )
    }

    // 🔥 CHECAR CRÉDITOS ANTES DE REGENERAR
    const creditsNeeded = 1; // Regeneração custa 1 crédito
    
    // Verificar saldo de créditos
    const { data: purchases } = await supabase
      .from('credit_purchases')
      .select('credits')
      .eq('user_id', user.id)
      .eq('status', 'completed')

    const { data: usage } = await supabase
      .from('credit_usage')
      .select('credits_used')
      .eq('user_id', user.id)

    const totalCredits = purchases?.reduce((sum, p) => sum + p.credits, 0) || 0
    const usedCredits = usage?.reduce((sum, u) => sum + u.credits_used, 0) || 0
    const availableCredits = totalCredits - usedCredits

    if (availableCredits < creditsNeeded) {
      return new Response(
        JSON.stringify({ 
          error: 'Créditos insuficientes para regenerar capa',
          availableCredits,
          requiredCredits: creditsNeeded,
          message: 'Compre mais créditos para continuar'
        }),
        { status: 402, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }
    
    console.log(`💳 Usuário tem ${availableCredits} créditos. Necessário: ${creditsNeeded}`);

    console.log(`🔄 Starting cover regeneration for: ${finalMovieTitle}`)

    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    
    // Chamar a função regenerate-single-cover com o token real do usuário
    const response = await fetch(`${supabaseUrl}/functions/v1/regenerate-single-cover`, {
      method: 'POST',
      headers: {
        'Authorization': authHeader, // Passar o token real do usuário
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        movieTitle: finalMovieTitle,
        originalImageUrl,
        streamingPlatform,
        photoType,
        aspectRatio,
        gender,
        language,
        generateTitles,
        creativityLevel,
        isManualRegeneration: true // 🔥 MARCAR COMO REGENERAÇÃO MANUAL
      })
    })

    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`Regeneration failed: ${errorText}`)
    }

    const result = await response.json()

    if (result.success) {
      console.log(`✅ Cover regenerated successfully: ${finalMovieTitle}`)
      
      return new Response(
        JSON.stringify({
          success: true,
          message: 'Nova versão da capa gerada com sucesso!',
          image_url: result.image_url,
          movie_title: result.movie_title,
          safe_prompt_used: result.safe_prompt_used
        }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    } else {
      console.log(`❌ Cover regeneration failed: ${result.error}`)
      
      return new Response(
        JSON.stringify({
          success: false,
          error: result.error || 'Falha na regeneração da capa'
        }),
        { 
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

  } catch (error) {
    console.error('Error in regenerate-cover:', error)
    return new Response(
      JSON.stringify({ 
        success: false,
        error: error.message 
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})