// @ts-ignore
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
}

serve(async (req: Request) => {
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  if (req.method !== 'POST') {
    return new Response(
      JSON.stringify({ error: 'Method not allowed' }),
      {
        status: 405,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    );
  }

  try {
    const {
      movieTitle,
      streamingPlatform,
      currentPrompts,
      instruction,
      model
    } = await req.json();

    // Validate required fields
    if (!movieTitle || !currentPrompts || !instruction || !model) {
      return new Response(
        JSON.stringify({ error: 'Missing required fields' }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        }
      );
    }

    // Get OpenAI API key from environment
    // @ts-ignore
    const openaiApiKey = Deno.env.get('OPENAI_API_KEY');
    if (!openaiApiKey) {
      console.error('OpenAI API key not found in environment variables');
      return new Response(
        JSON.stringify({ error: 'OpenAI API key not configured' }),
        {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        }
      );
    }

    // Prepare the system prompt
    const systemPrompt = `You are an expert AI prompt engineer specializing in image generation prompts for movie/series posters. 

Your task is to improve and regenerate 5 different prompts based on user instructions while maintaining the core structure and purpose of each prompt type.

PROMPT TYPES:
1. base_prompt - Main prompt for general poster generation
2. safe_prompt - Safer/simpler version for content moderation
3. gender_male_prompt - Specific version for male characters  
4. gender_female_prompt - Specific version for female characters
5. couple_prompt - Version for couples/multiple people

REQUIREMENTS:
- Keep all prompts in English
- Maintain the core structure of transforming a person's photo into movie poster style
- Include title positioning instruction: "Place the '${movieTitle}' text at the bottom center of the image with elegant and readable font."
- Preserve the essence of preserving facial features while changing clothes/style
- Follow the user's specific instructions for improvements
- Return ONLY a valid JSON object with the 5 prompts

RESPONSE FORMAT:
{
  "base_prompt": "...",
  "safe_prompt": "...", 
  "gender_male_prompt": "...",
  "gender_female_prompt": "...",
  "couple_prompt": "..."
}`;

    const userPrompt = `Movie/Series: "${movieTitle}" (${streamingPlatform})

CURRENT PROMPTS:
1. Base: ${currentPrompts.base_prompt}
2. Safe: ${currentPrompts.safe_prompt}
3. Male: ${currentPrompts.gender_male_prompt}  
4. Female: ${currentPrompts.gender_female_prompt}
5. Couple: ${currentPrompts.couple_prompt}

USER INSTRUCTIONS:
${instruction}

Please regenerate all 5 prompts following the user's instructions while maintaining the core functionality. Return only the JSON object with the improved prompts.`;

    // Call OpenAI API
    const openaiResponse = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openaiApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: model,
        messages: [
          {
            role: 'system',
            content: systemPrompt
          },
          {
            role: 'user', 
            content: userPrompt
          }
        ],
        temperature: 0.7,
        max_tokens: 2000,
        response_format: { type: 'json_object' }
      })
    });

    if (!openaiResponse.ok) {
      const errorData = await openaiResponse.text();
      console.error('OpenAI API error:', errorData);
      return new Response(
        JSON.stringify({ error: 'Failed to generate prompts with OpenAI' }),
        {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        }
      );
    }

    const openaiData = await openaiResponse.json();
    const generatedContent = openaiData.choices[0]?.message?.content;

    if (!generatedContent) {
      return new Response(
        JSON.stringify({ error: 'No content generated by OpenAI' }),
        {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        }
      );
    }

    // Parse the JSON response from OpenAI
    let prompts;
    try {
      prompts = JSON.parse(generatedContent);
    } catch (parseError) {
      console.error('Failed to parse OpenAI response:', generatedContent);
      return new Response(
        JSON.stringify({ error: 'Invalid JSON response from OpenAI' }),
        {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        }
      );
    }

    // Validate that all required prompts are present
    const requiredFields = ['base_prompt', 'safe_prompt', 'gender_male_prompt', 'gender_female_prompt', 'couple_prompt'];
    for (const field of requiredFields) {
      if (!prompts[field]) {
        return new Response(
          JSON.stringify({ error: `Missing ${field} in OpenAI response` }),
          {
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          }
        );
      }
    }

    // Return the regenerated prompts
    return new Response(
      JSON.stringify({ prompts }),
      {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    );

  } catch (error) {
    console.error('Error in regenerate-prompts function:', error);
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    );
  }
}); 