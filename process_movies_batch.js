const fs = require('fs');

// Ler o arquivo JSON
const jsonData = JSON.parse(fs.readFileSync('backup-movies-json-03-n8n.json', 'utf8'));

// Dividir em lotes de 10 itens
const batchSize = 10;
const batches = [];

for (let i = 0; i < jsonData.length; i += batchSize) {
    batches.push(jsonData.slice(i, i + batchSize));
}

console.log(`Total de itens: ${jsonData.length}`);
console.log(`Total de lotes: ${batches.length}`);

// Salvar cada lote em um arquivo separado
batches.forEach((batch, index) => {
    fs.writeFileSync(`batch_${index + 1}.json`, JSON.stringify(batch, null, 2));
    console.log(`Lote ${index + 1} salvo com ${batch.length} itens`);
});