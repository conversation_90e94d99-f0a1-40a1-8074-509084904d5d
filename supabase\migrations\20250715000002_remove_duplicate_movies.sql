-- Identificar filmes duplicados (mesmo título e plataforma)
WITH duplicates AS (
  SELECT 
    title, 
    streaming_platform,
    COUNT(*) as count,
    MIN(created_at) as oldest_created_at
  FROM 
    public.unified_movies_series
  GROUP BY 
    title, 
    streaming_platform
  HAVING 
    COUNT(*) > 1
),

-- Selecionar os IDs a serem excluídos (todos exceto o mais antigo de cada grupo)
to_delete AS (
  SELECT 
    ums.id
  FROM 
    public.unified_movies_series ums
  JOIN 
    duplicates d 
    ON ums.title = d.title 
    AND ums.streaming_platform = d.streaming_platform
  WHERE 
    ums.created_at > d.oldest_created_at
)

-- Excluir os duplicados (exceto o mais antimo de cada grupo)
DELETE FROM 
  public.unified_movies_series
WHERE 
  id IN (SELECT id FROM to_delete);

-- Adicionar uma restrição única para evitar duplicatas no futuro
DO $$
BEGIN
  -- Verificar se a restrição já existe para evitar erros
  IF NOT EXISTS (
    SELECT 1 
    FROM information_schema.table_constraints 
    WHERE constraint_name = 'unified_movies_series_title_platform_key'
  ) THEN
    -- Adicionar restrição única
    ALTER TABLE public.unified_movies_series
    ADD CONSTRAINT unified_movies_series_title_platform_key 
    UNIQUE (title, streaming_platform);
  END IF;
END $$;
