/** @type {import('tailwindcss').Config} */
export default {
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  darkMode: 'class',
  theme: {
    extend: {
      fontFamily: {
        sans: ['Inter', 'sans-serif'],
      },
      colors: {
        'brand-bg': '#F5F5F5',
        'brand-text': '#111111',
        'brand-primary': '#00C9A7',
        'brand-secondary': '#FFC764',
        'brand-accent': '#FF6B6B',
        'brand-black': '#111111',
        'brand-white': '#FFFFFF',
      },
      boxShadow: {
        'brutal': '4px 4px 0px #111111',
        'brutal-sm': '2px 2px 0px #111111',
        'brutal-lg': '8px 8px 0px #111111',
        'brutal-hover': '6px 6px 0px #111111',
        'brutal-pressed': '2px 2px 0px #111111',
      },
      borderWidth: {
        '3': '3px',
        '5': '5px',
        '6': '6px',
      },
      animation: {
        'float': 'float 6s ease-in-out infinite',
        'pulse-soft': 'pulse-soft 2s ease-in-out infinite',
        'blob': 'blob 7s infinite',
        'background-position-spin': 'background-position-spin 3000ms infinite linear',
        'aurora': 'aurora 60s linear infinite',
        'gradient': 'gradient 8s linear infinite',
        'meteor': 'meteor 5s linear infinite',
        'shine': 'shine 2s infinite linear',
        'line-shadow': 'line-shadow 2s ease-in-out infinite',
        'shiny-text': 'shiny-text 8s infinite',
      },
      keyframes: {
        float: {
          '0%, 100%': { transform: 'translateY(0px)' },
          '50%': { transform: 'translateY(-10px)' },
        },
        'pulse-soft': {
          '0%, 100%': { opacity: '1' },
          '50%': { opacity: '0.8' },
        },
        blob: {
          '0%': {
            transform: 'translate(0px, 0px) scale(1)',
          },
          '33%': {
            transform: 'translate(30px, -50px) scale(1.1)',
          },
          '66%': {
            transform: 'translate(-20px, 20px) scale(0.9)',
          },
          '100%': {
            transform: 'translate(0px, 0px) scale(1)',
          },
        },
        'background-position-spin': {
          '0%': { backgroundPosition: 'top center' },
          '100%': { backgroundPosition: 'bottom center' },
        },
        aurora: {
          from: {
            backgroundPosition: '50% 50%, 50% 50%',
          },
          to: {
            backgroundPosition: '350% 50%, 350% 50%',
          },
        },
        gradient: {
          to: {
            backgroundPosition: 'var(--bg-size) 0',
          },
        },
        meteor: {
          '0%': { transform: 'rotate(215deg) translateX(0)', opacity: '1' },
          '70%': { opacity: '1' },
          '100%': {
            transform: 'rotate(215deg) translateX(-500px)',
            opacity: '0',
          },
        },
        shine: {
          '0%': { backgroundPosition: '0 0' },
          '100%': { backgroundPosition: '-200% 0' },
        },
        'line-shadow': {
          '0%, 100%': { backgroundPosition: '0% 0%' },
          '50%': { backgroundPosition: '100% 100%' },
        },
        'shiny-text': {
          '0%, 90%, 100%': {
            backgroundPosition: 'calc(-100% - var(--shiny-width)) 0',
          },
          '30%, 60%': {
            backgroundPosition: 'calc(100% + var(--shiny-width)) 0',
          },
        },
      }
    },
  },
  plugins: [
    require("daisyui")
  ],
  daisyui: {
    themes: ["light", "corporate"],
    darkTheme: "light",
    base: true,
    styled: true,
    utils: true,
    prefix: "",
    logs: false,
    themeRoot: ":root",
  },
};
