import React, { useState, useRef } from 'react';
import { generateCanvaPoster, checkPosterStatus, PosterData } from '../lib/canvaService';
import { Link } from 'react-router-dom';
import { Clock } from 'lucide-react';

interface CanvaPosterGeneratorProps {
  defaultTemplateId?: string;
}

const CanvaPosterGenerator: React.FC<CanvaPosterGeneratorProps> = ({ 
  defaultTemplateId = 'EAGqDjHn_M4' // Template ID padrão
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [posterUrl, setPosterUrl] = useState<string | null>(null);
  const [generationId, setGenerationId] = useState<string | null>(null);

  const [formData, setFormData] = useState<PosterData>({
    ID: defaultTemplateId,
    TITLE: '',
    DESCRIPTION: '',
    CONTINUE: ''
  });
  
  // Referências para os inputs de arquivo
  const capaInputRef = useRef<HTMLInputElement>(null);
  const avatarInputRef = useRef<HTMLInputElement>(null);
  const photoInputRefs = useRef<Array<HTMLInputElement | null>>(
    Array(12).fill(null)
  );
  
  // Estado para armazenar as visualizações de imagens
  const [imagePreviewUrls, setImagePreviewUrls] = useState<Record<string, string>>({});
  
  // Manipulador para alterações de campo de texto
  const handleTextChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };
  
  // Manipulador para upload de imagens
  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>, fieldName: string) => {
    const file = e.target.files?.[0];
    if (!file) return;
    
    try {
      // Criar uma URL temporária para visualização
      const previewUrl = URL.createObjectURL(file);
      setImagePreviewUrls(prev => ({
        ...prev,
        [fieldName]: previewUrl
      }));
      
      // Fazer upload do arquivo para algum storage (ex: Supabase Storage)
      const { data: storageData, error: uploadError } = await uploadImageToStorage(file);
      if (uploadError) throw new Error(uploadError.message);
      
      // Atualizar formData com a URL pública da imagem
      setFormData(prev => ({
        ...prev,
        [fieldName]: storageData?.publicUrl || ''
      }));
      
    } catch (err: any) {
      setError(`Erro ao fazer upload da imagem: ${err.message}`);
    }
  };
  
  // Função para fazer upload de imagens para o Storage
  const uploadImageToStorage = async (file: File) => {
    // Gerar um nome único para o arquivo
    const fileExt = file.name.split('.').pop();
    const fileName = `${Math.random().toString(36).substring(2)}-${Date.now()}.${fileExt}`;
    const filePath = `poster-images/${fileName}`;
    
    // Upload para o Supabase Storage
    const { error } = await supabase.storage
      .from('poster-assets')
      .upload(filePath, file);
    
    if (error) return { data: null, error };
    
    // Obter a URL pública
    const { data: publicUrlData } = supabase.storage
      .from('poster-assets')
      .getPublicUrl(filePath);
    
    return { 
      data: { 
        path: filePath,
        publicUrl: publicUrlData.publicUrl 
      }, 
      error: null 
    };
  };
  
  // Função para gerar o poster
  const handleGeneratePoster = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);
    
    try {
      // Verificar se temos pelo menos uma imagem
      if (!formData.CAPA && !formData.AVATAR && !Object.keys(formData).some(key => key.startsWith('PHOTO_'))) {
        throw new Error('É necessário fornecer pelo menos uma imagem');
      }
      
      // Enviar dados para gerar o poster
      const response = await generateCanvaPoster(formData);
      setGenerationId(response.id);
      
      // Se já estiver concluído, mostrar a URL
      if (response.status === 'completed' && response.designUrl) {
        setPosterUrl(response.designUrl);
        setIsLoading(false);
      } else {
        // Começar a verificar o status
        const id = response.id;
        setGenerationId(id);
        startPolling(id);
      }
    } catch (err: any) {
      setError(`Erro ao gerar o poster: ${err.message}`);
      setIsLoading(false);
    }
  };
  
  // Função para verificar o status do poster periodicamente
  const startPolling = (id: string) => {
    const pollInterval = setInterval(async () => {
      try {
        const statusData = await checkPosterStatus(id);
        
        if (statusData.status === 'completed') {
          clearInterval(pollInterval);
          setPosterUrl(statusData.design_url);
          setIsLoading(false);
        } else if (statusData.status === 'failed') {
          clearInterval(pollInterval);
          setError('A geração do poster falhou');
          setIsLoading(false);
        }
      } catch (err: any) {
        clearInterval(pollInterval);
        setError(`Erro ao verificar status: ${err.message}`);
        setIsLoading(false);
      }
    }, 2000); // Verificar a cada 2 segundos
    
    // Limpar intervalo após 60 segundos de qualquer forma
    setTimeout(() => {
      clearInterval(pollInterval);
      if (isLoading) {
        setIsLoading(false);
        setError('Tempo esgotado ao aguardar a geração do poster');
      }
    }, 60000);
  };
  
  // Importação do Supabase
  // Necessário para o método uploadImageToStorage
  const { supabase } = require('../lib/supabase');
  
  return (
    <div className="bg-brand-white p-2">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-brand-text">Gerador de Pôsteres Canva</h2>
        <Link 
          to="/canva-history" 
          className="flex items-center space-x-2 text-brand-primary hover:text-brand-accent transition-colors font-semibold"
        >
          <Clock className="h-4 w-4" />
          <span>Ver histórico de pôsteres</span>
        </Link>
      </div>
      
      {error && (
        <div className="bg-brand-accent/20 border-2 border-brand-accent text-brand-accent px-4 py-3 rounded-lg mb-4 font-medium">
          {error}
        </div>
      )}
      
      {posterUrl ? (
        <div className="mb-6 text-center">
          <h3 className="text-2xl font-bold mb-4 text-brand-text">Seu pôster foi gerado!</h3>
          <div className="aspect-[2/3] bg-gray-100 rounded-lg overflow-hidden border-2 border-brand-black shadow-brutal mx-auto max-w-sm">
            <img 
              src={posterUrl} 
              alt="Poster gerado" 
              className="w-full h-full object-contain" 
            />
          </div>
          <div className="mt-6 flex gap-4 justify-center">
            <a 
              href={posterUrl} 
              target="_blank" 
              rel="noopener noreferrer"
              className="bg-brand-primary text-brand-black px-6 py-3 rounded-lg border-2 border-brand-black shadow-brutal-sm hover:shadow-brutal-hover active:shadow-brutal-pressed transition-all font-bold"
            >
              Abrir no Canva
            </a>
            <button 
              onClick={() => {
                setPosterUrl(null);
                setGenerationId(null);
              }}
              className="bg-brand-white text-brand-black px-6 py-3 rounded-lg border-2 border-brand-black shadow-brutal-sm hover:shadow-brutal-hover active:shadow-brutal-pressed transition-all font-bold"
            >
              Criar novo pôster
            </button>
          </div>
        </div>
      ) : (
        <form onSubmit={handleGeneratePoster} className="space-y-8">
          {/* Template ID */}
          <div>
            <label className="block text-sm font-bold text-brand-text mb-2">
              Template ID
            </label>
            <input
              type="text"
              name="ID"
              value={formData.ID}
              onChange={handleTextChange}
              className="w-full px-4 py-3 border-2 border-brand-black rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-primary shadow-brutal-sm"
              required
            />
            <p className="text-xs text-brand-text/70 mt-1">
              ID do template Canva a ser utilizado (padrão: EAGqDjHn_M4)
            </p>
          </div>
          
          {/* Campos de texto */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-bold text-brand-text mb-2">
                Título
              </label>
              <input
                type="text"
                name="TITLE"
                value={formData.TITLE}
                onChange={handleTextChange}
                className="w-full px-4 py-3 border-2 border-brand-black rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-primary shadow-brutal-sm"
              />
            </div>
            <div>
              <label className="block text-sm font-bold text-brand-text mb-2">
                Continue Assistindo (para Netflix)
              </label>
              <input
                type="text"
                name="CONTINUE"
                value={formData.CONTINUE}
                onChange={handleTextChange}
                className="w-full px-4 py-3 border-2 border-brand-black rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-primary shadow-brutal-sm"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-bold text-brand-text mb-2">
              Descrição
            </label>
            <textarea
              name="DESCRIPTION"
              value={formData.DESCRIPTION}
              onChange={handleTextChange}
              rows={4}
              className="w-full px-4 py-3 border-2 border-brand-black rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-primary shadow-brutal-sm"
            />
          </div>

          {/* Upload de Imagens */}
          <div className="space-y-6">
            <h3 className="text-lg font-bold text-brand-text border-b-2 border-brand-black pb-2">Imagens Principais</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="text-center">
                <label className="block text-sm font-bold text-brand-text mb-2">Capa Principal</label>
                <div className="w-full aspect-[16/9] bg-gray-100 rounded-lg border-2 border-brand-black flex items-center justify-center">
                  {imagePreviewUrls.CAPA ? (
                    <img src={imagePreviewUrls.CAPA} alt="Preview Capa" className="w-full h-full object-cover rounded-md"/>
                                        ) : <span className="text-sm text-brand-text/60">21:9</span>}
                </div>
                <button type="button" onClick={() => capaInputRef.current?.click()} className="mt-2 bg-brand-secondary text-brand-black px-4 py-2 rounded-lg border-2 border-brand-black shadow-brutal-sm hover:shadow-brutal-hover active:shadow-brutal-pressed transition-all font-semibold text-sm">
                  Escolher Capa
                </button>
                <input type="file" ref={capaInputRef} onChange={(e) => handleImageUpload(e, 'CAPA')} className="hidden" accept="image/*"/>
              </div>
              <div className="text-center">
                <label className="block text-sm font-bold text-brand-text mb-2">Avatar do Perfil</label>
                <div className="w-full aspect-square bg-gray-100 rounded-lg border-2 border-brand-black flex items-center justify-center">
                   {imagePreviewUrls.AVATAR ? (
                    <img src={imagePreviewUrls.AVATAR} alt="Preview Avatar" className="w-full h-full object-cover rounded-md"/>
                  ) : <span className="text-sm text-brand-text/60">1:1</span>}
                </div>
                <button type="button" onClick={() => avatarInputRef.current?.click()} className="mt-2 bg-brand-secondary text-brand-black px-4 py-2 rounded-lg border-2 border-brand-black shadow-brutal-sm hover:shadow-brutal-hover active:shadow-brutal-pressed transition-all font-semibold text-sm">
                  Escolher Avatar
                </button>
                <input type="file" ref={avatarInputRef} onChange={(e) => handleImageUpload(e, 'AVATAR')} className="hidden" accept="image/*"/>
              </div>
            </div>

            <h3 className="text-lg font-bold text-brand-text border-b-2 border-brand-black pb-2">Grid de Fotos</h3>
            <div className="grid grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
              {Array.from({ length: 12 }).map((_, i) => {
                const fieldName = `PHOTO_${(i + 1).toString().padStart(2, '0')}`;
                return (
                  <div key={fieldName} className="text-center">
                    <label className="block text-xs font-bold text-brand-text mb-1 truncate">Foto {i + 1}</label>
                    <div className="w-full aspect-[2/3] bg-gray-100 rounded-lg border-2 border-brand-black flex items-center justify-center">
                       {imagePreviewUrls[fieldName] ? (
                        <img src={imagePreviewUrls[fieldName]} alt={`Preview Foto ${i+1}`} className="w-full h-full object-cover rounded-md"/>
                      ) : <span className="text-xs text-brand-text/60">3:4</span>}
                    </div>
                     <button type="button" onClick={() => photoInputRefs.current[i]?.click()} className="mt-2 bg-brand-white text-brand-black px-2 py-1 rounded-lg border-2 border-brand-black shadow-brutal-sm hover:shadow-brutal-hover active:shadow-brutal-pressed transition-all font-semibold text-xs">
                      Enviar
                    </button>
                    <input type="file" ref={el => photoInputRefs.current[i] = el} onChange={(e) => handleImageUpload(e, fieldName)} className="hidden" accept="image/*"/>
                  </div>
                );
              })}
            </div>
          </div>
          
          <button 
            type="submit" 
            disabled={isLoading}
            className="w-full bg-brand-primary text-brand-black px-6 py-4 rounded-lg border-2 border-brand-black shadow-brutal-sm hover:shadow-brutal-hover active:shadow-brutal-pressed transition-all font-bold text-lg disabled:bg-gray-400 disabled:shadow-none"
          >
            {isLoading ? 'Gerando...' : 'Gerar Poster'}
          </button>
        </form>
      )}
    </div>
  );
};

export default CanvaPosterGenerator;
