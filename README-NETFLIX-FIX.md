# Fix para Problema Netflix - Apenas 3 Capas

## 🚨 Problema Identificado

Netflix deveria gerar **12 capas**, mas está gerando apenas **3**.

### An<PERSON><PERSON><PERSON> do <PERSON>

```
Found 4 movies for netflix. Processing 4 of them.
```

**ERRO NO CÓDIGO**: A função está limitando para 4 filmes, não 12!

## 🔧 Solução Imediata

### 1. Verificar o platformConfig

O problema está na configuração da plataforma:

```typescript
const platformConfig = {
  netflix: { aspectRatio: '5:4', quantity: 12 }, // CORRETO: 12
amazon: { aspectRatio: '5:4', quantity: 11 }, // CORRETO: 11
disney: { aspectRatio: '3:5', quantity: 9, totalQuantity: 9 }, // CORRETO: 9
  // ...
}
```

### 2. Verificar o cálculo de moviesToProcess

```typescript
let moviesToProcess = Math.min(totalCoversToGenerate, movies.length)
```

Se `totalCoversToGenerate` está retornando 4, há um erro na lógica.

### 3. Debug necessário

Adicionar logs para verificar:
```typescript
console.log('Platform config:', config);
console.log('Total covers to generate:', totalCoversToGenerate);
console.log('Movies available:', movies.length);
console.log('Movies to process:', moviesToProcess);
```

## 🚀 Ação Corretiva

1. **Verificar a função no Supabase** (não local)
2. **Adicionar logs de debug**
3. **Testar com quantidade fixa**:

```typescript
// TEMPORÁRIO: Forçar 12 filmes
let moviesToProcess = streamingPlatform === 'netflix' ? 12 : Math.min(totalCoversToGenerate, movies.length)
```

## ⚡ Otimização para Evitar Timeout

### Processamento em Paralelo

```typescript
// Processar 3 filmes por vez em paralelo
const PARALLEL_BATCH_SIZE = 3;

for (let i = 0; i < moviesToProcess; i += PARALLEL_BATCH_SIZE) {
  const batch = movies.slice(i, Math.min(i + PARALLEL_BATCH_SIZE, moviesToProcess));
  
  const batchPromises = batch.map(async (movie, batchIndex) => {
    const movieIndex = i + batchIndex;
    // ... processo de geração
  });
  
  const results = await Promise.allSettled(batchPromises);
  
  // Processar resultados
  results.forEach((result, idx) => {
    if (result.status === 'fulfilled' && result.value) {
      allGeneratedUrls.push(result.value);
    }
  });
}
```

## 📝 Checklist de Verificação

- [ ] A função local tem `quantity: 12` para Netflix?
- [ ] O log mostra "Found 12 movies for netflix"?
- [ ] O log mostra "Processing 12 of them"?
- [ ] Há timeout nos logs do Supabase?
- [ ] Round 6 está sendo pulado corretamente?

## 🎯 Resultado Esperado

Após a correção, o log deve mostrar:
```
Found 12 movies for netflix. Processing 12 of them.
Processing movie 1/12: Round 6 (Squid Game)
Processing movie 2/12: Wednesday
...
Processing movie 12/12: Bridgerton
Finished generation. 11 covers successful.
```

(11 porque Round 6 provavelmente falhará por conteúdo sensível) 