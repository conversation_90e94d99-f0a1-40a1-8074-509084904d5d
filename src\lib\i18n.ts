import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    debug: true, // Mantenha como true durante o desenvolvimento
    fallbackLng: 'pt-BR',
    interpolation: {
      escapeValue: false, // React já protege contra XSS
    },
    resources: {
      en: {
        translation: {
          header: {
            credits: 'Credits',
            buyCredits: 'Buy More Credits',
            dashboard: 'Dashboard',
            manageAccount: 'Manage your account',
            myGenerations: 'My Generations',
            viewGeneratedPhotos: 'View generated photos',
            contact: 'Contact',
            getInTouch: 'Get in touch',
            admin: 'Admin',
            adminPanel: 'Administrative panel',
            signOut: 'Sign out',
            logout: 'Logout from account'
          },
          dashboard: {
            title: 'Dashboard',
            loading: 'Loading dashboard data...',
            availableCredits: 'Available Credits',
            usedCredits: 'Used Credits',
            totalCredits: 'Total Credits',
            creditHistory: 'Credit Usage History',
            purchaseHistory: 'Purchase History',
            date: 'Date',
            description: 'Description',
            creditsUsed: 'Credits Used',
            amount: 'Amount',
            status: 'Status',
            noCreditUsageFound: 'No credit usage found',
            noPurchaseFound: 'No purchases found'
          },
          dashboard_tabs: {
            dashboard: 'Dashboard',
            myGenerations: 'My Generations',
            logs: 'Logs History',
            credits: 'Credits',
            purchases: 'Purchases',
            test: 'Test'
          },
          notifications: {
            dataUpdated: 'Data updated!',
            errorLoadingData: 'Error loading dashboard data',
            loadingDashboardData: 'Loading dashboard data...'
          },
          creditBalance: {
            title: 'Your Credits',
            buyMore: 'Buy More',
            available: 'Available',
            used: 'Used',
            total: 'Total',
            howToUse: 'How to use your credits:',
            coverCost: '1 credit = 1 movie/series cover',
            posterCost: '3 credits = 1 final poster',
            regenCost: 'Regeneration = costs credits',
            hdDownload: 'HD Download = included',
            recentUsage: 'Recent Usage',
            creditHistory: 'Credit History',
            noCredits: 'No credits purchased',
            buyFirstCredits: 'Buy your first credits to start generating custom covers',
            lowBalanceWarning: 'You have only {{count}} credit left. | You have only {{count}} credits left.',
            considerBuying: 'Consider buying more credits to avoid running out.',
            status: 'Status'
          },
          contactForm: {
            messageSent: 'Message Sent!',
            fullName: 'Full Name',
            fullNamePlaceholder: 'Your full name',
            email: 'Email',
            emailPlaceholder: '<EMAIL>',
            subject: 'Subject',
            subjectPlaceholder: 'Briefly summarize the subject of your message',
            message: 'Message',
            messagePlaceholder: 'Describe in detail your question, suggestion, criticism, or the problem you encountered...',
            sending: 'Sending...',
            sendMessage: 'Send Message',
            contactInfoTitle: 'Contact Information',
            directEmail: 'Direct Email',
            responseTime: 'Response Time',
            technicalSupport: 'Technical Support',
            languages: 'Languages',
            title: '📬 Contact Us',
            subtitle: 'Have a question, suggestion or found a bug? We\'re here to help! Send us your message and we\'ll get back to you as soon as possible.',
            thankYou: 'Thank you for contacting us! We received your message and will get back to you soon.',
            redirecting: 'Redirecting in a few seconds...',
            fillRequired: 'Please fill in all required fields',
            successMessage: 'Message sent successfully! We will contact you soon.',
            sendError: 'Error sending message',
            tryAgain: 'Error sending message. Please try again.',
            types: {
              bug: 'Report Bug',
              bugDesc: 'Found an error or technical problem?',
              suggestion: 'Suggestion',
              suggestionDesc: 'Have an idea to improve PosterFlix?',
              feedback: 'Criticism/Feedback',
              feedbackDesc: 'Share your opinion about our platform',
              other: 'Other',
              otherDesc: 'General questions or other topics'
            }
          },
          admin: {
            loading: 'Loading admin panel...',
            title: 'Admin Panel',
            subtitle: 'System control and insights',
            analysisPeriod: 'Analysis Period',
            totalRevenue: 'Total Revenue',
            operationalCosts: 'Operational Costs',
            generationCostBreakdown: 'Generation Cost Breakdown',
            generations: 'generations',
            last7DaysActivity: 'Last 7 Days Activity',
            purchases: 'purchases',
            platformDistribution: 'Platform Distribution',
            successRate: 'Success Rate',
            covers: 'Covers',
            posters: 'Posters',
            overallSuccessRate: 'Overall Success Rate',
            top5UsersByRevenue: 'Top 5 Users by Revenue',
            systemAlerts: 'System Alerts',
            searchUser: 'Search User',
            searchUserPlaceholder: 'Enter User ID or part of it...',
            search: 'Search',
            userProfile: 'User Profile',
            userId: 'User ID',
            availableCredits: 'Available Credits',
            totalGenerations: 'Total Generations',
            totalPosters: 'Total Posters',
            viewGenerations: 'View Generations',
            regenerateFailures: 'Regenerate Failures',
            recentPurchases: 'Recent Purchases',
            credits: 'Credits',
            value: 'Value',
            status: 'Status',
            date: 'Date',
            failedGenerations: 'Failed Generations',
            filters: 'Filters',
            allStatuses: 'All Statuses',
            completed: 'Completed',
            processing: 'Processing',
            failed: 'Failed',
            allTypes: 'All Types',
            cover: 'Cover',
            poster: 'Poster',
            allPlatforms: 'All Platforms',
            update: 'Update',
            name: 'Name',
            email: 'Email',
            type: 'Type',
            actions: 'Actions',
            lastActivity: 'Last Activity',
            userNameUnavailable: 'Name not available',
            logFilters: 'Log Filters',
            allLevels: 'All Levels',
            error: 'Error',
            warning: 'Warning',
            info: 'Info',
            allServices: 'All Services',
            coverGeneration: 'Cover Generation',
            canvaPoster: 'Canva Poster',
            payments: 'Payments',
            updateLogs: 'Update Logs',
            timestamp: 'Timestamp',
            level: 'Level',
            service: 'Service',
            message: 'Message',
            noPreview: 'No preview',
            noTitle: 'No title'
          },
          canvaHistory: {
            title: 'Poster History',
            update: 'Update',
            loading: 'Loading history...',
            noPosters: 'No posters generated yet.',
            goToGenerate: 'Go to the poster generation page to create your first poster!',
            errorLoading: 'Error loading history',
            openInCanva: 'Open in Canva'
          },
          canvaPosterPage: {
            title: 'Custom Poster Generator',
            subtitle: 'Create your own movie posters with artificial intelligence.'
          },
          newGenerationModal: {
            title: 'New Generation',
            subtitle: 'How do you want to start?',
            fromScratch: 'Start from Scratch',
            fromScratchDesc1: '• Clear all current settings',
            fromScratchDesc2: '• Remove previously generated covers',
            fromScratchDesc3: '• Reset the wizard to the first step',
            continueEditing: 'Continue Editing',
            continueEditingDesc1: '• Keep photo, platform, and settings',
            continueEditingDesc2: '• Clear only the generated covers',
            continueEditingDesc3: '• Allow editing settings if you wish',
            noExistingDataDesc: 'No saved settings to reuse.',
            tip: 'Tip',
            tipDescription: 'The "Continue Editing" option will be available after you make your first generation.'
          },
          pricingModal: {
            buyCredits: 'Buy Credits',
            credits: 'credits',
            perCredit: 'per credit',
            buyNow: 'Buy {{credits}} Credits',
            processing: 'Processing...',
            howCreditsWork: 'How credits work:',
            coverCost: '1 credit = 1 cover',
            posterCost: '3 credits = 1 poster',
            regenCosts: 'Regeneration costs credits',
            hdDownload: 'HD Download',
            netflixCost: 'Netflix (12 covers):',
            disneyCost: 'Disney+ (9 covers):',
            amazonCost: 'Amazon (11 covers):',
            creditsCount: '{{count}} credits',
            mostPopular: 'Most Popular',
            youHave: 'You have {{count}} credits available',
            howCreditsWorkTitle: 'How credits work:',
            usageExamples: 'Usage examples:',
            netflixCalc: '12 credits + 3 for poster = 15 credits',
            disneyCalc: '9 credits + 3 for poster = 12 credits',
            amazonCalc: '11 credits + 3 for poster = 14 credits',
            securePayment: '💳 Secure payment processed by Stripe',
            ssl: '🔒 Your data is protected with SSL encryption',
            voucherSection: 'Have a promotional code?',
            hideVoucher: 'Hide promotional code',
            redeemVoucher: 'Redeem Promotional Code',
            needLogin: 'You need to be logged in to redeem a promotional code',
            loginFirst: 'Login first',
            enterCode: 'Enter your code',
            redeem: 'Redeem',
            checking: 'Checking...',
            codeHelp: 'Codes contain 6-12 characters (letters and numbers)',
            continueAuth: 'Continue',
            willLoginNext: 'Enter your code below. You will login/register in the next step.'
          },
          progressBar: {
            generatingCovers: 'Generating Covers...',
            generationComplete: 'Generation Complete',
            processing: 'Processing',
            completed: 'Completed',
            inProgress: 'In Progress',
            failed: 'Failed',
            complete: 'Complete'
          },
          fileUpload: {
            errorImageType: 'Please select image files only',
            errorImageSize: 'File too large. Maximum size: 10MB',
            successUpload: 'Image uploaded successfully! 📸',
            removed: 'Image removed',
            selectedImage: 'Selected Image',
            uploadYourPhoto: 'Upload Your Photo',
            dragAndDrop: 'Drag and drop your image here, or click to browse',
            photoTip: 'Use front-facing photos for best results. Avoid profile or distant shots.',
            highQuality: 'High Quality',
            highQualityDesc: 'Best results with clear photos',
            multipleFormats: 'Multiple Formats',
            multipleFormatsDesc: 'JPG, PNG, WEBP supported',
            upTo10MB: 'Up to 10MB',
            upTo10MBDesc: 'Large file support',
            chooseFile: 'Choose File',
            dropHere: 'Drop your image here!'
          },
          baseImageSelector: {
            title: 'Select Base Image',
            subtitle: 'Choose an image you have previously uploaded',
            loading: 'Loading your images...',
            noImages: 'No base images found',
            noImagesDesc: 'Your images will appear here after you upload for the first time',
            tryAgain: 'Try Again'
          },
          hero: {
            badge: '✨ AI Cover Generation Wizard',
            title: 'Create Your Movie Covers',
            title1: 'Create Your',
            title2: 'Movie Covers',
            title3: 'in Seconds',
            titleSpan: 'in Seconds',
            subtitle: 'Transform your photos into stunning movie covers with AI. Choose from Netflix, Disney+, and Amazon Prime styles.',
            premium: 'Premium Quality AI',
            cta: 'Start Creating',
            startCreating: 'Start Creating',
            demo: 'Watch Demo',
            watchDemo: 'Watch Demo',
            stat1: '50K+ Covers Created',
            stat2: '3 Platforms',
            stat3: '99% Satisfaction',
            lightningFast: 'Lightning Fast',
            lightningFastDesc: 'Generate up to 12 unique covers in under 2 minutes',
            premiumQuality: 'Premium Quality',
            premiumQualityDesc: 'Professional movie poster quality with AI precision',
            multipleStyles: 'Multiple Styles',
            multipleStylesDesc: 'Netflix, Disney+, Amazon Prime themed templates',
            coversCreated: 'Covers Created',
            platforms: 'Platforms',
            satisfaction: 'Satisfaction',
            avgTime: 'Avg. Time'
          },
          authCallback: {
            success: 'Login successful!',
            error: 'Error processing login. Please try again.',
            processing: 'Processing authentication...'
          },
          authForm: {
            googleRedirect: 'Redirecting to Google login...',
            googleError: 'Error logging in with Google',
            loginSuccess: 'Login successful!',
            signupSuccess: 'Signup successful! Check your email.',
            authError: 'Authentication error',
            access: 'Access',
            createAccount: 'Create Account',
            loginToContinue: 'Login to continue',
            joinToStart: 'Join us to get started',
            continueWithGoogle: 'Continue with Google',
            or: 'OR',
            fullName: 'Full Name',
            email: 'Email',
            password: 'Password',
            loading: 'Loading...',
            login: 'Login',
            dontHaveAccount: "Don't have an account?",
            signUp: 'Sign up',
            alreadyHaveAccount: 'Already have an account?',
            doLogin: 'Log in'
          },
          languageSelector: {
            title: 'Choose Language',
            subtitle: 'Select your preferred language for the covers',
            english: 'English',
            englishDesc: 'Generate posters in English style',
            portuguese: 'Portuguese',
            portugueseDesc: 'Generate posters in Brazilian style'
          },
          photoTypeSelector: {
            title: 'Choose Photo Type',
            subtitle: 'Select whether your photo has one person or a couple',
            individual: 'Individual',
            individualDesc: 'Single person photo',
            couple: 'Couple',
            coupleDesc: 'Two people photo'
          },
          quantitySelector: {
            title: 'Number of Covers',
            covers: 'Covers',
            quickGeneration: 'Quick generation',
            moreVariety: 'More variety',
            extendedCollection: 'Extended collection',
            completeCollection: 'Complete collection',
            free: 'Free',
            premium: 'Premium'
          },
          templateSelector: {
            title: 'Choose Poster Template',
            subtitle: 'Each template has a specific layout and number of photos.',
            netflixDesc: '12 photos (5:4) + cover (21:9) + avatar (1:1)',
            disneyDesc: '6 photos (5:3) + 3 photos (6:5) + cover (21:9) + avatar (1:1)',
            amazonDesc: '11 photos (5:4) + cover (19:9) + avatar (1:1)',
            photos: 'PHOTOS',
            aboutTemplates: 'About Templates',
            aboutTemplatesDesc: 'Each template has a specific layout optimized for the chosen platform. The number and format of photos vary.',
            netflixPhotos: 'Netflix: 12 photos',
            disneyPhotos: 'Disney+: 9 photos',
            amazonPhotos: 'Prime Video: 11 photos'
          },
          titleGeneration: {
            title: 'Title Generation',
            subtitle: 'Generate movie/series titles on posters',
            importantNotice: 'Important Notice',
            noticeText: 'AI-generated text may contain spelling errors or incorrect letters. The titles are generated automatically and may not be perfectly accurate.'
          },
          creativityLevel: {
            title: 'Creativity Level',
            subtitle: 'Choose how the AI should transform your photo in the movies.',
            moreCreative: 'More Creative',
            moreCreativeDesc: 'Total artistic freedom. The AI uses your photo as inspiration, but can completely recreate the look, hair, and pose.',
            moreCreativeExample: 'Ideal for dramatic transformations',
            faithfulFace: 'Faithful Face',
            faithfulFaceDesc: 'Keeps your face as faithful as possible, but allows for creativity in hair, expression, and pose.',
            faithfulFaceExample: 'Balance between fidelity and creativity',
            strictPreservation: 'Strict Preservation',
            strictPreservationDesc: 'Keeps your original features, hair, and pose exactly. Only changes background and clothes.',
            strictPreservationExample: 'Maximum fidelity to the original photo',
            tip: 'Tip:',
            tipText: 'Creative: Best for dramatic transformations • Faithful Face: Ideal for most cases • Strict: When you want to keep your exact appearance'
          },
          checkoutForm: {
            title: 'Finalize Payment',
            credits: 'Credits:',
            creditsCount: '{{count}} credits',
            total: 'Total:',
            perCredit: 'per credit',
            paymentInfo: 'Payment Information',
            processing: 'Processing...',
            pay: 'Pay {{price}}',
            securePayment: '🔒 Secure payment processed by Stripe'
          },
          plans: {
            starter_credits: 'Starter Plan',
            popular_credits: 'Popular Plan',
            master_credits: 'Master Plan',
            starter_desc: 'Ideal for getting started and testing',
            popular_desc: 'The best value for money',
            master_desc: 'For heavy users',
            feature_economy: 'Economy',
            feature_priority: 'Priority Support'
          },
          packBalance: {
            title: 'Your Packs',
            buyMore: 'Buy More',
            available: 'Available',
            used: 'Used',
            total: 'Total',
            purchasedPacks: 'Purchased Packs',
            purchasedOn: 'Purchased on {date}',
            packsAvailable: '{available}/{total} available',
            packsUsed: '{used} used',
            recentUsage: 'Recent Usage',
            noPacks: 'No packs purchased',
            buyFirstPack: 'Buy your first pack to start generating custom covers',
            buyFirstPackButton: 'Buy First Pack',
            lowBalanceWarning: 'You have only {count} pack left. | You have only {count} packs left.',
            considerBuying: 'Consider buying more packs to avoid running out.'
          },
          packInfo: {
            title: 'How Packs Work',
            eachPackIncludes: 'Each pack includes:',
            includesPlatform: '• Covers for 1 platform (Netflix, Disney+, or Amazon)',
            includesCovers: '• Up to 12 cover images per pack',
            includesPoster: '• 1 final printable poster',
            coverRegen: 'Cover Regeneration:',
            coverRegenDesc: 'Unlimited - you can regenerate any cover image as many times as you like',
            finalPoster: 'Final Poster:',
            finalPosterDesc: 'Can only be generated once per pack',
            printablePoster: 'Printable Poster:',
            printablePosterDesc: 'Includes all your covers organized in a professional layout',
            tip: 'Tip:',
            tipText: 'First generate all the covers you like, then create the final poster with them!'
          },
          generationLogs: {
            title: 'Generation History',
            filters: 'Filters:',
            allStatus: 'All Statuses',
            completed: 'Completed',
            failed: 'Failed',
            processing: 'Processing',
            allPlatforms: 'All Platforms',
            netflix: 'Netflix',
            disney: 'Disney+',
            amazon: 'Prime Video',
            fix: 'Fix',
            fixTooltip: 'Fix orphaned records (incorrect status)',
            update: 'Update',
            noGenerations: 'No generations found',
            duration: {
              inProgress: 'In progress...',
              seconds: 's',
              minutes: 'min'
            },
            status: {
              completed: 'Completed',
              failed: 'Failed',
              processing: 'Processing',
              unknown: 'Unknown'
            },
            platforms: {
              netflix: 'NETFLIX',
              disney: 'DISNEY+',
              amazon: 'PRIME VIDEO'
            },
            photoTypes: {
              individual: 'Individual',
              couple: 'Couple'
            },
            stats: {
              movie: 'movie',
              movies: 'movies'
            },
            modal: {
              title: 'Generation Details',
              close: '✕',
              platform: 'Platform:',
              type: 'Type:',
              status: 'Status:',
              started: 'Started:',
              finished: 'Finished:',
              duration: 'Duration:',
              processedMovies: 'Processed Movies',
              badges: {
                safePrompt: 'Safe Prompt',
                fallback: 'Fallback:'
              },
              error: 'Error:'
            },
            messages: {
              orphanFixed: 'orphaned records',
              noOrphanFound: 'No orphaned records found',
              errorFetching: 'Error loading generation history',
              errorFixing: 'Error fixing orphaned records'
            }
          },
          wizard: {
            title: 'Create Your Movie Covers',
            subtitle: 'Follow these simple steps to generate amazing covers for your photos',
            progress: 'Progress',
            previous: 'Previous',
            next: 'Next',
            steps: {
              initialChoice: {
                title: 'Initial Choice',
                description: 'Start or load',
                howToStart: 'How do you want to start?',
                withExisting: 'You can start a new generation with a new photo or load your last work to continue where you left off.',
                firstTime: "Let's start creating your first custom cover generation!",
                startFromScratch: 'Start from Scratch',
                loadGeneration: 'Load Generation',
                firstTimeNotice: 'First time?',
                firstTimeDesc: 'After creating your first generation, you can load previous work to continue where you left off.'
              },
              upload: {
                title: 'Upload Photo',
                description: 'Choose your image'
              },
              photoType: {
                title: 'Photo Type',
                description: 'Individual or couple'
              },
              gender: {
                title: 'Gender',
                description: 'Select for more precision'
              },
              platform: {
                title: 'Platform Style',
                description: 'Netflix, Disney+, Amazon'
              },
              creativity: {
                title: 'Creativity Level',
                description: 'How to transform your photo'
              },
              language: {
                title: 'Language',
                description: 'Choose your language'
              },
              titles: {
                title: 'Title Options',
                description: 'Configure titles & name',
                titleOptions: 'Title Options',
                configureTitle: 'Configure your name and title preferences.',
                yourName: 'Your Name *',
                namePlaceholder: 'Enter your name for the covers',
                nameDescription: 'This name will appear on your personalized movie covers.',
                titleGeneration: 'Title Generation',
                titleGenerationDesc: 'Generate movie/series titles on posters.',
                importantNotice: 'Important Notice',
                aiWarning: 'AI-generated text may contain spelling errors or incorrect letters. Titles are generated automatically and may not be perfectly accurate.'
              },
              payment: {
                title: 'Buy Credits',
                description: 'Acquire necessary credits',
                buyCredits: 'Buy Credits',
                needCredits: 'You need {{count}} credits for this generation.',
                haveCredits: 'You have {{count}} credits available.',
                securePayment: 'Secure payment processed by Stripe.'
              },
              generate: {
                title: 'Generate',
                description: 'Create your covers',
                readyToGenerate: 'Ready to Generate!',
                allReady: "Everything's ready! This generation will use {{count}} credits.",
                haveCredits: 'You have {{count}} credits available.',
                generating: 'Generating...',
                generateCovers: 'Generate Movie Covers',
                willGenerate: 'This will generate up to {{count}} unique covers in your selected style.'
              }
            }
          },
          coverGrid: {
            posterSaved: '🎉 Poster saved! Now you can download it directly and it appears in "My Generations".',
            posterSavedCanva: '🎉 Poster saved in "My Generations" > "Posters"! Canva link available.',
            refreshTooltip: 'Click if covers don\'t appear or are outdated',
            printPosterTemplate: 'Print Poster',
            downloadAll: 'Download all covers',
            chooseFromGallery: 'Choose from gallery'
          },
          landing: {
            brand: 'PosterFlix',
            hero: {
              badge: '✨ Hollywood Cinematic Technology',
              title: 'Create Amazing Cinematic Posters with AI',
              title1: 'BECOME A',
              title2: 'HOLLYWOOD',
              title3: 'STAR',
              subtitle: 'Transform your ideas into professional cinematic posters in seconds. Our advanced AI understands styles, genres and visual elements to create unique art.',
              premium: 'Premium Quality • Not a cheap filter',
              cta: 'Get Started Free',
              demo: 'Watch Demo',
              watchDemo: 'Watch Demo',
              stat1: 'Posters Created',
              stat2: 'Movie Styles',
              stat3: 'Satisfaction'
            },
            howItWorks: {
              title: 'HOW IT WORKS',
              subtitle: 'Three simple steps to create your cinematic poster',
              step1: 'Upload your photo',
              desc1: 'Choose a clear photo of your face',
              step2: 'Choose the style',
              desc2: 'Select genre and platform',
              step3: 'Download your poster',
              desc3: 'Get your poster in high quality',
              steps: {
                upload: {
                  title: 'Upload',
                  description: 'Upload your photo or describe your poster idea.'
                },
                style: {
                  title: 'Choose Style',
                  description: 'Select from hundreds of available cinematic styles.'
                },
                download: {
                  title: 'Download your Poster',
                  description: 'Get your professional high-resolution poster in seconds.'
                }
              }
            },
            platforms: {
              title: 'CHOOSE YOUR CINEMATIC UNIVERSE',
              subtitle: 'Each platform has its unique visual identity. Our AI masters the aesthetic codes of each one to create authentic posters.',
              cta: 'Which cinematic universe matches you?',
              ctaButton: 'DISCOVER MY STYLE',
              netflix: {
                title: 'NETFLIX',
                subtitle: 'Drama & Suspense',
                desc: 'Dark and intense cinematic aesthetic. Dramatic tones that capture the essence of the platform\'s greatest hits.',
                feature1: '12 unique covers',
                feature2: 'Premium noir style',
                feature3: 'Iconic typography',
                features: {
                  covers: '12 unique covers',
                  style: 'Premium noir style',
                  typography: 'Iconic typography'
                }
              },
              disney: {
                title: 'DISNEY+',
                subtitle: 'Fantasy & Adventure',
                desc: 'Cinematic magic with vibrant colors and epic compositions. The visual that awakens the hero within you.',
                feature1: '9 magical covers',
                feature2: 'Epic style',
                feature3: 'Vibrant colors',
                features: {
                  covers: '9 magical covers',
                  style: 'Epic style',
                  colors: 'Vibrant colors'
                }
              },
              amazon: {
                title: 'PRIME VIDEO',
                subtitle: 'Action & Thriller',
                desc: 'Premium cinematic visual with focus on action and intensity. The aesthetic of modern blockbusters.',
                feature1: '11 dynamic covers',
                feature2: 'Blockbuster style',
                feature3: 'Premium composition',
                features: {
                  covers: '11 dynamic covers',
                  style: 'Blockbuster style',
                  composition: 'Premium composition'
                }
              }
            },
            features: {
              title: 'INCREDIBLE FEATURES',
              ai: 'Advanced AI',
              aiDesc: 'Cutting-edge technology for realistic results',
              fast: 'Super Fast',
              fastDesc: 'Generate your poster in under 2 minutes',
              quality: 'High Quality',
              qualityDesc: '4K images ready for printing',
              easy: 'Easy to Use',
              easyDesc: 'Simple and intuitive interface',
              items: {
                ai: {
                  title: 'Advanced AI',
                  description: 'State-of-the-art algorithms that understand cinematic styles and create unique art.'
                },
                speed: {
                  title: 'Fast Generation',
                  description: 'Create professional posters in seconds, not hours.'
                },
                quality: {
                  title: 'Premium Quality',
                  description: 'High resolution and professional quality for all your projects.'
                },
                ease: {
                  title: 'Easy to Use',
                  description: 'Intuitive interface that anyone can use, no technical knowledge required.'
                },
                styles: {
                  title: 'Varied Styles',
                  description: 'Hundreds of cinematic styles, from classics to modern.'
                },
                updates: {
                  title: 'Always Updated',
                  description: 'New features and improvements constantly added.'
                }
              }
            },
            testimonials: {
              title: 'WHAT USERS SAY',
              quote1: 'I never thought it would be so easy! I turned an ordinary photo into an epic movie cover. My friends loved it!',
              role1: 'Content Creator',
              quote2: 'The quality is incredible. It looks like a real movie poster. I used it for a gift and it was an absolute success.',
              role2: 'Graphic Designer',
              quote3: 'This is addictive! I\'ve already created posters for my whole family. The style selection feature is my favorite.',
              role3: 'Movie Fan',
              stat1: 'Happy Users',
              stat2: 'Posters Created',
              stat3: 'Average Rating',
              stat4: 'Would Recommend',
              ana: {
                name: 'Ana Silva',
                role: 'Art Director',
                company: 'Creative Studio',
                content: 'The quality of the generated posters is impressive. I was able to create professional materials in minutes that would have taken hours before.'
              },
              carlos: {
                name: 'Carlos Santos',
                role: 'Producer',
                company: 'FilmeCorp',
                content: 'It revolutionized our creative process. The AI perfectly understands the cinematic style we are looking for.'
              },
              marina: {
                name: 'Marina Costa',
                role: 'Graphic Designer',
                company: 'Visual Studio',
                content: 'Indispensable tool for any professional in the field. The variety of styles and quality are exceptional.'
              }
            },
            faq: {
              title: 'WHY TRUST US?',
              subtitle: 'Total transparency. No tricks. No fine print. Just professional results you can trust.',
              secure: 'ABSOLUTE PRIVACY',
              secureDesc: 'Your photos are processed with military-grade encryption and automatically deleted in 24h. Zero permanent storage.',
              secureBadge: 'SSL 256-bit',
              quality: 'GUARANTEED QUALITY',
              qualityDesc: 'Native 4K resolution, professional colors, cinematic composition. If it\'s not perfect, we\'ll redo it for free.',
              qualityBadge: '4K Ultra HD',
              rights: 'YOUR RIGHTS PROTECTED',
              rightsDesc: 'You own 100% of the commercial rights to generated images. Use as you wish: social media, printing, commercialization.',
              rightsBadge: '100% Yours',
              faqTitle: 'QUESTIONS YOU MIGHT HAVE',
              q1: 'Will my photo really look good?',
              a1: 'Our AI was trained with thousands of real Hollywood posters. The result is indistinguishable from a professional poster.',
              q2: 'Is it safe to send my photo?',
              a2: 'Absolutely. We use the same encryption as banks and delete everything in 24h. Even we can\'t access your photos afterwards.',
              q3: 'Can I use it commercially?',
              a3: 'Yes! You have full rights. Sell, print, post wherever you want. The image is 100% yours.',
              q4: 'What if I don\'t like it?',
              a4: 'We\'ll redo it as many times as needed until it\'s perfect. Your satisfaction is guaranteed.',
              qualityFaq: {
                question: 'Will my photo really look good?',
                answer: 'Our AI was trained with thousands of real Hollywood posters. The result is indistinguishable from a professional poster.'
              },
              security: {
                question: 'Is it safe to send my photo?',
                answer: 'Absolutely. We use the same encryption as banks and delete everything in 24h. Even we can\'t access your photos afterwards.'
              },
              commercial: {
                question: 'Can I use it commercially?',
                answer: 'Yes! You have full rights. Sell, print, post wherever you want. The image is 100% yours.'
              },
              refund: {
                question: 'What if I don\'t like it?',
                answer: 'We\'ll redo it as many times as needed until it\'s perfect. Your satisfaction is guaranteed.'
              },
              socialProof: '+2,847 people created their posters today',
              socialProofSub: 'Join the community of stars'
            },
            cta: {
              urgency: 'LIMITED OFFER • TODAY ONLY',
              title: 'YOUR DEBUT HAPPENS NOW',
              subtitle: 'Don\'t let your cinematic dreams wait. Thousands have already discovered the protagonist within them.',
              scarcity1: '2,847 posters',
              scarcity1Sub: 'created today',
              scarcity2: '98% satisfaction',
              scarcity2Sub: 'from users',
              scarcity3: '< 2 minutes',
              scarcity3Sub: 'to create',
              button: 'BECOME A STAR NOW',
              guarantee: 'Satisfaction guarantee • We\'ll redo until perfect',
              finalProof: '"I turned an ordinary selfie into the most epic poster of my life. Now I feel like a true protagonist!" - Ana, Designer'
            },
            footer: {
              text: '© 2024 AI Streaming Poster. All rights reserved.'
            },
            demo: {
              title: 'SEE THE MAGIC HAPPEN',
              subtitle: 'Test our AI in real time! See how we transform an ordinary photo into professional movie covers.',
              originalPhoto: 'ORIGINAL PHOTO',
              yourPhoto: 'YOUR PHOTO',
              aiGenerated: 'AI GENERATED',
              generating: 'Generating...',
              tryNow: 'TRY NOW',
              reset: 'RESET',
              info: 'This is a demonstration. Click "Try Now" to see how our AI transforms photos into movie covers!',
              feature1: 'Fast',
              feature1Desc: 'Under 2 minutes',
              feature2: 'Realistic',
              feature2Desc: 'Latest AI technology',
              feature3: 'Multiple Styles',
              feature3Desc: '3 different platforms'
            },
            features2: {
              title: 'Advanced Features',
              subtitle: 'Everything you need for professional results',
              advanced: {
                title: 'Advanced AI',
                description: 'State-of-the-art algorithms for realistic results'
              },
              fast: {
                title: 'Lightning Fast',
                description: 'Generate professional posters in seconds'
              },
              quality: {
                title: 'Premium Quality',
                description: 'High resolution output ready for print'
              },
              easy: {
                title: 'Easy to Use',
                description: 'Intuitive interface anyone can master'
              }
            },
            velocity: {
              text: 'TRANSFORM • CREATE • INSPIRE • GENERATE • DESIGN • INNOVATE • TRANSFORM • CREATE • INSPIRE • GENERATE • DESIGN • INNOVATE'
            },
            nav: {
              features: 'Features',
              howItWorks: 'How It Works',
              testimonials: 'Testimonials',
              demo: 'Demo',
              login: 'Login',
              getStarted: 'Get Started',
              dashboard: 'Dashboard'
            },
            stats: {
              posters: 'Posters Created',
              users: 'Active Users',
              satisfaction: 'Satisfaction',
              support: 'Support'
            },
            footer: {
              description: 'Transform your ideas into professional cinematic posters in seconds with our advanced AI.',
              product: 'Product',
              navigation: 'Navigation',
              about: 'About',
              aboutLine1: 'AI-powered poster generation',
              aboutLine2: 'Professional quality results',
              aboutLine3: 'Trusted by creators worldwide',
              copyright: '© 2024 AI Streaming Poster. All rights reserved.'
            }
          }
        }
      },
      'pt-BR': {
        translation: {
          header: {
            credits: 'Créditos',
            buyCredits: 'Comprar mais créditos',
            dashboard: 'Dashboard',
            manageAccount: 'Gerencie sua conta',
            myGenerations: 'Minhas Gerações',
            viewGeneratedPhotos: 'Ver fotos geradas',
            contact: 'Contato',
            getInTouch: 'Entre em contato',
            admin: 'Admin',
            adminPanel: 'Painel administrativo',
            signOut: 'Sair',
            logout: 'Sair da conta'
          },
          dashboard: {
            title: 'Dashboard',
            loading: 'Carregando dados do dashboard...',
            availableCredits: 'Créditos Disponíveis',
            usedCredits: 'Créditos Utilizados',
            totalCredits: 'Créditos Totais',
            creditHistory: 'Histórico de Uso de Créditos',
            purchaseHistory: 'Histórico de Compras',
            date: 'Data',
            description: 'Descrição',
            creditsUsed: 'Créditos Utilizados',
            amount: 'Valor',
            status: 'Status',
            noCreditUsageFound: 'Nenhum uso de crédito encontrado',
            noPurchaseFound: 'Nenhuma compra encontrada'
          },
          dashboard_tabs: {
            dashboard: 'Dashboard',
            myGenerations: 'Minhas Gerações',
            logs: 'Histórico de Logs',
            credits: 'Créditos',
            purchases: 'Compras',
            test: 'Test'
          },
          notifications: {
            dataUpdated: 'Dados atualizados!',
            errorLoadingData: 'Erro ao carregar dados do dashboard',
            loadingDashboardData: 'Carregando dados do dashboard...'
          },
          creditBalance: {
            title: 'Seus Créditos',
            buyMore: 'Comprar Mais',
            available: 'Disponíveis',
            used: 'Utilizados',
            total: 'Total',
            howToUse: 'Como usar seus créditos:',
            coverCost: '1 crédito = 1 capa de filme/série',
            posterCost: '3 créditos = 1 poster final',
            regenCost: 'Regeneração = custa créditos',
            hdDownload: 'Download HD = incluído',
            recentUsage: 'Uso Recente',
            creditHistory: 'Histórico de Créditos',
            noCredits: 'Nenhum crédito adquirido',
            buyFirstCredits: 'Compre seus primeiros créditos para começar a gerar capas personalizadas',
            lowBalanceWarning: 'Você tem apenas {{count}} crédito restante. | Você tem apenas {{count}} créditos restantes.',
            considerBuying: 'Considere comprar mais créditos para não ficar sem.',
            status: 'Status'
          },
          contactForm: {
            messageSent: 'Mensagem Enviada!',
            fullName: 'Nome Completo',
            fullNamePlaceholder: 'Seu nome completo',
            email: 'Email',
            emailPlaceholder: '<EMAIL>',
            subject: 'Assunto',
            subjectPlaceholder: 'Resuma brevemente o assunto da sua mensagem',
            message: 'Mensagem',
            messagePlaceholder: 'Descreva detalhadamente sua dúvida, sugestão, crítica ou problema encontrado...',
            sending: 'Enviando...',
            sendMessage: 'Enviar Mensagem',
            contactInfoTitle: 'Informações de Contato',
            directEmail: 'Email direto',
            responseTime: 'Tempo de resposta',
            technicalSupport: 'Suporte técnico',
            languages: 'Idiomas',
            title: '📬 Fale Conosco',
            subtitle: 'Tem alguma dúvida, sugestão ou encontrou um bug? Estamos aqui para ajudar! Envie sua mensagem e responderemos o mais rápido possível.',
            thankYou: 'Obrigado pelo seu contato! Recebemos sua mensagem e entraremos em contato em breve.',
            redirecting: 'Redirecionando em alguns segundos...',
            fillRequired: 'Por favor, preencha todos os campos obrigatórios',
            successMessage: 'Mensagem enviada com sucesso! Entraremos em contato em breve.',
            sendError: 'Erro ao enviar mensagem',
            tryAgain: 'Erro ao enviar mensagem. Tente novamente.',
            types: {
              bug: 'Reportar Bug',
              bugDesc: 'Encontrou algum erro ou problema técnico?',
              suggestion: 'Sugestão',
              suggestionDesc: 'Tem uma ideia para melhorar o PosterFlix?',
              feedback: 'Crítica/Feedback',
              feedbackDesc: 'Compartilhe sua opinião sobre nossa plataforma',
              other: 'Outros',
              otherDesc: 'Dúvidas gerais ou outros assuntos'
            }
          },
          admin: {
            loading: 'Carregando painel administrativo...',
            title: 'Painel Administrativo',
            subtitle: 'Controle e insights do sistema',
            analysisPeriod: 'Período de Análise',
            totalRevenue: 'Receita Total',
            operationalCosts: 'Custos Operacionais',
            generationCostBreakdown: 'Breakdown de Custos por Geração',
            generations: 'gerações',
            last7DaysActivity: 'Atividade dos Últimos 7 Dias',
            purchases: 'compras',
            platformDistribution: 'Distribuição por Plataforma',
            successRate: 'Taxa de Sucesso',
            covers: 'Capas',
            posters: 'Posters',
            overallSuccessRate: 'Taxa Geral de Sucesso',
            top5UsersByRevenue: 'Top 5 Usuários por Receita',
            systemAlerts: 'Alertas do Sistema',
            searchUser: 'Buscar Usuário',
            searchUserPlaceholder: 'Digite o User ID ou parte dele...',
            search: 'Buscar',
            userProfile: 'Perfil do Usuário',
            userId: 'User ID',
            availableCredits: 'Créditos Disponíveis',
            totalGenerations: 'Total Gerações',
            totalPosters: 'Total Posters',
            viewGenerations: 'Ver Gerações',
            regenerateFailures: 'Regenerar Falhas',
            recentPurchases: 'Compras Recentes',
            credits: 'Créditos',
            value: 'Valor',
            status: 'Status',
            date: 'Data',
            failedGenerations: 'Gerações Falhadas',
            filters: 'Filtros',
            allStatuses: 'Todos os Status',
            completed: 'Completo',
            processing: 'Processando',
            failed: 'Falhado',
            allTypes: 'Todos os Tipos',
            cover: 'Capa',
            poster: 'Poster',
            allPlatforms: 'Todas as Plataformas',
            update: 'Atualizar',
            name: 'Nome',
            email: 'Email',
            type: 'Tipo',
            actions: 'Ações',
            userNameUnavailable: 'Nome não disponível',
            logFilters: 'Filtros de Logs',
            allLevels: 'Todos os Níveis',
            error: 'Erro',
            warning: 'Warning',
            info: 'Info',
            allServices: 'Todos os Serviços',
            coverGeneration: 'Geração de Capas',
            canvaPoster: 'Canva Poster',
            payments: 'Pagamentos',
            updateLogs: 'Atualizar Logs',
            timestamp: 'Timestamp',
            level: 'Nível',
            service: 'Serviço',
            message: 'Mensagem',
            noPreview: 'Sem preview',
            noTitle: 'Sem título'
          },
          canvaHistory: {
            title: 'Histórico de Pôsteres',
            update: 'Atualizar',
            loading: 'Carregando histórico...',
            noPosters: 'Nenhum pôster gerado ainda.',
            goToGenerate: 'Vá para a página de geração de pôsteres para criar seu primeiro pôster!',
            errorLoading: 'Erro ao carregar histórico',
            openInCanva: 'Abrir no Canva'
          },
          canvaPosterPage: {
            title: 'Gerador de Pôsteres Personalizados',
            subtitle: 'Crie seus próprios pôsteres de filme com inteligência artificial.'
          },
          newGenerationModal: {
            title: 'Nova Geração',
            subtitle: 'Como você quer começar?',
            fromScratch: 'Criar do Zero',
            fromScratchDesc1: '• Limpar todas as configurações atuais',
            fromScratchDesc2: '• Remover covers gerados anteriormente',
            fromScratchDesc3: '• Resetar o wizard para o primeiro passo',
            continueEditing: 'Continuar Editando',
            continueEditingDesc1: '• Manter foto, plataforma e configurações',
            continueEditingDesc2: '• Limpar apenas os covers gerados',
            continueEditingDesc3: '• Permitir editar configurações se desejar',
            noExistingDataDesc: 'Não há configurações salvas para reutilizar.',
            tip: 'Dica',
            tipDescription: 'A opção "Continuar Editando" ficará disponível após você fazer sua primeira geração.'
          },
          pricingModal: {
            buyCredits: 'Compre Créditos',
            credits: 'créditos',
            perCredit: 'por crédito',
            buyNow: 'Comprar {{credits}} Créditos',
            processing: 'Processando...',
            howCreditsWork: 'Como funcionam os créditos:',
            coverCost: '1 crédito = 1 capa',
            posterCost: '3 créditos = 1 poster',
            regenCosts: 'Regeneração custa créditos',
            hdDownload: 'Download em HD',
            netflixCost: 'Netflix (12 capas):',
            disneyCost: 'Disney+ (9 capas):',
            amazonCost: 'Amazon (11 capas):',
            creditsCount: '{{count}} créditos',
            mostPopular: 'Mais Popular',
            youHave: 'Você tem {{count}} créditos disponíveis',
            howCreditsWorkTitle: 'Como funcionam os créditos:',
            usageExamples: 'Exemplos de uso:',
            netflixCalc: '12 créditos + 3 para poster = 15 créditos',
            disneyCalc: '9 créditos + 3 para poster = 12 créditos',
            amazonCalc: '11 créditos + 3 para poster = 14 créditos',
            securePayment: '💳 Pagamento seguro processado pelo Stripe',
            ssl: '🔒 Seus dados estão protegidos com criptografia SSL',
            voucherSection: 'Tenho um código promocional',
            hideVoucher: 'Ocultar código promocional',
            redeemVoucher: 'Resgatar Código Promocional',
            needLogin: 'Você precisa estar logado para resgatar um código promocional',
            loginFirst: 'Fazer login primeiro',
            enterCode: 'Digite seu código',
            redeem: 'Resgatar',
            checking: 'Verificando...',
            codeHelp: 'Códigos contêm 6-12 caracteres (letras e números)',
            continueAuth: 'Continuar',
            willLoginNext: 'Digite seu código abaixo. Você fará login/cadastro na próxima etapa.'
          },
          progressBar: {
            generatingCovers: 'Gerando Capas...',
            generationComplete: 'Geração Concluída',
            processing: 'Processando',
            completed: 'Concluídas',
            inProgress: 'Em Progresso',
            failed: 'Falharam',
            complete: 'Completo'
          },
          fileUpload: {
            errorImageType: 'Por favor, selecione apenas arquivos de imagem',
            errorImageSize: 'Arquivo muito grande. Tamanho máximo: 10MB',
            successUpload: 'Imagem carregada com sucesso! 📸',
            removed: 'Imagem removida',
            selectedImage: 'Imagem Selecionada',
            uploadYourPhoto: 'Envie Sua Foto',
            dragAndDrop: 'Arraste e solte sua imagem aqui, ou clique para procurar',
            photoTip: 'Use fotos de frente para melhores resultados. Evite fotos de perfil ou à distância.',
            highQuality: 'Alta Qualidade',
            highQualityDesc: 'Melhores resultados com fotos nítidas',
            multipleFormats: 'Múltiplos Formatos',
            multipleFormatsDesc: 'Suporte para JPG, PNG, WEBP',
            upTo10MB: 'Até 10MB',
            upTo10MBDesc: 'Suporte para arquivos grandes',
            chooseFile: 'Escolher Arquivo',
            dropHere: 'Solte sua imagem aqui!'
          },
          baseImageSelector: {
            title: 'Selecionar Foto Base',
            subtitle: 'Escolha uma foto que você já enviou anteriormente',
            loading: 'Carregando suas fotos...',
            noImages: 'Nenhuma foto base encontrada',
            noImagesDesc: 'Suas fotos aparecerão aqui após você fazer upload pela primeira vez',
            tryAgain: 'Tentar Novamente'
          },
          hero: {
            badge: '✨ Assistente de Geração de Capas com IA',
            title: 'Crie Suas Capas de Filme',
            titleSpan: 'em Segundos',
            subtitle: 'Transforme suas fotos em capas de filmes deslumbrantes com IA. Escolha entre os estilos Netflix, Disney+ e Amazon Prime.',
            startCreating: 'Começar a Criar',
            watchDemo: 'Ver Demonstração',
            lightningFast: 'Rápido como um Raio',
            lightningFastDesc: 'Gere até 12 capas exclusivas em menos de 2 minutos',
            premiumQuality: 'Qualidade Premium',
            premiumQualityDesc: 'Qualidade de pôster de filme profissional com precisão de IA',
            multipleStyles: 'Múltiplos Estilos',
            multipleStylesDesc: 'Templates temáticos da Netflix, Disney+, Amazon Prime',
            coversCreated: 'Capas Criadas',
            platforms: 'Plataformas',
            satisfaction: 'Satisfação',
            avgTime: 'Tempo Médio'
          },
          authCallback: {
            success: 'Login realizado com sucesso!',
            error: 'Erro ao processar login. Tente novamente.',
            processing: 'Processando autenticação...'
          },
          authForm: {
            googleRedirect: 'Redirecionando para login com Google...',
            googleError: 'Erro ao fazer login com Google',
            loginSuccess: 'Login realizado com sucesso!',
            signupSuccess: 'Cadastro realizado! Verifique seu email.',
            authError: 'Erro na autenticação',
            access: 'Acessar',
            createAccount: 'Criar Conta',
            loginToContinue: 'Entre para continuar',
            joinToStart: 'Junte-se a nós para começar',
            continueWithGoogle: 'Continuar com Google',
            or: 'OU',
            fullName: 'Nome Completo',
            email: 'Email',
            password: 'Senha',
            loading: 'Carregando...',
            login: 'Entrar',
            dontHaveAccount: 'Não tem uma conta?',
            signUp: 'Cadastre-se',
            alreadyHaveAccount: 'Já tem uma conta?',
            doLogin: 'Faça login'
          },
          languageSelector: {
            title: 'Escolha o Idioma',
            subtitle: 'Selecione o idioma de sua preferência para as capas',
            english: 'Inglês',
            englishDesc: 'Gerar posters em estilo inglês',
            portuguese: 'Português',
            portugueseDesc: 'Gerar posters em estilo brasileiro'
          },
          photoTypeSelector: {
            title: 'Escolha o Tipo de Foto',
            subtitle: 'Selecione se sua foto tem uma pessoa ou um casal',
            individual: 'Individual',
            individualDesc: 'Foto de uma pessoa',
            couple: 'Casal',
            coupleDesc: 'Foto de duas pessoas'
          },
          quantitySelector: {
            title: 'Número de Capas',
            covers: 'Capas',
            quickGeneration: 'Geração rápida',
            moreVariety: 'Mais variedade',
            extendedCollection: 'Coleção estendida',
            completeCollection: 'Coleção completa',
            free: 'Grátis',
            premium: 'Premium'
          },
          templateSelector: {
            title: 'Escolha o Template do Poster',
            subtitle: 'Cada template tem um layout e número de fotos específico.',
            netflixDesc: '12 fotos (5:4) + capa (21:9) + avatar (1:1)',
            disneyDesc: '6 fotos (3:5) + 3 fotos (6:5) + capa (21:9) + avatar (1:1)',
            amazonDesc: '11 fotos (5:4) + capa (19:9) + avatar (1:1)',
            photos: 'FOTOS',
            aboutTemplates: 'Sobre os Templates',
            aboutTemplatesDesc: 'Cada template tem um layout específico otimizado para a plataforma escolhida. O número e formato das fotos variam.',
            netflixPhotos: 'Netflix: 12 fotos',
            disneyPhotos: 'Disney+: 9 fotos',
            amazonPhotos: 'Prime Video: 11 fotos'
          },
          titleGeneration: {
            title: 'Geração de Título',
            subtitle: 'Gerar títulos de filmes/séries nos pôsteres',
            importantNotice: 'Aviso Importante',
            noticeText: 'AI-generated text may contain spelling errors or incorrect letters. The titles are generated automatically and may not be perfectly accurate.'
          },
          creativityLevel: {
            title: 'Nível de Criatividade',
            subtitle: 'Escolha como a IA deve transformar sua foto nos filmes.',
            moreCreative: 'Mais Criativo',
            moreCreativeDesc: 'Total liberdade artística. A IA usa sua foto como inspiração, mas pode recriar completamente o visual, cabelo e pose.',
            moreCreativeExample: 'Ideal para transformações dramáticas',
            faithfulFace: 'Rosto Fiel',
            faithfulFaceDesc: 'Mantém seu rosto o mais fiel possível, mas permite criatividade no cabelo, expressão e pose.',
            faithfulFaceExample: 'Equilíbrio entre fidelidade e criatividade',
            strictPreservation: 'Preservação Estrita',
            strictPreservationDesc: 'Mantém exatamente suas feições, cabelo e pose originais. Apenas troca fundo e roupas.',
            strictPreservationExample: 'Máxima fidelidade à foto original',
            tip: 'Dica:',
            tipText: 'Criativo: Melhor para transformações dramáticas • Rosto Fiel: Ideal para a maioria dos casos • Estrito: Quando você quer manter exatamente sua aparência'
          },
          checkoutForm: {
            title: 'Finalizar Pagamento',
            credits: 'Créditos:',
            creditsCount: '{{count}} créditos',
            total: 'Total:',
            perCredit: 'por crédito',
            paymentInfo: 'Informações de Pagamento',
            processing: 'Processando...',
            pay: 'Pagar {{price}}',
            securePayment: '🔒 Pagamento seguro processado pelo Stripe'
          },
          plans: {
            starter_credits: 'Plano Inicial',
            popular_credits: 'Plano Popular',
            master_credits: 'Plano Mestre',
            starter_desc: 'Ideal para começar e testar',
            popular_desc: 'O melhor custo-benefício',
            master_desc: 'Para usuários intensivos',
            feature_economy: 'Economia',
            feature_priority: 'Suporte Prioritário'
          },
          packBalance: {
            title: 'Seus Packs',
            buyMore: 'Comprar Mais',
            available: 'Disponíveis',
            used: 'Utilizados',
            total: 'Total',
            purchasedPacks: 'Packs Adquiridos',
            purchasedOn: 'Comprado em {date}',
            packsAvailable: '{available}/{total} disponíveis',
            packsUsed: '{used} utilizados',
            recentUsage: 'Uso Recente',
            noPacks: 'Nenhum pack adquirido',
            buyFirstPack: 'Compre seu primeiro pack para começar a gerar capas personalizadas',
            buyFirstPackButton: 'Comprar Primeiro Pack',
            lowBalanceWarning: 'Você tem apenas {count} pack restante. | Você tem apenas {count} packs restantes.',
            considerBuying: 'Considere comprar mais packs para não ficar sem.'
          },
          packInfo: {
            title: 'Como funcionam os Packs',
            eachPackIncludes: 'Cada pack inclui:',
            includesPlatform: '• Capas para 1 plataforma (Netflix, Disney+ ou Amazon)',
            includesCovers: '• Até 12 imagens cover por pack',
            includesPoster: '• 1 poster final para impressão',
            coverRegen: 'Regeneração das covers:',
            coverRegenDesc: 'Ilimitada - você pode regenerar qualquer imagem cover quantas vezes quiser',
            finalPoster: 'Poster final:',
            finalPosterDesc: 'Pode ser gerado apenas 1 vez por pack',
            printablePoster: 'Poster para impressão:',
            printablePosterDesc: 'Inclui todas as suas covers organizadas em um layout profissional',
            tip: 'Dica:',
            tipText: 'Gere primeiro todas as covers que você gosta, depois crie o poster final com elas!'
          },
          generationLogs: {
            title: 'Histórico de Gerações',
            filters: 'Filtros:',
            allStatus: 'Todos os Status',
            completed: 'Concluídas',
            failed: 'Falharam',
            processing: 'Processando',
            allPlatforms: 'Todas as Plataformas',
            netflix: 'Netflix',
            disney: 'Disney+',
            amazon: 'Prime Video',
            fix: 'Corrigir',
            fixTooltip: 'Corrigir registros órfãos (status incorreto)',
            update: 'Atualizar',
            noGenerations: 'Nenhuma geração encontrada',
            duration: {
              inProgress: 'Em andamento...',
              seconds: 's',
              minutes: 'min'
            },
            status: {
              completed: 'Concluída',
              failed: 'Falhou',
              processing: 'Processando',
              unknown: 'Desconhecido'
            },
            platforms: {
              netflix: 'NETFLIX',
              disney: 'DISNEY+',
              amazon: 'PRIME VIDEO'
            },
            photoTypes: {
              individual: 'Individual',
              couple: 'Casal'
            },
            stats: {
              movie: 'filme',
              movies: 'filmes'
            },
            modal: {
              title: 'Detalhes da Geração',
              close: '✕',
              platform: 'Plataforma:',
              type: 'Tipo:',
              status: 'Status:',
              started: 'Iniciado:',
              finished: 'Finalizado:',
              duration: 'Duração:',
              processedMovies: 'Filmes Processados',
              badges: {
                safePrompt: 'Prompt Safe',
                fallback: 'Fallback:'
              },
              error: 'Erro:'
            },
            messages: {
              orphanFixed: 'registros órfãos',
              noOrphanFound: 'Nenhum registro órfão encontrado',
              errorFetching: 'Erro ao carregar histórico de gerações',
              errorFixing: 'Erro ao corrigir registros órfãos'
            }
          },
          wizard: {
            title: 'Crie Suas Capas de Filme',
            subtitle: 'Siga estes passos simples para gerar capas incríveis para suas fotos',
            progress: 'Progresso',
            previous: 'Anterior',
            next: 'Próximo',
            steps: {
              initialChoice: {
                title: 'Escolha Inicial',
                description: 'Começar ou carregar',
                howToStart: 'Como você quer começar?',
                withExisting: 'Você pode iniciar uma nova geração com uma nova foto ou carregar seu último trabalho para continuar de onde parou.',
                firstTime: 'Vamos começar criando sua primeira geração de capas personalizadas!',
                startFromScratch: 'Começar do Zero',
                loadGeneration: 'Carregar Geração',
                firstTimeNotice: 'Primeira vez?',
                firstTimeDesc: 'Depois de criar sua primeira geração, você poderá carregar trabalhos anteriores para continuar de onde parou.'
              },
              upload: {
                title: 'Upload da Foto',
                description: 'Escolha sua imagem'
              },
              photoType: {
                title: 'Tipo de Foto',
                description: 'Individual ou casal'
              },
              gender: {
                title: 'Gênero',
                description: 'Selecione para mais precisão'
              },
              platform: {
                title: 'Estilo da Plataforma',
                description: 'Netflix, Disney+, Amazon'
              },
              creativity: {
                title: 'Nível de Criatividade',
                description: 'Como transformar sua foto'
              },
              language: {
                title: 'Idioma',
                description: 'Escolha seu idioma'
              },
              titles: {
                title: 'Opções de Título',
                description: 'Configure títulos e nome',
                titleOptions: 'Opções de Título',
                configureTitle: 'Configure seu nome e preferências de título.',
                yourName: 'Seu Nome *',
                namePlaceholder: 'Digite seu nome para as capas',
                nameDescription: 'Este nome aparecerá em seus covers de filme personalizados.',
                titleGeneration: 'Geração de Título',
                titleGenerationDesc: 'Gerar títulos de filmes/séries nos pôsteres.',
                importantNotice: 'Aviso Importante',
                aiWarning: 'O texto gerado por IA pode conter erros ou letras incorretas. Os títulos são gerados automaticamente e podem não ser perfeitamente precisos.'
              },
              payment: {
                title: 'Comprar Créditos',
                description: 'Adquirir créditos necessários',
                buyCredits: 'Comprar Créditos',
                needCredits: 'Você precisa de {{count}} créditos para esta geração.',
                haveCredits: 'Você tem {{count}} créditos disponíveis.',
                securePayment: 'Pagamento seguro processado por Stripe.'
              },
              generate: {
                title: 'Gerar',
                description: 'Crie suas capas',
                readyToGenerate: 'Pronto para Gerar!',
                allReady: 'Está tudo pronto! Esta geração usará {{count}} créditos.',
                haveCredits: 'Você tem {{count}} créditos disponíveis.',
                generating: 'Gerando...',
                generateCovers: 'Gerar Covers de Filme',
                willGenerate: 'Isso irá gerar até {{count}} capas únicas no seu estilo selecionado.'
              }
            }
          },
          coverGrid: {
            posterSaved: '🎉 Poster salvo! Agora você pode baixá-lo diretamente e ele aparece em "Minhas Gerações".',
            posterSavedCanva: '🎉 Poster salvo em "Minhas Gerações" > "Posters"! Link do Canva disponível.',
            refreshTooltip: 'Clique se as capas não aparecem ou estão desatualizadas',
            printPosterTemplate: 'Poster para Impressão',
            downloadAll: 'Baixar todas as capas',
            chooseFromGallery: 'Escolher da galeria'
          },
          landing: {
            brand: 'PosterFlix',
            hero: {
              badge: '✨ Tecnologia Cinematográfica de Hollywood',
              title: 'Crie Suas Capas de Filme',
              title1: 'VIRE UM',
              title2: 'ASTRO DE',
              title3: 'HOLLYWOOD',
              titleSpan: 'em Segundos',
              subtitle: 'Desperte o ator que existe em você. Nossa IA transforma suas fotos comuns em pôsteres cinematográficos dignos de Hollywood. Não é só um pôster - é a sua entrada para o estrelato.',
              premium: 'Qualidade Premium • Não é um filtro barato',
              cta: 'CRIAR MEU PÔSTER',
              startCreating: 'Começar a Criar',
              demo: 'Ver Demo',
              watchDemo: 'Ver Demo',
              stat1: 'Pôsteres Criados',
              stat2: 'Estilos de Filme',
              stat3: 'Satisfação',
              lightningFast: 'Super Rápido',
              lightningFastDesc: 'Gere até 12 capas únicas em menos de 2 minutos',
              premiumQuality: 'Qualidade Premium',
              premiumQualityDesc: 'Qualidade profissional de poster de cinema com precisão de IA',
              multipleStyles: 'Múltiplos Estilos',
              multipleStylesDesc: 'Templates temáticos Netflix, Disney+, Amazon Prime',
              coversCreated: 'Capas Criadas',
              platforms: 'Plataformas',
              satisfaction: 'Satisfação',
              avgTime: 'Tempo Médio'
            },
            howItWorks: {
              title: 'COMO FUNCIONA',
              subtitle: 'Três passos simples para criar seu poster cinematográfico',
              step1: 'Upload sua foto',
              desc1: 'Escolha uma foto clara do seu rosto',
              step2: 'Escolha o estilo',
              desc2: 'Selecione o gênero e plataforma',
              step3: 'Baixe seu pôster',
              desc3: 'Receba seu pôster em alta qualidade',
              steps: {
                upload: {
                  title: 'Faça Upload',
                  description: 'Envie sua foto ou descreva sua ideia para o poster.'
                },
                style: {
                  title: 'Escolha o Estilo',
                  description: 'Selecione entre centenas de estilos cinematográficos disponíveis.'
                },
                download: {
                  title: 'Baixe seu Poster',
                  description: 'Receba seu poster profissional em alta resolução em segundos.'
                }
              }
            },
            platforms: {
              title: 'ESCOLHA SEU UNIVERSO CINEMATOGRÁFICO',
              subtitle: 'Cada plataforma tem sua identidade visual única. Nossa IA domina os códigos estéticos de cada uma para criar pôsteres autênticos.',
              cta: 'Qual universo cinematográfico combina com você?',
              ctaButton: 'DESCOBRIR MEU ESTILO',
              netflix: {
                title: 'NETFLIX',
                subtitle: 'Drama & Suspense',
                desc: 'Estética cinematográfica sombria e intensa. Tons dramáticos que capturam a essência dos grandes sucessos da plataforma.',
                feature1: '12 capas únicas',
                feature2: 'Estilo noir premium',
                feature3: 'Tipografia icônica',
                features: {
                  covers: '12 capas únicas',
                  style: 'Estilo noir premium',
                  typography: 'Tipografia icônica'
                }
              },
              disney: {
                title: 'DISNEY+',
                subtitle: 'Fantasia & Aventura',
                desc: 'Magia cinematográfica com cores vibrantes e composições épicas. O visual que desperta o herói que existe em você.',
                feature1: '9 capas mágicas',
                feature2: 'Estilo épico',
                feature3: 'Cores vibrantes',
                features: {
                  covers: '9 capas mágicas',
                  style: 'Estilo épico',
                  colors: 'Cores vibrantes'
                }
              },
              amazon: {
                title: 'PRIME VIDEO',
                subtitle: 'Ação & Thriller',
                desc: 'Visual cinematográfico premium com foco em ação e intensidade. A estética dos blockbusters modernos.',
                feature1: '11 capas dinâmicas',
                feature2: 'Estilo blockbuster',
                feature3: 'Composição premium',
                features: {
                  covers: '11 capas dinâmicas',
                  style: 'Estilo blockbuster',
                  composition: 'Composição premium'
                }
              }
            },
            features: {
              title: 'RECURSOS INCRÍVEIS',
              ai: 'IA Avançada',
              aiDesc: 'Tecnologia de ponta para resultados realistas',
              fast: 'Super Rápido',
              fastDesc: 'Gere seu pôster em menos de 2 minutos',
              quality: 'Alta Qualidade',
              qualityDesc: 'Imagens em 4K prontas para impressão',
              easy: 'Fácil de Usar',
              easyDesc: 'Interface simples e intuitiva',
              items: {
                aiAdvanced: {
                  title: 'IA Avançada',
                  description: 'Tecnologia de ponta para resultados realistas'
                },
                fastGeneration: {
                  title: 'Geração Rápida',
                  description: 'Crie posters profissionais em segundos'
                },
                premiumQuality: {
                  title: 'Qualidade Premium',
                  description: 'Saída em alta resolução pronta para impressão'
                },
                easyToUse: {
                  title: 'Fácil de Usar',
                  description: 'Interface intuitiva que qualquer pessoa pode dominar'
                },
                variousStyles: {
                  title: 'Estilos Variados',
                  description: 'Múltiplos templates para diferentes plataformas'
                },
                alwaysUpdated: {
                  title: 'Sempre Atualizado',
                  description: 'Novos estilos e recursos adicionados regularmente'
                }
              }
            },
            testimonials: {
              title: 'O QUE DIZEM OS USUÁRIOS',
              quote1: 'Nunca pensei que fosse tão fácil! Transformei uma foto comum numa capa de filme épica. Meus amigos adoraram!',
              role1: 'Criadora de Conteúdo',
              quote2: 'A qualidade é incrível. Parece um pôster de cinema real. Usei para um presente e foi um sucesso absoluto.',
              role2: 'Designer Gráfico',
              quote3: 'Isso é viciante! Já criei pôsteres para toda minha família. A função de escolher o estilo é minha favorita.',
              role3: 'Fã de Cinema',
              stat1: 'Usuários Felizes',
              stat2: 'Pôsteres Criados',
              stat3: 'Avaliação Média',
              stat4: 'Recomendariam',
              ana: {
                name: 'Ana Silva',
                role: 'Diretora de Arte',
                company: 'Estúdio Criativo',
                content: 'A qualidade dos posters gerados é impressionante. Consegui criar materiais profissionais em minutos que antes levariam horas.'
              },
              carlos: {
                name: 'Carlos Santos',
                role: 'Produtor',
                company: 'FilmeCorp',
                content: 'Revolucionou nosso processo criativo. A IA entende perfeitamente o estilo cinematográfico que buscamos.'
              },
              marina: {
                name: 'Marina Costa',
                role: 'Designer Gráfica',
                company: 'Visual Studio',
                content: 'Ferramenta indispensável para qualquer profissional da área. A variedade de estilos e a qualidade são excepcionais.'
              }
            },
            faq: {
              title: 'POR QUE CONFIAR EM NÓS?',
              subtitle: 'Transparência total. Sem pegadinhas. Sem letra miúda. Apenas resultados profissionais que você pode confiar.',
              secure: 'PRIVACIDADE ABSOLUTA',
              secureDesc: 'Suas fotos são processadas com criptografia militar e automaticamente deletadas em 24h. Zero armazenamento permanente.',
              secureBadge: 'SSL 256-bit',
              quality: 'QUALIDADE GARANTIDA',
              qualityDesc: 'Resolução 4K nativa, cores profissionais, composição cinematográfica. Se não ficar perfeito, refazemos grátis.',
              qualityBadge: '4K Ultra HD',
              rights: 'SEUS DIREITOS PROTEGIDOS',
              rightsDesc: 'Você possui 100% dos direitos comerciais das imagens geradas. Use como quiser: redes sociais, impressão, comercialização.',
              rightsBadge: '100% Seus',
              faqTitle: 'PERGUNTAS QUE VOCÊ PODE TER',
              q1: 'Minha foto fica realmente boa?',
              a1: 'Nossa IA foi treinada com milhares de pôsteres reais de Hollywood. O resultado é indistinguível de um pôster profissional.',
              q2: 'É seguro enviar minha foto?',
              a2: 'Totalmente. Usamos a mesma criptografia dos bancos e deletamos tudo em 24h. Nem nós conseguimos acessar suas fotos depois.',
              q3: 'Posso usar comercialmente?',
              a3: 'Sim! Você tem direitos totais. Venda, imprima, poste onde quiser. A imagem é 100% sua.',
              q4: 'E se eu não gostar?',
              a4: 'Refazemos quantas vezes precisar até ficar perfeito. Sua satisfação é garantida.',
              qualityFaq: {
                question: 'Minha foto fica realmente boa?',
                answer: 'Nossa IA foi treinada com milhares de pôsteres reais de Hollywood. O resultado é indistinguível de um pôster profissional.'
              },
              security: {
                question: 'É seguro enviar minha foto?',
                answer: 'Totalmente. Usamos a mesma criptografia dos bancos e deletamos tudo em 24h. Nem nós conseguimos acessar suas fotos depois.'
              },
              commercial: {
                question: 'Posso usar comercialmente?',
                answer: 'Sim! Você tem direitos totais. Venda, imprima, poste onde quiser. A imagem é 100% sua.'
              },
              refund: {
                question: 'E se eu não gostar?',
                answer: 'Refazemos quantas vezes precisar até ficar perfeito. Sua satisfação é garantida.'
              },
              socialProof: '+2.847 pessoas criaram seus pôsteres hoje',
              socialProofSub: 'Junte-se à comunidade de estrelas'
            },
            cta: {
              urgency: 'OFERTA LIMITADA • APENAS HOJE',
              title: 'SUA ESTREIA ACONTECE AGORA',
              subtitle: 'Não deixe seus sonhos cinematográficos esperando. Milhares já descobriram o protagonista que existe dentro deles.',
              scarcity1: '2.847 pôsteres',
              scarcity1Sub: 'criados hoje',
              scarcity2: '98% satisfação',
              scarcity2Sub: 'dos usuários',
              scarcity3: '< 2 minutos',
              scarcity3Sub: 'para criar',
              button: 'VIRAR ESTRELA AGORA',
              guarantee: 'Garantia de satisfação • Refazemos até ficar perfeito',
              finalProof: '"Transformei uma selfie comum no pôster mais épico da minha vida. Agora me sinto como um verdadeiro protagonista!" - Ana, Designer'
            },

            demo: {
              title: 'VEJA A MAGIA ACONTECER',
              subtitle: 'Teste nossa IA em tempo real! Veja como transformamos uma foto comum em capas de cinema profissionais.',
              originalPhoto: 'FOTO ORIGINAL',
              yourPhoto: 'SUA FOTO',
              aiGenerated: 'GERADO PELA IA',
              generating: 'Gerando...',
              tryNow: 'TESTAR AGORA',
              reset: 'RESETAR',
              info: 'Esta é uma demonstração. Clique em "Testar Agora" para ver como nossa IA transforma fotos em capas de cinema!',
              feature1: 'Rápido',
              feature1Desc: 'Menos de 2 minutos',
              feature2: 'Realista',
              feature2Desc: 'IA de última geração',
              feature3: 'Múltiplos Estilos',
              feature3Desc: '3 plataformas diferentes'
            },
            features2: {
              title: 'Recursos Avançados',
              subtitle: 'Tudo que você precisa para resultados profissionais',
              advanced: {
                title: 'IA Avançada',
                description: 'Algoritmos de última geração para resultados realistas'
              },
              fast: {
                title: 'Super Rápido',
                description: 'Gere posters profissionais em segundos'
              },
              quality: {
                title: 'Qualidade Premium',
                description: 'Saída em alta resolução pronta para impressão'
              },
              easy: {
                title: 'Fácil de Usar',
                description: 'Interface intuitiva que qualquer pessoa pode dominar'
              }
            },
            velocity: {
              text: 'TRANSFORMAR • CRIAR • INSPIRAR • GERAR • DESIGN • INOVAR • TRANSFORMAR • CRIAR • INSPIRAR • GERAR • DESIGN • INOVAR'
            },
            nav: {
              features: 'Recursos',
              howItWorks: 'Como Funciona',
              testimonials: 'Depoimentos',
              demo: 'Demo',
              login: 'Entrar',
              getStarted: 'Começar Grátis',
              dashboard: 'Dashboard'
            },
            stats: {
              posters: 'Posters Criados',
              users: 'Usuários Ativos',
              satisfaction: 'Satisfação',
              support: 'Suporte'
            },
            footer: {
              description: 'Transforme suas ideias em posters cinematográficos profissionais em segundos com nossa IA avançada.',
              product: 'Produto',
              navigation: 'Navegação',
              about: 'Sobre',
              aboutLine1: 'Geração de posters com IA',
              aboutLine2: 'Resultados de qualidade profissional',
              aboutLine3: 'Confiado por criadores no mundo todo',
              copyright: '© 2024 AI Streaming Poster. Todos os direitos reservados.'
            }
          }
        }
      }
    }
  });

export default i18n;