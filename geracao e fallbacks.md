Análise Completa: Como Funciona a Geração de Covers
Baseado na análise do código, aqui está a ordem exata e os fallbacks quando você clica em "Gerar Covers de Filme":

🎯 SELEÇÃO DO PROMPT (Ordem de Prioridade)
Se photoType === 'cover' (Capa Principal/Banner):

Usa base_prompt + instruções específicas de posicionamento
Adiciona: "Position the person on the right side... Place the title in bottom right corner"
Se photoType === 'casal' e existe couple_prompt:

Usa couple_prompt específico do filme
Se photoType === 'individual' e gender === 'male' e existe gender_male_prompt:

Usa gender_male_prompt específico do filme
Se photoType === 'individual' e gender === 'female' e existe gender_female_prompt:

Usa gender_female_prompt específico do filme
Fallback Final:

Usa base_prompt se nenhuma das condições acima for atendida
🔧 CONSTRUÇÃO DO PROMPT FINAL
A ordem de concatenação é:

[Nível de Criatividade] + [Instrução de Título] + [Prompt Base Selecionado]
Onde:

Nível de Criatividade: Vem da tabela creativity_levels (criativo/rostoFiel/estrito)
Instrução de Título: Se generateTitles = true, adiciona instruções para colocar o título na imagem
Prompt Base: O prompt selecionado conforme a lógica acima
🔄 SISTEMA DE FALLBACK (4 Tentativas)
Se a primeira tentativa falhar, o sistema executa até 4 tentativas:

1ª TENTATIVA - Prompt Principal
Usa a lógica completa descrita acima
Inclui: Criatividade + Título + Prompt Específico
2ª TENTATIVA - Sem Instrução de Título
Mesmo prompt, mas SEM a instrução de título
Mantém: Criatividade + Prompt Específico
3ª TENTATIVA - Apenas o Título
Usa apenas o nome do filme/série
Se generateTitles = true: adiciona instrução de posicionamento do título
SEM criatividade (skipCreativity = true)
4ª TENTATIVA - Safe Prompt
Usa safe_prompt se existir e for diferente do base_prompt
Inclui instrução de título se generateTitles = true
Mantém o nível de criatividade
🎨 Ajustes de Gênero Específicos
Para prompts individuais, o sistema também aplica ajustes específicos de gênero do arquivo movieDatabase.ts:

Masculino: Substitui instruções de roupa por versões masculinas específicas do filme
Feminino: Substitui instruções de roupa por versões femininas específicas do filme
Fallback: Se não há ajuste específico, mantém o prompt original
📊 Exemplo Prático
Para um filme como "Andor" com:

photoType: 'individual'
gender: 'male'
generateTitles: true
creativityLevel: 'rostoFiel'
1ª Tentativa:

[Prompt de Criatividade Rosto Fiel] + [Instrução do Título "Andor"] + [gender_male_prompt específico do Andor]
Se falhar, 2ª Tentativa:

[Prompt de Criatividade Rosto Fiel] + [gender_male_prompt específico do Andor]
Se falhar, 3ª Tentativa:

"Andor" (apenas o título)
Se falhar, 4ª Tentativa:

[Instrução do Título "Andor"] + [safe_prompt do Andor]
Essa é a ordem exata e os fallbacks que o sistema usa quando você clica em "Gerar Covers de Filme"! 🎬