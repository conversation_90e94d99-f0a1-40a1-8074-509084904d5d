const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Obter variáveis de ambiente
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Erro: Variáveis de ambiente SUPABASE_URL e SUPABASE_SERVICE_ROLE_KEY (ou SUPABASE_ANON_KEY) são necessárias.');
  process.exit(1);
}

// Criar cliente Supabase
const supabase = createClient(supabaseUrl, supabaseKey);

async function main() {
  console.log('🚀 Inicializando ambiente para Canva Poster Generator...');

  try {
    // 1. Verificar se o bucket existe e criá-lo se necessário
    console.log('✓ Verificando bucket de armazenamento...');
    const { data: buckets, error: bucketsError } = await supabase.storage.listBuckets();
    
    if (bucketsError) {
      throw new Error(`Erro ao listar buckets: ${bucketsError.message}`);
    }
    
    const bucketExists = buckets.some(bucket => bucket.name === 'poster-assets');
    
    if (!bucketExists) {
      console.log('✓ Criando bucket poster-assets...');
      const { error: createBucketError } = await supabase.storage.createBucket('poster-assets', {
        public: true,
        fileSizeLimit: 5242880 // 5MB
      });
      
      if (createBucketError) {
        throw new Error(`Erro ao criar bucket: ${createBucketError.message}`);
      }
      console.log('✓ Bucket poster-assets criado com sucesso!');
    } else {
      console.log('✓ Bucket poster-assets já existe.');
    }

    // 2. Chamando a função apply-migrations para criar tabelas e políticas
    console.log('✓ Aplicando migrações SQL...');
    const { data: migrationData, error: migrationError } = await supabase.functions.invoke('apply-migrations');
    
    if (migrationError) {
      throw new Error(`Erro ao aplicar migrações: ${migrationError.message}`);
    }
    
    console.log('✓ Migrações SQL aplicadas com sucesso!');
    
    // 3. Verificar existência da tabela canva_generations
    console.log('✓ Verificando tabela canva_generations...');
    const { data: tablesData, error: tablesError } = await supabase
      .from('canva_generations')
      .select('id')
      .limit(1);
      
    if (tablesError && !tablesError.message.includes('does not exist')) {
      throw new Error(`Erro ao verificar tabela: ${tablesError.message}`);
    }
    
    console.log('✓ Ambiente inicializado com sucesso!');
    console.log('\n🎉 Todas as configurações foram concluídas. O gerador de pôsteres Canva está pronto para uso!');
    
  } catch (error) {
    console.error('❌ Erro durante inicialização:', error.message);
    process.exit(1);
  }
}

main();
