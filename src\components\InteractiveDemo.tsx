import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { <PERSON>, Spark<PERSON>, Zap, RotateCcw } from 'lucide-react';

interface InteractiveDemoProps {
  isDark: boolean;
}

const InteractiveDemo: React.FC<InteractiveDemoProps> = ({ isDark }) => {
  const { t } = useTranslation();
  const [isGenerating, setIsGenerating] = useState(false);
  const [hasGenerated, setHasGenerated] = useState(false);
  const [currentCover, setCurrentCover] = useState(0);
  const [progress, setProgress] = useState(0);
  const [currentStep, setCurrentStep] = useState('');
  const [selectedPlatform, setSelectedPlatform] = useState<'netflix' | 'disney' | 'amazon'>('netflix');

  const originalPhoto = '/casal-demo.jpg';
  
  const generatedCovers = {
    netflix: [
      '/generations/netflix/netflix-1.jpg',
      '/generations/netflix/netflix-2.jpg',
      '/generations/netflix/netflix-3.jpg',
      '/generations/netflix/netflix-4.jpg',
      '/generations/netflix/netflix-5.jpg',
      '/generations/netflix/netflix-6.jpg',
    ],
    disney: [
      '/generations/disney/disney-1.jpg',
      '/generations/disney/disney-2.jpg',
      '/generations/disney/disney-3.jpg',
      '/generations/disney/disney-4.jpg',
      '/generations/disney/disney-5.jpg',
      '/generations/disney/disney-6.jpg',
      '/generations/disney/disney-7.jpg',
    ],
    amazon: [
      '/generations/amazon/amazon-1.jpg',
      '/generations/amazon/amazon-2.jpg',
      '/generations/amazon/amazon-3.jpg',
      '/generations/amazon/amazon-4.jpg',
      '/generations/amazon/amazon-5.jpg',
      '/generations/amazon/amazon-6.jpg',
      '/generations/amazon/amazon-7.jpg',
    ]
  };

  const platforms = [
    { id: 'netflix' as const, name: 'Netflix', color: 'bg-red-600' },
    { id: 'disney' as const, name: 'Disney+', color: 'bg-blue-600' },
    { id: 'amazon' as const, name: 'Prime Video', color: 'bg-cyan-600' }
  ];

  useEffect(() => {
    setHasGenerated(false);
    setProgress(0);
    setCurrentStep('');
    const randomIndex = Math.floor(Math.random() * generatedCovers[selectedPlatform].length);
    setCurrentCover(randomIndex);
  }, [selectedPlatform]);

  const startGeneration = () => {
    if (isGenerating) return;
    
    setIsGenerating(true);
    setHasGenerated(false);
    setProgress(0);
    setCurrentStep('Iniciando processamento...');
    
    const steps = [
      { text: 'Analisando sua foto...', duration: 3000 },
      { text: 'Detectando características faciais...', duration: 4000 },
      { text: 'Gerando contexto cinematográfico...', duration: 5000 },
      { text: 'Aplicando estilo da plataforma...', duration: 6000 },
      { text: 'Criando composição profissional...', duration: 5000 },
      { text: 'Ajustando iluminação e cores...', duration: 4000 },
      { text: 'Finalizando sua capa de cinema...', duration: 3000 }
    ];
    
    let currentStepIndex = 0;
    let totalElapsed = 0;
    const totalDuration = 30000;
    
    const runStep = () => {
      if (currentStepIndex < steps.length) {
        const step = steps[currentStepIndex];
        setCurrentStep(step.text);
        
        const stepInterval = setInterval(() => {
          totalElapsed += 100;
          const newProgress = Math.min((totalElapsed / totalDuration) * 100, 100);
          setProgress(newProgress);
        }, 100);
        
        setTimeout(() => {
          clearInterval(stepInterval);
          currentStepIndex++;
          if (currentStepIndex < steps.length) {
            runStep();
          } else {
            setProgress(100);
            setCurrentStep('Capa criada com sucesso!');
            setTimeout(() => {
              setIsGenerating(false);
              setHasGenerated(true);
              const randomIndex = Math.floor(Math.random() * generatedCovers[selectedPlatform].length);
              setCurrentCover(randomIndex);
            }, 1000);
          }
        }, step.duration);
      }
    };
    
    runStep();
  };

  const resetDemo = () => {
    setProgress(0);
    setIsGenerating(false);
    setHasGenerated(false);
    setCurrentStep('');
    const randomIndex = Math.floor(Math.random() * generatedCovers[selectedPlatform].length);
    setCurrentCover(randomIndex);
  };

  return (
    <div className="relative">
      {/* Platform Selection - BRUTAL STYLE */}
      <div className="flex justify-center mb-12">
        <div className={`inline-flex border-8 p-2 ${
          isDark ? 'border-white bg-black' : 'border-black bg-white'
        }`}>
          {platforms.map((platform) => (
            <button
              key={platform.id}
              onClick={() => setSelectedPlatform(platform.id)}
              className={`px-6 py-3 font-black transition-all border-4 ${
                selectedPlatform === platform.id
                  ? `text-white border-black ${platform.color}`
                  : isDark
                  ? 'bg-transparent text-white border-transparent hover:bg-gray-800'
                  : 'bg-transparent text-black border-transparent hover:bg-gray-100'
              }`}
            >
              {platform.name}
            </button>
          ))}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
        
        {/* Left Side - Original Photo - BRUTAL STYLE */}
        <div className="text-center">
          <h3 className={`text-2xl font-black mb-6 ${
            isDark ? 'text-white' : 'text-black'
          }`}>
            {t('landing.demo.originalPhoto', 'FOTO ORIGINAL')}
          </h3>
          
          <div className={`relative mx-auto w-80 h-96 border-8 overflow-hidden ${
            isDark ? 'border-white' : 'border-black'
          }`}>
            <img
              src={originalPhoto}
              alt="Foto original"
              className="w-full h-full object-cover"
            />
            
            {/* Badge - BRUTAL STYLE */}
            <div className={`absolute top-4 left-4 px-3 py-1 border-2 text-sm font-black ${
              isDark ? 'border-white bg-black text-white' : 'border-black bg-white text-black'
            }`}>
              {t('landing.demo.yourPhoto', 'SUA FOTO')}
            </div>

            {/* Corner accent - BRUTAL */}
            <div className={`absolute -bottom-3 -right-3 w-12 h-12 bg-green-500 border-4 ${
              isDark ? 'border-white' : 'border-black'
            } transform rotate-45`} />
          </div>
        </div>

        {/* Right Side - Generated Covers - BRUTAL STYLE */}
        <div className="text-center">
          <h3 className={`text-2xl font-black mb-6 ${
            isDark ? 'text-white' : 'text-black'
          }`}>
            {t('landing.demo.aiGenerated', 'GERADO PELA IA')}
          </h3>
          
          {/* Generated Cover Display - BRUTAL */}
          <div className="relative mx-auto w-80 h-96 mb-8">
            <div className={`relative w-full h-full border-8 overflow-hidden ${
              isDark ? 'border-white' : 'border-black'
            } ${isGenerating ? 'animate-pulse' : ''}`}>
              
              {isGenerating ? (
                // Loading State - BRUTAL
                <div className={`w-full h-full flex items-center justify-center ${
                  isDark ? 'bg-gray-800' : 'bg-gray-200'
                }`}>
                  <div className="text-center p-6">
                    <Sparkles className={`w-16 h-16 mx-auto mb-6 animate-spin ${
                      isDark ? 'text-yellow-400' : 'text-green-500'
                    }`} />
                    
                    <p className={`text-lg font-black mb-4 ${
                      isDark ? 'text-white' : 'text-black'
                    }`}>
                      {currentStep}
                    </p>
                    
                    {/* Progress Bar - BRUTAL */}
                    <div className={`w-64 h-4 mb-4 border-2 ${
                      isDark ? 'bg-gray-700 border-gray-600' : 'bg-gray-300 border-gray-400'
                    }`}>
                      <div 
                        className={`h-full transition-all duration-500 ${
                          isDark ? 'bg-yellow-400' : 'bg-green-500'
                        }`}
                        style={{ width: `${progress}%` }}
                      />
                    </div>
                    
                    <p className={`text-xl font-black opacity-70 ${
                      isDark ? 'text-white' : 'text-black'
                    }`}>{Math.round(progress)}%</p>
                    
                    <p className={`text-sm mt-2 opacity-50 ${
                      isDark ? 'text-white' : 'text-black'
                    }`}>
                      Tempo estimado: {Math.max(0, Math.round(30 - (progress * 30 / 100)))}s
                    </p>
                  </div>
                </div>
              ) : hasGenerated ? (
                // Generated Cover
                <img
                  src={generatedCovers[selectedPlatform][currentCover]}
                  alt="Capa gerada"
                  className="w-full h-full object-cover"
                />
              ) : (
                // Initial State - BRUTAL
                <div className={`w-full h-full flex items-center justify-center ${
                  isDark ? 'bg-gray-800' : 'bg-gray-200'
                }`}>
                  <div className={`text-center ${
                    isDark ? 'text-gray-500' : 'text-gray-500'
                  }`}>
                    <Sparkles className="w-16 h-16 mx-auto mb-4 opacity-30" />
                    <h3 className="text-xl font-black mb-2">Sua capa aparecerá aqui</h3>
                    <p className="text-sm opacity-70">Clique em "Testar Agora" para gerar</p>
                  </div>
                </div>
              )}
              
              {/* Platform badge - BRUTAL */}
              {!isGenerating && hasGenerated && (
                <div className={`absolute top-4 right-4 px-3 py-1 border-2 text-sm font-black text-white border-black ${
                  platforms.find(p => p.id === selectedPlatform)?.color
                }`}>
                  {platforms.find(p => p.id === selectedPlatform)?.name}
                </div>
              )}
            </div>

            {/* Corner accent - BRUTAL */}
            {hasGenerated && (
              <div className={`absolute -bottom-3 -right-3 w-12 h-12 ${
                platforms.find(p => p.id === selectedPlatform)?.color
              } border-4 ${
                isDark ? 'border-white' : 'border-black'
              } transform rotate-45`} />
            )}
          </div>

          {/* Controls - BRUTAL */}
          <div className="flex justify-center space-x-4">
            <button
              onClick={startGeneration}
              disabled={isGenerating}
              className={`flex items-center space-x-2 px-6 py-3 border-8 font-black transition-all ${
                isDark
                  ? 'border-white bg-green-500 text-white hover:opacity-80'
                  : 'border-black bg-green-500 text-white hover:opacity-80'
              } disabled:opacity-50 disabled:cursor-not-allowed`}
            >
              {isGenerating ? (
                <>
                  <Zap className="w-5 h-5 animate-pulse" />
                  <span>{t('landing.demo.generating', 'Gerando...')}</span>
                </>
              ) : (
                <>
                  <Play className="w-5 h-5" />
                  <span>{t('landing.demo.tryNow', 'TESTAR AGORA')}</span>
                </>
              )}
            </button>

            <button
              onClick={resetDemo}
              className={`flex items-center space-x-2 px-6 py-3 border-8 font-black transition-all ${
                isDark
                  ? 'border-white bg-gray-900 hover:bg-gray-800 text-white'
                  : 'border-black bg-white hover:bg-gray-100 text-black'
              }`}
            >
              <RotateCcw className="w-5 h-5" />
              <span>{t('landing.demo.reset', 'RESETAR')}</span>
            </button>
          </div>

          {/* Info */}
          <p className={`text-sm mt-6 opacity-70 max-w-sm mx-auto font-bold ${
            isDark ? 'text-white' : 'text-black'
          }`}>
            {t('landing.demo.info', 'Esta é uma demonstração. Clique em "Testar Agora" para ver como nossa IA transforma fotos em capas de cinema!')}
          </p>
        </div>
      </div>

      {/* Features Row - BRUTAL STYLE */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-16">
        {[
          {
            icon: <Zap className="w-6 h-6" />,
            title: t('landing.demo.feature1', 'Rápido'),
            desc: t('landing.demo.feature1Desc', 'Menos de 2 minutos'),
            color: 'bg-green-500'
          },
          {
            icon: <Sparkles className="w-6 h-6" />,
            title: t('landing.demo.feature2', 'Realista'),
            desc: t('landing.demo.feature2Desc', 'IA de última geração'),
            color: 'bg-yellow-500'
          },
          {
            icon: <Play className="w-6 h-6" />,
            title: t('landing.demo.feature3', 'Múltiplos Estilos'),
            desc: t('landing.demo.feature3Desc', '3 plataformas diferentes'),
            color: 'bg-purple-500'
          }
        ].map((feature, index) => (
          <div key={index} className="text-center">
            <div className={`w-12 h-12 border-6 ${
              isDark ? 'border-white' : 'border-black'
            } ${feature.color} flex items-center justify-center text-white mx-auto mb-3 transform rotate-12`}>
              {feature.icon}
            </div>
            <h4 className={`font-black text-lg mb-1 ${
              isDark ? 'text-white' : 'text-black'
            }`}>{feature.title}</h4>
            <p className={`text-sm opacity-70 ${
              isDark ? 'text-white' : 'text-black'
            }`}>{feature.desc}</p>
          </div>
        ))}
      </div>
    </div>
  );
};

export default InteractiveDemo; 