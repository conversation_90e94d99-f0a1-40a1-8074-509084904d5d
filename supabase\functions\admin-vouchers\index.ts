import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from "https://esm.sh/@supabase/supabase-js@2"

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE',
}

const supabaseUrl = Deno.env.get('SUPABASE_URL')!
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // 1. AUTHENTICATION & ADMIN CHECK
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      return new Response(JSON.stringify({ error: 'Falta o cabeçalho de autenticação' }), { status: 401, headers: corsHeaders })
    }
    
    const supabase = createClient(supabaseUrl, supabaseServiceKey)
    
    const { data: { user } } = await supabase.auth.getUser(authHeader.replace('Bearer ', ''));
    if (!user) {
      return new Response(JSON.stringify({ error: 'Usuário não autenticado' }), { status: 401, headers: corsHeaders });
    }

    // Verificar se é admin - buscar o usuário no banco para ter certeza dos metadados
    const { data: userData, error: adminError } = await supabase.auth.admin.getUserById(user.id);
    
    if (adminError || !userData.user || userData.user.user_metadata?.role !== 'admin') {
      return new Response(JSON.stringify({ error: 'Acesso não autorizado: requer privilégios de administrador.' }), { status: 403, headers: corsHeaders });
    }
    
    // Se o usuário for admin, continue com a lógica
    const url = new URL(req.url)
    const action = url.searchParams.get('action')

    switch (req.method) {
      case 'GET':
        if (action === 'list') {
          return await listVouchers(supabase)
        } else if (action === 'stats') {
          return await getVoucherStats(supabase)
        }
        break

      case 'POST':
        if (action === 'create') {
          const body = await req.json()
          return await createVoucher(supabase, body)
        }
        break

      case 'PUT':
        if (action === 'update') {
          const body = await req.json()
          return await updateVoucher(supabase, body)
        }
        break

      case 'DELETE':
        if (action === 'delete') {
          const body = await req.json()
          return await deleteVoucher(supabase, body)
        }
        break
    }

    return new Response(
      JSON.stringify({ error: 'Invalid action or method' }),
      { 
        status: 400, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )

  } catch (error) {
    console.error('Admin vouchers error:', error)
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})

async function listVouchers(supabase: any) {
  const { data: vouchers, error } = await supabase
    .from('vouchers')
    .select(`
      *,
      voucher_redemptions(
        id,
        user_id,
        user_email,
        credits_redeemed,
        redeemed_at
      )
    `)
    .order('created_at', { ascending: false })

  if (error) {
    console.error('Error listing vouchers:', error)
    return new Response(
      JSON.stringify({ error: 'Failed to list vouchers' }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }

  return new Response(
    JSON.stringify({ vouchers }),
    { 
      status: 200, 
      headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
    }
  )
}

async function getVoucherStats(supabase: any) {
  // Get total vouchers by status
  const { data: statusStats, error: statusError } = await supabase
    .from('vouchers')
    .select('status')

  if (statusError) {
    console.error('Error getting voucher status stats:', statusError)
    return new Response(
      JSON.stringify({ error: 'Failed to get voucher stats' }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }

  // Get redemption stats
  const { data: redemptionStats, error: redemptionError } = await supabase
    .from('voucher_redemptions')
    .select(`
      credits_redeemed,
      redeemed_at,
      vouchers!inner(credits)
    `)

  if (redemptionError) {
    console.error('Error getting redemption stats:', redemptionError)
    return new Response(
      JSON.stringify({ error: 'Failed to get redemption stats' }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }

  // Process stats
  const stats = {
    totalVouchers: statusStats.length,
    activeVouchers: statusStats.filter((v: any) => v.status === 'active').length,
    expiredVouchers: statusStats.filter((v: any) => v.status === 'expired').length,
    disabledVouchers: statusStats.filter((v: any) => v.status === 'disabled').length,
    totalRedemptions: redemptionStats.length,
    totalCreditsRedeemed: redemptionStats.reduce((sum: number, r: any) => sum + r.credits_redeemed, 0),
    recentRedemptions: redemptionStats
      .sort((a: any, b: any) => new Date(b.redeemed_at).getTime() - new Date(a.redeemed_at).getTime())
      .slice(0, 10)
  }

  return new Response(
    JSON.stringify({ stats }),
    { 
      status: 200, 
      headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
    }
  )
}

async function createVoucher(supabase: any, body: any) {
  console.log('🔨 createVoucher called with body:', JSON.stringify(body, null, 2))
  
  const { 
    credits, 
    maxUses = 1, 
    emailRestriction, 
    expiresAt, 
    description,
    customCode 
  } = body

  console.log('📊 Extracted values:', { credits, maxUses, emailRestriction, expiresAt, description, customCode })

  if (!credits || credits <= 0) {
    console.log('❌ Invalid credits:', credits)
    return new Response(
      JSON.stringify({ error: 'Credits must be greater than 0' }),
      { 
        status: 400, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }

  // Validate and clean data
  const cleanEmailRestriction = emailRestriction && emailRestriction.trim() !== '' ? emailRestriction.trim() : null
  const cleanExpiresAt = expiresAt && expiresAt.trim() !== '' ? expiresAt.trim() : null
  const cleanDescription = description && description.trim() !== '' ? description.trim() : null
  const cleanCustomCode = customCode && customCode.trim() !== '' ? customCode.trim().toUpperCase() : null

  console.log('🧹 Cleaned values:', { cleanEmailRestriction, cleanExpiresAt, cleanDescription, cleanCustomCode })

  let voucherCode = cleanCustomCode
  
  // Generate code if not provided
  if (!voucherCode) {
    console.log('🎲 Generating voucher code...')
    const { data: generatedCode, error: codeError } = await supabase
      .rpc('generate_voucher_code', { length: 8 })

    console.log('🎯 Code generation result:', { generatedCode, codeError })

    if (codeError || !generatedCode) {
      console.error('❌ Error generating voucher code:', codeError)
      return new Response(
        JSON.stringify({ error: 'Failed to generate voucher code' }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }
    
    voucherCode = generatedCode
    console.log('✅ Generated code:', voucherCode)
  }

  // Insert voucher
  const voucherData = {
    code: voucherCode.toUpperCase(),
    credits,
    max_uses: maxUses,
    email_restriction: cleanEmailRestriction,
    expires_at: cleanExpiresAt,
    description: cleanDescription,
    status: 'active'
  }
  
  console.log('💾 Inserting voucher with data:', voucherData)
  
  const { data: voucher, error } = await supabase
    .from('vouchers')
    .insert(voucherData)
    .select()
    .single()

  console.log('🔍 Insert result:', { voucher, error })

  if (error) {
    console.error('❌ Error creating voucher:', error)
    return new Response(
      JSON.stringify({ 
        error: error.code === '23505' ? 'Voucher code already exists' : 'Failed to create voucher' 
      }),
      { 
        status: error.code === '23505' ? 400 : 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }

  console.log('✅ Voucher created successfully:', voucher)
  
  return new Response(
    JSON.stringify({ voucher }),
    { 
      status: 201, 
      headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
    }
  )
}

async function updateVoucher(supabase: any, body: any) {
  const { id, status, description } = body

  if (!id) {
    return new Response(
      JSON.stringify({ error: 'Voucher ID is required' }),
      { 
        status: 400, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }

  const updates: any = {}
  if (status !== undefined) updates.status = status
  if (description !== undefined) updates.description = description

  const { data: voucher, error } = await supabase
    .from('vouchers')
    .update(updates)
    .eq('id', id)
    .select()
    .single()

  if (error) {
    console.error('Error updating voucher:', error)
    return new Response(
      JSON.stringify({ error: 'Failed to update voucher' }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }

  return new Response(
    JSON.stringify({ voucher }),
    { 
      status: 200, 
      headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
    }
  )
}

async function deleteVoucher(supabase: any, body: any) {
  const { id } = body

  if (!id) {
    return new Response(
      JSON.stringify({ error: 'Voucher ID is required' }),
      { 
        status: 400, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }

  // Check if voucher has been used
  const { data: redemptions, error: redemptionError } = await supabase
    .from('voucher_redemptions')
    .select('id')
    .eq('voucher_id', id)
    .limit(1)

  if (redemptionError) {
    console.error('Error checking voucher redemptions:', redemptionError)
    return new Response(
      JSON.stringify({ error: 'Failed to check voucher usage' }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }

  if (redemptions && redemptions.length > 0) {
    return new Response(
      JSON.stringify({ error: 'Cannot delete voucher that has been used' }),
      { 
        status: 400, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }

  const { error } = await supabase
    .from('vouchers')
    .delete()
    .eq('id', id)

  if (error) {
    console.error('Error deleting voucher:', error)
    return new Response(
      JSON.stringify({ error: 'Failed to delete voucher' }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }

  return new Response(
    JSON.stringify({ success: true }),
    { 
      status: 200, 
      headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
    }
  )
} 