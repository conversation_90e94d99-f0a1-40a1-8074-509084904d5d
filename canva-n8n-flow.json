{"name": "Canva Integration Flow", "nodes": [{"parameters": {"url": "https://api.canva.com/rest/v1/users/me", "authentication": "genericCredentialType", "genericAuthType": "oAuth2Api", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1180, 220], "id": "c5098851-d7d1-43f2-9d9e-5aab8db5f344", "name": "Get Current User", "credentials": {"oAuth2Api": {"id": "mmVJdXwXUvl6N0wP", "name": "Canva Connection Kotas Version"}}}, {"parameters": {"url": "https://api.canva.com/rest/v1/designs", "authentication": "genericCredentialType", "genericAuthType": "oAuth2Api", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1180, 480], "id": "990fc7bb-6bdf-423b-9f8e-e4c9a3ee2e81", "name": "List Designs", "credentials": {"oAuth2Api": {"id": "mmVJdXwXUvl6N0wP", "name": "Canva Connection Kotas Version"}}}, {"parameters": {"url": "https://api.canva.com/rest/v1/assets", "authentication": "genericCredentialType", "genericAuthType": "oAuth2Api", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1180, 740], "id": "981d7d4e-fa2b-433b-9996-0cd2b6a7a757", "name": "List Assets", "credentials": {"oAuth2Api": {"id": "mmVJdXwXUvl6N0wP", "name": "Canva Connection Kotas Version"}}}, {"parameters": {"url": "https://api.canva.com/rest/v1/folders", "authentication": "genericCredentialType", "genericAuthType": "oAuth2Api", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1180, 1000], "id": "ee84f5fb-ecac-4983-94e1-20e1010a53de", "name": "List Folders", "credentials": {"oAuth2Api": {"id": "mmVJdXwXUvl6N0wP", "name": "Canva Connection Kotas Version"}}}, {"parameters": {"url": "https://api.canva.com/rest/v1/comments", "authentication": "genericCredentialType", "genericAuthType": "oAuth2Api", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1180, 1260], "id": "43819631-9db8-4144-8bdd-73e8d32d55ed", "name": "List Comments", "credentials": {"oAuth2Api": {"id": "mmVJdXwXUvl6N0wP", "name": "Canva Connection Kotas Version"}}}, {"parameters": {"url": "https://api.canva.com/rest/v1/brand-templates", "authentication": "genericCredentialType", "genericAuthType": "oAuth2Api", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1180, 1520], "id": "8331d718-d43d-4d1f-9ed5-cb64de307dfb", "name": "List Brand Templates", "credentials": {"oAuth2Api": {"id": "mmVJdXwXUvl6N0wP", "name": "Canva Connection Kotas Version"}}}, {"parameters": {"functionCode": "const imgFields = [\n  \"CAPA\", \"AVATAR\",\n  ...Array(12).fill().map((_,i)=>`PHOTO_${(i+1).toString().padStart(2,'0')}`)\n];\n\nconst body = items[0].json.body; // <--- PEGA DADOS DENTRO DE .body\n\nconst imgs = imgFields.map(field => {\n  if (body && body[field]) {\n    return { field, url: body[field] };\n  }\n}).filter(Boolean);\n\nreturn imgs.map(img => ({ json: img }));\n"}, "name": "Preparar Lista de Upload", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [1180, -40], "id": "7be8d7d1-a808-494b-a6e2-369a071d6f5a"}, {"parameters": {"method": "POST", "url": "https://api.canva.com/rest/v1/asset-uploads", "authentication": "genericCredentialType", "genericAuthType": "oAuth2Api", "sendBody": true, "contentType": "binaryData", "inputDataFieldName": "data", "options": {}}, "name": "Fazer Upload Imagem", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1620, -40], "id": "b3ebf438-4e1e-4665-a3a6-011cd625013b", "credentials": {"oAuth2Api": {"id": "SthsgLLneTnczql1", "name": "Canva Connection Personal Version"}}}, {"parameters": {"functionCode": "// Junta os asset_ids retornados com os campos, e pega textos do input original\nconst original = $items(\"Webhook1\")[0].json.body; // <--- CORRIGIDO\nconst imgs = $items(\"Fazer Upload Imagem\", 0, 0);\nconst imgMap = {};\nimgs.forEach(({json}) => { imgMap[json.field] = json.asset_id });\n\n// textos recebidos no webhook (ex: TITLE, DESCRIPTION...)\nconst textos = [\"TITLE\", \"DESCRIPTION\", \"CONTINUE\"].reduce((obj, key) => {\n  if (original[key]) obj[key] = { type: \"text\", text: original[key] };\n  return obj;\n}, {});\n\n// monta estrutura final do Canva\nconst data = {\n  ...textos,\n  ...Object.fromEntries(\n    Object.entries(imgMap).map(([k, v]) => [k, { type: \"image\", asset_id: v }])\n  )\n};\nreturn [{ json: { brand_template_id: \"EAGqDjHn_M4\", data } }];\n"}, "name": "Montar Payload Canva", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [1840, -40], "id": "39a88a4b-5d92-456c-9e74-5464d9ce96e5"}, {"parameters": {"method": "POST", "url": "https://api.canva.com/rest/v1/autofills", "authentication": "genericCredentialType", "genericAuthType": "oAuth2Api", "sendBody": true, "specifyBody": "json", "jsonBody": "={{$json}}", "options": {}}, "name": "Criar Design", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [2060, -40], "id": "8085e4b6-2e5f-4f54-8636-c9ede8638260", "credentials": {"oAuth2Api": {"id": "SthsgLLneTnczql1", "name": "Canva Connection Personal Version"}}}, {"parameters": {"url": "https://api.canva.com/rest/v1/autofills/{{$json[\"id\"]}}", "authentication": "genericCredentialType", "genericAuthType": "oAuth2Api", "options": {}}, "name": "Checar Status Design", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [2280, -40], "id": "95ca02d2-6c13-427e-a551-6bb7cb969af7", "credentials": {"oAuth2Api": {"id": "SthsgLLneTnczql1", "name": "Canva Connection Personal Version"}}}, {"parameters": {"url": "={{$json[\"url\"]}}.png", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1400, -40], "id": "e3906909-7ca3-410d-a72c-8f33cc6a3d46", "name": "Baixar Imagem"}, {"parameters": {"options": {"reset": false}}, "name": "SplitInBatches1", "type": "n8n-nodes-base.splitInBatches", "position": [1620, 2950], "id": "47fd4466-083e-4b9f-9608-00e4de4ec02e", "typeVersion": 3, "alwaysOutputData": false, "notesInFlow": false}, {"parameters": {"method": "POST", "url": "https://api.canva.com/rest/v1/asset-uploads", "authentication": "genericCredentialType", "genericAuthType": "oAuth2Api", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "content-type", "value": "application/octet-stream"}, {"name": "Asset-Upload-Metadata", "value": "{ \"name_base64\": \"TXkgQXdlc29tZSBVcGxvYWQg8J+agA==\" }"}]}, "sendBody": true, "contentType": "binaryData", "inputDataFieldName": "data", "options": {}}, "type": "n8n-nodes-base.httpRequest", "name": "Upload Imagem1", "position": [2500, 2950], "id": "c2be907b-f5fd-4058-be2d-6d1a04898797", "typeVersion": 4.2, "credentials": {"oAuth2Api": {"id": "SthsgLLneTnczql1", "name": "Canva Connection Personal Version"}}}, {"parameters": {"functionCode": "// Pega o id do design e prepara para o polling\nreturn [{json: { id: items[0].json.id, tentativas: 0 } }];"}, "name": "Preparar Poll1", "type": "n8n-nodes-base.function", "position": [2500, 2550], "id": "bb25c852-a724-4fef-a300-5ea6f52eacc1", "typeVersion": 1}, {"parameters": {"url": "={{ $json.url }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "name": "Baixar Imagem2", "position": [2280, 2950], "id": "4231acfc-4716-4f8d-8159-45f9aaffbb36", "typeVersion": 4.2}, {"parameters": {"method": "POST", "url": "https://api.canva.com/rest/v1/autofills", "authentication": "genericCredentialType", "genericAuthType": "oAuth2Api", "sendBody": true, "specifyBody": "json", "jsonBody": "={{$json}}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "name": "Criar Design2", "position": [2060, 2560], "id": "9768457d-ede4-43b0-bad2-7e23ce2f6791", "typeVersion": 4.2, "credentials": {"oAuth2Api": {"id": "SthsgLLneTnczql1", "name": "Canva Connection Personal Version"}}}, {"parameters": {"url": "=https://api.canva.com/rest/v1/autofills/{{ $('Criar Design2').item.json.job.id }}", "authentication": "genericCredentialType", "genericAuthType": "oAuth2Api", "options": {}}, "type": "n8n-nodes-base.httpRequest", "name": "Checar Status Design2", "position": [2720, 2550], "id": "d7b80e5f-f872-42ea-9a64-95a9029bb61b", "typeVersion": 4.2, "credentials": {"oAuth2Api": {"id": "SthsgLLneTnczql1", "name": "Canva Connection Personal Version"}}}, {"parameters": {"amount": 3}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [3600, 3050], "id": "e04f092e-bcc6-4a63-8f9b-dd0a8f07a65d", "name": "Wait1", "webhookId": "019a18b8-0c8a-4041-985f-ea521dada090"}, {"parameters": {"url": "=https://api.canva.com/rest/v1/asset-uploads/{{ $json.jobId }}", "authentication": "genericCredentialType", "genericAuthType": "oAuth2Api", "sendBody": true, "specifyBody": "json", "jsonBody": "={{$json}}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "name": "GET status imagem1", "position": [2940, 2950], "id": "815e68d1-0161-4b86-a04e-be53e6d71840", "typeVersion": 4.2, "credentials": {"oAuth2Api": {"id": "SthsgLLneTnczql1", "name": "Canva Connection Personal Version"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "840b9956-2844-4550-b7e9-ebb6615df4eb", "leftValue": "={{ $json.status }}", "rightValue": "success", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [3380, 2880], "id": "b8ca4764-8a8f-4dcd-9208-b9f44b3be363", "name": "If1"}, {"parameters": {"assignments": {"assignments": [{"id": "d10deabe-de5a-409c-9296-4de033eb8418", "name": "field", "value": "={{ $json.field }}", "type": "string"}, {"id": "8eb0ffa2-4c3e-48c4-a5c0-923e4e2589c1", "name": "url", "value": "={{ $json.url }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1840, 2950], "id": "9e2bc04a-a860-4483-9e81-0374acdcd335", "name": "Edit Fields1"}, {"parameters": {"jsCode": "return [{\n  json: {\n    field: $json.field,\n    url: $json.url,\n       tentativas: 0,\n  }\n}];\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2060, 2950], "id": "921282f2-cfa4-4cd6-a5d8-dbd7c67dc05c", "name": "Code4"}, {"parameters": {"jsCode": "return [{\n  json: {\n    jobId: items[0].json.job.id,\n    status: items[0].json.job.status,\n       tentativas: 0,\n    field:$('Code4').first().json.field,\n  }\n}];\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2720, 2950], "id": "defc578a-2c43-477b-a59c-aca0665287df", "name": "Code5"}, {"parameters": {"jsCode": "const status = items[0].json.job.status;\nconst tentativas = $json.tentativas || 0;\nconst jobId = items[0].json.job.id || $json.jobId;\nconst field = $json.field ||  $('Code5').first().json.field // puxa do próprio contexto ou da primeira vez\n\nif (status === \"failed\") {\n  throw new Error(\"Falha no upload do asset: \" + (items[0].json.job.error?.message || \"Erro desconhecido\"));\n}\nif (tentativas >= 10) {\n  throw new Error(\"Asset upload demorou demais!\");\n}\n\nif (status === \"success\") {\n  return [{\n    json: {\n      asset_id: items[0].json.job.asset.id,\n      field, // já está carregado!\n      status: \"success\"\n    }\n  }];\n}\n\n// status in_progress: devolve tudo de novo, inclusive o field\nreturn [{\n  json: {\n    jobId,\n    tentativas: tentativas + 1,\n    status: \"in_progress\",\n    field // mantém o field vivo no loop!\n  }\n}];\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [3160, 2875], "id": "ba182c08-3720-4fa2-bcda-557297dbad98", "name": "Code6"}, {"parameters": {"jsCode": "// Salva os asset_ids e fields para o batch\nreturn [{ json: {\n  field: $input.first().json.field,\n  asset_id: $input.first().json.asset_id,\n}}];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [3600, 2750], "id": "f971ae76-108f-4248-a51d-f1a27b2e62ad", "name": "<PERSON><PERSON> Asset IDs1"}, {"parameters": {"httpMethod": "POST", "path": "criar-design-canva", "authentication": "headerAuth", "responseMode": "responseNode", "options": {}}, "name": "Webhook1", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [1180, 2950], "id": "b36e033e-bd56-46f6-89fa-c0542f766076", "webhookId": "b4f56239-519a-4ba4-a056-bb0c336e22e7", "credentials": {"httpHeaderAuth": {"id": "5GeOG7OyXwPlgrZr", "name": "Canva key webhook"}}}, {"parameters": {"jsCode": "const data = $getWorkflowStaticData('global');\ndata.assets = data.assets || [];\n\nfor (const item of items) {\n  data.assets.push({\n    field: item.json.field,\n    asset_id: item.json.asset_id\n  });\n}\n\n// Return nothing to end this branch of the execution\nreturn [];\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [3820, 3025], "id": "6fa9a91f-6c9a-4e18-854c-d7779102a87e", "name": "Code7", "alwaysOutputData": true}, {"parameters": {"jsCode": "const data = $getWorkflowStaticData('global');\ndata.assets = []; // limpa os assets\n\n// Prepara lista de imagens\nconst imgFields = [\n  \"CAPA\", \"AVATAR\",\n  ...Array(12).fill().map((_,i)=>`PHOTO_${(i+1).toString().padStart(2,'0')}`)\n];\nconst body = items[0].json.body;\nconst imgs = imgFields.map(field => {\n  if (body && body[field]) {\n    return { field, url: body[field] };\n  }\n}).filter(Boolean);\n\n// --- SALVA OS TEXTOS NO GLOBAL ---\nconst textos = {\n  TITLE: body.TITLE,\n  DESCRIPTION: body.DESCRIPTION,\n  CONTINUE: body.CONTINUE,\n  ID: body.ID\n};\ndata.textos = textos;\n\nreturn imgs.map(img => ({\n  json: {\n    ...img, // field, url\n    textos: textos\n  }\n}));\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1400, 2960], "id": "b90a98f8-a17a-44ad-a2a2-d5965480f45c", "name": "Code8"}, {"parameters": {"jsCode": "// Pega tanto os assets quanto os textos da memória global\nconst globalData = $getWorkflowStaticData('global');\nconst assets = globalData.assets;\nconst textos = globalData.textos;\n\n// Monta a estrutura canva\nconst data = {\n  ...[\"TITLE\", \"DESCRIPTION\", \"CONTINUE\"].reduce((obj, key) => {\n    if (textos && textos[key]) obj[key] = { type: \"text\", text: textos[key] };\n    return obj;\n  }, {}),\n  ...Object.fromEntries(\n    assets.map(a => [a.field, { type: \"image\", asset_id: a.asset_id }])\n  )\n};\n\nreturn [{ json: { brand_template_id: textos.ID, data } }];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1840, 2560], "id": "3ae604f3-2e46-4030-a98e-35f328c73cc8", "name": "Code9"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "6cbf9c01-c743-4b1a-88c7-14530340fbd4", "leftValue": "={{ $json.final }}", "rightValue": "in_progress", "operator": {"type": "boolean", "operation": "true", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [3160, 2475], "id": "4307492c-af1c-4e16-9294-20a57aff4477", "name": "If3", "alwaysOutputData": false}, {"parameters": {"jsCode": "const status = items[0].json.job?.status || items[0].json.status || \"undefined\";\nconst status_b = $input.first().json.job?.status || $input.first().json.status || \"undefined\";\nconst tentativas = $json.tentativas || 0;\nconst id = $json.id || items[0].json.id;\n\n\nif (status === \"success\" || status_b === \"success\") {\n  return [{\n    json: {\n      final: true,\n      url: items[0].json.result?.urls?.edit_url || $input.first().json.job?.result.design.url || null,\n      result: items[0].json.result?.design || $input.first().json.job?.result.design || null,\n    }\n  }];\n} else {\n\n\n  return [{ json: { final: false, id, tentativas: tentativas + 1, status: status, status_b: status_b } }];\n}"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2940, 2475], "id": "b88a92b5-c77d-4154-8f37-b6e92e0626d5", "name": "Code10"}, {"parameters": {"jsCode": "// Pega o id do design e prepara para o polling\nreturn [{json: { id: items[0].json.id, tentativas: 0 } }];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2280, 2550], "id": "4d6c2493-b1aa-4486-8934-4c289ffc4e12", "name": "Code11"}, {"parameters": {"amount": 15}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [3380, 2650], "id": "8ac3c97e-0b6d-409e-8f1b-30bef2eda886", "name": "Wait2", "webhookId": "c372bf28-531c-4565-90ef-51123bc81178"}, {"parameters": {"method": "POST", "url": "https://automate.felvieira.com.br/webhook-test/criar-design-canva", "sendBody": true, "specifyBody": "json", "jsonBody": " {\n      \"CAPA\": \"https://placehold.co/1600x900\",\n      \"AVATAR\": \"https://placehold.co/600x600\",\n      \"PHOTO_01\": \"https://placehold.co/600x400\",\n      \"PHOTO_02\": \"https://placehold.co/600x400\",\n      \"PHOTO_03\": \"https://placehold.co/600x400\",\n      \"PHOTO_04\": \"https://placehold.co/600x400\",\n      \"PHOTO_05\": \"https://placehold.co/600x400\",\n      \"PHOTO_06\": \"https://placehold.co/600x400\",\n      \"PHOTO_07\": \"https://placehold.co/600x400\",\n      \"PHOTO_08\": \"https://placehold.co/600x400\",\n      \"PHOTO_09\": \"https://placehold.co/600x400\",\n      \"PHOTO_10\": \"https://placehold.co/600x400\",\n      \"PHOTO_11\": \"https://placehold.co/600x400\",\n      \"PHOTO_12\": \"https://placehold.co/600x400\",\n      \"TITLE\": \"Título completo do design\",\n      \"DESCRIPTION\": \"Descrição detalhada do conteúdo desse design, pode colocar o que quiser aqui.\",\n      \"CONTINUE\": \"Clique para continuar\"\n    }", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1180, 1780], "id": "6df1a8dc-9e12-4b78-aa08-d86d452fb6b3", "name": "Webhook COMPLETO"}, {"parameters": {"method": "POST", "url": "https://automate.felvieira.com.br/webhook-test/criar-design-canva", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "specifyBody": "json", "jsonBody": " {\n      \"CAPA\": \"https://placehold.co/1600x900\",\n      \"AVATAR\": \"https://placehold.co/600x600\",\n   \"DESCRIPTION\": \"Descrição detalhada do conteúdo desse design, pode colocar o que quiser aqui.\",\n      \"CONTINUE\": \"Clique para continuar\"\n\n    }", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1180, 2040], "id": "0c796092-cfd4-44e3-bc47-cef47625f696", "name": "TESTE Webhook", "credentials": {"httpHeaderAuth": {"id": "5GeOG7OyXwPlgrZr", "name": "Canva key webhook"}}}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.3, "position": [4040, 2300], "id": "38665d70-f3e0-4364-acb2-919173abb702", "name": "Respond to Webhook"}, {"parameters": {"url": "=https://api.canva.com/rest/v1/exports/{{ $json.job.id }}", "authentication": "genericCredentialType", "genericAuthType": "oAuth2Api", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [3600, 2425], "id": "5b1c30d3-f570-4b28-a80a-bec4461d1c55", "name": "HTTP Request", "credentials": {"oAuth2Api": {"id": "SthsgLLneTnczql1", "name": "Canva Connection Personal Version"}}}, {"parameters": {"method": "POST", "url": "=https://api.canva.com/rest/v1/exports", "authentication": "genericCredentialType", "genericAuthType": "oAuth2Api", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"design_id\":\"{{ $('Checar Status Design2').item.json.job.result.design.id }}\" ,\n  \"format\": {\n    \"type\": \"png\",\n    \"export_quality\": \"pro\"\n  }\n} ", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [3380, 2425], "id": "579d9434-7823-4b69-a646-03dbe728ff13", "name": "HTTP Request1", "credentials": {"oAuth2Api": {"id": "SthsgLLneTnczql1", "name": "Canva Connection Personal Version"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "c908945e-bf58-4ec9-b785-b7f32c486a7c", "leftValue": "={{ $json.job.status }}", "rightValue": "success", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [3820, 2350], "id": "a5444189-99ea-4a20-ad59-9803e980f788", "name": "If"}, {"parameters": {}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [4040, 2525], "id": "29a8ae3e-f92b-4ced-bdad-07d6b3814449", "name": "Wait", "webhookId": "84355565-26f4-4694-a4e8-614c6dbb2431"}], "pinData": {"Webhook1": [{"json": {"headers": {"host": "automate.felvieira.com.br", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "content-length": "1898", "accept": "*/*", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7", "content-type": "application/json", "key": "n8ncanvaapicall", "origin": "http://localhost:5173", "priority": "u=1, i", "referer": "http://localhost:5173/", "sec-ch-ua": "\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"", "sec-ch-ua-mobile": "?0", "sec-ch-ua-platform": "\"Windows\"", "sec-fetch-dest": "empty", "sec-fetch-mode": "cors", "sec-fetch-site": "cross-site", "x-forwarded-for": "***************", "x-forwarded-host": "automate.felvieira.com.br", "x-forwarded-port": "443", "x-forwarded-proto": "https", "x-forwarded-server": "6479fcc22a41", "x-real-ip": "***************"}, "params": {}, "query": {}, "body": {"ID": "EAGqDjHn_M4", "CAPA": "https://uptmptfpumgrnlxukwau.supabase.co/storage/v1/object/public/generated-images/covers/1750121164890-cover.jpg", "AVATAR": "https://uptmptfpumgrnlxukwau.supabase.co/storage/v1/object/public/cover-images/uploads/1750120752460-97o4zjhmxkj.jpg", "DESCRIPTION": "Felipe é transportado para diferentes séries da NETFLIX, onde vive como o protagonista, enfrentando aventuras únicas em cada episódio enquanto tenta voltar para sua realidade.", "PHOTO_01": "https://uptmptfpumgrnlxukwau.supabase.co/storage/v1/object/public/generated-images/covers/1750120776244-1-wednesday.jpg", "PHOTO_02": "https://uptmptfpumgrnlxukwau.supabase.co/storage/v1/object/public/generated-images/covers/1750120790038-2-stranger-things-5.jpg", "PHOTO_03": "https://uptmptfpumgrnlxukwau.supabase.co/storage/v1/object/public/generated-images/covers/1750120803627-3-elite.jpg", "PHOTO_04": "https://uptmptfpumgrnlxukwau.supabase.co/storage/v1/object/public/generated-images/covers/1750120817024-4-money-heist--la-casa-de-papel-.jpg", "PHOTO_05": "https://uptmptfpumgrnlxukwau.supabase.co/storage/v1/object/public/generated-images/covers/1750120838581-6-ozark.jpg", "PHOTO_06": "https://uptmptfpumgrnlxukwau.supabase.co/storage/v1/object/public/generated-images/covers/1750120851895-7-dark.jpg", "PHOTO_07": "https://uptmptfpumgrnlxukwau.supabase.co/storage/v1/object/public/generated-images/covers/1750120865491-8-narcos.jpg", "PHOTO_08": "https://uptmptfpumgrnlxukwau.supabase.co/storage/v1/object/public/generated-images/covers/1750120879699-9-the-crown.jpg", "PHOTO_09": "https://uptmptfpumgrnlxukwau.supabase.co/storage/v1/object/public/generated-images/covers/1750120893446-10-orange-is-the-new-black.jpg", "PHOTO_10": "https://placehold.co/600x400", "PHOTO_11": "https://placehold.co/600x400", "PHOTO_12": "https://placehold.co/600x400", "TITLE": "Posters NETFLIX - <PERSON>", "CONTINUE": "Continue assistindo como Felipe"}, "webhookUrl": "https://automate.felvieira.com.br/webhook/criar-design-canva", "executionMode": "production"}}]}, "connections": {"Preparar Lista de Upload": {"main": [[{"node": "Baixar Imagem", "type": "main", "index": 0}]]}, "Fazer Upload Imagem": {"main": [[{"node": "Montar Payload Canva", "type": "main", "index": 0}]]}, "Montar Payload Canva": {"main": [[{"node": "Criar Design", "type": "main", "index": 0}]]}, "Criar Design": {"main": [[{"node": "Checar Status Design", "type": "main", "index": 0}]]}, "Baixar Imagem": {"main": [[{"node": "Fazer Upload Imagem", "type": "main", "index": 0}]]}, "SplitInBatches1": {"main": [[{"node": "Code9", "type": "main", "index": 0}], [{"node": "Edit Fields1", "type": "main", "index": 0}]]}, "Upload Imagem1": {"main": [[{"node": "Code5", "type": "main", "index": 0}]]}, "Preparar Poll1": {"main": [[{"node": "Checar Status Design2", "type": "main", "index": 0}]]}, "Baixar Imagem2": {"main": [[{"node": "Upload Imagem1", "type": "main", "index": 0}]]}, "Criar Design2": {"main": [[{"node": "Code11", "type": "main", "index": 0}]]}, "Checar Status Design2": {"main": [[{"node": "Code10", "type": "main", "index": 0}]]}, "Wait1": {"main": [[{"node": "GET status imagem1", "type": "main", "index": 0}]]}, "GET status imagem1": {"main": [[{"node": "Code6", "type": "main", "index": 0}]]}, "If1": {"main": [[{"node": "<PERSON><PERSON> Asset IDs1", "type": "main", "index": 0}], [{"node": "Wait1", "type": "main", "index": 0}]]}, "Edit Fields1": {"main": [[{"node": "Code4", "type": "main", "index": 0}]]}, "Code4": {"main": [[{"node": "Baixar Imagem2", "type": "main", "index": 0}]]}, "Code5": {"main": [[{"node": "GET status imagem1", "type": "main", "index": 0}]]}, "Code6": {"main": [[{"node": "If1", "type": "main", "index": 0}]]}, "Salvar Asset IDs1": {"main": [[{"node": "Code7", "type": "main", "index": 0}]]}, "Webhook1": {"main": [[{"node": "Code8", "type": "main", "index": 0}]]}, "Code8": {"main": [[{"node": "SplitInBatches1", "type": "main", "index": 0}]]}, "Code7": {"main": [[{"node": "SplitInBatches1", "type": "main", "index": 0}]]}, "Code9": {"main": [[{"node": "Criar Design2", "type": "main", "index": 0}]]}, "If3": {"main": [[{"node": "HTTP Request1", "type": "main", "index": 0}], [{"node": "Wait2", "type": "main", "index": 0}]]}, "Code10": {"main": [[{"node": "If3", "type": "main", "index": 0}]]}, "Code11": {"main": [[{"node": "Preparar Poll1", "type": "main", "index": 0}]]}, "Wait2": {"main": [[{"node": "Checar Status Design2", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "HTTP Request1": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}, "If": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}], [{"node": "Wait", "type": "main", "index": 0}]]}, "Wait": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "9709819b-446b-4f73-8f55-f2f6c95db3fb", "meta": {"templateCredsSetupCompleted": true, "instanceId": "ffb589f6aef88443949ea9494479fd1bdc24f985dc27e6546202777d82451288"}, "id": "Dv190VGYy0rsu6U8", "tags": [{"createdAt": "2024-09-23T19:17:30.190Z", "updatedAt": "2024-09-23T19:17:30.190Z", "id": "0n179ZCWHIc7pk8e", "name": "<PERSON><PERSON><PERSON><PERSON>"}]}