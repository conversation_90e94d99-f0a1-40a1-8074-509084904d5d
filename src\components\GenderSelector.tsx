import React from 'react'
import { motion } from 'framer-motion'
import { User, Check } from 'lucide-react'

type Gender = 'male' | 'female'

interface GenderSelectorProps {
  selectedGender: Gender | null
  onSelectGender: (gender: Gender) => void
}

const genderOptions: { id: Gender; label: string; icon: React.ReactNode }[] = [
  { id: 'male', label: 'Masculino', icon: <User size={48} /> },
  { id: 'female', label: 'Feminino', icon: <User size={48} /> },
]

export default function GenderSelector({ selectedGender, onSelectGender }: GenderSelectorProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
      className="w-full max-w-2xl mx-auto"
    >
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {genderOptions.map((option) => {
          const isSelected = selectedGender === option.id
          
          return (
            <motion.div
              key={option.id}
              whileHover={{ scale: 1.03 }}
              whileTap={{ scale: 0.98 }}
              onClick={() => onSelectGender(option.id)}
              className={`
                relative p-8 rounded-2xl border-2 border-brand-black cursor-pointer 
                transition-all duration-200
                ${isSelected
                  ? 'bg-brand-primary shadow-brutal-pressed scale-[0.99]'
                  : 'bg-brand-white shadow-brutal hover:shadow-brutal-lg'
                }
              `}
            >
              {/* Indicador de seleção */}
              {isSelected && (
                <div className="absolute top-3 right-3 w-8 h-8 bg-brand-black rounded-full flex items-center justify-center">
                  <Check size={20} className="text-brand-white" />
                </div>
              )}
              
              <div className="flex flex-col items-center justify-center text-center">
                <div className={`mb-4 ${isSelected ? 'text-brand-black' : 'text-brand-text'}`}>
                  {option.icon}
                </div>
                <h3 className={`text-2xl font-black ${isSelected ? 'text-brand-black' : 'text-brand-text'}`}>
                  {option.label}
                </h3>
                {isSelected && (
                  <p className="text-sm font-bold text-brand-black mt-2">
                    ✓ Selecionado
                  </p>
                )}
              </div>
            </motion.div>
          )
        })}
      </div>
    </motion.div>
  )
} 