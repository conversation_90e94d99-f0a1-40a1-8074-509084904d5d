import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Download, 
  X, 
  Filter,
  Grid,
  List,
  Film,
  Image as ImageIcon,
  Printer
} from 'lucide-react'
import { Generation } from '../hooks/useUserData'
import { showToast } from '../utils/toast'

interface CoverGalleryProps {
  generations: Generation[]
  loading: boolean
}

type Category = 'series-films' | 'covers' | 'posters';

export default function CoverGallery({ generations, loading }: CoverGalleryProps) {
  const [selectedCover, setSelectedCover] = useState<{ url: string; generation: Generation; index: number } | null>(null)
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [filterPlatform, setFilterPlatform] = useState<string>('all')
  const [sortBy, setSortBy] = useState<'date' | 'platform'>('date')
  const [activeCategory, setActiveCategory] = useState<Category>('series-films');

  const allCovers = generations.flatMap(generation => 
    generation.generated_covers.map((cover, index) => ({
      url: cover,
      generation,
      coverIndex: index,
      // Mocking category for now, this should come from data
      category: 'series-films' as Category
    }))
  )
  
  const filteredCovers = allCovers
    .filter(cover => {
      const categoryMatch = activeCategory === 'series-films'; // Temporary logic
      const platformMatch = filterPlatform === 'all' || cover.generation.streaming_platform === filterPlatform;
      return categoryMatch && platformMatch;
    })
    .sort((a, b) => {
      if (sortBy === 'date') {
        return new Date(b.generation.created_at).getTime() - new Date(a.generation.created_at).getTime()
      }
      return a.generation.streaming_platform.localeCompare(b.generation.streaming_platform)
    })

  const downloadCover = async (url: string, generation: Generation, index: number) => {
    const downloadToast = showToast.loading('Downloading image...')
    try {
      const response = await fetch(url)
      const blob = await response.blob()
      const downloadUrl = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = downloadUrl
      link.download = `generation-${generation.id.slice(0, 8)}-${generation.streaming_platform}-cover-${index + 1}.jpg`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(downloadUrl)
      showToast.dismiss(downloadToast)
      showToast.success('Image downloaded successfully! 📥')
    } catch (error) {
      console.error('Error downloading cover:', error)
      showToast.dismiss(downloadToast)
      showToast.error('Error downloading image')
    }
  }

  const getPlatformBrand = (platform: string) => {
    switch (platform) {
      case 'netflix': return { name: 'NETFLIX', color: 'bg-[#E50914]' };
      case 'disney': return { name: 'DISNEY+', color: 'bg-[#1D3A8A]' };
      case 'amazon': return { name: 'PRIME VIDEO', color: 'bg-[#00A8E1]' };
      default: return { name: platform.toUpperCase(), color: 'bg-gray-700' };
    }
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="w-16 h-16 border-4 border-dashed rounded-full animate-spin border-brand-primary"></div>
      </div>
    )
  }

  const categories = [
    { id: 'series-films', name: 'Series & Films', icon: Film, count: filteredCovers.length },
    { id: 'covers', name: 'Cover Images', icon: ImageIcon, count: 0 },
    { id: 'posters', name: 'Posters', icon: Printer, count: 0 },
  ]

  return (
    <div className="space-y-6">
      {/* Category Tabs */}
      <div className="flex items-center space-x-2 border-b-2 border-brand-black pb-4">
        {categories.map(cat => (
          <button
            key={cat.id}
            onClick={() => setActiveCategory(cat.id as Category)}
            className={`flex items-center space-x-2 py-2 px-4 rounded-lg border-2 border-brand-black font-semibold transition-all ${
              activeCategory === cat.id ? 'bg-brand-primary shadow-brutal-pressed' : 'bg-brand-white shadow-brutal-sm hover:shadow-brutal-hover'
            }`}
          >
            <cat.icon className="w-5 h-5 text-brand-black" />
            <span className="text-brand-black">{cat.name}</span>
            <span className="text-xs bg-brand-white text-brand-black font-bold px-2 py-1 rounded-md border-2 border-brand-black">{cat.count}</span>
          </button>
        ))}
      </div>

      {/* Controls */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between p-4 bg-brand-white border-2 border-brand-black rounded-lg shadow-brutal">
        <div className="flex items-center space-x-2">
            <h2 className="text-xl font-bold text-brand-text">
              {filteredCovers.length} capas
            </h2>
            <div className="flex bg-gray-100 rounded-lg p-1 border-2 border-brand-black">
              <button onClick={() => setViewMode('grid')} className={`p-2 rounded-md transition-colors ${viewMode === 'grid' ? 'bg-brand-secondary' : ''}`}><Grid className="w-4 h-4" /></button>
              <button onClick={() => setViewMode('list')} className={`p-2 rounded-md transition-colors ${viewMode === 'list' ? 'bg-brand-secondary' : ''}`}><List className="w-4 h-4" /></button>
            </div>
        </div>

        <div className="flex flex-wrap gap-3">
          <div className="relative">
            <select
              value={filterPlatform}
              onChange={(e) => setFilterPlatform(e.target.value)}
              className="appearance-none w-full bg-brand-white border-2 border-brand-black rounded-lg py-2 px-4 pr-8 font-semibold text-brand-black shadow-brutal-sm"
            >
              <option value="all">Todas as Plataformas</option>
              <option value="netflix">Netflix</option>
              <option value="disney">Disney+</option>
              <option value="amazon">Amazon Prime</option>
            </select>
          </div>
          <div className="relative">
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as 'date' | 'platform')}
              className="appearance-none w-full bg-brand-white border-2 border-brand-black rounded-lg py-2 px-4 pr-8 font-semibold text-brand-black shadow-brutal-sm"
            >
              <option value="date">Ordenar por Data</option>
              <option value="platform">Ordenar por Plataforma</option>
            </select>
          </div>
        </div>
      </div>

      {filteredCovers.length === 0 ? (
         <div className="text-center py-12 bg-brand-white border-2 border-brand-black rounded-lg shadow-brutal p-8">
            <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4 border-2 border-brand-black">
              <Grid className="w-12 h-12 text-gray-400" />
            </div>
            <h3 className="text-xl font-bold text-brand-text mb-2">Nenhuma capa encontrada</h3>
            <p className="text-brand-text/80">
              {filterPlatform === 'all' 
                                  ? 'Comece a criar seus primeiros covers de filme!' 
                : `Nenhuma capa encontrada para ${getPlatformBrand(filterPlatform).name}`
              }
            </p>
          </div>
      ) : viewMode === 'grid' ? (
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-6">
          {filteredCovers.map((cover, index) => {
            const platform = getPlatformBrand(cover.generation.streaming_platform);
            return (
              <motion.div
                key={`${cover.generation.id}-${cover.coverIndex}`}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: index * 0.05 }}
                className="group relative aspect-[2/3] bg-brand-white rounded-lg overflow-hidden border-2 border-brand-black shadow-brutal hover:shadow-brutal-lg transition-all"
              >
                <img
                  src={cover.url}
                  alt={`Cover ${cover.coverIndex + 1}`}
                  className="w-full h-full object-cover"
                  loading="lazy"
                />
                
                <div className="absolute top-2 left-2 px-2 py-1 bg-black/70 text-white text-xs font-bold rounded border-2 border-white/50">
                  5:4
                </div>
                <div className={`absolute top-2 right-2 px-2 py-1 ${platform.color} text-white text-xs font-bold rounded`}>
                  {platform.name}
                </div>
                
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-70 transition-all flex items-center justify-center opacity-0 group-hover:opacity-100">
                  <div className="flex space-x-3">
                    <button
                      onClick={(e) => { e.stopPropagation(); setSelectedCover({ url: cover.url, generation: cover.generation, index: cover.coverIndex }) }}
                      className="w-12 h-12 bg-brand-white rounded-lg border-2 border-brand-black shadow-brutal-sm flex items-center justify-center"
                      title="Visualizar"
                    >
                      <ImageIcon className="w-6 h-6 text-brand-black" />
                    </button>
                    <button
                      onClick={(e) => { e.stopPropagation(); downloadCover(cover.url, cover.generation, cover.coverIndex) }}
                      className="w-12 h-12 bg-brand-secondary rounded-lg border-2 border-brand-black shadow-brutal-sm flex items-center justify-center"
                      title="Baixar"
                    >
                      <Download className="w-6 h-6 text-brand-black" />
                    </button>
                  </div>
                </div>
              </motion.div>
            )
          })}
        </div>
      ) : (
        <div className="space-y-4">
          {filteredCovers.map((cover, index) => {
             const platform = getPlatformBrand(cover.generation.streaming_platform);
            return (
              <motion.div
                key={`${cover.generation.id}-${cover.coverIndex}`}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.05 }}
                className="bg-brand-white border-2 border-brand-black rounded-lg p-4 flex items-center space-x-4 shadow-brutal"
              >
                <img src={cover.url} alt={`Cover ${cover.coverIndex + 1}`} className="w-20 h-28 object-cover rounded-md border-2 border-brand-black" />
                <div className="flex-grow">
                  <h3 className="font-bold text-lg text-brand-text">Capa #{cover.coverIndex + 1}</h3>
                  <p className={`font-semibold text-sm ${platform.color} text-white px-2 py-1 inline-block rounded-md my-1`}>{platform.name}</p>
                  <p className="text-sm text-brand-text/70">{new Date(cover.generation.created_at).toLocaleString('pt-BR')}</p>
                </div>
                <div className="flex space-x-3">
                  <button onClick={() => setSelectedCover({ url: cover.url, generation: cover.generation, index: cover.coverIndex })} className="p-3 bg-brand-white rounded-lg border-2 border-brand-black shadow-brutal-sm"><ImageIcon className="w-5 h-5" /></button>
                  <button onClick={() => downloadCover(cover.url, cover.generation, cover.coverIndex)} className="p-3 bg-brand-secondary rounded-lg border-2 border-brand-black shadow-brutal-sm"><Download className="w-5 h-5" /></button>
                </div>
              </motion.div>
          )})}
        </div>
      )}

      <AnimatePresence>
        {selectedCover && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/80 flex items-center justify-center z-[100] p-4"
            onClick={() => setSelectedCover(null)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="relative bg-brand-white rounded-lg border-2 border-brand-black shadow-brutal-lg max-w-3xl w-full"
              onClick={(e) => e.stopPropagation()}
            >
              <img
                src={selectedCover.url}
                alt={`Cover ${selectedCover.index + 1}`}
                className="w-full h-auto max-h-[80vh] object-contain rounded-t-lg"
              />
              <div className="p-4 border-t-2 border-brand-black">
                <div className="flex justify-between items-start mb-3">
                  <div>
                    <div className="font-bold text-brand-text text-lg mb-1">
                      Capa #{selectedCover.index + 1}
                    </div>
                    {/* 🎬 MOSTRAR QUAL SÉRIE/FILME REPRESENTA */}
                    <div className="text-sm text-brand-text/70 mb-2">
                      <span className="font-semibold">Série:</span> {selectedCover.generation.streaming_platform?.toUpperCase() || 'PLATAFORMA'}
                    </div>
                    <div className="text-xs text-brand-text/60">
                      Criado em {new Date(selectedCover.generation.created_at).toLocaleString('pt-BR')}
                    </div>
                  </div>
                </div>
                <div className="flex space-x-2">
                    <button
                        onClick={() => downloadCover(selectedCover.url, selectedCover.generation, selectedCover.index)}
                        className="flex items-center space-x-2 py-2 px-4 rounded-lg border-2 border-brand-black font-semibold transition-all bg-brand-secondary shadow-brutal-sm hover:shadow-brutal-hover"
                    >
                        <Download className="w-5 h-5" />
                        <span>Baixar</span>
                    </button>
                    <button
                        onClick={() => setSelectedCover(null)}
                        className="flex items-center space-x-2 py-2 px-4 rounded-lg border-2 border-brand-black font-semibold transition-all bg-brand-white shadow-brutal-sm hover:shadow-brutal-hover"
                    >
                        <X className="w-5 h-5" />
                        <span>Fechar</span>
                    </button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
} 