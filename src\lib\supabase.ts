import { createClient } from '@supabase/supabase-js'

// Acessando as variáveis de ambiente de forma segura
const getEnv = (key: string): string => {
  // Tenta acessar via import.meta.env (Vite)
  if (import.meta.env[`VITE_${key}`]) {
    return import.meta.env[`VITE_${key}`] as string;
  }
  
  // Tenta acessar via process.env (pode funcionar em alguns ambientes)
  if (process.env[`VITE_${key}`]) {
    return process.env[`VITE_${key}`] as string;
  }
  
  // Tenta acessar diretamente (pode funcionar em alguns ambientes de build)
  const envValue = (window as any).__env?.[`VITE_${key}`];
  if (envValue) {
    return envValue;
  }
  
  console.error(`Environment variable VITE_${key} is not defined`);
  return '';
};

const supabaseUrl = getEnv('SUPABASE_URL');
const supabaseKey = getEnv('SUPABASE_ANON_KEY');

if (!supabaseUrl || !supabaseKey) {
  const errorMsg = 'Missing Supabase environment variables. Please check your configuration.';
  console.error(errorMsg);
  throw new Error(errorMsg);
}

export const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
    flowType: 'pkce',
    debug: false, // 🔥 DESABILITADO para parar logs do GoTrueClient
  }
})

export type Database = {
  public: {
    Tables: {
      unified_movies_series: {
        Row: {
          id: string
          title: string
          streaming_platform: 'netflix' | 'disney' | 'amazon'
          type: 'movie' | 'series'
          year?: number | null
          genre?: string | null
          poster_style_prompt?: string | null
          base_prompt?: string | null
          safe_prompt?: string | null
          gender_male_prompt?: string | null
          gender_female_prompt?: string | null
          couple_prompt?: string | null
          is_active: boolean
          default_creativity_level_id?: string | null
          created_at: string
          updated_at: string
          total_attempts: number
          successful_attempts: number
          failed_attempts: number
          e005_errors: number
          success_rate: number
          last_attempt_at?: string | null
          last_error_message?: string | null
        }
        Insert: {
          id?: string
          title: string
          streaming_platform: 'netflix' | 'disney' | 'amazon'
          type: 'movie' | 'series'
          year: number
          genre?: string | null
          poster_style_prompt: string
          created_at?: string
        }
        Update: {
          id?: string
          title?: string
          streaming_platform?: 'netflix' | 'disney' | 'amazon'
          type?: 'movie' | 'series'
          year?: number
          genre?: string | null
          poster_style_prompt?: string
          created_at?: string
        }
      }
      cover_generations: {
        Row: {
          id: string
          user_id: string | null
          original_image_url: string
          photo_type: 'individual' | 'casal'
          streaming_platform: 'netflix' | 'disney' | 'amazon'
          generated_covers: any
          status: 'processing' | 'completed' | 'failed'
          created_at: string
          completed_at: string | null
        }
        Insert: {
          id?: string
          user_id?: string | null
          original_image_url: string
          photo_type: 'individual' | 'casal'
          streaming_platform: 'netflix' | 'disney' | 'amazon'
          generated_covers: any
          status?: 'processing' | 'completed' | 'failed'
          created_at?: string
          completed_at?: string | null
        }
        Update: {
          id?: string
          user_id?: string | null
          original_image_url?: string
          photo_type?: 'individual' | 'casal'
          streaming_platform?: 'netflix' | 'disney' | 'amazon'
          generated_covers?: any
          status?: 'processing' | 'completed' | 'failed'
          created_at?: string
          completed_at?: string | null
        }
      }
      payment_intents: {
        Row: {
          id: string
          stripe_payment_intent_id: string
          user_id: string | null
          plan_id: string
          amount: number
          currency: string
          status: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          stripe_payment_intent_id: string
          user_id?: string | null
          plan_id: string
          amount: number
          currency?: string
          status?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          stripe_payment_intent_id?: string
          user_id?: string | null
          plan_id?: string
          amount?: number
          currency?: string
          status?: string
          created_at?: string
          updated_at?: string
        }
      }
      user_subscriptions: {
        Row: {
          id: string
          user_id: string | null
          plan_id: string
          status: string
          stripe_payment_intent_id: string | null
          activated_at: string
          expires_at: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id?: string | null
          plan_id: string
          status?: string
          stripe_payment_intent_id?: string | null
          activated_at?: string
          expires_at?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string | null
          plan_id?: string
          status?: string
          stripe_payment_intent_id?: string | null
          activated_at?: string
          expires_at?: string | null
          created_at?: string
          updated_at?: string
        }
      }
    }
  }
}

// Auth helpers
export async function signInWithGoogle(returnTo = '/dashboard') {
  // Store the intended return URL
  sessionStorage.setItem('returnTo', returnTo);
  
  const { error } = await supabase.auth.signInWithOAuth({ 
    provider: 'google',
    options: {
      redirectTo: `${window.location.origin}/auth/callback`
    }
  });
  
  if (error) {
    console.error('Google sign-in error:', error);
    throw error;
  }
}

export async function signUpWithEmail(email: string, password: string, fullName?: string) {
  const { data, error } = await supabase.auth.signUp({ 
    email, 
    password,
    options: {
      data: {
        full_name: fullName || '',
        name: fullName || ''
      }
    }
  })
  if (error) throw error
  return data
}

export async function signInWithEmail(email: string, password: string) {
  const { data, error } = await supabase.auth.signInWithPassword({ email, password })
  if (error) throw error
  return data
}

export async function signOut() {
  const { error } = await supabase.auth.signOut()
  if (error) throw error
}