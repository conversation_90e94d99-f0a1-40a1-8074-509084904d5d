# 🔑 Como Configurar Token do Canva

## 📋 **Problema Explicado**
A Edge Function precisa do **mesmo token** que o N8N está usando porque:

⚠️ **IMPORTANTE: O Canva NÃO suporta `client_credentials`!**

- ✅ **Grant types suportados pelo Canva**: `authorization_code`, `refresh_token`
- ❌ **NÃO suporta**: `client_credentials` (grant type para server-to-server)
- 🔄 **Requer OAuth2 + PKCE**: Necessita autorização do usuário via navegador

Por isso não conseguimos gerar token automaticamente com client_id + client_secret. Precisamos usar um token já autorizado via OAuth.

## 🚀 **NOVA FUNCIONALIDADE: Renovação Automática**

A Edge Function agora suporta **renovação automática** de tokens usando `refresh_token`! 

### **Prioridade de Tokens (em ordem):**
1. 🎯 **Token no payload** (para testes manuais)
2. 🔑 **Token nas variáveis de ambiente** (CANVA_ACCESS_TOKEN)
3. 🔄 **Renovação automática** (via CANVA_REFRESH_TOKEN)

## 🔍 **1. Obter Tokens do N8N (Access + Refresh)**

### **Opção A: Via Interface do N8N**
1. Acesse `https://automate.felvieira.com.br`
2. Vá em **Credentials** → **Canva Connection**  
3. Copie tanto o **Access Token** quanto o **Refresh Token**

### **Opção B: Via DevTools (Mais Completo)**
1. Abra o fluxo N8N que funciona
2. Execute um nó qualquer do Canva
3. Abra **DevTools** (F12) → **Network**
4. Procure request para `api.canva.com/rest/v1/oauth/token` 
5. Na **Response**, você verá algo como:
   ```json
   {
     "access_token": "JagALLazU0i2ld9WW4zTO...",
     "refresh_token": "JABix5nolsk9k8n2r0f8nq1...",
     "token_type": "Bearer",
     "expires_in": 14400
   }
   ```
6. Copie ambos os tokens!

## ⚙️ **2. Configurar no Supabase (Renovação Automática)**

### **Configuração Completa (Recomendado)**
```bash
# Token atual (expira em 4 horas)
supabase secrets set CANVA_ACCESS_TOKEN="Bearer access_token_aqui"

# Token de renovação (nunca expira, mas só pode ser usado uma vez)
supabase secrets set CANVA_REFRESH_TOKEN="refresh_token_aqui"

# Credenciais (já configuradas)
supabase secrets set CANVA_CLIENT_ID="OC-AZdfZOcE7mgD"
supabase secrets set CANVA_CLIENT_SECRET="cnvca9UbcRNeju-iX-YioJQNkDTYTxpbzcfrpZ_AGkA_vYEc50913078"
```

### **Configuração Simples (Apenas Access Token)**
```bash
supabase secrets set CANVA_ACCESS_TOKEN="Bearer access_token_aqui"
```

### **Para Teste Rápido (Via Payload)**
```json
{
  "CANVA_ACCESS_TOKEN": "Bearer access_token_aqui",
  "ID": "EAGqDjHn_M4",
  // ... outros campos
}
```

## 🔄 **Como Funciona a Renovação Automática**

Quando você configurar `CANVA_REFRESH_TOKEN`, a Edge Function:

1. ✅ **Tenta usar token existente** (payload ou CANVA_ACCESS_TOKEN)
2. 🔄 **Se não tiver, renova automaticamente** usando refresh_token:
   ```bash
   POST https://api.canva.com/rest/v1/oauth/token
   Authorization: Basic base64(client_id:client_secret)
   Content-Type: application/x-www-form-urlencoded
   
   grant_type=refresh_token&refresh_token=seu_refresh_token
   ```
3. ✅ **Usa o novo token** para fazer a request
4. 📝 **Logs o processo** para debugging

## 🧪 **3. Testar**

### **Teste Básico (Com Token Manual)**
1. Acesse `http://localhost:5175/`
2. Dashboard → Aba **"Test"** 🧪
3. Cole o access_token no campo
4. Clique **"🧪 Testar Edge Function"**

### **Teste de Renovação (Avançado)**
1. Configure apenas `CANVA_REFRESH_TOKEN` (sem CANVA_ACCESS_TOKEN)
2. Execute o teste
3. Verifique nos logs se aparece "🔄 Renovando token via refresh_token..."
4. Se funcionar = ✅ Renovação automática funcionando!

## ⚠️ **Importante sobre Tokens**

### **Access Token**
- ⏰ **Expira em**: 4 horas (14400 segundos)
- 📏 **Tamanho**: Até 4KB
- 🔑 **Uso**: Autenticação nas APIs

### **Refresh Token**
- ♾️ **Nunca expira**: Mas só pode ser usado uma vez
- 🔄 **Função**: Gerar novos access_tokens
- 🆕 **Renovação**: Cada uso gera um novo refresh_token

### **Fluxo de Produção Ideal**
1. **Primeira configuração**: Access + Refresh tokens
2. **Quando access expira**: Edge Function usa refresh automaticamente
3. **Novo refresh_token**: Idealmente seria salvo no banco para próxima vez
4. **Sem intervenção manual**: Sistema funcionaria indefinidamente

## 🎯 **Vantagens da Nova Implementação**

- ✅ **Zero configuração manual** após setup inicial
- ✅ **Renovação automática** quando tokens expiram  
- ✅ **Fallback robusto**: Múltiplas fontes de token
- ✅ **Compatível com produção**: Sem necessidade de intervenção
- ✅ **Logs detalhados**: Fácil debugging

---

**🎉 Agora a Edge Function é totalmente autossuficiente e pode substituir o N8N com renovação automática de tokens!** 