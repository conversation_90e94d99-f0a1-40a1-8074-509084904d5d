-- Create movies table to replace the hardcoded movieDatabase
CREATE TABLE IF NOT EXISTS movies (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    streaming_platform VARCHAR(50) NOT NULL CHECK (streaming_platform IN ('netflix', 'disney', 'amazon')),
    base_prompt TEXT NOT NULL,
    safe_prompt TEXT,
    gender_male_prompt TEXT,
    gender_female_prompt TEXT,
    couple_prompt TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES auth.users(id),
    UNIQUE(title, streaming_platform)
);

-- Create movie generation statistics table
CREATE TABLE IF NOT EXISTS movie_generation_stats (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    movie_id UUID REFERENCES movies(id) ON DELETE CASCADE,
    total_attempts INTEGER DEFAULT 0,
    successful_attempts INTEGER DEFAULT 0,
    failed_attempts INTEGER DEFAULT 0,
    e005_errors INTEGER DEFAULT 0, -- Sensitive content errors
    last_attempt_at TIMESTAMP WITH TIME ZONE,
    last_error_message TEXT,
    success_rate DECIMAL(5,2) GENERATED ALWAYS AS (
        CASE 
            WHEN total_attempts > 0 THEN (successful_attempts::DECIMAL / total_attempts::DECIMAL) * 100
            ELSE 0
        END
    ) STORED,
    UNIQUE(movie_id)
);

-- Create function to update movie stats
CREATE OR REPLACE FUNCTION update_movie_stats(
    p_movie_title VARCHAR(255),
    p_streaming_platform VARCHAR(50),
    p_success BOOLEAN,
    p_error_message TEXT DEFAULT NULL
)
RETURNS VOID AS $$
DECLARE
    v_movie_id UUID;
BEGIN
    -- Get movie ID
    SELECT id INTO v_movie_id 
    FROM movies 
    WHERE title = p_movie_title AND streaming_platform = p_streaming_platform;
    
    IF v_movie_id IS NULL THEN
        RETURN; -- Movie not found, skip stats update
    END IF;
    
    -- Insert or update stats
    INSERT INTO movie_generation_stats (movie_id, total_attempts, successful_attempts, failed_attempts, e005_errors, last_attempt_at, last_error_message)
    VALUES (
        v_movie_id,
        1,
        CASE WHEN p_success THEN 1 ELSE 0 END,
        CASE WHEN p_success THEN 0 ELSE 1 END,
        CASE WHEN p_error_message LIKE '%E005%' THEN 1 ELSE 0 END,
        NOW(),
        p_error_message
    )
    ON CONFLICT (movie_id) DO UPDATE SET
        total_attempts = movie_generation_stats.total_attempts + 1,
        successful_attempts = movie_generation_stats.successful_attempts + CASE WHEN p_success THEN 1 ELSE 0 END,
        failed_attempts = movie_generation_stats.failed_attempts + CASE WHEN p_success THEN 0 ELSE 1 END,
        e005_errors = movie_generation_stats.e005_errors + CASE WHEN p_error_message LIKE '%E005%' THEN 1 ELSE 0 END,
        last_attempt_at = NOW(),
        last_error_message = CASE WHEN NOT p_success THEN p_error_message ELSE movie_generation_stats.last_error_message END;
END;
$$ LANGUAGE plpgsql;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_movies_platform ON movies(streaming_platform);
CREATE INDEX IF NOT EXISTS idx_movies_active ON movies(is_active);
CREATE INDEX IF NOT EXISTS idx_movie_stats_success_rate ON movie_generation_stats(success_rate);
CREATE INDEX IF NOT EXISTS idx_movie_stats_e005 ON movie_generation_stats(e005_errors);

-- Enable RLS
ALTER TABLE movies ENABLE ROW LEVEL SECURITY;
ALTER TABLE movie_generation_stats ENABLE ROW LEVEL SECURITY;

-- RLS Policies for movies table
CREATE POLICY "Admin can manage movies" ON movies
    FOR ALL USING (
        auth.jwt() ->> 'user_metadata' ->> 'role' = 'admin'
    );

CREATE POLICY "Users can read active movies" ON movies
    FOR SELECT USING (is_active = true);

-- RLS Policies for movie stats table
CREATE POLICY "Admin can manage movie stats" ON movie_generation_stats
    FOR ALL USING (
        auth.jwt() ->> 'user_metadata' ->> 'role' = 'admin'
    );

CREATE POLICY "Users can read movie stats" ON movie_generation_stats
    FOR SELECT USING (true); 