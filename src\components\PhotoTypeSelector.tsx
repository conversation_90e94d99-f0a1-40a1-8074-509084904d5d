import React from 'react'
import { useTranslation } from 'react-i18next'
import { User, Users } from 'lucide-react'
import { motion } from 'framer-motion'
import { PhotoType } from '../types'

interface PhotoTypeSelectorProps {
  selectedType: PhotoType | null
  onSelect: (type: PhotoType) => void
}

export default function PhotoTypeSelector({ selectedType, onSelect }: PhotoTypeSelectorProps) {
  const { t } = useTranslation()

  const options = [
    {
      type: 'individual' as PhotoType,
      label: t('photoTypeSelector.individual'),
      description: t('photoTypeSelector.individualDesc'),
      icon: User
    },
    {
      type: 'casal' as PhotoType,
      label: t('photoTypeSelector.couple'),
      description: t('photoTypeSelector.coupleDesc'),
      icon: Users
    }
  ]

  return (
    <div className="w-full max-w-2xl mx-auto">
      <div className="text-center mb-10">
        <div className="w-20 h-20 bg-brand-white rounded-xl flex items-center justify-center mx-auto mb-6 border-2 border-brand-black shadow-brutal">
          <Users className="w-10 h-10 text-brand-black" />
        </div>
        
        <h3 className="text-3xl font-black text-brand-text mb-2">{t('photoTypeSelector.title')}</h3>
        <p className="text-lg text-brand-text/80">{t('photoTypeSelector.subtitle')}</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {options.map((option, index) => {
          const Icon = option.icon
          const isSelected = selectedType === option.type
          
          return (
            <button
              key={option.type}
              onClick={() => onSelect(option.type)}
              className={`
                p-8 rounded-xl transition-all duration-200 text-center border-2 border-brand-black
                ${isSelected 
                  ? 'bg-brand-primary shadow-brutal-pressed' 
                  : 'bg-brand-white shadow-brutal hover:shadow-brutal-hover active:shadow-brutal-pressed'
                }
              `}
            >
              <div className={`
                w-16 h-16 rounded-lg flex items-center justify-center mx-auto mb-5 transition-all duration-300 border-2 border-brand-black
                ${isSelected 
                  ? 'bg-brand-white' 
                  : 'bg-gray-100'
                }
              `}>
                <Icon size={32} className="text-brand-black" />
              </div>
              
              <h4 className={`font-bold text-xl mb-2 ${isSelected ? 'text-brand-black' : 'text-brand-text'}`}>
                {option.label}
              </h4>
              <p className={`text-sm ${isSelected ? 'text-brand-black/80' : 'text-brand-text/70'}`}>
                {option.description}
              </p>
            </button>
          )
        })}
      </div>
    </div>
  )
}