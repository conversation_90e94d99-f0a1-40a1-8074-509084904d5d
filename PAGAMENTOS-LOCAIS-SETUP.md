# 💳 Configuração de Pagamentos Locais - Stripe + Supabase

Este documento explica como configurar o sistema de pagamentos para funcionar em ambiente de desenvolvimento local.

## 🔍 Problema Original

Em desenvolvimento local, temos um conflito de chaves:
- **Frontend local** usa chaves de teste do `.env.local`
- **Edge Functions do Supabase** usam chaves de produção configuradas no painel
- **Resultado:** Client secret incompatível entre frontend e backend

## 🔧 Solução Implementada

### 1. **Detecção Automática de Ambiente**

A Edge Function `create-payment-intent` foi modificada para detectar automaticamente se a requisição vem do localhost:

```typescript
// Detectar ambiente baseado no origin/referer
const isLocal = req.headers.get('origin')?.includes('localhost') || 
                req.headers.get('referer')?.includes('localhost')

// Chaves de teste hardcoded para desenvolvimento
const localTestKeys = {
  secret: 'sk_test_51PlGJpDOvhLb9hmW88SlnQFl9qCOVwA3aLH44rCxCVubVjYZcoeqQ6oEC5tLssstgAIhEccnjJhdwuyXvYN9ANFZ00M7t1NppZ',
  publishable: 'pk_test_51PlGJpDOvhLb9hmWCxdc7fFHVqc3Cifn2KCfgLjhFYwiQKy8sxgYdqCdcdUFENebYdTueAbOrfVP2vrNzVUf5auY00mIuAepTw'
}

// Usar chaves apropriadas baseado no ambiente
const stripeSecretKey = isLocal 
  ? localTestKeys.secret 
  : (Deno.env.get('STRIPE_SECRET_KEY') || localTestKeys.secret)
```

### 2. **Configuração do Webhook Local**

Para processar pagamentos em desenvolvimento, criamos um servidor webhook local.

#### **Arquivo: `webhook-server.cjs`**

```javascript
const express = require('express');
const { createClient } = require('@supabase/supabase-js');
const app = express();
const port = 3000;

// Configuração do Supabase
const supabaseUrl = 'https://uptmptfpumgrnlxukwau.supabase.co';
const supabaseServiceKey = 'sua_service_role_key_aqui';
const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Middleware
app.use(express.json());
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, stripe-signature');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  if (req.method === 'OPTIONS') {
    res.sendStatus(200);
  } else {
    next();
  }
});

// Webhook do Stripe
app.post('/api/stripe/webhook', async (req, res) => {
  console.log('🔥 Webhook recebido:', req.body.type);
  
  try {
    const event = req.body;
    
    switch (event.type) {
      case 'payment_intent.succeeded':
        const paymentIntent = event.data.object;
        const userId = paymentIntent.metadata.userId;
        const credits = parseInt(paymentIntent.metadata.credits) || 0;

        // Atualizar status da compra
        await supabase
          .from('credit_purchases')
          .update({ 
            status: 'completed',
            completed_at: new Date().toISOString()
          })
          .eq('stripe_payment_intent_id', paymentIntent.id);

        // Adicionar créditos ao usuário
        const { data: existingCredits } = await supabase
          .from('user_credits')
          .select('*')
          .eq('user_id', userId)
          .single();

        if (!existingCredits) {
          // Criar registro se não existir
          await supabase
            .from('user_credits')
            .insert({
              user_id: userId,
              total_credits: credits,
              available_credits: credits,
              used_credits: 0
            });
        } else {
          // Atualizar registro existente
          await supabase
            .from('user_credits')
            .update({
              total_credits: existingCredits.total_credits + credits,
              available_credits: existingCredits.available_credits + credits,
              updated_at: new Date().toISOString()
            })
            .eq('user_id', userId);
        }

        res.json({ received: true, message: `${credits} créditos processados` });
        break;

      default:
        res.json({ received: true, message: 'Evento não tratado' });
    }
  } catch (error) {
    console.error('💥 Erro no webhook:', error);
    res.status(500).json({ error: error.message });
  }
});

app.listen(port, () => {
  console.log(`🚀 Webhook server rodando na porta ${port}`);
});
```

## 🌐 Configuração do ngrok

### 1. **Instalar ngrok**
```bash
# Via chocolatey (Windows)
choco install ngrok

# Ou baixar de: https://ngrok.com/download
```

### 2. **Expor servidor local**
```bash
# Expor porta 3000 (onde roda o webhook server)
ngrok http 3000
```

### 3. **Copiar URL pública**
O ngrok vai gerar uma URL como: `https://f7a3268b8578.ngrok-free.app`

## ⚙️ Configuração no Stripe Dashboard

### 1. **Acessar Webhooks**
- Dashboard do Stripe → **Developers** → **Webhooks**
- URL: https://dashboard.stripe.com/test/webhooks

### 2. **Criar Endpoint**
- **URL:** `https://sua-url-ngrok.ngrok-free.app/api/stripe/webhook`
- **Eventos:**
  - `payment_intent.succeeded`
  - `payment_intent.payment_failed`

### 3. **Copiar Webhook Secret**
- Após criar, copie o **Signing secret** (formato: `whsec_...`)
- Atualize no `.env.local`:

```env
STRIPE_WEBHOOK_SECRET=whsec_seu_webhook_secret_aqui
```

## 🚀 Como Executar

### 1. **Instalar dependências**
```bash
npm install express @supabase/supabase-js
```

### 2. **Iniciar ngrok**
```bash
ngrok http 3000
```

### 3. **Iniciar webhook server**
```bash
node webhook-server.cjs
```

### 4. **Iniciar aplicação**
```bash
npm run dev
```

### 5. **Deploy da Edge Function (se modificada)**
```bash
npx supabase functions deploy create-payment-intent
```

## 🧪 Testes

### 1. **Teste básico do servidor**
```bash
curl https://sua-url-ngrok.ngrok-free.app
```

### 2. **Teste manual do webhook**
```bash
curl -X POST https://sua-url-ngrok.ngrok-free.app/test-webhook
```

### 3. **Teste de pagamento completo**
1. Acesse sua aplicação local
2. Faça um pagamento de teste
3. Verifique logs no terminal do webhook server
4. Confirme que créditos foram adicionados

## 📋 Variáveis de Ambiente

### **`.env.local` (Desenvolvimento)**
```env
# Supabase
VITE_SUPABASE_URL=https://uptmptfpumgrnlxukwau.supabase.co
VITE_SUPABASE_ANON_KEY=sua_anon_key
SUPABASE_SERVICE_ROLE_KEY=sua_service_role_key

# Stripe TEST Keys
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_51PlGJpDOvhLb9hmW...
STRIPE_SECRET_KEY=sk_test_51PlGJpDOvhLb9hmW...
STRIPE_WEBHOOK_SECRET=whsec_seu_webhook_secret_local

# Stripe Product IDs - TEST
VITE_STRIPE_STARTER_CREDITS_ID=prod_SVeFgga19mY0Gl
VITE_STRIPE_POPULAR_CREDITS_ID=prod_SVeHwRl5tn4cP6
VITE_STRIPE_MASTER_CREDITS_ID=prod_SVeIPaf5R93PYg
```

### **Supabase Edge Functions (Produção)**
- Configuradas no painel do Supabase
- Usam chaves de produção
- Detectam automaticamente ambiente local

## 🔄 Fluxo Completo

### **Desenvolvimento Local:**
1. **Frontend** usa chaves de teste do `.env.local`
2. **Edge Function** detecta origem localhost
3. **Edge Function** usa chaves de teste hardcoded
4. **Stripe** cria payment intent com chaves consistentes
5. **Webhook local** processa pagamento confirmado
6. **Créditos** são adicionados ao usuário

### **Produção:**
1. **Frontend** usa chaves de produção
2. **Edge Function** usa chaves de produção do Supabase
3. **Webhook do Supabase** processa automaticamente
4. **Créditos** são adicionados via webhook oficial

## 🐛 Troubleshooting

### **Erro: "Client secret does not match"**
- ✅ Verificar se chaves de teste estão corretas
- ✅ Confirmar que Edge Function foi deployada
- ✅ Verificar se ngrok está rodando na mesma URL

### **Erro: "Webhook 404 Not Found"**
- ✅ Confirmar que webhook server está rodando
- ✅ Verificar se URL no Stripe está correta
- ✅ Testar rota manualmente no navegador

### **Pagamento não processa créditos**
- ✅ Verificar logs do webhook server
- ✅ Confirmar que webhook secret está correto
- ✅ Testar webhook manualmente

## 📝 Notas Importantes

- **Nunca commitar** chaves reais no código
- **Sempre usar** chaves de teste em desenvolvimento
- **Webhook local** é apenas para desenvolvimento
- **Produção** usa webhook oficial do Supabase
- **ngrok URL** muda a cada reinicialização (versão gratuita)

## 🔗 Links Úteis

- [Stripe Dashboard - Webhooks](https://dashboard.stripe.com/test/webhooks)
- [Stripe Dashboard - API Keys](https://dashboard.stripe.com/test/apikeys)
- [ngrok Download](https://ngrok.com/download)
- [Supabase Edge Functions](https://supabase.com/docs/guides/functions)