import { serve } from 'https://deno.land/std@0.177.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.21.0';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    console.log('🔍 Admin-users function called with method:', req.method);
    console.log('🔍 URL:', req.url);
    // Usar SERVICE_ROLE_KEY para acessar auth.users
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    );

    // Verificar se o usuário é admin através do token
    const authHeader = req.headers.get('Authorization');
    console.log('🔍 Auth header present:', !!authHeader);
    console.log('🔍 Auth header length:', authHeader?.length || 0);
    console.log('🔍 Auth header starts with Bearer:', authHeader?.startsWith('Bearer ') || false);
    
    if (!authHeader) {
      console.error('❌ No authorization header');
      return new Response(JSON.stringify({ error: 'Cabeçalho de autorização ausente.' }), { 
        status: 401, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    if (!authHeader.startsWith('Bearer ')) {
      console.error('❌ Invalid authorization header format');
      return new Response(JSON.stringify({ error: 'Formato de autorização inválido.' }), { 
        status: 401, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // Extrair o token
    const token = authHeader.replace('Bearer ', '');
    console.log('🔍 Token length:', token.length);

    // Tentar autenticar com o token usando o cliente admin
    console.log('🔍 Attempting to get user with admin client...');
    let { data: { user }, error: userError } = await supabaseAdmin.auth.getUser(token);
    console.log('🔍 Admin client auth result:', { 
      userId: user?.id, 
      email: user?.email, 
      hasUser: !!user,
      errorMessage: userError?.message,
      errorCode: userError?.status
    });
    
    if (userError || !user) {
      console.error('❌ User authentication error details:', {
        message: userError?.message,
        status: userError?.status,
        name: userError?.name
      });
      
      // Tentar com o cliente anônimo como fallback
      console.log('🔍 Trying with anon client as fallback...');
      const userClient = createClient(
        Deno.env.get('SUPABASE_URL') ?? '',
        Deno.env.get('SUPABASE_ANON_KEY') ?? '',
        {
          global: { headers: { Authorization: authHeader } }
        }
      );
      
      const { data: { user: anonUser }, error: anonError } = await userClient.auth.getUser();
      console.log('🔍 Anon client auth result:', { 
        userId: anonUser?.id, 
        email: anonUser?.email, 
        hasUser: !!anonUser,
        errorMessage: anonError?.message
      });
      
      if (anonError || !anonUser) {
        return new Response(JSON.stringify({ 
          error: 'Erro de autenticação: ' + (userError?.message || anonError?.message),
          details: userError?.status || anonError?.status
        }), { 
          status: 401, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        });
      }
      
      // Usar o usuário do cliente anônimo
      user = anonUser;
    }

    console.log('🔍 Checking admin status for user:', user.id);
    // Verificar se é admin - buscar o usuário no banco para ter certeza dos metadados
    const { data: userData, error: adminError } = await supabaseAdmin.auth.admin.getUserById(user.id);
    console.log('🔍 Admin check result:', { 
      hasUserData: !!userData?.user,
      role: userData?.user?.user_metadata?.role, 
      errorMessage: adminError?.message 
    });
    
    if (adminError) {
      console.error('❌ Admin check error:', adminError);
      return new Response(JSON.stringify({ 
        error: 'Erro ao verificar privilégios de admin: ' + adminError.message 
      }), { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }
    
    if (!userData?.user) {
      console.error('❌ User data not found');
      return new Response(JSON.stringify({ error: 'Dados do usuário não encontrados.' }), { 
        status: 404, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }
    
    if (userData.user.user_metadata?.role !== 'admin') {
      console.error('❌ Access denied - not admin:', userData.user.user_metadata?.role);
      return new Response(JSON.stringify({ 
        error: 'Acesso negado: usuário não é administrador.',
        userRole: userData.user.user_metadata?.role || 'no_role'
      }), { 
        status: 403, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    console.log('✅ Admin access confirmed');

    // Parse do body JSON
    let body;
    try {
      const bodyText = await req.text();
      console.log('🔍 Raw body length:', bodyText.length);
      if (bodyText.length === 0) {
        console.error('❌ Empty request body');
        return new Response(JSON.stringify({ error: 'Corpo da requisição vazio.' }), { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        });
      }
      body = JSON.parse(bodyText);
      console.log('🔍 Parsed body action:', body.action);
    } catch (parseError: any) {
      console.error('❌ JSON parse error:', parseError);
      return new Response(JSON.stringify({ error: 'Corpo da requisição inválido: ' + parseError.message }), { 
        status: 400, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    const { action } = body;
    console.log('🔍 Processing action:', action);

    switch (action) {
      case 'list-users': {
        console.log('📋 Executing list-users action');
        // Buscar todos os usuários ativos
        const { data: users, error: usersError } = await supabaseAdmin.auth.admin.listUsers();
        
        if (usersError) {
          console.error('❌ Error listing users:', usersError);
          throw usersError;
        }

        console.log(`🔍 Found ${users.users.length} users`);

        // Para cada usuário, buscar dados adicionais
        const enrichedUsers = await Promise.all(
          users.users.map(async (user: any) => {
            const [
              { data: purchases },
              { data: vouchers },
              { data: creditTransactions },
              { data: coverGens },
              { data: posters }
            ] = await Promise.all([
              supabaseAdmin.from('credit_purchases').select('credits, amount, status, created_at').eq('user_id', user.id).eq('status', 'completed'),
              supabaseAdmin.from('voucher_redemptions').select('credits_redeemed').eq('user_id', user.id),
              supabaseAdmin.from('credit_transactions').select('amount, type').eq('user_id', user.id),
              supabaseAdmin.from('cover_generations').select('*').eq('user_id', user.id),
              supabaseAdmin.from('poster_images').select('*').eq('user_id', user.id)
            ]);

            // Calcular créditos totais: compras + vouchers
            const purchasedCredits = purchases?.reduce((sum: number, p: any) => sum + p.credits, 0) || 0;
            const voucherCredits = vouchers?.reduce((sum: number, v: any) => sum + v.credits_redeemed, 0) || 0;
            const spentCredits = creditTransactions?.filter((t: any) => t.type === 'debit').reduce((sum: number, t: any) => sum + Math.abs(t.amount), 0) || 0;
            
            const totalCredits = purchasedCredits + voucherCredits - spentCredits;
            const totalSpent = purchases?.reduce((sum: number, p: any) => sum + (p.amount / 100), 0) || 0;

            return {
              user_id: user.id,
              user_email: user.email || 'Email não disponível',
              user_name: user.user_metadata?.full_name || user.user_metadata?.name || 'Nome não disponível',
              total_credits: totalCredits,
              total_spent: totalSpent,
              total_generations: (coverGens?.length || 0) + (posters?.length || 0),
              failed_generations: coverGens?.filter((g: any) => g.status === 'failed').length || 0,
              first_purchase: purchases?.[0]?.created_at || user.created_at,
              last_activity: Math.max(
                new Date(coverGens?.[0]?.created_at || 0).getTime(),
                new Date(posters?.[0]?.created_at || 0).getTime(),
                new Date(purchases?.[0]?.created_at || 0).getTime()
              ),
              created_at: user.created_at
            };
          })
        );

        console.log(`✅ Returning ${enrichedUsers.length} users`);
        return new Response(
          JSON.stringify({ users: enrichedUsers }),
          { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        );
      }

      case 'user-stats': {
        console.log('📊 Executing user-stats action');
        const { data: users } = await supabaseAdmin.auth.admin.listUsers();
        
        return new Response(
          JSON.stringify({ 
            total_users: users.users.length,
            total_confirmed: users.users.filter((u: any) => u.email_confirmed_at).length 
          }),
          { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        );
      }

      case 'user-generations': {
        console.log('🎬 Executing user-generations action');
        const { data: coverGens } = await supabaseAdmin
          .from('cover_generations')
          .select('*')
          .order('created_at', { ascending: false })
          .limit(200);

        const { data: posterGens } = await supabaseAdmin
          .from('poster_images')
          .select('*')
          .order('created_at', { ascending: false })
          .limit(200);

        console.log(`🔍 Found ${coverGens?.length || 0} cover generations and ${posterGens?.length || 0} poster generations`);

        // Buscar dados dos usuários para as gerações (excluindo null)
        const userIds = Array.from(new Set([
          ...(coverGens || []).map((g: any) => g.user_id),
          ...(posterGens || []).map((p: any) => p.user_id)
        ].filter(id => id !== null)));

        console.log(`🔍 Need to fetch data for ${userIds.length} unique users`);

        const userMap: Record<string, any> = {};
        
        // Adicionar entrada para user_id null
        userMap['null'] = { 
          email: 'Sistema/Teste', 
          name: 'Geração Anônima' 
        };
        
        for (const userId of userIds) {
          try {
            const { data: user } = await supabaseAdmin.auth.admin.getUserById(userId);
            if (user.user) {
              userMap[userId] = {
                email: user.user.email,
                name: user.user.user_metadata?.full_name || user.user.user_metadata?.name
              };
            }
          } catch (error) {
            console.error(`Erro ao buscar usuário ${userId}:`, error);
            userMap[userId] = { email: 'Email não disponível', name: 'Nome não disponível' };
          }
        }

        // Combinar dados
        const allGenerations = [
          ...(coverGens || []).map((g: any) => {
            const userKey = g.user_id || 'null';
            return {
              id: g.id,
              user_id: g.user_id,
              user_email: userMap[userKey]?.email || 'Email não disponível',
              user_name: userMap[userKey]?.name || g.user_name || 'Nome não disponível',
              type: 'cover',
              status: g.status || 'completed',
              streaming_platform: g.streaming_platform,
              created_at: g.created_at,
              completed_at: g.completed_at
            };
          }),
          ...(posterGens || []).map((p: any) => {
            const userKey = p.user_id || 'null';
            return {
              id: p.id,
              user_id: p.user_id,
              user_email: userMap[userKey]?.email || 'Email não disponível',
              user_name: userMap[userKey]?.name || p.user_name || 'Nome não disponível',
              type: 'poster',
              status: p.poster_url ? 'completed' : 'failed',
              streaming_platform: p.streaming_platform,
              created_at: p.created_at,
              completed_at: p.completed_at || p.created_at,
              template_id: p.template_id
            };
          })
        ];

        console.log(`✅ Returning ${allGenerations.length} total generations`);
        return new Response(
          JSON.stringify({ generations: allGenerations }),
          { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        );
      }

      case 'user-purchases': {
        console.log('💳 Executing user-purchases action');
        const { data: purchases } = await supabaseAdmin
          .from('credit_purchases')
          .select('*')
          .order('created_at', { ascending: false })
          .limit(100);

        console.log(`🔍 Found ${purchases?.length || 0} purchases`);

        if (!purchases || purchases.length === 0) {
          return new Response(
            JSON.stringify({ purchases: [] }),
            { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
          );
        }

        // Buscar dados dos usuários para as compras
        const userIds = Array.from(new Set(purchases.map((p: any) => p.user_id)));
        console.log(`🔍 Need to fetch data for ${userIds.length} unique users`);

        const userMap: Record<string, any> = {};
        
        for (const userId of userIds) {
          try {
            const { data: user } = await supabaseAdmin.auth.admin.getUserById(userId);
            if (user.user) {
              userMap[userId] = {
                email: user.user.email,
                name: user.user.user_metadata?.full_name || user.user.user_metadata?.name
              };
            }
          } catch (error) {
            console.error(`Erro ao buscar usuário ${userId}:`, error);
            userMap[userId] = { email: 'Email não disponível', name: 'Nome não disponível' };
          }
        }

        // Combinar dados
        const enrichedPurchases = purchases.map((p: any) => ({
          id: p.id,
          user_id: p.user_id,
          user_email: userMap[p.user_id]?.email || 'Email não disponível',
          user_name: userMap[p.user_id]?.name || 'Nome não disponível',
          credits: p.credits,
          amount: p.amount,
          status: p.status,
          created_at: p.created_at,
          completed_at: p.completed_at,
          payment_intent_id: p.payment_intent_id,
          stripe_customer_id: p.stripe_customer_id
        }));

        console.log(`✅ Returning ${enrichedPurchases.length} purchases with user data`);
        return new Response(
          JSON.stringify({ purchases: enrichedPurchases }),
          { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        );
      }

      default:
        console.error('❌ Unknown action:', action);
        return new Response(
          JSON.stringify({ error: 'Ação não reconhecida: ' + action }),
          { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        );
    }

  } catch (error: any) {
    console.error('❌ Erro na admin-users function:', error);
    
    return new Response(
      JSON.stringify({ 
        error: 'Erro interno do servidor: ' + error.message,
        stack: error.stack 
      }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }
}); 