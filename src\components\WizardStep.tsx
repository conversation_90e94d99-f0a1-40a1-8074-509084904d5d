import React from 'react'
import { motion } from 'framer-motion'
import { Check } from 'lucide-react'

interface WizardStepProps {
  stepNumber: number
  title: string
  description?: string
  isActive: boolean
  isCompleted: boolean
  children: React.ReactNode
  className?: string
}

export default function WizardStep({
  stepNumber,
  title,
  description,
  isActive,
  isCompleted,
  children,
  className = ''
}: WizardStepProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: stepNumber * 0.1 }}
      className={`relative ${className}`}
    >
      {/* Step Header */}
      <div className="flex items-center mb-6">
        <div className={`
          flex items-center justify-center w-10 h-10 rounded-full border-2 transition-all duration-300
          ${isCompleted 
            ? 'bg-green-500 border-green-500 text-white' 
            : isActive 
              ? 'bg-purple-600 border-purple-600 text-white' 
              : 'bg-gray-100 border-gray-300 text-gray-500'
          }
        `}>
          {isCompleted ? (
            <Check className="w-5 h-5" />
          ) : (
            <span className="text-sm font-semibold">{stepNumber}</span>
          )}
        </div>
        
        <div className="ml-4">
          <h3 className={`text-lg font-semibold transition-colors duration-300 ${
            isActive ? 'text-gray-900' : 'text-gray-600'
          }`}>
            {title}
          </h3>
          {description && (
            <p className="text-sm text-gray-500 mt-1">{description}</p>
          )}
        </div>
      </div>

      {/* Step Content */}
      <motion.div
        initial={false}
        animate={{
          height: isActive ? 'auto' : 0,
          opacity: isActive ? 1 : 0
        }}
        transition={{ duration: 0.3 }}
        className="overflow-hidden"
      >
        <div className="ml-14 pb-8">
          {children}
        </div>
      </motion.div>

      {/* Connector Line */}
      {stepNumber < 6 && (
        <div className={`
          absolute left-5 top-10 w-0.5 h-16 transition-colors duration-300
          ${isCompleted ? 'bg-green-500' : 'bg-gray-200'}
        `} />
      )}
    </motion.div>
  )
} 