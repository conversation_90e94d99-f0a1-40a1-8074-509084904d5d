import React, { useState, useEffect } from 'react';
import { 
  BarChart3, 
  Users, 
  DollarSign, 
  Image, 
  TrendingUp, 
  Calendar,
  Eye,
  RefreshCw,
  Download,
  Clock,
  CheckCircle,
  XCircle,
  Zap,
  Search,
  Filter,
  AlertTriangle,
  FileText,
  Activity,
  UserCheck,
  Play,
  Pause,
  RotateCcw,
  Gift,
  MessageSquare,
  Mail,
  Settings,
  Film,
  TrendingDown
} from 'lucide-react';
import { motion } from 'framer-motion';
import { toast } from 'sonner';
import { supabase } from '../lib/supabase';
import { useTranslation } from 'react-i18next';
import { useAdminVouchers } from '../hooks/useAdminVouchers';
import { useNavigate } from 'react-router-dom';

interface AdminStats {
  totalUsers: number;
  totalCreditPurchases: number;
  totalRevenue: number;
  totalGenerations: number;
  totalPosterGenerations: number;
  totalOperationalCosts: number;
  totalProfit: number;
  recentActivity: {
    date: string;
    type: 'purchase' | 'generation' | 'poster';
    count: number;
    revenue?: number;
  }[];
}

interface CreditPurchase {
  id: string;
  user_id: string;
  user_email: string;
  user_name: string;
  credits: number;
  amount: number;
  status: string;
  created_at: string;
  completed_at?: string;
  payment_intent_id?: string;
  stripe_customer_id?: string;
}

interface PosterGeneration {
  id: string;
  user_id: string;
  template_id: string;
  poster_url?: string;
  canva_design_url?: string;
  streaming_platform?: string;
  user_name?: string;
  created_at: string;
  completed_at?: string;
  request_payload: any;
  user?: {
    email: string;
  };
}

interface UserProfile {
  user_id: string;
  user_email: string;
  user_name: string;
  total_credits: number;
  total_spent: number;
  total_generations: number;
  failed_generations: number;
  first_purchase: string;
  last_activity: number;
  created_at: string;
}

interface Generation {
  id: string;
  user_id: string;
  user_email: string;
  type: 'cover' | 'poster';
  status: 'processing' | 'completed' | 'failed';
  streaming_platform?: string;
  created_at: string;
  completed_at?: string;
  error_message?: string;
  template_id?: string;
  user_name: string;
}

interface LogEntry {
  id: string;
  timestamp: string;
  level: 'info' | 'warning' | 'error';
  service: string;
  message: string;
  details?: any;
  user_id?: string;
}

export default function AdminPage() {
  const { t } = useTranslation();
  const [stats, setStats] = useState<AdminStats>({
    totalUsers: 0,
    totalCreditPurchases: 0,
    totalRevenue: 0,
    totalGenerations: 0,
    totalPosterGenerations: 0,
    totalOperationalCosts: 0,
    totalProfit: 0,
    recentActivity: []
  });
  const [periodFilter, setPeriodFilter] = useState<'day' | 'week' | 'month' | 'year'>('month');
  const [purchases, setPurchases] = useState<CreditPurchase[]>([]);
  const [posterGenerations, setPosterGenerations] = useState<PosterGeneration[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'overview' | 'users' | 'generations' | 'regeneration' | 'purchases' | 'vouchers' | 'messages' | 'logs' | 'settings'>('overview');
  const [isRegenerating, setIsRegenerating] = useState<string | null>(null);
  const [user, setUser] = useState<any>(null);
  
  // Estados para busca de usuários
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedUser, setSelectedUser] = useState<UserProfile | null>(null);
  const [isSearching, setIsSearching] = useState(false);
  const [allUsers, setAllUsers] = useState<UserProfile[]>([]);
  
  // Estados para monitor de gerações
  const [generations, setGenerations] = useState<Generation[]>([]);
  const [generationFilters, setGenerationFilters] = useState({
    status: '',
    type: '',
    platform: '',
    dateRange: '7d'
  });
  
  // Estados para regeneração
  const [failedGenerations, setFailedGenerations] = useState<Generation[]>([]);
  const [selectedForRegeneration, setSelectedForRegeneration] = useState<string[]>([]);
  
  // Estados para logs
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [logFilters, setLogFilters] = useState({
    level: '',
    service: '',
    timeRange: '1h'
  });
  
  // Estados para mensagens de contato
  const [contactMessages, setContactMessages] = useState<any[]>([]);
  const [messageStats, setMessageStats] = useState({
    total: 0,
    pending: 0,
    read: 0,
    replied: 0,
    emailSent: 0
  });
  const [messageFilters, setMessageFilters] = useState({
    status: '',
    type: '',
    emailSent: ''
  });
  
  // Estados para dados reais dos gráficos
  const [activityData, setActivityData] = useState<any[]>([]);
  const [platformData, setPlatformData] = useState<any[]>([]);
  const [successRates, setSuccessRates] = useState({
    covers: 0,
    posters: 0,
    overall: 0
  });

  // Hook para gerenciar vouchers
  const {
    vouchers,
    stats: voucherStats,
    loading: vouchersLoading,
    listVouchers,
    getVoucherStats,
    createVoucher,
    updateVoucher,
    deleteVoucher,
  } = useAdminVouchers();

  // Estados para modal de criação de voucher
  const [showCreateVoucherModal, setShowCreateVoucherModal] = useState(false);
  const [voucherFormData, setVoucherFormData] = useState({
    credits: 10,
    maxUses: 1,
    emailRestriction: '',
    expiresAt: '',
    description: '',
    customCode: ''
  });

  const navigate = useNavigate();

  useEffect(() => {
    checkAdminAccess();
    loadAdminData();
    loadAllUsers(); // Carregar usuários para os insights
    loadRealInsightsData(); // Carregar dados reais para gráficos
  }, []);

  // Carregar dados específicos quando trocar de aba
  useEffect(() => {
    console.log('🔄 Aba ativa mudou para:', activeTab);
    
    if (activeTab === 'generations') {
      loadGenerations();
    } else if (activeTab === 'logs') {
      loadLogs();
    } else if (activeTab === 'users') {
      console.log('📋 Carregando usuários...');
      loadAllUsers();
    } else if (activeTab === 'vouchers') {
      listVouchers();
      getVoucherStats();
    } else if (activeTab === 'messages') {
      loadContactMessages();
    }
  }, [activeTab]);

  // Recarregar dados quando mudar o filtro de período
  useEffect(() => {
    if (activeTab === 'overview') {
      loadAdminDataByPeriod();
      loadRealInsightsData(); // Recarregar insights também
    }
  }, [periodFilter]);

  // Verificar se o usuário é admin autorizado
  const checkAdminAccess = async () => {
    try {
      const { data: { user }, error } = await supabase.auth.getUser();
      if (error || !user || user.user_metadata?.role !== 'admin') {
        window.location.href = '/';
        return;
      }
      setUser(user);
    } catch (error) {
      console.error('Erro ao verificar acesso admin:', error);
      window.location.href = '/';
    }
  };

  const loadAdminData = async () => {
    try {
      setIsLoading(true);

      // Carregar estatísticas gerais
      const [
        userStatsResult,
        purchasesResult,
        { count: generationsCount },
        { data: postersData }
      ] = await Promise.all([
        supabase.functions.invoke('admin-users', { body: { action: 'user-stats' } }),
        supabase.functions.invoke('admin-users', { body: { action: 'user-purchases' } }),
        supabase
          .from('cover_generations')
          .select('*', { count: 'exact', head: true }),
        supabase
          .from('poster_images')
          .select('*')
          .order('created_at', { ascending: false })
          .limit(50)
      ]);

      const usersCount = userStatsResult.data?.total_users || 0;
      const purchasesData = purchasesResult.data?.purchases || [];

      // Calcular receita total
      const totalRevenue = purchasesData.reduce((sum: number, purchase: any) => 
        purchase.status === 'completed' ? sum + (purchase.amount / 100) : sum, 0
      ) || 0;

      const posterCount = postersData?.length || 0;
      const totalGenerationsCount = generationsCount || 0;
      
      // Calcular custos operacionais - APENAS capas via Replicate custam R$ 0,50
      // Posters Canva via N8N são gratuitos
      const totalOperationalCosts = totalGenerationsCount * 0.5;
      const totalProfit = totalRevenue - totalOperationalCosts;

      setStats({
        totalUsers: usersCount || 0,
        totalCreditPurchases: purchasesData.filter((p: any) => p.status === 'completed').length || 0,
        totalRevenue,
        totalGenerations: totalGenerationsCount,
        totalPosterGenerations: posterCount,
        totalOperationalCosts,
        totalProfit,
        recentActivity: [] // Implementar se necessário
      });

      setPurchases(purchasesData || []);
      setPosterGenerations(postersData || []);

    } catch (error) {
      console.error('Erro ao carregar dados admin:', error);
      toast.error('Erro ao carregar dados de administração');
    } finally {
      setIsLoading(false);
    }
  };

  // Função para carregar dados filtrados por período
  const loadAdminDataByPeriod = async () => {
    try {
      setIsLoading(true);

      // Calcular data de início baseada no filtro
      const now = new Date();
      let startDate = new Date();
      
      switch (periodFilter) {
        case 'day':
          startDate.setHours(0, 0, 0, 0);
          break;
        case 'week':
          startDate.setDate(now.getDate() - 7);
          break;
        case 'month':
          startDate.setMonth(now.getMonth() - 1);
          break;
        case 'year':
          startDate.setFullYear(now.getFullYear() - 1);
          break;
      }

      // Carregar dados filtrados por período
      const [
        userStatsResult,
        purchasesResult,
        { count: generationsCount },
        { data: postersData }
      ] = await Promise.all([
        supabase.functions.invoke('admin-users', { body: { action: 'user-stats' } }),
        supabase.functions.invoke('admin-users', { body: { action: 'user-purchases' } }),
        supabase
          .from('cover_generations')
          .select('*', { count: 'exact', head: true })
          .gte('created_at', startDate.toISOString()),
        supabase
          .from('poster_images')
          .select('*')
          .gte('created_at', startDate.toISOString())
          .order('created_at', { ascending: false })
      ]);

      const usersCount = userStatsResult.data?.total_users || 0;
      const allPurchases = purchasesResult.data?.purchases || [];
      
      // Filtrar compras por período
      const purchasesData = allPurchases.filter((purchase: any) => {
        const purchaseDate = new Date(purchase.created_at);
        return purchaseDate >= startDate;
      });

      // Calcular receita do período
      const totalRevenue = purchasesData.reduce((sum: number, purchase: any) => 
        purchase.status === 'completed' ? sum + (purchase.amount / 100) : sum, 0
      ) || 0;

      const posterCount = postersData?.length || 0;
      const totalGenerationsCount = generationsCount || 0;
      
      // Calcular custos operacionais - APENAS capas via Replicate custam R$ 0,50
      // Posters Canva via N8N são gratuitos
      const totalOperationalCosts = totalGenerationsCount * 0.5;
      const totalProfit = totalRevenue - totalOperationalCosts;

      setStats({
        totalUsers: usersCount || 0,
        totalCreditPurchases: purchasesData?.filter((p: any) => p.status === 'completed').length || 0,
        totalRevenue,
        totalGenerations: totalGenerationsCount,
        totalPosterGenerations: posterCount,
        totalOperationalCosts,
        totalProfit,
        recentActivity: []
      });

    } catch (error) {
      console.error('Erro ao carregar dados por período:', error);
      toast.error('Erro ao carregar dados do período selecionado');
    } finally {
      setIsLoading(false);
    }
  };

  const handleRegeneratePoster = async (posterId: string, payload: any) => {
    try {
      setIsRegenerating(posterId);
      
      const { data, error } = await supabase.functions.invoke('canva-poster', {
        body: payload
      });

      if (error) throw error;

      toast.success('Poster regenerado com sucesso!');
      loadAdminData(); // Recarregar dados
      
    } catch (error) {
      console.error('Erro ao regenerar poster:', error);
      toast.error('Erro ao regenerar poster');
    } finally {
      setIsRegenerating(null);
    }
  };

  // Função para buscar usuário por email ou ID
  const searchUser = async (term: string) => {
    if (!term.trim()) return;
    
    try {
      setIsSearching(true);
      
      // Buscar por user_id diretamente nas tabelas
      const { data: userData } = await supabase
        .from('credit_purchases')
        .select('user_id')
        .eq('user_id', term)
        .limit(1);
      
      if (userData?.length) {
        await loadUserProfile(userData[0].user_id);
        return;
      }
      
      // Buscar por parte do user_id se não encontrou exato
      const { data: partialData } = await supabase
        .from('credit_purchases')
        .select('user_id')
        .ilike('user_id', `%${term}%`)
        .limit(1);
        
      if (partialData?.length) {
        await loadUserProfile(partialData[0].user_id);
        return;
      }
      
      toast.error('Usuário não encontrado');
      
    } catch (error) {
      console.error('Erro ao buscar usuário:', error);
      toast.error('Erro ao buscar usuário');
    } finally {
      setIsSearching(false);
    }
  };

  // Função para carregar perfil completo do usuário
  const loadUserProfile = async (userId: string) => {
    try {
      // Buscar dados do usuário de várias tabelas
      const [
        { data: creditPurchases },
        { data: creditUsage },
        { data: coverGenerations },
        { data: posterImages }
      ] = await Promise.all([
        supabase.from('credit_purchases').select('*').eq('user_id', userId).order('created_at', { ascending: false }),
        supabase.from('credit_usage').select('*').eq('user_id', userId).order('created_at', { ascending: false }),
        supabase.from('cover_generations').select('*').eq('user_id', userId).order('created_at', { ascending: false }),
        supabase.from('poster_images').select('*').eq('user_id', userId).order('created_at', { ascending: false })
      ]);

      // Calcular créditos
      const totalPurchased = creditPurchases?.filter(p => p.status === 'completed').reduce((sum, p) => sum + p.credits, 0) || 0;
      const totalUsed = creditUsage?.reduce((sum, u) => sum + u.credits_used, 0) || 0;
      const availableCredits = totalPurchased - totalUsed;

      // Gerações falhadas
      const failedGens = coverGenerations?.filter(g => g.status === 'failed') || [];

      const userProfile: UserProfile = {
        user_id: userId,
        user_email: userId, // Usar user_id como identificador
        user_name: userId,
        total_credits: totalPurchased,
        total_spent: 0, // Calcular total gasto
        total_generations: coverGenerations?.length || 0,
        failed_generations: failedGens.length,
        first_purchase: creditPurchases?.[0]?.created_at || '',
        last_activity: Math.max(
          new Date(coverGenerations?.[0]?.created_at || 0).getTime(),
          new Date(posterImages?.[0]?.created_at || 0).getTime()
        ),
        created_at: creditPurchases?.[0]?.created_at || coverGenerations?.[0]?.created_at || ''
      };

      setSelectedUser(userProfile);
      
    } catch (error) {
      console.error('Erro ao carregar perfil do usuário:', error);
      toast.error('Erro ao carregar dados do usuário');
    }
  };

  // Função para carregar todas as gerações
  const loadGenerations = async () => {
    try {
      console.log('🔄 Chamando Edge Function admin-users para gerações...');
      
      // Verificar se o usuário está autenticado antes de fazer a chamada
      const { data: { user: currentUser }, error: authError } = await supabase.auth.getUser();
      if (authError || !currentUser) {
        console.error('❌ Usuário não autenticado ao carregar gerações:', authError);
        toast.error('Usuário não autenticado');
        return;
      }
      
      if (currentUser.user_metadata?.role !== 'admin') {
        console.error('❌ Usuário não é admin:', currentUser.user_metadata?.role);
        toast.error('Acesso negado: usuário não é administrador');
        return;
      }

      console.log('✅ Usuário autenticado como admin, fazendo chamada...');
      
      // Usar Edge Function para acessar gerações com dados reais dos usuários
      const { data, error } = await supabase.functions.invoke('admin-users', {
        body: { action: 'user-generations' }
      });

      console.log('🔍 Resposta da Edge Function:', { data, error });

      if (error) {
        console.error('❌ Erro da Edge Function:', error);
        
        // Se for erro 401, tentar novamente após um delay
        if (error.message?.includes('401') || error.message?.includes('Unauthorized')) {
          console.log('🔄 Erro 401 detectado, tentando novamente em 2 segundos...');
          setTimeout(async () => {
            try {
              const retryResult = await supabase.functions.invoke('admin-users', {
                body: { action: 'user-generations' }
              });
              
              if (retryResult.error) {
                throw retryResult.error;
              }
              
              const allGenerations: Generation[] = retryResult.data?.generations || [];
              console.log(`✅ ${allGenerations.length} gerações carregadas (retry)`);
              
              setGenerations(allGenerations);
              
              // Filtrar falhadas para regeneração
              const failed = allGenerations.filter(g => g.status === 'failed');
              setFailedGenerations(failed);
              
            } catch (retryError) {
              console.error('❌ Erro no retry:', retryError);
              toast.error('Erro ao carregar gerações (retry): ' + (retryError as Error).message);
            }
          }, 2000);
          return;
        }
        
        throw error;
      }

      const allGenerations: Generation[] = data?.generations || [];
      console.log(`✅ ${allGenerations.length} gerações carregadas`);
      
      setGenerations(allGenerations);
      
      // Filtrar falhadas para regeneração
      const failed = allGenerations.filter(g => g.status === 'failed');
      setFailedGenerations(failed);

    } catch (error) {
      console.error('Erro ao carregar gerações:', error);
      toast.error('Erro ao carregar gerações: ' + (error as Error).message);
    }
  };

  // Função para carregar logs
  const loadLogs = async () => {
    try {
      // Carregar logs baseados em gerações falhadas com informações do usuário
      const { data: errorGens } = await supabase
        .from('cover_generations')
        .select('*')
        .eq('status', 'failed')
        .order('created_at', { ascending: false })
        .limit(100);

      // Buscar informações dos usuários para os logs
      const userIds = [...new Set(errorGens?.map(g => g.user_id) || [])];
      const userInfoPromises = userIds.map(async (userId) => {
        const { data: userInfo } = await supabase
          .from('cover_generations')
          .select('user_name')
          .eq('user_id', userId)
          .limit(1);
        return { userId, userName: userInfo?.[0]?.user_name || 'Nome não disponível' };
      });

      const userInfos = await Promise.all(userInfoPromises);
      const userMap = userInfos.reduce((acc, { userId, userName }) => {
        acc[userId] = userName;
        return acc;
      }, {} as Record<string, string>);

      const mockLogs: LogEntry[] = errorGens?.map(g => ({
        id: g.id,
        timestamp: g.created_at,
        level: 'error' as const,
        service: 'cover-generation',
        message: `Falha na geração de capa - ${g.streaming_platform} | Usuário: ${userMap[g.user_id]}`,
        details: { 
          user_id: g.user_id, 
          platform: g.streaming_platform, 
          user_name: userMap[g.user_id],
          supabase_log_url: `https://supabase.com/dashboard/project/${import.meta.env.VITE_SUPABASE_PROJECT_ID || 'seu-project-id'}/logs/explorer?q=user_id%3A${g.user_id}%20AND%20level%3AERROR`
        },
        user_id: g.user_id
      })) || [];

      setLogs(mockLogs);

    } catch (error) {
      console.error('Erro ao carregar logs:', error);
      toast.error('Erro ao carregar logs');
    }
  };

  // Função para carregar lista de todos os usuários
  const loadAllUsers = async () => {
    try {
      console.log('🔄 Chamando Edge Function admin-users para usuários...');
      
      // Verificar se o usuário está autenticado
      const { data: { user: currentUser }, error: authError } = await supabase.auth.getUser();
      console.log('👤 Usuário atual:', currentUser?.email, currentUser?.user_metadata?.role);
      
      if (authError || !currentUser) {
        console.error('❌ Usuário não autenticado ao carregar usuários:', authError);
        toast.error('Usuário não autenticado');
        return;
      }
      
      if (currentUser.user_metadata?.role !== 'admin') {
        console.error('❌ Usuário não é admin:', currentUser.user_metadata?.role);
        toast.error('Acesso negado: usuário não é administrador');
        return;
      }
      
      console.log('✅ Usuário autenticado como admin, fazendo chamada...');
      
      // Usar Edge Function para acessar dados reais dos usuários
      const { data, error } = await supabase.functions.invoke('admin-users', {
        body: { action: 'list-users' }
      });

      console.log('🔍 Resposta da Edge Function para usuários:', { data, error });

      if (error) {
        console.error('❌ Erro da Edge Function:', error);
        
        // Se for erro 401, tentar novamente após um delay
        if (error.message?.includes('401') || error.message?.includes('Unauthorized')) {
          console.log('🔄 Erro 401 detectado, tentando novamente em 2 segundos...');
          setTimeout(async () => {
            try {
              const retryResult = await supabase.functions.invoke('admin-users', {
                body: { action: 'list-users' }
              });
              
              if (retryResult.error) {
                throw retryResult.error;
              }
              
              const users = retryResult.data?.users || [];
              console.log(`✅ ${users.length} usuários carregados (retry):`, users);
              
              setAllUsers(users);
              
            } catch (retryError) {
              console.error('❌ Erro no retry:', retryError);
              toast.error('Erro ao carregar usuários (retry): ' + (retryError as Error).message);
            }
          }, 2000);
          return;
        }
        
        throw error;
      }

      const users = data?.users || [];
      console.log(`✅ ${users.length} usuários carregados:`, users);
      
      setAllUsers(users);

    } catch (error) {
      console.error('Erro ao carregar usuários:', error);
      toast.error('Erro ao carregar usuários: ' + (error as Error).message);
    }
  };

  // Função para carregar dados reais dos insights
  const loadRealInsightsData = async () => {
    try {
      // 1. Atividade dos últimos 7 dias (dados reais)
      const activityPromises = Array.from({ length: 7 }, (_, i) => {
        const date = new Date();
        date.setDate(date.getDate() - i);
        const startOfDay = new Date(date);
        startOfDay.setHours(0, 0, 0, 0);
        const endOfDay = new Date(date);
        endOfDay.setHours(23, 59, 59, 999);

        return Promise.all([
          supabase
            .from('credit_purchases')
            .select('*', { count: 'exact', head: true })
            .gte('created_at', startOfDay.toISOString())
            .lte('created_at', endOfDay.toISOString()),
          supabase
            .from('cover_generations')
            .select('*', { count: 'exact', head: true })
            .gte('created_at', startOfDay.toISOString())
            .lte('created_at', endOfDay.toISOString())
        ]).then(([purchases, generations]) => ({
          date: date.toLocaleDateString('pt-BR', { weekday: 'short' }),
          purchases: purchases.count || 0,
          generations: generations.count || 0
        }));
      });

      const realActivityData = await Promise.all(activityPromises);
      setActivityData(realActivityData);

      // 2. Distribuição por plataforma (dados reais)
      const { data: platformStats } = await supabase
        .from('cover_generations')
        .select('streaming_platform');

      if (platformStats) {
        const platformCounts = platformStats.reduce((acc: any, gen: any) => {
          const platform = gen.streaming_platform || 'outros';
          acc[platform] = (acc[platform] || 0) + 1;
          return acc;
        }, {});

        const platformData = Object.entries(platformCounts).map(([name, value]) => ({
          name: name.charAt(0).toUpperCase() + name.slice(1),
          value
        }));

        setPlatformData(platformData);
      }

      // 3. Taxa de sucesso real
      const { data: allGenerations } = await supabase
        .from('cover_generations')
        .select('status');

      if (allGenerations) {
        const totalGens = allGenerations.length;
        const successfulGens = allGenerations.filter(g => g.status === 'completed').length;
        const failedGens = allGenerations.filter(g => g.status === 'failed').length;

        setSuccessRates({
          covers: totalGens > 0 ? Math.round((successfulGens / totalGens) * 100) : 0,
          posters: 95, // Assumindo 95% de sucesso para posters Canva
          overall: totalGens > 0 ? Math.round((successfulGens / totalGens) * 100) : 0
        });
      }

    } catch (error) {
      console.error('Erro ao carregar dados dos insights:', error);
    }
  };

  // Função para carregar mensagens de contato
  const loadContactMessages = async () => {
    // Evitar chamadas múltiplas
    if (isLoading) return;
    
    try {
      setIsLoading(true);
      console.log('📬 Carregando mensagens de contato...');
      
      // Carregar mensagens da tabela contact_messages
      const { data: messages, error } = await supabase
        .from('contact_messages')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) {
        console.error('❌ Erro ao carregar mensagens:', error);
        toast.error('Erro ao carregar mensagens de contato');
        return;
      }

      console.log(`✅ ${messages?.length || 0} mensagens carregadas`);
      setContactMessages(messages || []);

      // Calcular estatísticas
      const total = messages?.length || 0;
      const pending = messages?.filter(m => m.status === 'pending').length || 0;
      const read = messages?.filter(m => m.status === 'read').length || 0;
      const replied = messages?.filter(m => m.status === 'replied').length || 0;
      const emailSent = messages?.filter(m => m.email_sent === true).length || 0;

      setMessageStats({
        total,
        pending,
        read,
        replied,
        emailSent
      });

    } catch (error) {
      console.error('Erro ao carregar mensagens:', error);
      toast.error('Erro ao carregar mensagens de contato');
    } finally {
      setIsLoading(false);
    }
  };

  // Função para atualizar status da mensagem
  const updateMessageStatus = async (messageId: string, newStatus: string) => {
    try {
      const { error } = await supabase
        .from('contact_messages')
        .update({ status: newStatus })
        .eq('id', messageId);

      if (error) {
        console.error('Erro ao atualizar status:', error);
        toast.error('Erro ao atualizar status da mensagem');
        return;
      }

      toast.success('Status atualizado com sucesso!');
      loadContactMessages(); // Recarregar mensagens
    } catch (error) {
      console.error('Erro ao atualizar status:', error);
      toast.error('Erro ao atualizar status da mensagem');
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">{t('admin.loading')}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">{t('admin.title')}</h1>
              <p className="text-gray-600">{t('admin.subtitle')}</p>
            </div>
            <div className="text-sm text-gray-500">
              Acesso restrito - Admin
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Tabs */}
        <div className="mb-8">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              {[
                { id: 'overview', label: 'Visão Geral', icon: BarChart3 },
                { id: 'users', label: 'Busca de Usuários', icon: Search },
                { id: 'generations', label: 'Monitor de Gerações', icon: Activity },
                { id: 'regeneration', label: 'Regeneração', icon: RefreshCw },
                { id: 'purchases', label: 'Compras', icon: DollarSign },
                { id: 'vouchers', label: 'Vouchers', icon: Gift },
                { id: 'messages', label: 'Mensagens', icon: FileText },
                { id: 'logs', label: 'Logs do Sistema', icon: FileText },
                { id: 'settings', label: 'Configurações', icon: Settings }
              ].map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id as any)}
                    className={`
                      flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm
                      ${activeTab === tab.id
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }
                    `}
                  >
                    <Icon className="w-4 h-4" />
                    <span>{tab.label}</span>
                  </button>
                );
              })}
            </nav>
          </div>
        </div>

        {/* Overview Tab */}
        {activeTab === 'overview' && (
          <div className="space-y-8">
            {/* Filtro de Período */}
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Período de Análise</h3>
              <div className="flex space-x-4">
                {[
                  { id: 'day', label: 'Hoje' },
                  { id: 'week', label: 'Esta Semana' },
                  { id: 'month', label: 'Este Mês' },
                  { id: 'year', label: 'Este Ano' }
                ].map((period) => (
                  <button
                    key={period.id}
                    onClick={() => setPeriodFilter(period.id as any)}
                    className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                      periodFilter === period.id
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    {period.label}
                  </button>
                ))}
              </div>
            </div>

            {/* Stats Cards - Primeira Linha */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-white rounded-lg shadow p-6"
              >
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <Users className="h-8 w-8 text-blue-600" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">
                        Total de Usuários
                      </dt>
                      <dd className="text-2xl font-bold text-gray-900">
                        {stats.totalUsers}
                      </dd>
                    </dl>
                  </div>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
                className="bg-white rounded-lg shadow p-6"
              >
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <Zap className="h-8 w-8 text-yellow-600" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">
                        Compras de Créditos
                      </dt>
                      <dd className="text-2xl font-bold text-gray-900">
                        {stats.totalCreditPurchases}
                      </dd>
                    </dl>
                  </div>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
                className="bg-white rounded-lg shadow p-6"
              >
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <DollarSign className="h-8 w-8 text-green-600" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">
                        Receita Total
                      </dt>
                      <dd className="text-2xl font-bold text-gray-900">
                        {formatCurrency(stats.totalRevenue)}
                      </dd>
                    </dl>
                  </div>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
                className="bg-white rounded-lg shadow p-6"
              >
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <Image className="h-8 w-8 text-purple-600" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">
                        Gerações de Capas
                      </dt>
                      <dd className="text-2xl font-bold text-gray-900">
                        {stats.totalGenerations}
                      </dd>
                    </dl>
                  </div>
                </div>
              </motion.div>
            </div>

            {/* Stats Cards - Segunda Linha */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
                className="bg-white rounded-lg shadow p-6"
              >
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <RefreshCw className="h-8 w-8 text-red-600" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">
                        Posters Canva
                      </dt>
                      <dd className="text-2xl font-bold text-gray-900">
                        {stats.totalPosterGenerations}
                      </dd>
                    </dl>
                  </div>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5 }}
                className="bg-white rounded-lg shadow p-6"
              >
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <TrendingUp className="h-8 w-8 text-red-600" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">
                        Custos Operacionais
                      </dt>
                      <dd className="text-2xl font-bold text-gray-900">
                        {formatCurrency(stats.totalOperationalCosts)}
                      </dd>
                      <dd className="text-xs text-gray-500">
                        Apenas capas Replicate
                      </dd>
                    </dl>
                  </div>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.6 }}
                className="bg-white rounded-lg shadow p-6"
              >
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className={`h-8 w-8 ${stats.totalProfit >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                      <TrendingUp className="h-8 w-8" />
                    </div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">
                        Lucro Aproximado
                      </dt>
                      <dd className={`text-2xl font-bold ${stats.totalProfit >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                        {formatCurrency(stats.totalProfit)}
                      </dd>
                    </dl>
                  </div>
                </div>
              </motion.div>
            </div>

            {/* Análise Financeira Detalhada */}
            <div className="bg-white rounded-lg shadow p-6 mb-8">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Análise Financeira - {periodFilter === 'day' ? 'Hoje' : periodFilter === 'week' ? 'Esta Semana' : periodFilter === 'month' ? 'Este Mês' : 'Este Ano'}
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="bg-green-50 p-4 rounded-lg">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-green-600 font-medium">{t('admin.totalRevenue')}</p>
                      <p className="text-2xl font-bold text-green-800">{formatCurrency(stats.totalRevenue)}</p>
                    </div>
                    <div className="h-12 w-12 bg-green-200 rounded-full flex items-center justify-center">
                      <DollarSign className="h-6 w-6 text-green-600" />
                    </div>
                  </div>
                </div>
                
                <div className="bg-red-50 p-4 rounded-lg">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-red-600 font-medium">{t('admin.operationalCosts')}</p>
                      <p className="text-2xl font-bold text-red-800">{formatCurrency(stats.totalOperationalCosts)}</p>
                      <p className="text-xs text-red-500">{stats.totalGenerations} gerações × R$ 0,50</p>
                    </div>
                    <div className="h-12 w-12 bg-red-200 rounded-full flex items-center justify-center">
                      <TrendingUp className="h-6 w-6 text-red-600" />
                    </div>
                  </div>
                </div>
                
                <div className={`${stats.totalProfit >= 0 ? 'bg-blue-50' : 'bg-red-50'} p-4 rounded-lg`}>
                  <div className="flex items-center justify-between">
                    <div>
                      <p className={`text-sm font-medium ${stats.totalProfit >= 0 ? 'text-blue-600' : 'text-red-600'}`}>
                        Lucro {stats.totalProfit >= 0 ? 'Líquido' : '(Prejuízo)'}
                      </p>
                      <p className={`text-2xl font-bold ${stats.totalProfit >= 0 ? 'text-blue-800' : 'text-red-800'}`}>
                        {formatCurrency(Math.abs(stats.totalProfit))}
                      </p>
                      <p className={`text-xs ${stats.totalProfit >= 0 ? 'text-blue-500' : 'text-red-500'}`}>
                        Margem: {stats.totalRevenue > 0 ? ((stats.totalProfit / stats.totalRevenue) * 100).toFixed(1) : 0}%
                      </p>
                    </div>
                    <div className={`h-12 w-12 ${stats.totalProfit >= 0 ? 'bg-blue-200' : 'bg-red-200'} rounded-full flex items-center justify-center`}>
                      <TrendingUp className={`h-6 w-6 ${stats.totalProfit >= 0 ? 'text-blue-600' : 'text-red-600'}`} />
                    </div>
                  </div>
                </div>
              </div>
              
                             {/* Breakdown de Custos */}
               <div className="mt-6 pt-6 border-t border-gray-200">
                 <h4 className="text-md font-medium text-gray-900 mb-3">Breakdown de Custos por Geração</h4>
                 <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                   <div className="bg-red-50 p-3 rounded border-l-4 border-red-500">
                     <div className="text-sm text-red-600 font-medium">💰 Capas via Replicate (CUSTAM)</div>
                     <div className="text-lg font-semibold text-red-800">{stats.totalGenerations} gerações</div>
                     <div className="text-sm text-red-600">
                       Custo: {formatCurrency(stats.totalGenerations * 0.5)} (R$ 0,50 cada)
                     </div>
                   </div>
                   <div className="bg-green-50 p-3 rounded border-l-4 border-green-500">
                     <div className="text-sm text-green-600 font-medium">🆓 Posters via N8N (GRATUITOS)</div>
                     <div className="text-lg font-semibold text-green-800">{stats.totalPosterGenerations} gerações</div>
                     <div className="text-sm text-green-600">
                       Custo: R$ 0,00 (gratuito via automação)
                     </div>
                   </div>
                 </div>
                 <div className="mt-3 p-3 bg-blue-50 rounded">
                   <div className="text-xs text-blue-700">
                     <strong>💡 Explicação:</strong> Apenas capas geradas via Supabase Function + Replicate custam R$ 0,50. 
                     Posters Canva são gerados via N8N + API normal (gratuito).
                   </div>
                 </div>
               </div>
            </div>

            {/* Insights e Gráficos Adicionais */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Gráfico de Atividade Recente */}
              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Atividade dos Últimos 7 Dias</h3>
                <div className="space-y-3">
                  {activityData.map((day, i) => (
                    <div key={i} className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">{day.date}</span>
                      <div className="flex space-x-4">
                        <div className="flex items-center">
                          <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                          <span className="text-xs text-gray-500">{day.purchases} compras</span>
                        </div>
                        <div className="flex items-center">
                          <div className="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
                          <span className="text-xs text-gray-500">{day.generations} gerações</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Distribuição por Plataforma */}
              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Distribuição por Plataforma</h3>
                <div className="space-y-4">
                  {platformData.length > 0 ? platformData.map((platform) => (
                    <div key={platform.name} className="flex items-center">
                      <div className="w-16 text-sm text-gray-600">{platform.name}</div>
                      <div className="flex-1 mx-4">
                        <div className="bg-gray-200 rounded-full h-2">
                          <div 
                            className={`${platform.color} h-2 rounded-full transition-all duration-300`}
                            style={{ width: `${platform.value}%` }}
                          ></div>
                        </div>
                      </div>
                      <div className="text-sm text-gray-900 font-medium">{platform.value}%</div>
                    </div>
                  )) : (
                    <div className="text-center text-gray-500 py-4">
                      Carregando dados de plataformas...
                    </div>
                  )}
                </div>
              </div>

              {/* Taxa de Sucesso */}
              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Taxa de Sucesso</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-green-600">{successRates.covers}%</div>
                    <div className="text-sm text-gray-500">{t('admin.covers')}</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-blue-600">{successRates.posters}%</div>
                    <div className="text-sm text-gray-500">{t('admin.posters')}</div>
                  </div>
                </div>
                <div className="mt-4 pt-4 border-t border-gray-200">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-600">{successRates.overall}%</div>
                    <div className="text-sm text-gray-500">{t('admin.overallSuccessRate')}</div>
                  </div>
                </div>
              </div>

              {/* Top Usuários */}
              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Top 5 Usuários por Receita</h3>
                <div className="space-y-3">
                  {allUsers.slice(0, 5).map((user, index) => (
                    <div key={user.user_id} className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold text-white mr-3 ${
                          index === 0 ? 'bg-yellow-500' : 
                          index === 1 ? 'bg-gray-400' : 
                          index === 2 ? 'bg-yellow-600' : 'bg-gray-300'
                        }`}>
                          {index + 1}
                        </div>
                        <span className="text-sm text-gray-900">{user.user_id.slice(0, 8)}...</span>
                      </div>
                      <div className="text-sm font-medium text-gray-900">
                        {formatCurrency(user.total_spent)}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Alertas e Notificações */}
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Alertas do Sistema</h3>
              <div className="space-y-3">
                <div className="p-3 bg-yellow-50 rounded-lg">
                  <div className="flex items-center mb-2">
                    <AlertTriangle className="w-5 h-5 text-yellow-600 mr-3" />
                    <div className="text-sm font-medium text-yellow-800">
                      {allUsers.reduce((sum, user) => sum + user.failed_generations, 0)} gerações falharam nas últimas 24h
                    </div>
                  </div>
                  <div className="ml-8 space-y-1">
                    {allUsers
                      .filter(user => user.failed_generations > 0)
                      .slice(0, 3)
                      .map(user => (
                        <div key={user.user_id} className="flex items-center justify-between text-xs">
                          <span className="text-yellow-700">
                            {user.user_name} - {user.failed_generations} falha(s)
                          </span>
                          <div className="flex space-x-2">
                            <button
                              onClick={() => searchUser(user.user_id)}
                              className="text-blue-600 hover:text-blue-800 underline"
                            >
                              Ver Perfil
                            </button>
                            <a
                              href={`https://supabase.com/dashboard/project/${import.meta.env.VITE_SUPABASE_PROJECT_ID || 'seu-project-id'}/logs/explorer?q=user_id%3A${user.user_id}%20AND%20level%3AERROR`}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-yellow-600 hover:text-yellow-800 underline"
                            >
                              Ver Logs
                            </a>
                          </div>
                        </div>
                      ))}
                    {allUsers.filter(user => user.failed_generations > 0).length > 3 && (
                      <div className="text-xs text-yellow-600 italic">
                        + {allUsers.filter(user => user.failed_generations > 0).length - 3} usuários com falhas...
                      </div>
                    )}
                  </div>
                </div>

                <div className="flex items-center p-3 bg-green-50 rounded-lg">
                  <CheckCircle className="w-5 h-5 text-green-600 mr-3" />
                  <div>
                    <div className="text-sm font-medium text-green-800">
                      Sistema operando normalmente
                    </div>
                    <div className="text-xs text-green-600">
                      Taxa de sucesso acima de 90% mantida
                    </div>
                  </div>
                </div>

                <div className="flex items-center p-3 bg-blue-50 rounded-lg">
                  <TrendingUp className="w-5 h-5 text-blue-600 mr-3" />
                  <div>
                    <div className="text-sm font-medium text-blue-800">
                      Receita total: {formatCurrency(stats.totalRevenue)}
                    </div>
                    <div className="text-xs text-blue-600">
                      Lucro: {formatCurrency(stats.totalProfit)} | Margem: {stats.totalRevenue > 0 ? ((stats.totalProfit / stats.totalRevenue) * 100).toFixed(1) : 0}%
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Users Tab - Busca de Usuários */}
        {activeTab === 'users' && (
          <div className="space-y-6">
            {/* Busca */}
            <div className="bg-white shadow rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Buscar Usuário</h3>
              <div className="flex space-x-4">
                <div className="flex-1">
                  <input
                    type="text"
                    placeholder="Digite o User ID ou parte dele..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    onKeyPress={(e) => e.key === 'Enter' && searchUser(searchTerm)}
                  />
                </div>
                <button
                  onClick={() => searchUser(searchTerm)}
                  disabled={isSearching}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 flex items-center space-x-2"
                >
                  {isSearching ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  ) : (
                    <Search className="w-4 h-4" />
                  )}
                  <span>{t('admin.search')}</span>
                </button>
              </div>
            </div>

            {/* Perfil do Usuário */}
            {selectedUser && (
              <div className="bg-white shadow rounded-lg p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Perfil do Usuário</h3>
                
                {/* Info Básica */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="text-sm text-gray-500">{t('admin.userId')}</div>
                    <div className="text-lg font-semibold">{selectedUser.user_id}</div>
                  </div>
                  <div className="bg-green-50 p-4 rounded-lg">
                    <div className="text-sm text-gray-500">{t('admin.availableCredits')}</div>
                    <div className="text-lg font-semibold text-green-600">{selectedUser.total_credits}</div>
                  </div>
                  <div className="bg-blue-50 p-4 rounded-lg">
                    <div className="text-sm text-gray-500">{t('admin.totalGenerations')}</div>
                    <div className="text-lg font-semibold text-blue-600">{selectedUser.total_generations}</div>
                  </div>
                  <div className="bg-purple-50 p-4 rounded-lg">
                    <div className="text-sm text-gray-500">{t('admin.totalPosters')}</div>
                    <div className="text-lg font-semibold text-purple-600">{selectedUser.total_generations}</div>
                  </div>
                </div>

                {/* Ações Rápidas */}
                <div className="flex space-x-4 mb-6">
                  <button
                    onClick={() => loadGenerations()}
                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center space-x-2"
                  >
                    <Activity className="w-4 h-4" />
                    <span>{t('admin.viewGenerations')}</span>
                  </button>
                  <button
                    onClick={() => setActiveTab('regeneration')}
                    className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center space-x-2"
                  >
                    <RefreshCw className="w-4 h-4" />
                    <span>{t('admin.regenerate')}</span>
                  </button>
                </div>

                {/* Compras Recentes */}
                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <h4 className="text-md font-medium text-gray-900 mb-4">{t('admin.recentPurchases')}</h4>
                  <div className="space-y-2">
                    <p className="text-sm text-gray-500">Dados de compras não disponíveis nesta visualização</p>
                  </div>
                </div>

                {/* Gerações Falhadas */}
                {selectedUser.failed_generations > 0 && (
                  <div className="bg-white border border-gray-200 rounded-lg p-6">
                    <h4 className="text-md font-medium text-gray-900 mb-4">{t('admin.failedGenerations')}</h4>
                    <div className="space-y-2">
                      <p className="text-sm text-gray-500">{selectedUser.failed_generations} gerações falharam</p>
                    </div>
                  </div>
                )}
              </div>
                         )}

             {/* Tabela de Todos os Usuários */}
             <div className="bg-white shadow rounded-lg">
               <div className="px-6 py-4 border-b border-gray-200">
                 <h3 className="text-lg font-medium text-gray-900">{t('admin.allUsers', { count: allUsers.length })}</h3>
               </div>
               <div className="overflow-x-auto">
                 <table className="min-w-full divide-y divide-gray-200">
                   <thead className="bg-gray-50">
                     <tr>
                       <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{t('admin.name')}</th>
                       <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{t('admin.email')}</th>
                       <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{t('admin.userId')}</th>
                       <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{t('admin.creditsPurchased')}</th>
                       <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{t('admin.totalSpent')}</th>
                       <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{t('admin.generations')}</th>
                       <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{t('admin.failures')}</th>
                       <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{t('admin.lastActivity')}</th>
                       <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{t('admin.actions')}</th>
                     </tr>
                   </thead>
                   <tbody className="bg-white divide-y divide-gray-200">
                     {(allUsers || []).map((user) => (
                       <tr key={user.user_id} className="hover:bg-gray-50">
                         <td className="px-6 py-4 whitespace-nowrap">
                           <div className="text-sm font-medium text-gray-900">
                             {user.user_name}
                           </div>
                         </td>
                         <td className="px-6 py-4 whitespace-nowrap">
                           <div className="text-sm text-gray-900">
                             {user.user_email.length > 25 ? `${user.user_email.substring(0, 25)}...` : user.user_email}
                           </div>
                         </td>
                         <td className="px-6 py-4 whitespace-nowrap">
                           <div className="text-sm text-gray-500">
                             {user.user_id.length > 15 ? `${user.user_id.substring(0, 15)}...` : user.user_id}
                           </div>
                         </td>
                         <td className="px-6 py-4 whitespace-nowrap">
                           <div className="text-sm text-gray-900">{user.total_credits}</div>
                         </td>
                         <td className="px-6 py-4 whitespace-nowrap">
                           <div className="text-sm text-gray-900">
                             {formatCurrency(user.total_spent)}
                           </div>
                         </td>
                         <td className="px-6 py-4 whitespace-nowrap">
                           <div className="text-sm text-gray-900">{user.total_generations}</div>
                         </td>
                         <td className="px-6 py-4 whitespace-nowrap">
                           {user.failed_generations > 0 ? (
                             <span className="px-2 py-1 text-xs rounded-full bg-red-100 text-red-800">
                               {user.failed_generations}
                             </span>
                           ) : (
                             <span className="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">
                               0
                             </span>
                           )}
                         </td>
                         <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                           {formatDate(new Date(user.last_activity).toISOString())}
                         </td>
                         <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                           <button
                             onClick={() => searchUser(user.user_id)}
                             className="text-blue-600 hover:text-blue-900 mr-4"
                           >
                             Ver Perfil
                           </button>
                         </td>
                       </tr>
                     ))}
                   </tbody>
                 </table>
               </div>
             </div>
           </div>
         )}

        {/* Generations Tab - Monitor de Gerações */}
        {activeTab === 'generations' && (
          <div className="space-y-6">


            {/* Filtros */}
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Filtros</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <select
                  value={generationFilters.status}
                  onChange={(e) => setGenerationFilters(prev => ({ ...prev, status: e.target.value }))}
                  className="border border-gray-300 rounded-md px-3 py-2"
                >
                  <option value="">{t('admin.allStatus')}</option>
                  <option value="completed">{t('admin.completed')}</option>
                  <option value="processing">{t('admin.processing')}</option>
                  <option value="failed">{t('admin.failed')}</option>
                </select>
                
                <select
                  value={generationFilters.type}
                  onChange={(e) => setGenerationFilters(prev => ({ ...prev, type: e.target.value }))}
                  className="border border-gray-300 rounded-md px-3 py-2"
                >
                  <option value="">{t('admin.allTypes')}</option>
                  <option value="cover">{t('admin.cover')}</option>
                  <option value="poster">{t('admin.poster')}</option>
                </select>
                
                <select
                  value={generationFilters.platform}
                  onChange={(e) => setGenerationFilters(prev => ({ ...prev, platform: e.target.value }))}
                  className="border border-gray-300 rounded-md px-3 py-2"
                >
                  <option value="">{t('admin.allPlatforms')}</option>
                  <option value="netflix">{t('admin.netflix')}</option>
                  <option value="disney">{t('admin.disney')}</option>
                  <option value="amazon">{t('admin.amazon')}</option>
                  <option value="hbo">{t('admin.hbo')}</option>
                </select>
                
                <button
                  onClick={() => {
                    loadGenerations();
                    toast.success(t('admin.filtersApplied'));
                  }}
                  className="md:col-span-1 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center space-x-2"
                >
                  <RefreshCw className="w-4 h-4" />
                  <span>{t('admin.update')}</span>
                </button>
              </div>
            </div>

            {/* Tabela de Gerações */}
            <div className="bg-white shadow rounded-lg">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">{t('admin.allGenerations', { count: (generations || []).length })}</h3>
              </div>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                                     <thead className="bg-gray-50">
                     <tr>
                       <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">{t('admin.name')}</th>
                       <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">{t('admin.email')}</th>
                       <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">{t('admin.userId')}</th>
                       <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">{t('admin.type')}</th>
                       <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">{t('admin.status')}</th>
                       <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Plataforma</th>
                       <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">{t('admin.date')}</th>
                       <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">{t('admin.actions')}</th>
                     </tr>
                   </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {(generations || [])
                      .filter(g => !generationFilters.status || g?.status === generationFilters.status)
                      .filter(g => !generationFilters.type || g?.type === generationFilters.type)
                      .filter(g => !generationFilters.platform || g?.streaming_platform === generationFilters.platform)
                      .slice(0, 100)
                      .map((generation) => (
                                             <tr key={generation.id}>
                         <td className="px-6 py-4 whitespace-nowrap">
                           <div className="text-sm font-medium text-gray-900">{generation.user_name || 'Nome não disponível'}</div>
                         </td>
                         <td className="px-6 py-4 whitespace-nowrap">
                           <div className="text-sm text-gray-900">
                             {generation.user_email ? 
                               (generation.user_email.length > 25 ? `${generation.user_email.substring(0, 25)}...` : generation.user_email) 
                               : 'Email não disponível'
                             }
                           </div>
                         </td>
                         <td className="px-6 py-4 whitespace-nowrap">
                           <div className="text-sm text-gray-500">
                             {generation.user_id ? 
                               (generation.user_id.length > 15 ? `${generation.user_id.substring(0, 15)}...` : generation.user_id) 
                               : 'ID não disponível'
                             }
                           </div>
                         </td>
                         <td className="px-6 py-4 whitespace-nowrap">
                           <span className={`px-2 py-1 text-xs rounded-full ${
                             generation.type === 'cover' ? 'bg-blue-100 text-blue-800' : 'bg-purple-100 text-purple-800'
                           }`}>
                             {generation.type === 'cover' ? 'Capa' : 'Poster'}
                           </span>
                         </td>
                         <td className="px-6 py-4 whitespace-nowrap">
                           <span className={`px-2 py-1 text-xs rounded-full ${
                             generation.status === 'completed' 
                               ? 'bg-green-100 text-green-800'
                               : generation.status === 'failed'
                               ? 'bg-red-100 text-red-800'
                               : 'bg-yellow-100 text-yellow-800'
                           }`}>
                             {generation.status}
                           </span>
                         </td>
                         <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                           {generation.streaming_platform?.toUpperCase() || 'N/A'}
                         </td>
                         <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                           {formatDate(generation.created_at)}
                         </td>
                         <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                           <button
                             onClick={() => searchUser(generation.user_id)}
                             className="text-blue-600 hover:text-blue-900 mr-4"
                           >
                             Ver Usuário
                           </button>
                         </td>
                       </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}

        {/* Logs Tab */}
        {activeTab === 'logs' && (
          <div className="space-y-6">
            {/* Filtros de Logs */}
            <div className="bg-white shadow rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Filtros de Logs</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <select
                  value={logFilters.level}
                  onChange={(e) => setLogFilters({...logFilters, level: e.target.value})}
                  className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Todos os Níveis</option>
                  <option value="error">Erro</option>
                  <option value="warning">Warning</option>
                  <option value="info">Info</option>
                </select>
                <select
                  value={logFilters.service}
                  onChange={(e) => setLogFilters({...logFilters, service: e.target.value})}
                  className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Todos os Serviços</option>
                  <option value="cover-generation">Geração de Capas</option>
                  <option value="canva-poster">Canva Poster</option>
                  <option value="payment">Pagamentos</option>
                </select>
                <button
                  onClick={loadLogs}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center space-x-2"
                >
                  <RefreshCw className="w-4 h-4" />
                  <span>Atualizar Logs</span>
                </button>
              </div>
            </div>

            {/* Tabela de Logs */}
            <div className="bg-white shadow rounded-lg">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">
                  Logs do Sistema ({(logs || []).length})
                </h3>
              </div>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                                         <tr>
                       <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Timestamp</th>
                       <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Nível</th>
                       <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Serviço</th>
                       <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Mensagem</th>
                       <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Nome</th>
                       <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Email</th>
                       <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">User ID</th>
                       <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Ações</th>
                     </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {(logs || [])
                      .filter(log => !logFilters.level || log?.level === logFilters.level)
                      .filter(log => !logFilters.service || log?.service === logFilters.service)
                      .map((log) => (
                                             <tr key={log.id}>
                         <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                           {formatDate(log.timestamp)}
                         </td>
                         <td className="px-6 py-4 whitespace-nowrap">
                           <span className={`px-2 py-1 text-xs rounded-full ${
                             log.level === 'error' 
                               ? 'bg-red-100 text-red-800'
                               : log.level === 'warning'
                               ? 'bg-yellow-100 text-yellow-800'
                               : 'bg-blue-100 text-blue-800'
                           }`}>
                             {log.level}
                           </span>
                         </td>
                         <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                           {log.service}
                         </td>
                         <td className="px-6 py-4 text-sm text-gray-900">
                           {log.message}
                         </td>
                         <td className="px-6 py-4 whitespace-nowrap">
                           <div className="text-sm font-medium text-gray-900">
                             {log.details?.user_name || 'Nome não disponível'}
                           </div>
                         </td>
                         <td className="px-6 py-4 whitespace-nowrap">
                           <div className="text-sm text-gray-900">
                             {log.user_id && log.user_id.includes('@') ? 
                               (log.user_id.length > 25 ? `${log.user_id.substring(0, 25)}...` : log.user_id) 
                               : 'Email não disponível'
                             }
                           </div>
                         </td>
                         <td className="px-6 py-4 whitespace-nowrap">
                           <div className="text-sm text-gray-500">
                             {log.user_id ? 
                               (log.user_id.length > 15 ? `${log.user_id.substring(0, 15)}...` : log.user_id) 
                               : 'N/A'
                             }
                           </div>
                         </td>
                         <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                           <div className="flex space-x-2">
                             {log.user_id && (
                               <button
                                 onClick={() => searchUser(log.user_id!)}
                                 className="text-blue-600 hover:text-blue-900 text-xs"
                               >
                                 Ver Perfil
                               </button>
                             )}
                             {log.details?.supabase_log_url && (
                               <a
                                 href={log.details.supabase_log_url}
                                 target="_blank"
                                 rel="noopener noreferrer"
                                 className="text-green-600 hover:text-green-900 text-xs"
                               >
                                 Ver Logs
                               </a>
                             )}
                           </div>
                         </td>
                       </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}

        {/* Purchases Tab */}
        {activeTab === 'purchases' && (
          <div className="bg-white shadow rounded-lg">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Compras de Créditos</h3>
            </div>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Nome
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Email
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      User ID
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Créditos
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Valor
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Data
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {(purchases || []).map((purchase) => (
                    <tr key={purchase.id}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          {purchase.user_name || 'Nome não disponível'}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {purchase.user_email || 'Email não disponível'}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-500">
                          {purchase.user_id ? 
                            (purchase.user_id.length > 15 ? `${purchase.user_id.substring(0, 15)}...` : purchase.user_id)
                            : 'ID não disponível'
                          }
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{purchase.credits}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {formatCurrency(purchase.amount / 100)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          purchase.status === 'completed' 
                            ? 'bg-green-100 text-green-800'
                            : purchase.status === 'pending'
                            ? 'bg-yellow-100 text-yellow-800'
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {purchase.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {formatDate(purchase.created_at)}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {/* Regeneration Tab - Sistema de Regeneração */}
        {activeTab === 'regeneration' && (
          <div className="bg-white shadow rounded-lg">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">
                Posters Canva - Sistema de Regeneração
              </h3>
              <p className="text-sm text-gray-600 mt-1">
                Clique em "Regenerar" para recriar um poster com as mesmas configurações originais
              </p>
            </div>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Nome
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Email
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      User ID
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Template/Plataforma
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Preview
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Data
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Ações
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {(posterGenerations || []).map((poster) => (
                    <tr key={poster.id}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          {poster.user_name || 'Nome não informado'}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {poster.user_id && poster.user_id.includes('@') ? 
                            (poster.user_id.length > 25 ? `${poster.user_id.substring(0, 25)}...` : poster.user_id) 
                            : 'Email não disponível'
                          }
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-500">
                          {poster.user_id ? 
                            (poster.user_id.length > 15 ? `${poster.user_id.substring(0, 15)}...` : poster.user_id)
                            : 'ID não disponível'
                          }
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex flex-col">
                          <div className="text-sm text-gray-900">
                            Template: {poster.template_id}
                          </div>
                          <div className="text-sm text-gray-500">
                            {poster.streaming_platform?.toUpperCase() || 'N/A'}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {poster.poster_url ? (
                          <img 
                            src={poster.poster_url} 
                            alt="Preview" 
                            className="h-16 w-24 object-cover rounded border"
                          />
                        ) : (
                          <div className="h-16 w-24 bg-gray-200 rounded border flex items-center justify-center">
                            <span className="text-xs text-gray-500">Sem preview</span>
                          </div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {formatDate(poster.created_at)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                        {poster.poster_url && (
                          <a
                            href={poster.poster_url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-600 hover:text-blue-900 inline-flex items-center"
                          >
                            <Eye className="w-4 h-4 mr-1" />
                            Ver
                          </a>
                        )}
                        {poster.request_payload && (
                          <button
                            onClick={() => handleRegeneratePoster(poster.id, poster.request_payload)}
                            disabled={isRegenerating === poster.id}
                            className="text-green-600 hover:text-green-900 inline-flex items-center disabled:opacity-50"
                          >
                            {isRegenerating === poster.id ? (
                              <>
                                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-green-600 mr-1"></div>
                                Gerando...
                              </>
                            ) : (
                              <>
                                <RefreshCw className="w-4 h-4 mr-1" />
                                Regenerar
                              </>
                            )}
                          </button>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {/* Vouchers Tab */}
        {activeTab === 'vouchers' && (
          <div className="space-y-6">
            {/* Header e Stats */}
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex justify-between items-center mb-6">
                <div>
                  <h3 className="text-lg font-medium text-gray-900">Gerenciamento de Vouchers</h3>
                  <p className="text-sm text-gray-600">Crie e gerencie códigos promocionais para distribuir créditos</p>
                </div>
                <button
                  onClick={() => setShowCreateVoucherModal(true)}
                  className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center space-x-2"
                >
                  <Gift className="w-4 h-4" />
                  <span>Criar Voucher</span>
                </button>
              </div>

              {/* Stats Cards */}
              {voucherStats && (
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                  <div className="bg-blue-50 p-4 rounded-lg">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-blue-600 font-medium">Total Vouchers</p>
                        <p className="text-2xl font-bold text-blue-800">{voucherStats.totalVouchers}</p>
                      </div>
                      <Gift className="h-8 w-8 text-blue-600" />
                    </div>
                  </div>
                  <div className="bg-green-50 p-4 rounded-lg">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-green-600 font-medium">Ativos</p>
                        <p className="text-2xl font-bold text-green-800">{voucherStats.activeVouchers}</p>
                      </div>
                      <CheckCircle className="h-8 w-8 text-green-600" />
                    </div>
                  </div>
                  <div className="bg-purple-50 p-4 rounded-lg">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-purple-600 font-medium">Resgates</p>
                        <p className="text-2xl font-bold text-purple-800">{voucherStats.totalRedemptions}</p>
                      </div>
                      <UserCheck className="h-8 w-8 text-purple-600" />
                    </div>
                  </div>
                  <div className="bg-yellow-50 p-4 rounded-lg">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-yellow-600 font-medium">Créditos Dados</p>
                        <p className="text-2xl font-bold text-yellow-800">{voucherStats.totalCreditsRedeemed}</p>
                      </div>
                      <Zap className="h-8 w-8 text-yellow-600" />
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Lista de Vouchers */}
            <div className="bg-white shadow rounded-lg">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">
                  Vouchers Criados ({vouchers.length})
                </h3>
              </div>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Código
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Créditos
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Usos
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Restrições
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Criado em
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Ações
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {vouchers.map((voucher) => (
                      <tr key={voucher.id}>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-mono font-bold text-gray-900">
                            {voucher.code}
                          </div>
                          {voucher.description && (
                            <div className="text-xs text-gray-500">{voucher.description}</div>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{voucher.credits}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">
                            {voucher.current_uses} / {voucher.max_uses}
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
                            <div 
                              className="bg-blue-600 h-2 rounded-full" 
                              style={{ width: `${(voucher.current_uses / voucher.max_uses) * 100}%` }}
                            />
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">
                            {voucher.email_restriction && (
                              <div className="text-xs text-gray-600">📧 {voucher.email_restriction}</div>
                            )}
                            {voucher.expires_at && (
                              <div className="text-xs text-gray-600">⏰ {formatDate(voucher.expires_at)}</div>
                            )}
                            {!voucher.email_restriction && !voucher.expires_at && (
                              <span className="text-gray-500">Nenhuma</span>
                            )}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            voucher.status === 'active' 
                              ? 'bg-green-100 text-green-800'
                              : voucher.status === 'expired'
                              ? 'bg-gray-100 text-gray-800'
                              : 'bg-red-100 text-red-800'
                          }`}>
                            {voucher.status === 'active' ? 'Ativo' : 
                             voucher.status === 'expired' ? 'Expirado' : 'Desabilitado'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {formatDate(voucher.created_at)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex space-x-2">
                            {voucher.status === 'active' ? (
                              <button
                                onClick={() => updateVoucher(voucher.id, 'disabled')}
                                className="text-red-600 hover:text-red-900"
                                disabled={vouchersLoading}
                              >
                                Desabilitar
                              </button>
                            ) : voucher.status === 'disabled' ? (
                              <button
                                onClick={() => updateVoucher(voucher.id, 'active')}
                                className="text-green-600 hover:text-green-900"
                                disabled={vouchersLoading}
                              >
                                Ativar
                              </button>
                            ) : null}
                            
                            {voucher.current_uses === 0 && (
                              <button
                                onClick={() => deleteVoucher(voucher.id)}
                                className="text-red-600 hover:text-red-900"
                                disabled={vouchersLoading}
                              >
                                Deletar
                              </button>
                            )}
                            
                            {voucher.voucher_redemptions.length > 0 && (
                              <span className="text-blue-600 text-xs">
                                {voucher.voucher_redemptions.length} resgate(s)
                              </span>
                            )}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>

            {/* Modal de Criação de Voucher */}
            {showCreateVoucherModal && (
              <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Criar Novo Voucher</h3>
                  
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Créditos</label>
                      <input
                        type="number"
                        min="1"
                        value={voucherFormData.credits}
                        onChange={(e) => setVoucherFormData({...voucherFormData, credits: parseInt(e.target.value) || 1})}
                        className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Máximo de Usos Globais</label>
                      <input
                        type="number"
                        min="1"
                        value={voucherFormData.maxUses}
                        onChange={(e) => setVoucherFormData({...voucherFormData, maxUses: parseInt(e.target.value) || 1})}
                        className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                      <p className="text-xs text-gray-500 mt-1">
                        Quantas pessoas diferentes podem usar este voucher (cada pessoa só pode usar 1 vez)
                      </p>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Email Específico (opcional)</label>
                      <input
                        type="email"
                        value={voucherFormData.emailRestriction}
                        onChange={(e) => setVoucherFormData({...voucherFormData, emailRestriction: e.target.value})}
                        placeholder="<EMAIL>"
                        className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Data de Expiração (opcional)</label>
                      <input
                        type="datetime-local"
                        value={voucherFormData.expiresAt}
                        onChange={(e) => setVoucherFormData({...voucherFormData, expiresAt: e.target.value})}
                        className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Código Personalizado (opcional)</label>
                      <input
                        type="text"
                        value={voucherFormData.customCode}
                        onChange={(e) => setVoucherFormData({...voucherFormData, customCode: e.target.value.toUpperCase()})}
                        placeholder="Ex: PROMO2024"
                        maxLength={12}
                        className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                      <p className="text-xs text-gray-500 mt-1">Deixe vazio para gerar automaticamente</p>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Descrição (opcional)</label>
                      <input
                        type="text"
                        value={voucherFormData.description}
                        onChange={(e) => setVoucherFormData({...voucherFormData, description: e.target.value})}
                        placeholder="Ex: Promoção de Natal 2024"
                        className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                  </div>
                  
                  <div className="flex justify-end space-x-3 mt-6">
                    <button
                      onClick={() => setShowCreateVoucherModal(false)}
                      className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
                    >
                      Cancelar
                    </button>
                    <button
                      onClick={async () => {
                        try {
                          await createVoucher({
                            credits: voucherFormData.credits,
                            maxUses: voucherFormData.maxUses,
                            emailRestriction: voucherFormData.emailRestriction || undefined,
                            expiresAt: voucherFormData.expiresAt || undefined,
                            description: voucherFormData.description || undefined,
                            customCode: voucherFormData.customCode || undefined
                          });
                          setShowCreateVoucherModal(false);
                          setVoucherFormData({
                            credits: 10,
                            maxUses: 1,
                            emailRestriction: '',
                            expiresAt: '',
                            description: '',
                            customCode: ''
                          });
                          toast.success('Voucher criado com sucesso!');
                          getVoucherStats(); // Refresh stats
                        } catch (error) {
                          toast.error('Erro ao criar voucher: ' + (error as Error).message);
                        }
                      }}
                      disabled={vouchersLoading}
                      className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50"
                    >
                      {vouchersLoading ? 'Criando...' : 'Criar Voucher'}
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

                 {/* Messages Tab */}
         {activeTab === 'messages' && (
           <div className="space-y-6">
             {/* Header e Stats */}
             <div className="bg-white rounded-lg shadow p-6">
               <div className="flex justify-between items-center mb-6">
                 <div>
                   <h3 className="text-lg font-medium text-gray-900">📬 Mensagens de Contato</h3>
                   <p className="text-sm text-gray-600">Visualize e gerencie mensagens enviadas pelos usuários</p>
                 </div>
                 <div className="flex space-x-2">
                   <button
                     onClick={loadContactMessages}
                     className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center space-x-2"
                   >
                     <RefreshCw className="w-4 h-4" />
                     <span>Atualizar</span>
                   </button>
                 </div>
               </div>

               {/* Stats Cards */}
               <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
                 <div className="bg-blue-50 p-4 rounded-lg">
                   <div className="flex items-center justify-between">
                     <div>
                       <p className="text-sm text-blue-600 font-medium">Total</p>
                       <p className="text-2xl font-bold text-blue-800">{messageStats.total}</p>
                     </div>
                     <MessageSquare className="h-8 w-8 text-blue-600" />
                   </div>
                 </div>
                 <div className="bg-yellow-50 p-4 rounded-lg">
                   <div className="flex items-center justify-between">
                     <div>
                       <p className="text-sm text-yellow-600 font-medium">Pendentes</p>
                       <p className="text-2xl font-bold text-yellow-800">{messageStats.pending}</p>
                     </div>
                     <Clock className="h-8 w-8 text-yellow-600" />
                   </div>
                 </div>
                 <div className="bg-green-50 p-4 rounded-lg">
                   <div className="flex items-center justify-between">
                     <div>
                       <p className="text-sm text-green-600 font-medium">Lidas</p>
                       <p className="text-2xl font-bold text-green-800">{messageStats.read}</p>
                     </div>
                     <Eye className="h-8 w-8 text-green-600" />
                   </div>
                 </div>
                 <div className="bg-purple-50 p-4 rounded-lg">
                   <div className="flex items-center justify-between">
                     <div>
                       <p className="text-sm text-purple-600 font-medium">Respondidas</p>
                       <p className="text-2xl font-bold text-purple-800">{messageStats.replied}</p>
                     </div>
                     <UserCheck className="h-8 w-8 text-purple-600" />
                   </div>
                 </div>
                 <div className="bg-indigo-50 p-4 rounded-lg">
                   <div className="flex items-center justify-between">
                     <div>
                       <p className="text-sm text-indigo-600 font-medium">Email Enviado</p>
                       <p className="text-2xl font-bold text-indigo-800">{messageStats.emailSent}</p>
                     </div>
                     <Mail className="h-8 w-8 text-indigo-600" />
                   </div>
                 </div>
               </div>
             </div>

             {/* Lista de Mensagens */}
             <div className="bg-white shadow rounded-lg">
               <div className="px-6 py-4 border-b border-gray-200">
                 <h3 className="text-lg font-medium text-gray-900">
                   Mensagens Recebidas ({contactMessages.length})
                 </h3>
               </div>
               {contactMessages.length === 0 ? (
                 <div className="text-center py-12">
                   <MessageSquare className="mx-auto h-12 w-12 text-gray-400" />
                   <h3 className="mt-2 text-sm font-medium text-gray-900">Nenhuma mensagem encontrada</h3>
                   <p className="mt-1 text-sm text-gray-500">Aguardando mensagens dos usuários...</p>
                 </div>
               ) : (
                 <div className="overflow-x-auto">
                   <table className="min-w-full divide-y divide-gray-200">
                     <thead className="bg-gray-50">
                       <tr>
                         <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                           Contato
                         </th>
                         <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                           Assunto & Tipo
                         </th>
                         <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                           Mensagem
                         </th>
                         <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                           Status
                         </th>
                         <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                           Data
                         </th>
                         <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                           Ações
                         </th>
                       </tr>
                     </thead>
                     <tbody className="bg-white divide-y divide-gray-200">
                       {contactMessages.map((message) => (
                         <tr key={message.id}>
                           <td className="px-6 py-4 whitespace-nowrap">
                             <div>
                               <div className="text-sm font-medium text-gray-900">{message.name}</div>
                               <div className="text-sm text-gray-500">{message.email}</div>
                               {message.email_sent && (
                                 <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                                   📧 Email enviado
                                 </span>
                               )}
                             </div>
                           </td>
                           <td className="px-6 py-4">
                             <div className="text-sm text-gray-900 font-medium">{message.subject}</div>
                             <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${
                               message.type === 'bug' ? 'bg-red-100 text-red-800' :
                               message.type === 'suggestion' ? 'bg-yellow-100 text-yellow-800' :
                               message.type === 'criticism' ? 'bg-blue-100 text-blue-800' :
                               'bg-gray-100 text-gray-800'
                             }`}>
                               {message.type === 'bug' ? '🐛 Bug' :
                                message.type === 'suggestion' ? '💡 Sugestão' :
                                message.type === 'criticism' ? '📝 Feedback' :
                                '📧 Outros'}
                             </span>
                           </td>
                           <td className="px-6 py-4">
                             <div className="text-sm text-gray-900 max-w-xs truncate">
                               {message.message}
                             </div>
                           </td>
                           <td className="px-6 py-4 whitespace-nowrap">
                             <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                               message.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                               message.status === 'read' ? 'bg-blue-100 text-blue-800' :
                               'bg-green-100 text-green-800'
                             }`}>
                               {message.status === 'pending' ? 'Pendente' :
                                message.status === 'read' ? 'Lida' : 'Respondida'}
                             </span>
                           </td>
                           <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                             {formatDate(message.created_at)}
                           </td>
                           <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                             <div className="flex space-x-2">
                               {message.status === 'pending' && (
                                 <button
                                   onClick={() => updateMessageStatus(message.id, 'read')}
                                   className="text-blue-600 hover:text-blue-900"
                                   title="Marcar como lida"
                                 >
                                   👁️
                                 </button>
                               )}
                               {message.status !== 'replied' && (
                                 <button
                                   onClick={() => updateMessageStatus(message.id, 'replied')}
                                   className="text-green-600 hover:text-green-900"
                                   title="Marcar como respondida"
                                 >
                                   ✅
                                 </button>
                               )}
                               <a
                                 href={`mailto:${message.email}?subject=Re: ${message.subject}&body=Olá ${message.name},%0D%0A%0D%0AObrigado pela sua mensagem.%0D%0A%0D%0A----%0D%0AMensagem original:%0D%0A${encodeURIComponent(message.message)}`}
                                 className="text-purple-600 hover:text-purple-900"
                                 title="Responder por email"
                               >
                                 📧
                               </a>
                             </div>
                           </td>
                         </tr>
                       ))}
                     </tbody>
                   </table>
                 </div>
               )}
             </div>
           </div>
         )}

                 {/* Settings Tab */}
         {activeTab === 'settings' && (
           <div className="space-y-6">
             {/* Admin Tools */}
             <div className="bg-white rounded-lg shadow p-6">
               <h3 className="text-lg font-medium text-gray-900 mb-4">Ferramentas de Administração</h3>
               <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                 
                 {/* Prompt Tester Card */}
                 <div className="bg-gradient-to-br from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                   <div className="flex items-center mb-4">
                     <div className="bg-blue-100 rounded-lg p-3">
                       <Settings className="w-6 h-6 text-blue-600" />
                     </div>
                     <div className="ml-4">
                       <h4 className="text-lg font-semibold text-gray-900">Testador de Prompts</h4>
                       <p className="text-sm text-gray-600">Teste e refine prompts de IA</p>
                     </div>
                   </div>
                   <p className="text-sm text-gray-700 mb-4">
                     Configure parâmetros, teste prompts existentes ou crie novos para afinar a geração de capas.
                   </p>
                   <div className="space-y-2 mb-4">
                     <div className="flex items-center text-xs text-gray-600">
                       <CheckCircle className="w-3 h-3 mr-2 text-green-500" />
                       <span>Prompts do banco de dados</span>
                     </div>
                     <div className="flex items-center text-xs text-gray-600">
                       <CheckCircle className="w-3 h-3 mr-2 text-green-500" />
                       <span>Prompts customizados</span>
                     </div>
                     <div className="flex items-center text-xs text-gray-600">
                       <CheckCircle className="w-3 h-3 mr-2 text-green-500" />
                       <span>Configuração completa de parâmetros</span>
                     </div>
                   </div>
                   <button
                     onClick={() => navigate('/admin/prompt-tester')}
                     className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors flex items-center justify-center space-x-2"
                   >
                     <Play className="w-4 h-4" />
                     <span>Abrir Testador</span>
                   </button>
                 </div>

                 {/* Movie Stats Card */}
                 <div className="bg-gradient-to-br from-red-50 to-rose-50 border border-red-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                   <div className="flex items-center mb-4">
                     <div className="bg-red-100 rounded-lg p-3">
                       <TrendingDown className="w-6 h-6 text-red-600" />
                     </div>
                     <div className="ml-4">
                       <h4 className="text-lg font-semibold text-gray-900">Estatísticas de Filmes</h4>
                       <p className="text-sm text-gray-600">Análise de erros e performance</p>
                     </div>
                   </div>
                   <p className="text-sm text-gray-700 mb-4">
                     Veja quais filmes geram mais erros E005 e suas taxas de sucesso para otimizar o sistema.
                   </p>
                   <div className="space-y-2 mb-4">
                     <div className="flex items-center text-xs text-gray-600">
                       <CheckCircle className="w-3 h-3 mr-2 text-green-500" />
                       <span>Ranking de erros E005</span>
                     </div>
                     <div className="flex items-center text-xs text-gray-600">
                       <CheckCircle className="w-3 h-3 mr-2 text-green-500" />
                       <span>Taxa de sucesso por filme</span>
                     </div>
                     <div className="flex items-center text-xs text-gray-600">
                       <CheckCircle className="w-3 h-3 mr-2 text-green-500" />
                       <span>Ativar/desativar filmes</span>
                     </div>
                   </div>
                   <button
                     onClick={() => navigate('/admin/movie-stats')}
                     className="w-full bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-md transition-colors flex items-center justify-center space-x-2"
                   >
                     <BarChart3 className="w-4 h-4" />
                     <span>Ver Estatísticas</span>
                   </button>
                 </div>

                 {/* Movies Management Card */}
                 <div className="bg-gradient-to-br from-green-50 to-emerald-50 border border-green-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                   <div className="flex items-center mb-4">
                     <div className="bg-green-100 rounded-lg p-3">
                       <Film className="w-6 h-6 text-green-600" />
                     </div>
                     <div className="ml-4">
                       <h4 className="text-lg font-semibold text-gray-900">Gerenciar Filmes</h4>
                       <p className="text-sm text-gray-600">CRUD de filmes e prompts</p>
                     </div>
                   </div>
                   <p className="text-sm text-gray-700 mb-4">
                     Adicione, edite ou remova filmes do banco de dados. Gerencie todos os prompts de cada filme.
                   </p>
                   <div className="space-y-2 mb-4">
                     <div className="flex items-center text-xs text-gray-600">
                       <CheckCircle className="w-3 h-3 mr-2 text-green-500" />
                       <span>Adicionar novos filmes</span>
                     </div>
                     <div className="flex items-center text-xs text-gray-600">
                       <CheckCircle className="w-3 h-3 mr-2 text-green-500" />
                       <span>Editar prompts existentes</span>
                     </div>
                     <div className="flex items-center text-xs text-gray-600">
                       <CheckCircle className="w-3 h-3 mr-2 text-green-500" />
                       <span>Remover filmes problemáticos</span>
                     </div>
                   </div>
                   <button
                     onClick={() => navigate('/admin/movies')}
                     className="w-full bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-md transition-colors flex items-center justify-center space-x-2"
                   >
                     <Film className="w-4 h-4" />
                     <span>Gerenciar Filmes</span>
                   </button>
                 </div>

                 {/* System Configuration Card */}
                 <div className="bg-gradient-to-br from-green-50 to-emerald-50 border border-green-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                   <div className="flex items-center mb-4">
                     <div className="bg-green-100 rounded-lg p-3">
                       <Settings className="w-6 h-6 text-green-600" />
                     </div>
                     <div className="ml-4">
                       <h4 className="text-lg font-semibold text-gray-900">Configurações do Sistema</h4>
                       <p className="text-sm text-gray-600">Configurações gerais</p>
                     </div>
                   </div>
                   <p className="text-sm text-gray-700 mb-4">
                     Gerencie configurações globais do sistema, limites de uso e parâmetros de geração.
                   </p>
                   <button
                     disabled
                     className="w-full bg-gray-300 text-gray-500 font-medium py-2 px-4 rounded-md cursor-not-allowed"
                   >
                     Em Breve
                   </button>
                 </div>

                 {/* Database Tools Card */}
                 <div className="bg-gradient-to-br from-purple-50 to-violet-50 border border-purple-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                   <div className="flex items-center mb-4">
                     <div className="bg-purple-100 rounded-lg p-3">
                       <Activity className="w-6 h-6 text-purple-600" />
                     </div>
                     <div className="ml-4">
                       <h4 className="text-lg font-semibold text-gray-900">Ferramentas de Banco</h4>
                       <p className="text-sm text-gray-600">Manutenção e limpeza</p>
                     </div>
                   </div>
                   <p className="text-sm text-gray-700 mb-4">
                     Ferramentas para limpeza de dados antigos, otimização de performance e backup.
                   </p>
                   <button
                     disabled
                     className="w-full bg-gray-300 text-gray-500 font-medium py-2 px-4 rounded-md cursor-not-allowed"
                   >
                     Em Breve
                   </button>
                 </div>

               </div>
             </div>

             {/* System Information */}
             <div className="bg-white rounded-lg shadow p-6">
               <h3 className="text-lg font-medium text-gray-900 mb-4">Informações do Sistema</h3>
               <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                 
                 <div className="space-y-3">
                   <h4 className="font-medium text-gray-900">Versão do Sistema</h4>
                   <div className="space-y-2">
                     <div className="flex justify-between items-center">
                       <span className="text-sm text-gray-600">Frontend</span>
                       <span className="text-sm font-mono bg-gray-100 px-2 py-1 rounded">v1.0.0</span>
                     </div>
                     <div className="flex justify-between items-center">
                       <span className="text-sm text-gray-600">Database</span>
                       <span className="text-sm font-mono bg-gray-100 px-2 py-1 rounded">Supabase</span>
                     </div>
                     <div className="flex justify-between items-center">
                       <span className="text-sm text-gray-600">IA Engine</span>
                       <span className="text-sm font-mono bg-gray-100 px-2 py-1 rounded">Replicate</span>
                     </div>
                   </div>
                 </div>

                 <div className="space-y-3">
                   <h4 className="font-medium text-gray-900">Status dos Serviços</h4>
                   <div className="space-y-2">
                     <div className="flex justify-between items-center">
                       <span className="text-sm text-gray-600">Supabase Functions</span>
                       <span className="flex items-center text-sm text-green-600">
                         <CheckCircle className="w-4 h-4 mr-1" />
                         Online
                       </span>
                     </div>
                     <div className="flex justify-between items-center">
                       <span className="text-sm text-gray-600">Replicate API</span>
                       <span className="flex items-center text-sm text-green-600">
                         <CheckCircle className="w-4 h-4 mr-1" />
                         Online
                       </span>
                     </div>
                     <div className="flex justify-between items-center">
                       <span className="text-sm text-gray-600">Canva Integration</span>
                       <span className="flex items-center text-sm text-green-600">
                         <CheckCircle className="w-4 h-4 mr-1" />
                         Online
                       </span>
                     </div>
                   </div>
                 </div>

               </div>
             </div>

             {/* Quick Actions */}
             <div className="bg-white rounded-lg shadow p-6">
               <h3 className="text-lg font-medium text-gray-900 mb-4">Ações Rápidas</h3>
               <div className="flex flex-wrap gap-3">
                 <button
                   onClick={() => navigate('/admin/prompt-tester')}
                   className="flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors"
                 >
                   <Settings className="w-4 h-4" />
                   <span>Testador de Prompts</span>
                 </button>
                 <button
                   onClick={() => setActiveTab('logs')}
                   className="flex items-center space-x-2 px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-md transition-colors"
                 >
                   <FileText className="w-4 h-4" />
                   <span>Ver Logs</span>
                 </button>
                 <button
                   onClick={() => setActiveTab('generations')}
                   className="flex items-center space-x-2 px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-md transition-colors"
                 >
                   <Activity className="w-4 h-4" />
                   <span>Monitor de Gerações</span>
                 </button>
               </div>
             </div>

           </div>
         )}
      </div>
    </div>
  );
} 