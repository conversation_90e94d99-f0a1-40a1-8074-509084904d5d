import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { X, Image, Loader2, Refresh<PERSON>w } from 'lucide-react'
import { useTranslation } from 'react-i18next'
import { useBaseImages } from '../hooks/useBaseImages'

interface BaseImage {
  id: string
  original_image_url: string
  file_name: string
  file_size: number
  content_type: string
  photo_type: 'individual' | 'casal'
  streaming_platform: 'netflix' | 'disney' | 'amazon'
  created_at: string
  user_id: string
}

interface BaseImageSelectorProps {
  isOpen: boolean
  onClose: () => void
  onSelectImage: (imageUrl: string, fileName: string) => void
  userId: string | null
}

export default function BaseImageSelector({ isOpen, onClose, onSelectImage, userId }: BaseImageSelectorProps) {
  const { t } = useTranslation()
  const { baseImages, isLoading, error, refetch } = useBaseImages(userId)

  const handleSelectImage = (image: BaseImage) => {
    onSelectImage(image.original_image_url, image.file_name)
    onClose()
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    })
  }

  if (!isOpen) return null

  return (
    <AnimatePresence>
      <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4">
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          className="bg-brand-white rounded-xl shadow-brutal-lg border-2 border-brand-black w-full max-w-4xl max-h-[90vh] overflow-hidden"
        >
          {/* Header */}
          <div className="p-6 border-b-2 border-brand-black flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-black text-brand-black">
                {t('baseImageSelector.title', { defaultValue: 'Selecionar Foto Base' })}
              </h2>
              <p className="text-brand-black/80">
                {t('baseImageSelector.subtitle', { defaultValue: 'Escolha uma foto que você já enviou anteriormente' })}
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={refetch}
                disabled={isLoading}
                className="p-2 rounded-lg border-2 border-brand-black bg-brand-white shadow-brutal-sm hover:shadow-brutal-hover active:shadow-brutal-pressed transition-all disabled:opacity-50"
                title="Atualizar lista"
              >
                <RefreshCw className={`w-5 h-5 ${isLoading ? 'animate-spin' : ''}`} />
              </button>
              <button
                onClick={onClose}
                className="p-2 rounded-lg border-2 border-brand-black bg-brand-white shadow-brutal-sm hover:shadow-brutal-hover active:shadow-brutal-pressed transition-all"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
          </div>

          {/* Content */}
          <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
            {isLoading ? (
              <div className="text-center py-12">
                <Loader2 className="w-12 h-12 text-brand-primary animate-spin mx-auto mb-4" />
                <p className="text-brand-text">
                  {t('baseImageSelector.loading', { defaultValue: 'Carregando suas fotos...' })}
                </p>
              </div>
            ) : error ? (
              <div className="text-center py-12">
                <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4 border-2 border-red-300">
                  <X className="w-8 h-8 text-red-500" />
                </div>
                <p className="text-red-600 mb-4">{error}</p>
                <button
                  onClick={refetch}
                  className="bg-brand-primary text-brand-black font-bold py-2 px-4 rounded-lg border-2 border-brand-black shadow-brutal-sm hover:shadow-brutal-hover active:shadow-brutal-pressed transition-all"
                >
                  {t('baseImageSelector.tryAgain', { defaultValue: 'Tentar Novamente' })}
                </button>
              </div>
            ) : baseImages.length === 0 ? (
              <div className="text-center py-12">
                <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4 border-2 border-gray-300">
                  <Image className="w-8 h-8 text-gray-400" />
                </div>
                <p className="text-brand-text mb-2">
                  {t('baseImageSelector.noImages', { defaultValue: 'Nenhuma foto base encontrada' })}
                </p>
                <p className="text-brand-text/70 text-sm">
                  {t('baseImageSelector.noImagesDesc', { defaultValue: 'Suas fotos aparecerão aqui após você fazer upload pela primeira vez' })}
                </p>
              </div>
            ) : (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                {baseImages.map((image) => (
                  <motion.div
                    key={image.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="bg-brand-white rounded-lg border-2 border-brand-black shadow-brutal-sm hover:shadow-brutal-hover transition-all cursor-pointer overflow-hidden"
                    onClick={() => handleSelectImage(image)}
                  >
                    <div className="aspect-square overflow-hidden">
                      <img
                        src={image.original_image_url}
                        alt={image.file_name}
                        className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
                      />
                    </div>
                    <div className="p-4">
                      <h3 className="font-bold text-brand-text truncate mb-2" title={image.file_name}>
                        {image.file_name}
                      </h3>
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-xs bg-brand-primary text-brand-black px-2 py-1 rounded font-bold">
                          {image.photo_type === 'individual' ? 'Individual' : 'Casal'}
                        </span>
                        <span className="text-xs bg-brand-secondary text-brand-black px-2 py-1 rounded font-bold">
                          {image.streaming_platform?.toUpperCase()}
                        </span>
                      </div>
                      <div className="text-xs text-brand-text/70 space-y-1">
                        <p>{formatFileSize(image.file_size)}</p>
                        <p>{formatDate(image.created_at)}</p>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            )}
          </div>
        </motion.div>
      </div>
    </AnimatePresence>
  )
} 