export interface CanvaAssetUploadResponse {
  asset_id: string;
  upload_url: string;
}

export interface CanvaDesignJobResponse {
  id: string;
  status: 'pending' | 'in-progress' | 'completed' | 'failed';
  brand_template_id: string;
  resource_url?: string;
  thumbnail_url?: string;
}

export interface CanvaTextFields {
  TITLE: string;
  SUBTITLE: string;
  DATE: string;
  LOCATION: string;
  DESCRIPTION: string;
  [key: string]: string;
}

export interface CanvaImageFields {
  CAPA: string;
  AVATAR: string;
  PHOTO_01?: string;
  PHOTO_02?: string;
  PHOTO_03?: string;
  PHOTO_04?: string;
  PHOTO_05?: string;
  PHOTO_06?: string;
  PHOTO_07?: string;
  PHOTO_08?: string;
  PHOTO_09?: string;
  PHOTO_10?: string;
  PHOTO_11?: string;
  PHOTO_12?: string;
  [key: string]: string | undefined;
}

export interface CanvaPosterRequest extends CanvaTextFields, CanvaImageFields {
  ID: string; // Brand template ID
}

export interface CanvaPosterResponse {
  id: string;
  status: 'processing' | 'completed' | 'failed';
  designUrl?: string;
  errorMessage?: string;
}

export interface CanvaGenerationRecord {
  id: string;
  user_id: string;
  status: 'processing' | 'completed' | 'failed';
  template_id: string;
  request_data: CanvaPosterRequest;
  design_url?: string;
  created_at: string;
  completed_at?: string;
  error_message?: string;
}
