import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { X, Check, Crown, Star, Package, Zap, Loader2, CreditCard, Gift, AlertCircle } from 'lucide-react';
import { CREDIT_PLANS } from '../lib/stripe';
import { useVoucher } from '../hooks/useVoucher';
import { showToast } from '../utils/toast';

interface PricingModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSelectPlan: (planId: string) => void;
  loading?: boolean;
  currentCredits?: number;
  user?: { id: string; email?: string } | null;
  onVoucherRedeemed?: () => void;
  onAuthRequired?: (voucherCode: string) => void; // New prop for handling auth requirement
}

export default function PricingModal({ isOpen, onClose, onSelectPlan, loading, currentCredits = 0, user, onVoucherRedeemed, onAuthRequired }: PricingModalProps) {
  const { t } = useTranslation();
  const [voucherCode, setVoucherCode] = useState('');
  const [showVoucherSection, setShowVoucherSection] = useState(false);
  const [voucherMessage, setVoucherMessage] = useState<{ type: 'success' | 'error' | null, text: string }>({ type: null, text: '' });
  const { redeemVoucher, validateVoucherCode, loading: voucherLoading } = useVoucher();

  if (!isOpen) return null;

  const handleVoucherRedeem = async () => {
    // Limpar mensagem anterior
    setVoucherMessage({ type: null, text: '' });
    
    if (!voucherCode.trim()) {
      const errorMsg = 'Por favor, digite um código de voucher';
      setVoucherMessage({ type: 'error', text: errorMsg });
      showToast.error(errorMsg);
      return;
    }

    if (!validateVoucherCode(voucherCode)) {
      const errorMsg = 'Código inválido. Use apenas letras e números (6-12 caracteres)';
      setVoucherMessage({ type: 'error', text: errorMsg });
      showToast.error(errorMsg);
      return;
    }

    // If user is not logged in, trigger auth flow
    if (!user?.id || !user?.email) {
      // Save voucher code and trigger authentication
      if (onAuthRequired) {
        onAuthRequired(voucherCode);
        onClose();
        return;
      } else {
        const errorMsg = 'Você precisa estar logado para resgatar vouchers';
        setVoucherMessage({ type: 'error', text: errorMsg });
        showToast.error(errorMsg);
        return;
      }
    }

    try {
      const result = await redeemVoucher(voucherCode, user.id, user.email);
      
      if (result.success) {
        // Show success message with credits amount
        const credits = result.data?.credits || 0;
        const successMsg = `🎉 Voucher resgatado! ${credits} créditos adicionados à sua conta!`;
        setVoucherMessage({ type: 'success', text: successMsg });
        showToast.success(successMsg);
        setVoucherCode('');
        
        // Manter o voucher aberto por 3 segundos para mostrar a mensagem, depois fechar
        setTimeout(() => {
          setShowVoucherSection(false);
          setVoucherMessage({ type: null, text: '' });
        }, 3000);
        
        onVoucherRedeemed?.();
      } else {
        // Show specific error messages based on error type
        let errorMessage = result.message || 'Erro desconhecido';
        
        switch (result.error) {
          case 'VALIDATION_ERROR':
            errorMessage = 'Erro ao validar o voucher. Verifique o código e tente novamente.';
            break;
          case 'VOUCHER_NOT_FOUND':
            errorMessage = 'Código de voucher não encontrado. Verifique se digitou corretamente.';
            break;
          case 'VOUCHER_INACTIVE':
            errorMessage = 'Este código de voucher não está ativo.';
            break;
          case 'VOUCHER_EXPIRED':
            errorMessage = 'Este voucher expirou e não pode mais ser utilizado.';
            break;
          case 'VOUCHER_LIMIT_REACHED':
            errorMessage = 'Este voucher atingiu o limite máximo de usos.';
            break;
          case 'VOUCHER_ALREADY_USED':
            errorMessage = 'Você já utilizou este código de voucher anteriormente.';
            break;
          case 'EMAIL_RESTRICTION':
            errorMessage = 'Este voucher é restrito a um email específico e não pode ser usado com sua conta.';
            break;
          case 'ALREADY_REDEEMED':
            errorMessage = 'Você já resgatou este voucher anteriormente.';
            break;
          case 'REDEMPTION_ERROR':
            errorMessage = 'Erro ao processar o voucher. Tente novamente em alguns instantes.';
            break;
          case 'DATABASE_ERROR':
            errorMessage = 'Erro interno do sistema. Tente novamente em alguns minutos.';
            break;
          case 'NETWORK_ERROR':
            errorMessage = 'Erro de conexão. Verifique sua internet e tente novamente.';
            break;
          case 'MISSING_PARAMETERS':
            errorMessage = 'Dados incompletos. Tente novamente.';
            break;
          case 'INTERNAL_ERROR':
            errorMessage = 'Erro interno do servidor. Entre em contato com o suporte se persistir.';
            break;
          default:
            // Use a mensagem que veio do servidor, se disponível
            errorMessage = result.message || 'Erro desconhecido ao resgatar voucher.';
            break;
        }
        
        setVoucherMessage({ type: 'error', text: errorMessage });
        showToast.error(errorMessage);
      }
    } catch (error) {
      console.error('Voucher redemption error:', error);
      const errorMsg = 'Erro inesperado. Tente novamente ou entre em contato com o suporte.';
      setVoucherMessage({ type: 'error', text: errorMsg });
      showToast.error(errorMsg);
    }
  };

  const renderPlan = (planKey: 'starter_credits' | 'popular_credits' | 'master_credits') => {
    const plan = CREDIT_PLANS[planKey];
    const isPopular = 'popular' in plan && plan.popular;
    
    return (
      <div className={`relative border-2 border-brand-black rounded-xl p-6 shadow-brutal hover:shadow-brutal-lg transition-all bg-brand-white flex flex-col h-full`}>
        {isPopular && (
          <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
            <span className="bg-brand-accent text-white px-4 py-1 rounded-lg text-sm font-bold border-2 border-brand-black shadow-brutal-sm">{t('pricingModal.mostPopular')}</span>
          </div>
        )}
        <div className="flex items-center space-x-3 mb-4">
          <div className={`w-10 h-10 rounded-lg border-2 border-brand-black flex items-center justify-center`}>
            {planKey === 'starter_credits' && <Package className="w-6 h-6 text-brand-primary" />}
            {planKey === 'popular_credits' && <Crown className="w-6 h-6 text-brand-accent" />}
            {planKey === 'master_credits' && <Star className="w-6 h-6 text-brand-primary" />}
          </div>
          <h3 className="text-xl font-bold text-brand-text">{t(plan.name)}</h3>
        </div>
        <div className="mb-5">
          <span className="text-4xl font-black text-brand-text">{plan.priceDisplay}</span>
          <div className="text-sm text-brand-text/80 mt-1">
            <span className="font-semibold">{plan.credits} {t('pricingModal.credits')}</span>
            <span> / {plan.pricePerCredit} {t('pricingModal.perCredit')}</span>
          </div>
        </div>
        <p className="text-brand-text/80 text-sm mb-6 font-medium">{t(plan.description)}</p>
        <ul className="space-y-3 mb-8 flex-grow">
          {plan.features.map((feature, index) => (
            <li key={index} className="flex items-start space-x-3">
              <Check className="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5 border-2 border-green-700 rounded-full p-0.5" />
              <span className="text-brand-text text-sm font-medium">{t(feature)}</span>
            </li>
          ))}
        </ul>
        <div className="mt-auto pt-4">
          <button onClick={() => onSelectPlan(planKey)} disabled={loading} className={`w-full py-4 px-6 rounded-lg font-bold text-lg border-2 border-brand-black shadow-brutal-sm hover:shadow-brutal-hover active:shadow-brutal-pressed transition-all duration-200 flex items-center justify-center space-x-2 ${
            isPopular 
              ? 'bg-brand-accent text-white' 
              : 'bg-brand-primary text-brand-black'
          }`}>
            {loading ? (<><Loader2 className="w-5 h-5 animate-spin" /><span>{t('pricingModal.processing')}</span></>) : (<><CreditCard className="w-5 h-5" /><span>{t('pricingModal.buyNow', { credits: plan.credits })}</span></>)}
          </button>
        </div>
      </div>
    );
  };

  return (
    <div className="fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-brand-white rounded-xl shadow-brutal-lg border-2 border-brand-black max-w-6xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6 border-b-2 border-brand-black">
          <div className="flex justify-between items-center">
            <div>
              <h2 className="text-3xl font-black text-brand-text">{t('pricingModal.buyCredits')}</h2>
              <p className="text-brand-text/80 mt-1 font-semibold">{t('pricingModal.howCreditsWork')}</p>
              {currentCredits > 0 && (
                <div className="mt-2 flex items-center space-x-2">
                  <Zap className="w-4 h-4 text-yellow-500" />
                  <span className="text-sm font-semibold text-brand-text">{t('pricingModal.youHave', { count: currentCredits })}</span>
                </div>
              )}
            </div>
            <button onClick={onClose} className="p-3 bg-brand-white rounded-lg border-2 border-brand-black shadow-brutal-sm hover:shadow-brutal-hover active:shadow-brutal-pressed transition-all">
              <X size={20} className="text-brand-black" />
            </button>
          </div>
        </div>
        <div className="p-8">
          <div className="grid md:grid-cols-3 gap-8 items-stretch">
            {renderPlan('starter_credits')}
            {renderPlan('popular_credits')}
            {renderPlan('master_credits')}
          </div>

          {/* Voucher Section */}
          <div className="mt-8 text-center">
            <button
              onClick={() => setShowVoucherSection(!showVoucherSection)}
              className="text-brand-text hover:text-brand-primary font-semibold text-sm transition-colors duration-200 flex items-center justify-center space-x-2 mx-auto"
            >
              <Gift className="w-4 h-4" />
              <span>{showVoucherSection ? t('pricingModal.hideVoucher') : t('pricingModal.voucherSection')}</span>
            </button>
            
            {showVoucherSection && (
              <div className="mt-4 max-w-md mx-auto">
                <div className="bg-gray-50 border-2 border-brand-black rounded-lg p-6 shadow-brutal">
                  <div className="flex items-center space-x-2 mb-4">
                    <Gift className="w-5 h-5 text-brand-primary" />
                    <h4 className="font-bold text-brand-text">{t('pricingModal.redeemVoucher')}</h4>
                  </div>
                  
                  <div>
                    {!user && (
                      <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                        <div className="flex items-center space-x-2">
                          <AlertCircle className="w-4 h-4 text-blue-500" />
                          <p className="text-sm text-blue-700 font-medium">
                            {onAuthRequired ? t('pricingModal.willLoginNext') : t('pricingModal.needLogin')}
                          </p>
                        </div>
                      </div>
                    )}
                    
                    <div className="flex space-x-2">
                      <input
                        type="text"
                        value={voucherCode}
                        onChange={(e) => {
                          setVoucherCode(e.target.value.toUpperCase());
                          // Limpar mensagem quando usuário digitar
                          if (voucherMessage.type) {
                            setVoucherMessage({ type: null, text: '' });
                          }
                        }}
                        placeholder={t('pricingModal.enterCode')}
                        className="flex-1 px-3 py-2 border-2 border-brand-black rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-primary focus:border-brand-primary font-mono text-sm"
                        maxLength={12}
                        disabled={voucherLoading}
                      />
                      <button
                        onClick={handleVoucherRedeem}
                        disabled={!voucherCode.trim() || voucherLoading}
                        className="px-4 py-2 bg-brand-primary text-brand-black font-bold rounded-lg border-2 border-brand-black shadow-brutal-sm hover:shadow-brutal-hover active:shadow-brutal-pressed transition-all disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
                      >
                        {voucherLoading ? (
                          <Loader2 className="w-4 h-4 animate-spin" />
                        ) : (
                          <Gift className="w-4 h-4" />
                        )}
                        <span>
                          {voucherLoading 
                            ? t('pricingModal.checking') 
                            : !user 
                              ? (onAuthRequired ? t('pricingModal.continueAuth') : t('pricingModal.redeem'))
                              : t('pricingModal.redeem')
                          }
                        </span>
                      </button>
                    </div>

                    {/* Mensagem de erro/sucesso */}
                    {voucherMessage.type && (
                      <div className={`mt-3 p-3 rounded-lg border-2 ${
                        voucherMessage.type === 'success' 
                          ? 'bg-green-50 border-green-200 text-green-800' 
                          : 'bg-red-50 border-red-200 text-red-800'
                      }`}>
                        <div className="flex items-start space-x-2">
                          <div className="flex-shrink-0 mt-0.5">
                            {voucherMessage.type === 'success' ? (
                              <Check className="w-4 h-4 text-green-600" />
                            ) : (
                              <AlertCircle className="w-4 h-4 text-red-600" />
                            )}
                          </div>
                          <p className="text-sm font-medium">
                            {voucherMessage.text}
                          </p>
                        </div>
                      </div>
                    )}

                    <p className="text-xs text-brand-text/60 mt-2">
                      {t('pricingModal.codeHelp')}
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>
          <div className="mt-10 text-center bg-gray-100 border-2 border-brand-black rounded-lg p-6 shadow-brutal">
            <h4 className="font-bold text-brand-text text-lg mb-4">{t('pricingModal.howCreditsWorkTitle')}</h4>
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm font-semibold text-brand-text">
              <div className="flex items-center justify-center space-x-2 bg-brand-white p-3 rounded-md border-2 border-brand-black"><Check className="w-4 h-4 text-green-500" /><span>{t('pricingModal.coverCost')}</span></div>
              <div className="flex items-center justify-center space-x-2 bg-brand-white p-3 rounded-md border-2 border-brand-black"><Check className="w-4 h-4 text-green-500" /><span>{t('pricingModal.posterCost')}</span></div>
              <div className="flex items-center justify-center space-x-2 bg-brand-white p-3 rounded-md border-2 border-brand-black"><Check className="w-4 h-4 text-green-500" /><span>{t('pricingModal.regenCosts')}</span></div>
              <div className="flex items-center justify-center space-x-2 bg-brand-white p-3 rounded-md border-2 border-brand-black"><Check className="w-4 h-4 text-green-500" /><span>{t('pricingModal.hdDownload')}</span></div>
            </div>
            <div className="mt-6 bg-blue-50 border-2 border-blue-300 rounded-lg p-4">
              <h5 className="font-bold text-blue-800 mb-2">💡 {t('pricingModal.usageExamples')}</h5>
              <div className="grid md:grid-cols-3 gap-4 text-sm text-blue-700">
                <div><strong>{t('pricingModal.netflixCost')}</strong><br />{t('pricingModal.netflixCalc')}</div>
                <div><strong>{t('pricingModal.disneyCost')}</strong><br />{t('pricingModal.disneyCalc')}</div>
                <div><strong>{t('pricingModal.amazonCost')}</strong><br />{t('pricingModal.amazonCalc')}</div>
              </div>
            </div>
          </div>
          <div className="mt-6 text-center space-y-2">
            <p className="text-brand-text/70 text-sm font-semibold">{t('pricingModal.securePayment')}</p>
            <p className="text-brand-text/70 text-sm font-semibold">{t('pricingModal.ssl')}</p>
          </div>
        </div>
      </div>
    </div>
  );
} 