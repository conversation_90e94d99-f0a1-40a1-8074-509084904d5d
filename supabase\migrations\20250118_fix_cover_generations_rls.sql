/*
  # Fix RLS policies for cover_generations table
  
  1. Security Updates
    - Update RLS policies to handle authenticated users properly
    - Fix the issue with checking existing generations
    - Ensure users can only see their own data
  
  2. Changes
    - Drop existing policies
    - Create new policies with proper auth checks
*/

-- Drop existing policies
DROP POLICY IF EXISTS "cover_generations_insert_policy" ON cover_generations;
DROP POLICY IF EXISTS "cover_generations_select_policy" ON cover_generations;
DROP POLICY IF EXISTS "cover_generations_update_policy" ON cover_generations;

-- Create new policies with proper auth handling

-- Policy for INSERT - authenticated users can insert their own data
CREATE POLICY "cover_generations_insert_policy"
  ON cover_generations
  FOR INSERT
  TO authenticated
  WITH CHECK (
    auth.uid() = user_id OR user_id IS NULL
  );

-- Policy for SELECT - authenticated users can see their own data
CREATE POLICY "cover_generations_select_policy"
  ON cover_generations
  FOR SELECT
  TO authenticated
  USING (
    auth.uid() = user_id
  );

-- Policy for UPDATE - authenticated users can update their own data
CREATE POLICY "cover_generations_update_policy"
  ON cover_generations
  FOR UPDATE
  TO authenticated
  USING (
    auth.uid() = user_id
  )
  WITH CHECK (
    auth.uid() = user_id
  );

-- Also create policies for anonymous users (for public generation)
CREATE POLICY "cover_generations_anon_insert_policy"
  ON cover_generations
  FOR INSERT
  TO anon
  WITH CHECK (
    user_id IS NULL
  );

CREATE POLICY "cover_generations_anon_select_policy"
  ON cover_generations
  FOR SELECT
  TO anon
  USING (
    user_id IS NULL
  );

-- Add index for better performance on user_id queries
CREATE INDEX IF NOT EXISTS idx_cover_generations_user_id_created 
  ON cover_generations(user_id, created_at DESC); 