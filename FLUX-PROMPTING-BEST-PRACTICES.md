# 🎨 FLUX Prompting Best Practices Guide
*Baseado na documentação oficial da Black Forest Labs*

## 📚 Fontes de Referência
- [Black Forest Labs - Prompting Guide Image-to-Image](https://docs.bfl.ml/guides/prompting_guide_kontext_i2i)
- [Black Forest Labs - API Reference](https://docs.bfl.ml/api-reference/tasks/edit-or-create-an-image-with-flux-kontext-pro)

## 📐 Aspect Ratios Suportados

### **Replicate FLUX Kontext - Aspect Ratios Válidos**
O Replicate suporta **APENAS** os seguintes aspect ratios:

```
✅ SUPORTADOS:
- "1:1"    - Quadrado
- "16:9"   - Widescreen (landscape)
- "9:16"   - Vertical (portrait)
- "4:3"    - Clássico TV (landscape)
- "3:4"    - C<PERSON><PERSON>si<PERSON> retrato (portrait)
- "3:2"    - Fotografia padrão (landscape)
- "2:3"    - Fotografia retrato (portrait)
- "4:5"    - Instagram retrato
- "5:4"    - Instagram landscape
- "21:9"   - Ultra-wide (cinema)
- "9:21"   - Ultra-tall (vertical cinema)
- "2:1"    - Panorâmico
- "1:2"    - Vertical longo
```

### **❌ Aspect Ratios NÃO Suportados**
```
❌ INVÁLIDOS (causam erro 422):
- "9:10"   - Não existe
- "3:5"    - Use "3:4" como alternativa
- "5:3"    - Use "4:3" como alternativa  
- "6:5"    - Use "5:4" como alternativa
```

### **🔄 Conversões Automáticas**
Nossa plataforma converte automaticamente ratios antigos:
```typescript
const validAspectRatio = aspectRatio === '3:5' ? '3:4' : 
                        aspectRatio === '5:3' ? '4:3' : 
                        aspectRatio === '6:5' ? '5:4' : 
                        aspectRatio === '9:10' ? '3:4' :
                        aspectRatio
```

### **📱 Configuração por Plataforma**
- **Netflix**: `3:4` (todas as 12 capas)
- **Disney+**: `16:9` (primeiras 6) + `3:4` (últimas 3)  
- **Amazon Prime**: `3:4` (todas as 11 capas)

## 🎯 Princípios Fundamentais

### **Máximo de 512 tokens por prompt**
FLUX Kontext tem limite de 512 tokens. Seja conciso mas específico.

### **Seja Específico e Descritivo**
- Use detalhes vívidos ao invés de descrições vagas
- Inclua características físicas específicas, materiais, cores, texturas
- **Exemplo**: "Um homem de 30 anos com cabelo castanho ondulado, olhos verdes, barba por fazer, vestindo camisa de linho azul claro"

### **Estrutura de Prompt Ideal**
```
[AÇÃO ESPECÍFICA] + [ELEMENTO A MUDAR] + [DESCRIÇÃO DETALHADA] + [PRESERVAR IDENTIDADE] + [AMBIENTE/CONTEXTO]
```

## 👕 **TRANSFORMAÇÕES DE ROUPAS - BOAS PRÁTICAS OFICIAIS**

### **✅ Fórmula Correta para Mudança de Roupas**
Baseado na documentação oficial da Black Forest Labs:

```
"Change the clothes to [descrição específica da roupa] while preserving the exact facial features, eye color, and facial expression"
```

### **❌ Erros Comuns que Não Funcionam**
```
❌ "Vista agasalho verde-petróleo" → Muito vago
❌ "Transform into Round 6 character" → Pode mudar identidade
❌ "Coloque roupas de Round 6" → Instrução imprecisa
```

### **✅ Prompts Corretos para Roupas**
```
✅ "Change the clothes to a green-teal numbered tracksuit from Squid Game with number on front and back while preserving the exact facial features, eye color, and facial expression"

✅ "Replace the current outfit with a green-teal tracksuit with visible numbers, maintaining the same person's identity and facial characteristics"

✅ "Change only the clothing to Squid Game contestant uniform (green tracksuit with numbers) while keeping everything else about the person identical"
```

## 🎭 **Consistência de Personagem - Oficial BFL**

### **Framework para Manter Consistência**
1. **Estabeleça a Referência**: "Esta pessoa..." ou "O homem com cabelo castanho..."
2. **Especifique a Transformação**: Declare claramente o que está mudando
   - Ambiente: "...agora em uma praia tropical"
   - Atividade: "...agora colhendo ervas daninhas no jardim"
   - Estilo: "Transformar para estilo Claymation mantendo a mesma pessoa"
3. **Preserve Marcadores de Identidade**: Mencione explicitamente o que deve permanecer consistente
   - "...mantendo as mesmas características faciais, penteado e expressão"
   - "...preservando sua aparência distintiva"

### **⚠️ Erro Comum Oficial**
Usar referências vagas como "ela" ao invés de "A mulher com cabelo preto curto"

### **🔄 Escolha de Verbos Importantes**
- **"Transform"** sem qualificadores → Pode implicar mudança completa
- **"Change the clothes"** → Mantém identidade, muda apenas roupas
- **"Replace the outfit"** → Foco específico na vestimenta
- **"Convert"** → Use com cuidado, pode alterar identidade

## 🎨 **Controle de Composição - Oficial BFL**

### **Manter Posicionamento Exato**
```
"Change the background to [novo ambiente] while keeping the person in the exact same position, scale, and pose. Maintain identical subject placement, camera angle, framing, and perspective. Only replace the environment around them"
```

### **Problema Comum: Mudanças Indesejadas**
```
❌ Prompts Simples que Causam Problemas:
- "Ele está agora em uma praia ensolarada" → Posição e escala mudam
- "Coloque-o em uma praia" → Ângulo da câmera e enquadramento mudam

✅ Prompts Precisos que Mantêm Posicionamento:
- "Change the background to a beach while keeping the person in the exact same position, scale, and pose"
```

## 📝 **Edição de Texto - Oficial BFL**

### **Formato Correto**
```
Replace '[texto original]' with '[novo texto]'
```

### **Exemplos Práticos**
- `Replace 'JOY' with 'BFL'`
- `Change 'Montreal' to 'FLUX'`
- `Replace 'Choose joy' with 'Choose BFL' keeping the same typography`

### **Melhores Práticas para Texto**
- Use fontes claras e legíveis quando possível
- Especifique preservação quando necessário: "Replace 'joy' with 'BFL' while maintaining the same font style and color"
- Mantenha comprimento de texto similar

## 🚨 **Quando os Resultados Não Atendem às Expectativas**

### **Identidade do Personagem Muda Muito**
```
❌ Prompts Vagos Substituem Identidade:
"Transform the person into a Viking" → Substituição completa das características faciais

✅ Prompts Detalhados Preservam Identidade:
"Transform the man into a viking warrior while preserving his exact facial features, eye color, and facial expression"

✅ Prompts Focados Mudam Apenas o Necessário:
"Change the clothes to be a viking warrior" → Mantém identidade perfeita mudando apenas roupas
```

### **Por que isso acontece?**
O verbo "transform" sem qualificadores frequentemente sinaliza ao Kontext que uma mudança completa é desejada.

## 🎬 **Templates Específicos por Plataforma**

### **Netflix/Round 6 - Template Correto**
```
"Change the clothes to a green-teal numbered tracksuit from Squid Game with number [X] on front and back while preserving the exact facial features, eye color, and facial expression. Background: surreal oversized playground with vibrantly colored stairs and concrete walls under harsh industrial lighting. Cinematic style with high contrast, dystopian survival game atmosphere. Main colors: teal-green, hot pink, pure white, institutional gray."
```

### **Disney+ Style**
```
"Change the clothes to [descrição do traje Disney] while maintaining the exact facial features and identity. Transform the background to a magical Disney setting with bright, vibrant colors and fantastical elements. Preserve the person's unique appearance while adapting to Disney's art style."
```

### **Amazon Prime Style**
```
"Change the outfit to [descrição do traje] while preserving the exact facial features, eye color, and expression. Background: [ambiente Amazon]. Gritty, realistic textures with dynamic lighting and Amazon's signature darker color palette."
```

## 🔄 **Edição Iterativa - Recomendação Oficial**

### **Sequência Recomendada**
1. **Primeira Edição**: Mudança básica de roupas/personagem
2. **Segunda Edição**: Ajustes de ambiente/cenário  
3. **Terceira Edição**: Refinamentos de detalhes específicos

### **Exemplo de Sequência**
```
1. "Change the clothes to Squid Game tracksuit while preserving facial features"
2. "Change background to Squid Game playground setting"
3. "Add dramatic lighting and dystopian atmosphere"
```

## 💡 **Resumo das Melhores Práticas - Oficial BFL**

1. **Seja específico**: Linguagem precisa dá melhores resultados
2. **Comece simples**: Teste edições básicas primeiro, depois construa sobre resultados bem-sucedidos
3. **Preserve intencionalmente**: Declare explicitamente o que deve permanecer inalterado
4. **Itere quando necessário**: Transformações complexas frequentemente requerem múltiplas etapas
5. **Nomeie sujeitos diretamente**: Use "a mulher com cabelo preto curto" ao invés de pronomes
6. **Use aspas para texto**: Cite o texto exato que quer mudar
7. **Controle composição explicitamente**: Especifique "manter o ângulo exato da câmera, posição e enquadramento"
8. **Escolha verbos cuidadosamente**: "Transform" pode implicar mudança completa, "change the clothes" dá mais controle

### **Lembre-se**: Ser mais explícito nunca prejudica, desde que o número de instruções por edição não seja muito complicado.

## 🎯 **Prompt Corrigido para Round 6**

### **❌ Prompt Problemático Original**
```
"Vista agasalho masculino verde-petróleo numerado de Round 6 com número na frente e nas costas. Mantendo o rosto da pessoa na foto o mais fiel possível, mas com liberdade criativa para o cabelo e a pose e o corpo..."
```

### **✅ Prompt Corrigido Baseado nas Boas Práticas**
```
"Change the clothes to a green-teal numbered tracksuit from Squid Game with number 456 on front and back while preserving the exact facial features, eye color, and facial expression. Background: surreal oversized playground with vibrantly colored stairs and concrete walls under harsh industrial lighting. Cinematic style with high contrast evoking dystopian survival game atmosphere. Main colors: teal-green, hot pink, pure white, institutional gray."
```

**Principais Correções:**
- ✅ Usa "Change the clothes" ao invés de "Vista"
- ✅ Especifica preservação de características faciais
- ✅ Descrição mais específica da roupa
- ✅ Controle melhor da composição
- ✅ Linguagem mais precisa e direta

---

*Este guia deve ser consultado sempre que criar ou modificar prompts para garantir os melhores resultados com FLUX Kontext.*