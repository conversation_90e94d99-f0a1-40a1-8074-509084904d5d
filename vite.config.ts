import {defineConfig, loadEnv} from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';

// https://vitejs.dev/config/
export default defineConfig(({mode}) => { // Carrega as variáveis de ambiente baseado no modo (development/production)
    const env = loadEnv(mode, process.cwd(), '');

    // Filtra apenas as variáveis de ambiente que começam com VITE_
    const envWithProcessPrefix = Object.entries(env).reduce((prev, [key, val]) => {
        if (key.startsWith('VITE_')) {
            return {
                ...prev,
                [`import.meta.env.${key}`]: JSON.stringify(val)
            };
        }
        return prev;
    }, {});

    return {
        define: {
            ... envWithProcessPrefix,
            // Garante que o objeto process.env exista para compatibilidade
            'process.env': {},
            'process.platform': JSON.stringify('browser'),
            'process.browser': true
        },
        plugins: [react()],
        optimizeDeps: {
            exclude: ['lucide-react']
        },
        build: {
            outDir: 'dist',
            emptyOutDir: true,
            // Garante que as variáveis de ambiente sejam substituídas durante o build
            target: 'esnext',
            sourcemap: false,
            minify: 'terser',
            terserOptions: {
            compress: { // Remove todos os console.* em produção
                drop_console: true,
                drop_debugger: true,
                pure_funcs: ['console.log', 'console.info', 'console.debug', 'console.warn']
            }
        }
        },
        resolve: {
            alias: {
                '@': path.resolve(__dirname, './src')
            }
        }
    };
});
