import React, { useState, useEffect, useCallback } from 'react'
import { Download, Loader2, RotateCcw, X, Printer, Check, Plus, Image as ImageIcon, FolderOpen, RefreshCw, Trash2, GalleryHorizontal, Wand2, ExternalLink } from 'lucide-react'
import { motion, AnimatePresence } from 'framer-motion'
import { toast as showToast } from 'sonner'
import { supabase } from '../lib/supabase'
import PosterStatus from './PosterStatus'
import MyGenerations from './MyGenerations'
import { PhotoType, StreamingPlatform } from '../types'
import GenerationProgressBar from './GenerationProgressBar'

interface GenerationProgress {
  total: number
  completed: number
  failed: number
  inProgress: number
}

interface CoverGridProps {
  covers: string[]
  isLoading: boolean
  onRegenerateIndividual?: (index: number, addCoverCallback?: (url: string) => void) => void
  onRegenerateCoverImage?: () => void;
  regeneratingIndex?: number | null
  regeneratingIndices?: Set<number>
  expectedQuantity?: number | null
  streamingPlatform?: string
  photoType?: string
  originalImageUrl?: string
  userName?: string
  failedIndices?: Set<number>
  onRetryFailed?: (index: number) => void
  progress?: GenerationProgress
}

export default function CoverGrid({ 
  covers, 
  isLoading, 
  onRegenerateIndividual,
  onRegenerateCoverImage,
  regeneratingIndex,
  regeneratingIndices = new Set(),
  expectedQuantity,
  streamingPlatform,
  photoType,
  originalImageUrl,
  userName,
  failedIndices = new Set(),
  onRetryFailed,
  progress
}: CoverGridProps) {
  const [selectedCover, setSelectedCover] = useState<string | null>(null)
  const [selectedIndex, setSelectedIndex] = useState<number | null>(null)
  const [isGeneratingPoster, setIsGeneratingPoster] = useState(false)
  const [showPosterStatus, setShowPosterStatus] = useState(false)
  const [finalPoster, setFinalPoster] = useState<string | null>(null)
  const [generatedCover, setGeneratedCover] = useState<string | null>(null)
  const [selectedCovers, setSelectedCovers] = useState<Set<number>>(new Set())
  const [allCovers, setAllCovers] = useState<string[]>([])
  const [showGenerateButton, setShowGenerateButton] = useState(true)
  const [showGallery, setShowGallery] = useState(false)

  // Atualiza a lista de todas as capas quando novas capas são geradas
  useEffect(() => {
    if (covers.length > 0) {
      setAllCovers(prevCovers => {
        const newCovers = [...prevCovers]
        covers.forEach(cover => {
          if (!newCovers.includes(cover)) {
            newCovers.push(cover)
          }
        })
        return newCovers
      })
    }
  }, [covers])

  // Função para adicionar nova capa regenerada
  const addRegeneratedCover = (newCoverUrl: string) => {
    setAllCovers(prevCovers => [...prevCovers, newCoverUrl])
  }

  // Seleciona automaticamente as primeiras capas baseado na plataforma
  useEffect(() => {
    if (allCovers.length > 0 && selectedCovers.size === 0) {
      const requiredQuantity = getRequiredQuantity()
      const initialSelection = new Set<number>()
      for (let i = 0; i < Math.min(requiredQuantity, allCovers.length); i++) {
        initialSelection.add(i)
      }
      setSelectedCovers(initialSelection)
    }
  }, [allCovers, streamingPlatform])

  const getRequiredQuantity = () => {
    switch (streamingPlatform) {
      case 'netflix': return 12
      case 'disney': return 9
      case 'amazon': return 11
      default: return 12
    }
  }

  const toggleCoverSelection = (index: number) => {
    const newSelection = new Set(selectedCovers)
    const requiredQuantity = getRequiredQuantity()
    
    if (newSelection.has(index)) {
      newSelection.delete(index)
    } else {
      if (newSelection.size < requiredQuantity) {
        newSelection.add(index)
      } else {
        showToast.warning(`Você pode selecionar no máximo ${requiredQuantity} capas para ${streamingPlatform?.toUpperCase() || 'esta plataforma'}`)
        return
      }
    }
    
    setSelectedCovers(newSelection)
  }

  const handleRegenerateIndividual = (index: number) => {
    if (onRegenerateIndividual) {
      onRegenerateIndividual(index, addRegeneratedCover)
      showToast.info('Regenerando capa... Uma nova opção será adicionada à sua galeria!')
    }
  }

  const getSelectedCoversArray = () => {
    return Array.from(selectedCovers)
      .sort((a, b) => a - b)
      .map(index => allCovers[index])
      .filter(Boolean)
  }

  const canGeneratePoster = () => {
    const requiredQuantity = getRequiredQuantity()
    return selectedCovers.size === requiredQuantity && 
           streamingPlatform && 
           originalImageUrl && 
           userName?.trim()
  }

  const handleDownloadCover = async (url: string, filename: string) => {
    try {
      const response = await fetch(url)
      const blob = await response.blob()
      const downloadUrl = window.URL.createObjectURL(blob)
      
      const link = document.createElement('a')
      link.href = downloadUrl
      link.download = `${filename}.jpg`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      
      window.URL.revokeObjectURL(downloadUrl)
      showToast.success('Download iniciado!')
    } catch (error) {
      console.error('Erro no download:', error)
      showToast.error('Erro ao fazer download')
    }
  }

  const handleDownload = async (url: string, index: number) => {
    const downloadToast = showToast.loading('Baixando imagem...')
    try {
      const response = await fetch(url)
      const blob = await response.blob()
      const downloadUrl = window.URL.createObjectURL(blob)
      
      const link = document.createElement('a')
      link.href = downloadUrl
      link.download = `movie-cover-${index + 1}.jpg`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      
      window.URL.revokeObjectURL(downloadUrl)
      showToast.dismiss(downloadToast)
      showToast.success('Imagem baixada com sucesso! 📥')
    } catch (error) {
      console.error('Error downloading image:', error)
      showToast.dismiss(downloadToast)
      showToast.error('Erro ao baixar imagem')
    }
  }

  const handleDownloadPoster = async () => {
    if (!finalPoster) return
    
    const downloadToast = showToast.loading('Baixando poster final...')
    try {
      const response = await fetch(finalPoster)
      const blob = await response.blob()
      const downloadUrl = window.URL.createObjectURL(blob)
      
      const link = document.createElement('a')
      link.href = downloadUrl
      link.download = `poster-final-${userName || 'usuario'}.jpg`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      
      window.URL.revokeObjectURL(downloadUrl)
      showToast.dismiss(downloadToast)
      showToast.success('Poster final baixado com sucesso! 🎉')
    } catch (error) {
      console.error('Error downloading poster:', error)
      showToast.dismiss(downloadToast)
      showToast.error('Erro ao baixar poster final')
    }
  }

  const clearPoster = () => {
    setFinalPoster(null)
    setGeneratedCover(null)
    showToast.success('Poster removido!')
  }

  const handleDownloadAll = async () => {
    const downloadToast = showToast.loading(`Baixando ${allCovers.length} imagens...`)
    
    try {
      for (let i = 0; i < allCovers.length; i++) {
        await handleDownload(allCovers[i], i)
        // Small delay between downloads
        await new Promise(resolve => setTimeout(resolve, 500))
      }
      showToast.dismiss(downloadToast)
      showToast.success(`Todas as ${allCovers.length} imagens foram baixadas! 🎉`)
    } catch (error) {
      showToast.dismiss(downloadToast)
      showToast.error('Erro ao baixar algumas imagens')
    }
  }

  const generatePrintPoster = async () => {
    if (!canGeneratePoster()) {
      showToast.warning('Selecione exatamente as capas necessárias para gerar o poster')
      return
    }

    setIsGeneratingPoster(true)
    setShowPosterStatus(true)
    setShowGenerateButton(false)

    const toastId = showToast.loading('Preparando seu pôster para impressão, aguarde...')

    try {
      // Primeiro gera a cover image
      const coverUrl = await generateCoverImage()
      if (!coverUrl) {
        throw new Error('Falha ao gerar capa principal')
      }

      // Salva a cover image no banco para aparecer no dashboard
      await saveCoverImageToDatabase(coverUrl)

      // Define o ID do template baseado no streaming
      const templateIds = {
        netflix: 'EAGqDjHn_M4',
        disney: 'EAGqWLwQR0Q', 
        amazon: 'EAGqV5bYqfQ'
      }
      const templateId = templateIds[streamingPlatform as keyof typeof templateIds] || templateIds.netflix

      // Texto personalizado com concordância verbal
      const isCouple = photoType === 'casal'
      const verb = isCouple ? 'são transportados' : 'é transportado'
      const pronoun = isCouple ? 'eles vivem' : 'vive'
      
      const platformName = streamingPlatform ? streamingPlatform.charAt(0).toUpperCase() + streamingPlatform.slice(1) : 'Disney'
      const customDescription = `${userName || 'Caio'} ${verb} para diferentes séries da ${platformName}, onde ${pronoun} como o protagonista, enfrentando aventuras únicas em cada episódio enquanto tenta voltar para sua realidade.`

      // Prepara payload completo para o webhook
      const payload = {
        ID: templateId,
        CAPA: coverUrl,
        AVATAR: originalImageUrl,
        PHOTO_01: getSelectedCoversArray()[0] || "https://placehold.co/600x400",
        PHOTO_02: getSelectedCoversArray()[1] || "https://placehold.co/600x400",
        PHOTO_03: getSelectedCoversArray()[2] || "https://placehold.co/600x400",
        PHOTO_04: getSelectedCoversArray()[3] || "https://placehold.co/600x400",
        PHOTO_05: getSelectedCoversArray()[4] || "https://placehold.co/600x400",
        PHOTO_06: getSelectedCoversArray()[5] || "https://placehold.co/600x400",
        PHOTO_07: getSelectedCoversArray()[6] || "https://placehold.co/600x400",
        PHOTO_08: getSelectedCoversArray()[7] || "https://placehold.co/600x400",
        PHOTO_09: getSelectedCoversArray()[8] || "https://placehold.co/600x400",
        PHOTO_10: getSelectedCoversArray()[9] || "https://placehold.co/600x400",
        PHOTO_11: getSelectedCoversArray()[10] || "https://placehold.co/600x400",
        PHOTO_12: getSelectedCoversArray()[11] || "https://placehold.co/600x400",
        TITLE: `Poster ${streamingPlatform?.toUpperCase() || 'STREAMING'} - ${userName || 'Personalizado'}`,
        DESCRIPTION: customDescription,
        CONTINUE: 'Clique para continuar'
      }

      console.log('Chamando webhook direto do frontend:', payload)

      // Chama o webhook direto do frontend
      const webhookResponse = await fetch('https://automate.felvieira.com.br/webhook/criar-design-canva', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'key': 'n8ncanvaapicall'
        },
        body: JSON.stringify(payload)
      })

      if (!webhookResponse.ok) {
        throw new Error(`Erro no webhook: ${webhookResponse.status}`)
      }

      const webhookResult = await webhookResponse.json()
      console.log('Resposta do webhook:', webhookResult)
      
      // Fecha o modal de status
      setShowPosterStatus(false)
      
      // Fecha o toast de loading
      showToast.dismiss(toastId)
      
      // Define a URL do poster final
      const posterUrl = webhookResult.design_url || webhookResult.url || null
      
      if (posterUrl) {
        setFinalPoster(posterUrl)
        showToast.success('Poster criado com sucesso! 🎉 Clique para abrir no Canva.')
        window.open(posterUrl, '_blank')
      } else {
        showToast.success('Poster enviado para processamento! 🎨 Verifique seu Canva.')
      }

    } catch (error) {
      console.error('Erro ao gerar poster:', error)
      
      // Fecha o toast de loading
      showToast.dismiss(toastId)
      
      // Mostra erro
      showToast.error('Falha ao gerar poster. Tente novamente.')
      setShowPosterStatus(false)
    } finally {
      setIsGeneratingPoster(false)
    }
  }

  const saveCoverImageToDatabase = async (coverUrl: string) => {
    try {
      console.log('🔍 Iniciando salvamento da cover image...')
      
      const { data: { session } } = await supabase.auth.getSession()
      if (!session) {
        console.warn('❌ Usuário não autenticado - não salvando cover image')
        return
      }

      console.log('✅ Usuário autenticado:', session.user.id)

      const insertData = {
        user_id: session.user.id,
        image_url: coverUrl,
        movie_title: `Capa ${streamingPlatform?.toUpperCase() || 'STREAMING'}`,
        streaming_platform: streamingPlatform || 'netflix',
        photo_type: photoType || 'individual',
        aspect_ratio: '16:9',
        created_at: new Date().toISOString()
      }

      console.log('📝 Dados para inserção:', insertData)

      const { data, error } = await supabase
        .from('cover_images')
        .insert(insertData)
        .select()

      if (error) {
        console.error('❌ Erro ao salvar cover image no banco:', error)
        throw error
      }

      console.log('✅ Cover image salva no banco com sucesso:', data)
      showToast.success('Cover image salva no dashboard! 📸')
      
    } catch (error) {
      console.error('💥 Erro ao salvar cover image no banco:', error)
      showToast.warning('Cover image criada, mas não foi salva no dashboard')
      // Não falha o processo todo, só loga o erro
    }
  }

  const generateCoverImage = async () => {
    try {
      const { data: { session } } = await supabase.auth.getSession()
      const authToken = session?.access_token || import.meta.env.VITE_SUPABASE_ANON_KEY
      
      const apiUrl = `${import.meta.env.VITE_SUPABASE_URL}/functions/v1/generate-cover-image`

      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          imageUrl: originalImageUrl,
          photoType,
          streamingPlatform,
          aspectRatio: '16:9'
        })
      })

      const data = await response.json()

      if (!response.ok) {
        const errorMessage = data.error || `Erro na geração de capa: ${response.status}`
        const details = data.details ? data.details.join(', ') : ''
        const fullError = details ? `${errorMessage} - ${details}` : errorMessage
        
        console.error('Erro detalhado na geração de capa:', data)
        showToast.error(fullError)
        throw new Error(fullError)
      }
      
      if (data.coverUrl) {
        setGeneratedCover(data.coverUrl)
      }
      
      return data.coverUrl

    } catch (error) {
      console.error('Erro ao gerar imagem de capa:', error)
      const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido'
      if (!errorMessage.includes('Erro na geração de capa')) {
        showToast.error(`Erro ao gerar capa: ${errorMessage}`)
      }
      return null
    }
  }

  const generateAvatarImage = async () => {
    try {
      const { data: { session } } = await supabase.auth.getSession()
      const authToken = session?.access_token || import.meta.env.VITE_SUPABASE_ANON_KEY
      
      const apiUrl = `${import.meta.env.VITE_SUPABASE_URL}/functions/v1/generate-avatar-image`

      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          imageUrl: originalImageUrl,
          photoType,
          aspectRatio: '1:1'
        })
      })

      const data = await response.json()

      if (!response.ok) {
        const errorMessage = data.error || `Erro na geração de avatar: ${response.status}`
        const details = data.details ? data.details.join(', ') : ''
        const fullError = details ? `${errorMessage} - ${details}` : errorMessage
        
        console.error('Erro detalhado na geração de avatar:', data)
        showToast.error(fullError)
        throw new Error(fullError)
      }

      return data.avatarUrl

    } catch (error) {
      console.error('Erro ao gerar imagem de avatar:', error)
      const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido'
      if (!errorMessage.includes('Erro na geração de avatar')) {
        showToast.error(`Erro ao gerar avatar: ${errorMessage}`)
      }
      return null
    }
  }

  const openModal = (coverUrl: string, index: number) => {
    setSelectedCover(coverUrl)
    setSelectedIndex(index)
  }

  const closeModal = () => {
    setSelectedCover(null)
    setSelectedIndex(null)
  }

  const handleShowGallery = () => {
    setShowGallery(true)
  }

  const handleSelectFromGallery = (selectedUrls: string[]) => {
    const requiredQuantity = getRequiredQuantity()
    
    if (selectedUrls.length !== requiredQuantity) {
      showToast.warning(`Você deve selecionar exatamente ${requiredQuantity} imagens para ${streamingPlatform?.toUpperCase()}`)
      return
    }
    
    // Replace current covers with selected ones
    setAllCovers(selectedUrls)
    
    // Update selection to use the new covers
    const newSelection = new Set<number>()
    for (let i = 0; i < selectedUrls.length; i++) {
      newSelection.add(i)
    }
    setSelectedCovers(newSelection)
    
    setShowGallery(false)
    showToast.success(`${selectedUrls.length} imagens selecionadas da galeria!`)
  }

  const handleRegenerateFailedCover = async (index: number) => {
    if (!onRegenerateIndividual) {
      showToast.error('Função de regeneração não disponível')
      return
    }

    // Call the regeneration function
    onRegenerateIndividual(index, addRegeneratedCover)
    showToast.info('Regenerando capa... Uma nova opção será adicionada à sua galeria!')
  }

  // Função para salvar poster no banco de dados
  const savePosterToDatabase = async (posterUrl: string, coverUrl: string) => {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) {
        console.error('Usuário não autenticado')
        return
      }

      const { error } = await supabase
        .from('cover_images')
        .insert({
          user_id: user.id,
          image_url: posterUrl,
          movie_title: `Poster ${streamingPlatform?.toUpperCase() || 'STREAMING'} - ${userName || 'Personalizado'}`,
          streaming_platform: streamingPlatform || 'custom',
          photo_type: photoType || 'individual',
          is_poster: true,
          cover_image_url: coverUrl
        })

      if (error) {
        console.error('Erro ao salvar poster no banco:', error)
      } else {
        console.log('✅ Poster salvo no banco com sucesso!')
      }
    } catch (error) {
      console.error('Erro ao salvar poster no banco:', error)
    }
  }

  const generatePosterOnly = async () => {
    if (!canGeneratePoster()) {
      showToast.warning('Selecione exatamente as capas necessárias para gerar o poster')
      return
    }

    if (!generatedCover) {
      showToast.error('Capa principal não encontrada. Gere a capa primeiro.')
      return
    }

    setIsGeneratingPoster(true)
    setShowPosterStatus(true)

    const toastId = showToast.loading('Criando poster final com capa existente...')

    try {
      // Define o ID do template baseado no streaming
      const templateIds = {
        netflix: 'EAGqDjHn_M4',
        disney: 'EAGqWLwQR0Q', 
        amazon: 'EAGqV5bYqfQ'
      }
      const templateId = templateIds[streamingPlatform as keyof typeof templateIds] || templateIds.netflix

      // Texto personalizado com concordância verbal
      const isCouple = photoType === 'casal'
      const verb = isCouple ? 'são transportados' : 'é transportado'
      const pronoun = isCouple ? 'eles vivem' : 'vive'
      
      const platformName = streamingPlatform ? streamingPlatform.charAt(0).toUpperCase() + streamingPlatform.slice(1) : 'Disney'
      const customDescription = `${userName || 'Caio'} ${verb} para diferentes séries da ${platformName}, onde ${pronoun} como o protagonista, enfrentando aventuras únicas em cada episódio enquanto tenta voltar para sua realidade.`

      // Prepara payload completo para o webhook usando a capa existente
      const payload = {
        ID: templateId,
        CAPA: generatedCover, // Usa a capa já gerada
        AVATAR: originalImageUrl,
        PHOTO_01: getSelectedCoversArray()[0] || "https://placehold.co/600x400",
        PHOTO_02: getSelectedCoversArray()[1] || "https://placehold.co/600x400",
        PHOTO_03: getSelectedCoversArray()[2] || "https://placehold.co/600x400",
        PHOTO_04: getSelectedCoversArray()[3] || "https://placehold.co/600x400",
        PHOTO_05: getSelectedCoversArray()[4] || "https://placehold.co/600x400",
        PHOTO_06: getSelectedCoversArray()[5] || "https://placehold.co/600x400",
        PHOTO_07: getSelectedCoversArray()[6] || "https://placehold.co/600x400",
        PHOTO_08: getSelectedCoversArray()[7] || "https://placehold.co/600x400",
        PHOTO_09: getSelectedCoversArray()[8] || "https://placehold.co/600x400",
        PHOTO_10: getSelectedCoversArray()[9] || "https://placehold.co/600x400",
        PHOTO_11: getSelectedCoversArray()[10] || "https://placehold.co/600x400",
        PHOTO_12: getSelectedCoversArray()[11] || "https://placehold.co/600x400",
        TITLE: `Poster ${streamingPlatform?.toUpperCase() || 'STREAMING'} - ${userName || 'Personalizado'}`,
        DESCRIPTION: customDescription,
        CONTINUE: 'Clique para continuar'
      }

      console.log('🚀 Gerando poster apenas (sem regenerar capa):', payload)

      // Chama o webhook direto do frontend
      const webhookResponse = await fetch('https://automate.felvieira.com.br/webhook/criar-design-canva', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'key': 'n8ncanvaapicall'
        },
        body: JSON.stringify(payload)
      })

      if (!webhookResponse.ok) {
        throw new Error(`Erro no webhook: ${webhookResponse.status}`)
      }

      const webhookResult = await webhookResponse.json()
      console.log('✅ Resposta do webhook (poster apenas):', webhookResult)
      
      // Fecha o modal de status
      setShowPosterStatus(false)
      
      // Fecha o toast de loading
      showToast.dismiss(toastId)
      
      // Define a URL do poster final
      const posterUrl = webhookResult.design_url || webhookResult.url || null
      
      if (posterUrl) {
        setFinalPoster(posterUrl)
        
        // Salva no banco de dados
        await savePosterToDatabase(posterUrl, generatedCover)
        
        showToast.success('Poster criado e salvo com sucesso! 🎉 Agora aparece em "Minhas Gerações"')
      } else {
        showToast.success('Poster enviado para processamento! 🎨 Verifique seu Canva.')
      }

    } catch (error) {
      console.error('Erro ao gerar poster apenas:', error)
      
      // Fecha o toast de loading
      showToast.dismiss(toastId)
      
      // Mostra erro
      showToast.error('Falha ao gerar poster. Tente novamente.')
      setShowPosterStatus(false)
    } finally {
      setIsGeneratingPoster(false)
    }
  }

  if (isLoading) {
    const loadingQuantity = expectedQuantity || 2
    
    return (
      <div className="w-full py-12">
        {/* Progress Bar */}
        {progress && (
          <GenerationProgressBar 
            progress={progress} 
            isGenerating={isLoading} 
          />
        )}
        
        <div className="text-center mb-8 max-w-2xl mx-auto">
          <div className="w-20 h-20 bg-brand-primary mx-auto rounded-2xl flex items-center justify-center border-2 border-brand-black mb-6 shadow-brutal">
            <Loader2 className="w-10 h-10 animate-spin text-brand-black" />
          </div>
          <h3 className="text-3xl font-black text-brand-text mb-2">
            Gerando Suas Capas de Filme
          </h3>
          <p className="text-brand-text/80 mb-6">
            Estamos criando {loadingQuantity} capas em paralelo. Isso pode levar alguns minutos.
          </p>
        </div>
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
          {Array.from({ length: loadingQuantity }).map((_, index) => {
            const isCompleted = progress && index < progress.completed
            const isFailed = progress && failedIndices.has(index)
            const isInProgress = progress && index < (progress.completed + progress.inProgress) && !isCompleted && !isFailed
            
            return (
              <div 
                key={index} 
                className={`
                  aspect-[2/3] rounded-2xl border-2 border-brand-black flex items-center justify-center transition-all duration-300
                  ${isCompleted ? 'bg-green-200' : 
                    isFailed ? 'bg-red-200' :
                    isInProgress ? 'bg-blue-200 animate-pulse' :
                    'bg-gray-200 animate-pulse'
                  }
                `}
              >
                  {isCompleted ? (
                    <Check className="w-10 h-10 text-green-700" />
                  ) : isFailed ? (
                    <X className="w-10 h-10 text-red-700" />
                  ) : isInProgress ? (
                    <Loader2 className="w-10 h-10 animate-spin text-blue-700" />
                  ) : (
                    <div className="text-gray-500 font-bold text-lg">#{index + 1}</div>
                  )}
              </div>
            )
          })}
        </div>
      </div>
    )
  }

  if (allCovers.length === 0) {
    return null
  }

  const requiredQuantity = getRequiredQuantity()

  return (
    <>
      <div className="w-full">
        {/* Poster Final Section - Movido para o topo */}
        {(finalPoster || generatedCover) && (
          <div className="mb-8 p-6 bg-brand-white rounded-lg border-2 border-brand-black shadow-brutal-lg">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h3 className="text-xl font-bold text-brand-text">🎉 Imagens Criadas!</h3>
                <p className="text-sm text-brand-text/70">Suas imagens personalizadas estão prontas</p>
              </div>
              <button
                onClick={() => {
                  setFinalPoster(null)
                  setGeneratedCover(null)
                }}
                className="p-2 bg-brand-white rounded-lg border-2 border-brand-black shadow-brutal-sm hover:shadow-brutal-hover transition-all duration-200"
              >
                <X size={20} className="text-brand-text" />
              </button>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Cover Image */}
              {generatedCover && (
                <div className="space-y-4">
                  <div className="text-center">
                    <h4 className="text-lg font-bold text-brand-text mb-2">Capa Principal</h4>
                    <div className="relative group">
                      <img
                        src={generatedCover}
                        alt="Capa Principal"
                        className="w-full max-h-64 object-cover rounded-lg border-2 border-brand-black cursor-pointer hover:shadow-brutal-hover transition-all duration-200"
                        onClick={() => window.open(generatedCover, '_blank')}
                      />
                      <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100">
                        <span className="text-white font-bold">Clique para ampliar</span>
                      </div>
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => window.open(generatedCover, '_blank')}
                      className="flex-1 bg-brand-secondary text-brand-black py-2 px-3 rounded-lg font-bold border-2 border-brand-black shadow-brutal-sm hover:shadow-brutal-hover transition-all duration-200 flex items-center justify-center space-x-2"
                    >
                      <ExternalLink size={16} />
                      <span>Visualizar</span>
                    </button>
                    <button
                      onClick={() => handleDownloadCover(generatedCover, 'capa-principal')}
                      className="bg-brand-white text-brand-text py-2 px-3 rounded-lg font-bold border-2 border-brand-black shadow-brutal-sm hover:shadow-brutal-hover transition-all duration-200 flex items-center justify-center"
                      title="Download"
                    >
                      <Download size={16} />
                    </button>
                  </div>
                </div>
              )}

              {/* Poster Final */}
              {finalPoster && (
                <div className="space-y-4">
                  <div className="text-center">
                    <h4 className="text-lg font-bold text-brand-text mb-2">Poster Final</h4>
                    <div className="relative group">
                      <div className="w-full h-64 bg-brand-secondary rounded-lg border-2 border-brand-black flex items-center justify-center cursor-pointer hover:shadow-brutal-hover transition-all duration-200"
                           onClick={() => window.open(finalPoster, '_blank')}>
                        <div className="text-center">
                          <Printer size={48} className="text-brand-black mx-auto mb-2" />
                          <p className="font-bold text-brand-black">Poster no Canva</p>
                          <p className="text-sm text-brand-black/70">Clique para abrir</p>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="flex space-x-2">
                      <button
                        onClick={() => window.open(finalPoster, '_blank')}
                        className="flex-1 bg-brand-primary text-brand-black py-2 px-3 rounded-lg font-bold border-2 border-brand-black shadow-brutal-sm hover:shadow-brutal-hover transition-all duration-200 flex items-center justify-center space-x-2"
                      >
                        <ExternalLink size={16} />
                        <span>Abrir no Canva</span>
                      </button>
                      <button
                        onClick={handleDownloadPoster}
                        className="bg-brand-white text-brand-text py-2 px-3 rounded-lg font-bold border-2 border-brand-black shadow-brutal-sm hover:shadow-brutal-hover transition-all duration-200 flex items-center justify-center"
                        title="Download"
                      >
                        <Download size={16} />
                      </button>
                    </div>
                    
                    {/* Botão para regenerar apenas o poster usando a capa existente */}
                    {generatedCover && canGeneratePoster() && (
                      <button
                        onClick={generatePosterOnly}
                        disabled={isGeneratingPoster}
                        className={`
                          w-full py-2 px-3 rounded-lg font-bold border-2 border-brand-black shadow-brutal-sm hover:shadow-brutal-hover transition-all duration-200 flex items-center justify-center space-x-2
                          ${isGeneratingPoster 
                            ? 'bg-gray-300 text-gray-500 cursor-not-allowed' 
                            : 'bg-brand-accent text-brand-black'
                          }
                        `}
                        title="Regenera o poster usando a mesma capa"
                      >
                        {isGeneratingPoster ? (
                          <>
                            <Loader2 size={16} className="animate-spin" />
                            <span>Regenerando...</span>
                          </>
                        ) : (
                          <>
                            <RefreshCw size={16} />
                            <span>Regenerar Poster</span>
                          </>
                        )}
                      </button>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Seção de Geração do Poster */}
        {allCovers.length > 0 && (
          <div className="mb-8 p-6 bg-brand-white rounded-2xl border-2 border-brand-black shadow-brutal">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-bold text-brand-text">
                Selecione as Capas para o Poster
              </h3>
              <div className="text-sm text-brand-text">
                {selectedCovers.size} de {requiredQuantity} selecionadas
              </div>
            </div>
            
            <div className="bg-blue-100 rounded-lg p-4 border-2 border-brand-black mb-4 shadow-brutal-sm">
              <p className="text-sm text-blue-900 font-semibold">
                <strong>📋 Instruções:</strong> Selecione exatamente <strong>{requiredQuantity} capas</strong> para criar seu poster do {streamingPlatform?.toUpperCase() || 'streaming'}. 
                Você pode regenerar capas individuais para ter mais opções. Clique nas capas para selecioná-las.
              </p>
            </div>

            <div className="w-full bg-brand-white rounded-full h-4 p-1 border-2 border-brand-black shadow-brutal-sm mb-6">
              <div 
                className="bg-brand-primary h-full rounded-full transition-all duration-300 border border-brand-black"
                style={{ width: `${(selectedCovers.size / requiredQuantity) * 100}%` }}
              />
            </div>
          </div>
        )}

        {allCovers.length > 0 && (
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 space-y-4 sm:space-y-0">
            <h3 className="text-2xl font-bold text-brand-text">
              Suas Capas Geradas ({allCovers.length})
            </h3>
            <div className="flex space-x-3">
            <button
              onClick={handleDownloadAll}
              className="px-4 py-2 bg-brand-white text-brand-black rounded-lg border-2 border-brand-black hover:shadow-brutal-hover active:shadow-brutal-pressed transition-all duration-200 flex items-center space-x-2 shadow-brutal-sm"
            >
              <Download size={16} />
              <span>Baixar Todas</span>
            </button>
            
            <button
              onClick={handleShowGallery}
              className="px-4 py-2 bg-brand-white text-brand-black rounded-lg border-2 border-brand-black hover:shadow-brutal-hover active:shadow-brutal-pressed transition-all duration-200 flex items-center space-x-2 shadow-brutal-sm"
            >
              <FolderOpen size={16} />
              <span>Escolher da Galeria</span>
            </button>
            
            {/* BOTÕES SEPARADOS */}
            <div className="flex space-x-2">
              {/* Botão Gerar Capa */}
              <button
                onClick={generatePrintPoster}
                disabled={isGeneratingPoster || !originalImageUrl || !userName?.trim()}
                className={`
                  px-4 py-3 rounded-lg transition-all duration-200 flex items-center space-x-2 border-2 border-brand-black font-bold
                  ${isGeneratingPoster || !originalImageUrl || !userName?.trim()
                    ? 'bg-gray-300 text-gray-500 cursor-not-allowed' 
                    : 'bg-brand-primary text-brand-black hover:shadow-brutal-hover active:shadow-brutal-pressed shadow-brutal-sm'
                  }
                `}
                title="Gera a capa principal + avatar em uma só ação"
              >
                {isGeneratingPoster ? (
                  <>
                    <Loader2 size={16} className="animate-spin" />
                    <span>Gerando...</span>
                  </>
                ) : (
                  <>
                    <Wand2 size={16} />
                    <span>Gerar Capa</span>
                  </>
                )}
              </button>
              
              {/* Botão Gerar Poster Final */}
              <button
                onClick={generatePosterOnly}
                disabled={isGeneratingPoster || !canGeneratePoster() || !generatedCover}
                className={`
                  px-4 py-3 rounded-lg transition-all duration-200 flex items-center space-x-2 border-2 border-brand-black font-bold
                  ${isGeneratingPoster || !canGeneratePoster() || !generatedCover
                    ? 'bg-gray-300 text-gray-500 cursor-not-allowed' 
                    : 'bg-brand-secondary text-brand-black hover:shadow-brutal-hover active:shadow-brutal-pressed shadow-brutal-sm'
                  }
                `}
                title="Gera o poster final usando a capa já criada"
              >
                {isGeneratingPoster ? (
                  <>
                    <Loader2 size={16} className="animate-spin" />
                    <span>Gerando...</span>
                  </>
                ) : (
                  <>
                    <Printer size={16} />
                    <span>Gerar Poster Final</span>
                  </>
                )}
              </button>
            </div>
            
            <div className="text-xs text-brand-text max-w-xs">
              {!generatedCover ? (
                <p className="text-orange-600">
                  ⚠️ Primeiro clique em "Gerar Capa" para criar a capa principal
                </p>
              ) : !canGeneratePoster() ? (
                <p className="text-orange-600">
                  ⚠️ Selecione {requiredQuantity} capas para gerar o poster final
                </p>
                              ) : (
                <p className="text-green-600">
                  ✅ Pronto para gerar o poster final!
                </p>
              )}
            </div>
          </div>
        )}
        
        {allCovers.length > 0 && (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
          {Array.from({ length: Math.max(requiredQuantity, allCovers.length) }).map((_, index) => {
            const coverUrl = allCovers[index] || null
            const isSelected = selectedCovers.has(index)
            const isRegenerating = regeneratingIndex === index || regeneratingIndices.has(index)
            const isMissing = !coverUrl && !isRegenerating && index < requiredQuantity
            
            return (
              <motion.div 
                key={`slot-${index}`} 
                className="group relative"
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: index * 0.1 }}
              >
                <div className={`
                  aspect-[2/3] bg-brand-white rounded-2xl overflow-hidden cursor-pointer transition-all duration-300 relative border-2 border-brand-black
                  ${isSelected 
                    ? 'shadow-brutal-pressed scale-[0.98]' 
                    : 'shadow-brutal hover:shadow-brutal-lg'
                  }
                `}>
                  {isRegenerating ? (
                    <div className="w-full h-full bg-brand-white flex items-center justify-center">
                      <div className="text-center">
                        <Loader2 className="w-8 h-8 animate-spin text-brand-primary mx-auto mb-2" />
                        <p className="text-sm text-brand-text">Regenerando...</p>
                      </div>
                    </div>
                  ) : coverUrl ? (
                    <img
                      src={coverUrl}
                      alt={`Capa gerada ${index + 1}`}
                      className="w-full h-full object-cover"
                      loading="lazy"
                    />
                  ) : (
                    <div className="w-full h-full bg-gray-100 flex flex-col items-center justify-center border-2 border-dashed border-gray-400 rounded-lg m-2">
                      <ImageIcon className="w-12 h-12 text-gray-400 mb-2" />
                      <p className="text-sm text-gray-500 mb-3 text-center px-2">
                        {isMissing ? 'Capa não gerada' : 'Slot vazio'}
                      </p>
                      <div className="flex flex-col space-y-2 w-full px-2">
                        <button
                          onClick={(e) => {
                            e.stopPropagation()
                            handleRegenerateFailedCover(index)
                          }}
                          disabled={regeneratingIndices.has(index)}
                          className={`w-full px-3 py-2 rounded-md text-xs transition-colors flex items-center justify-center space-x-1 border-2 border-brand-black font-bold ${
                            regeneratingIndices.has(index) 
                              ? 'bg-gray-400 text-gray-200 cursor-not-allowed' 
                              : 'bg-brand-primary text-brand-black hover:bg-brand-accent'
                          }`}
                        >
                          <RotateCcw size={14} />
                          <span>{regeneratingIndices.has(index) ? 'Regenerando...' : 'Regenerar'}</span>
                        </button>
                        
                        <button
                          onClick={(e) => {
                            e.stopPropagation()
                            handleShowGallery()
                          }}
                          className="w-full px-3 py-2 rounded-md text-xs transition-colors flex items-center justify-center space-x-1 border-2 border-brand-black font-bold bg-brand-white text-brand-black hover:bg-gray-50"
                        >
                          <GalleryHorizontal size={14} />
                          <span>Da Galeria</span>
                        </button>
                      </div>
                    </div>
                  )}
                  
                  <div 
                    className={`absolute inset-0 ${coverUrl && !isRegenerating ? 'cursor-pointer' : 'pointer-events-none'}`}
                    onClick={() => coverUrl && !isRegenerating && toggleCoverSelection(index)}
                  />
                  
                  {isSelected && !isRegenerating && coverUrl && (
                    <div className="absolute top-2 right-2 w-8 h-8 bg-brand-primary rounded-full flex items-center justify-center border-2 border-brand-black pointer-events-none shadow-brutal-sm">
                      <Check size={20} className="text-white" />
                    </div>
                  )}
                  
                  <div className="absolute top-2 left-2 w-8 h-8 bg-black/70 rounded-full flex items-center justify-center border-2 border-white/50 text-xs font-bold text-white pointer-events-none">
                    {index + 1}
                  </div>
                </div>
                
                {!isRegenerating && coverUrl && (
                  <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-70 transition-all duration-300 rounded-xl flex items-center justify-center opacity-0 group-hover:opacity-100 p-2">
                    <div className="flex flex-col space-y-3 w-full">
                      <button
                          onClick={(e) => {
                            e.stopPropagation();
                            toggleCoverSelection(index);
                          }}
                          className={`
                            w-full py-2 px-3 rounded-lg text-sm font-bold transition-all duration-200 flex items-center justify-center space-x-2 border-2 border-brand-black
                            ${isSelected 
                              ? 'bg-brand-primary text-brand-black shadow-brutal-sm' 
                              : 'bg-brand-white text-brand-text shadow-brutal-sm hover:shadow-brutal-pressed'
                            }
                          `}
                        >
                          <Check size={16} />
                          <span>{isSelected ? 'Selecionada' : 'Selecionar'}</span>
                        </button>
                      <div className="flex space-x-2">
                        <button
                          onClick={(e) => { e.stopPropagation(); openModal(coverUrl, index); }}
                          className="w-full h-12 bg-brand-white rounded-lg border-2 border-brand-black shadow-brutal-sm flex items-center justify-center" title="Visualizar">
                          <ImageIcon size={20} className="text-brand-black" />
                        </button>
                        
                        <button
                          onClick={(e) => { e.stopPropagation(); handleDownloadCover(coverUrl, `capa-${index + 1}`); }}
                          className="w-full h-12 bg-brand-white rounded-lg border-2 border-brand-black shadow-brutal-sm flex items-center justify-center" title="Baixar">
                          <Download size={20} className="text-brand-black" />
                        </button>
                        
                        <button
                          onClick={(e) => { e.stopPropagation(); if (!regeneratingIndices.has(index)) handleRegenerateIndividual(index); }}
                          disabled={regeneratingIndices.has(index)}
                          className={`w-full h-12 rounded-lg border-2 border-brand-black shadow-brutal-sm flex items-center justify-center ${regeneratingIndices.has(index) ? 'bg-gray-400' : 'bg-brand-white'}`} title={regeneratingIndices.has(index) ? 'Regenerando...' : 'Regenerar capa'}>
                          <RotateCcw size={20} className="text-brand-black" />
                        </button>
                      </div>
                    </div>
                  </div>
                )}
              </motion.div>
            )
          })}
        </div>
        )}
      </div>

      {/* Modal Melhorado com estilo do Dashboard */}
      <AnimatePresence>
        {selectedCover && selectedIndex !== null && (
          <motion.div 
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4"
            onClick={closeModal}
          >
            <motion.div 
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="relative bg-brand-white rounded-lg border-2 border-brand-black shadow-brutal-lg max-w-2xl w-full"
              onClick={(e) => e.stopPropagation()}
            >
              <button
                onClick={closeModal}
                className="absolute -top-4 -right-4 w-12 h-12 bg-brand-accent text-white rounded-full border-2 border-brand-black hover:bg-brand-accent-dark transition-colors z-10 shadow-brutal-sm flex items-center justify-center"
              >
                <X size={24} />
              </button>
              
              <div className="p-4">
                <img
                  src={selectedCover}
                  alt={`Capa gerada ${selectedIndex + 1}`}
                  className="w-full max-h-[70vh] object-contain rounded-md border-2 border-brand-black"
                />
              </div>
              
              <div className="p-4 border-t-2 border-brand-black flex justify-between items-center bg-gray-50">
                <div>
                  <h3 className="text-xl font-bold text-brand-text">
                    Capa #{selectedIndex + 1}
                  </h3>
                  <div className="flex items-center space-x-2 text-sm text-brand-text mt-1">
                    <span className="px-3 py-1 bg-brand-secondary text-brand-black rounded-md font-bold border-2 border-brand-black">
                      {streamingPlatform?.toUpperCase() || 'STREAMING'}
                    </span>
                    <span className="capitalize font-semibold">{photoType}</span>
                  </div>
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={() => handleDownloadCover(selectedCover, `capa-${selectedIndex + 1}`)}
                    className="p-3 bg-brand-primary rounded-lg hover:bg-brand-accent-dark transition-colors shadow-brutal-sm border-2 border-brand-black flex items-center space-x-2 font-bold text-brand-black"
                    title="Baixar HD"
                  >
                    <Download size={20} />
                    <span>Baixar</span>
                  </button>
                  <button
                    onClick={() => {
                      toggleCoverSelection(selectedIndex)
                      closeModal()
                    }}
                    className={`p-3 rounded-lg transition-colors shadow-brutal-sm border-2 border-brand-black flex items-center space-x-2 font-bold ${
                      selectedCovers.has(selectedIndex)
                        ? 'bg-red-400 text-brand-black'
                        : 'bg-brand-white text-brand-black'
                    }`}
                    title={selectedCovers.has(selectedIndex) ? 'Remover Seleção' : 'Selecionar'}
                  >
                    <Check size={20} />
                    <span>{selectedCovers.has(selectedIndex) ? 'Selecionado' : 'Selecionar'}</span>
                  </button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Poster Status Modal */}
      <PosterStatus 
        isOpen={showPosterStatus}
        onClose={() => setShowPosterStatus(false)}
        totalImages={1}
        generatedImages={finalPoster ? 1 : 0}
        isGenerating={isGeneratingPoster}
        templateName="Poster para Impressão"
      />

      {/* Gallery Modal */}
      <AnimatePresence>
        {showGallery && (
          <motion.div 
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
            onClick={() => setShowGallery(false)}
          >
            <motion.div 
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-brand-white rounded-2xl p-6 max-w-6xl max-h-[90vh] overflow-y-auto shadow-brutal-lg border-2 border-brand-black w-full"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-2xl font-bold text-brand-text">Escolher Fotos da Galeria</h3>
                <button
                  onClick={() => setShowGallery(false)}
                  className="p-2 bg-brand-white rounded-lg border-2 border-brand-black hover:shadow-brutal-hover active:shadow-brutal-pressed transition-all duration-200"
                >
                  <X size={20} className="text-brand-text" />
                </button>
              </div>
              
              <div className="mb-4 p-4 bg-blue-100 rounded-lg border-2 border-brand-black">
                <p className="text-sm text-blue-900">
                  <strong>💡 Instruções:</strong> Selecione <strong>quantas fotos quiser</strong> da sua galeria para substituir as capas atuais. 
                  Recomendamos <strong>{getRequiredQuantity()} fotos</strong> para um poster completo do {streamingPlatform?.toUpperCase() || 'streaming'}, mas você pode escolher menos se preferir.
                </p>
              </div>

              <MyGenerations 
                selectionMode={true}
                maxSelection={getRequiredQuantity()}
                onSelectImages={handleSelectFromGallery}
                onCancel={() => setShowGallery(false)}
              />
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  )
}