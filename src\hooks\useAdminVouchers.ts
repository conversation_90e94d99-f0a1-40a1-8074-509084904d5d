import { useState } from 'react';
import { supabase } from '../lib/supabase';

interface Voucher {
  id: string;
  code: string;
  credits: number;
  max_uses: number;
  current_uses: number;
  email_restriction?: string;
  expires_at?: string;
  status: 'active' | 'disabled' | 'expired';
  description?: string;
  created_at: string;
  voucher_redemptions: VoucherRedemption[];
}

interface VoucherRedemption {
  id: string;
  user_id: string;
  user_email: string;
  credits_redeemed: number;
  redeemed_at: string;
}

interface VoucherStats {
  totalVouchers: number;
  activeVouchers: number;
  expiredVouchers: number;
  disabledVouchers: number;
  totalRedemptions: number;
  totalCreditsRedeemed: number;
  recentRedemptions: VoucherRedemption[];
}

interface CreateVoucherData {
  credits: number;
  maxUses?: number;
  emailRestriction?: string;
  expiresAt?: string;
  description?: string;
  customCode?: string;
}

export const useAdminVouchers = () => {
  const [loading, setLoading] = useState(false);
  const [vouchers, setVouchers] = useState<Voucher[]>([]);
  const [stats, setStats] = useState<VoucherStats | null>(null);

  const getAuthHeaders = async () => {
    const { data: { session } } = await supabase.auth.getSession();
    if (!session?.access_token) {
      throw new Error('Usuário não autenticado');
    }
    return {
      'Authorization': `Bearer ${session.access_token}`,
      'Content-Type': 'application/json',
    };
  };

  const listVouchers = async () => {
    setLoading(true);
    try {
      const headers = await getAuthHeaders();
      const response = await fetch(
        `${import.meta.env.VITE_SUPABASE_URL}/functions/v1/admin-vouchers?action=list`,
        {
          method: 'GET',
          headers,
        }
      );

      if (!response.ok) {
        throw new Error('Falha ao carregar vouchers');
      }

      const result = await response.json();
      setVouchers(result.vouchers || []);
      return result.vouchers;
    } catch (error) {
      console.error('Error listing vouchers:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const getVoucherStats = async () => {
    setLoading(true);
    try {
      const headers = await getAuthHeaders();
      const response = await fetch(
        `${import.meta.env.VITE_SUPABASE_URL}/functions/v1/admin-vouchers?action=stats`,
        {
          method: 'GET',
          headers,
        }
      );

      if (!response.ok) {
        throw new Error('Falha ao carregar estatísticas');
      }

      const result = await response.json();
      setStats(result.stats);
      return result.stats;
    } catch (error) {
      console.error('Error getting voucher stats:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const createVoucher = async (data: CreateVoucherData) => {
    setLoading(true);
    try {
      const headers = await getAuthHeaders();
      const response = await fetch(
        `${import.meta.env.VITE_SUPABASE_URL}/functions/v1/admin-vouchers?action=create`,
        {
          method: 'POST',
          headers,
          body: JSON.stringify(data),
        }
      );

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Falha ao criar voucher');
      }

      await listVouchers(); // Refresh list
      return result.voucher;
    } catch (error) {
      console.error('Error creating voucher:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const updateVoucher = async (id: string, status: string, description?: string) => {
    setLoading(true);
    try {
      const headers = await getAuthHeaders();
      const response = await fetch(
        `${import.meta.env.VITE_SUPABASE_URL}/functions/v1/admin-vouchers?action=update`,
        {
          method: 'PUT',
          headers,
          body: JSON.stringify({ id, status, description }),
        }
      );

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Falha ao atualizar voucher');
      }

      await listVouchers(); // Refresh list
      return result.voucher;
    } catch (error) {
      console.error('Error updating voucher:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const deleteVoucher = async (id: string) => {
    setLoading(true);
    try {
      const headers = await getAuthHeaders();
      const response = await fetch(
        `${import.meta.env.VITE_SUPABASE_URL}/functions/v1/admin-vouchers?action=delete`,
        {
          method: 'DELETE',
          headers,
          body: JSON.stringify({ id }),
        }
      );

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Falha ao deletar voucher');
      }

      await listVouchers(); // Refresh list
      return true;
    } catch (error) {
      console.error('Error deleting voucher:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  return {
    vouchers,
    stats,
    loading,
    listVouchers,
    getVoucherStats,
    createVoucher,
    updateVoucher,
    deleteVoucher,
  };
}; 