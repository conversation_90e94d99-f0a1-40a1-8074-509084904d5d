import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { movieDatabase, genderAdjustments } from '../_shared/movieDatabase.ts'
import { 
  buildPrompt, 
  createSafePrompt, 
  pollForResult 
} from '../_shared/promptBuilder.ts'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
}

interface SingleCoverRequest {
  movieTitle: string
  originalImageUrl: string
  streamingPlatform: string
  photoType: string
  aspectRatio: string
  userId: string
  generationId: string
  position: number // Posição na sequência (1-12)
  gender?: string
  language?: string
  generateTitles?: boolean
  creativityLevel?: 'criativo' | 'rostoFiel' | 'estrito'
  useBackup?: boolean // Se deve usar filme de backup em caso de erro
}

interface GeneratedCover {
  movie_title: string
  image_url?: string
  success: boolean
  error?: string
  fallback_used?: boolean
  fallback_movie?: string
  safe_prompt_used?: boolean
  generated_at: string
  position: number
}

async function downloadAndUploadToSupabase(
  replicateImageUrl: string, 
  movieTitle: string, 
  streamingPlatform: string
): Promise<string | null> {
  try {
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceRoleKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const supabase = createClient(supabaseUrl, supabaseServiceRoleKey)

    console.log(`Downloading image from Replicate for ${movieTitle}...`)
    
    const imageResponse = await fetch(replicateImageUrl)
    if (!imageResponse.ok) {
      throw new Error(`Failed to download image: ${imageResponse.status}`)
    }
    
    const imageBlob = await imageResponse.blob()
    const imageBuffer = await imageBlob.arrayBuffer()
    
    const timestamp = Date.now()
    const sanitizedTitle = movieTitle.toLowerCase().replace(/[^a-z0-9]/g, '-')
    const filename = `covers/${timestamp}-${sanitizedTitle}-${streamingPlatform}.jpg`
    
    console.log(`Uploading to Supabase Storage: ${filename}`)
    
    const { data, error } = await supabase.storage
      .from('generated-images')
      .upload(filename, imageBuffer, {
        contentType: 'image/jpeg',
        upsert: false
      })
    
    if (error) {
      console.error(`Supabase upload error for ${movieTitle}:`, error)
      return null
    }
    
    const { data: { publicUrl } } = supabase.storage
      .from('generated-images')
      .getPublicUrl(filename)
    
    console.log(`Successfully uploaded ${movieTitle} to Supabase: ${publicUrl}`)
    return publicUrl
    
  } catch (error) {
    console.error(`Error downloading/uploading ${movieTitle}:`, error)
    return null
  }
}

async function generateSingleCoverAttempt(
  movie: any,
  originalImageUrl: string,
  streamingPlatform: string,
  photoType: string,
  aspectRatio: string,
  gender?: string,
  language: string = 'portuguese',
  generateTitles: boolean = false,
  creativityLevel: 'criativo' | 'rostoFiel' | 'estrito' = 'rostoFiel'
): Promise<{ success: boolean, image_url?: string, error?: string }> {
  
  try {
    const replicateToken = Deno.env.get('REPLICATE_API_TOKEN')
    if (!replicateToken) {
      throw new Error('REPLICATE_API_TOKEN not found')
    }

    const moviePrompt = movie.prompt[language] || movie.prompt.english

    console.log(`🎬 Attempting ${movie.title}`)
    console.log(`📋 Gender: ${gender}, Titles: ${generateTitles}, Creativity: ${creativityLevel}`)

    // Usar buildPrompt para incluir gender adjustments e títulos
    const optimizedPrompt = buildPrompt(moviePrompt, {
      language: language as 'english' | 'portuguese',
      photoType: photoType as 'individual' | 'casal',
      gender: gender as 'male' | 'female' | undefined,
      addTitle: generateTitles,
      movieTitle: movie.title,
      coverIndex: Math.floor(Math.random() * 12),
      creativityLevel: creativityLevel
    })

    console.log(`🔍 Prompt tokens estimados: ${optimizedPrompt.split(' ').length}`)
    console.log(`🔍 Prompt: ${optimizedPrompt.substring(0, 200)}...`)
    
    // Converter aspect ratios antigos para os suportados pelo Flux Kontext
    const validAspectRatio = aspectRatio === '3:5' ? '3:4' : 
                            aspectRatio === '6:5' ? '5:4' : 
                            aspectRatio === '5:4' ? '5:4' : 
                            aspectRatio

    const replicatePayload = {
      input: {
        input_image: originalImageUrl,
        prompt: optimizedPrompt,
        aspect_ratio: validAspectRatio,
        output_format: 'jpg',
        safety_tolerance: 5,
        num_inference_steps: 28,
        guidance_scale: 3.5
      }
    }

    const prediction = await fetch('https://api.replicate.com/v1/models/black-forest-labs/flux-kontext-max/predictions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${replicateToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(replicatePayload)
    })

    if (!prediction.ok) {
      const errorText = await prediction.text()
      throw new Error(`Replicate API error: ${errorText}`)
    }

    const predictionData = await prediction.json()
    console.log(`🚀 Started generation for ${movie.title}: ${predictionData.status}`)

    // Polling para resultado
    const pollResult = await pollForResult(predictionData.id, movie.title, replicateToken)
    
    if (pollResult.imageUrl) {
      // Download e upload para Supabase
      const supabaseImageUrl = await downloadAndUploadToSupabase(pollResult.imageUrl, movie.title, streamingPlatform)
      
      if (supabaseImageUrl) {
        return { success: true, image_url: supabaseImageUrl }
      } else {
        return { success: false, error: 'Failed to upload to Supabase Storage' }
      }
    } else {
      // 🔥 PRESERVAR O ERRO ORIGINAL (incluindo E005)
      return { success: false, error: pollResult.error || 'Unknown polling error' }
    }

  } catch (error) {
    console.error(`❌ Error generating ${movie.title}:`, error)
    return { success: false, error: error.message }
  }
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const {
      movieTitle,
      originalImageUrl,
      streamingPlatform,
      photoType,
      aspectRatio,
      userId,
      generationId,
      position,
      gender,
      language = 'portuguese',
      generateTitles = false,
      creativityLevel = 'rostoFiel',
      useBackup = true
    }: SingleCoverRequest = await req.json()

    console.log(`🎯 Starting single cover generation for position ${position}: ${movieTitle}`)

    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceRoleKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const supabase = createClient(supabaseUrl, supabaseServiceRoleKey)

    const movies = movieDatabase[streamingPlatform as keyof typeof movieDatabase]
    if (!movies) {
      throw new Error(`Invalid streaming platform: ${streamingPlatform}`)
    }

    // Encontrar o filme principal
    const mainMovie = movies.find(m => m.title === movieTitle)
    if (!mainMovie) {
      throw new Error(`Movie ${movieTitle} not found in ${streamingPlatform}`)
    }

    const result: GeneratedCover = {
      movie_title: movieTitle,
      success: false,
      generated_at: new Date().toISOString(),
      position: position
    }

    // 🎬 TENTATIVA 1: FILME PRINCIPAL
    let attempt = await generateSingleCoverAttempt(
      mainMovie,
      originalImageUrl,
      streamingPlatform,
      photoType,
      aspectRatio,
      gender,
      language,
      generateTitles,
      creativityLevel
    )

    if (attempt.success) {
      result.success = true
      result.image_url = attempt.image_url
    }
    // 🛡️ TENTATIVA 2: PROMPT SAFE (se E005)
    else if (!attempt.success && attempt.error?.includes('E005')) {
      console.log(`🛡️ Trying safe prompt for ${movieTitle}`)
      
      const safePrompt = createSafePrompt(mainMovie.prompt, photoType)
      const safeMovie = { ...mainMovie, prompt: safePrompt }
      
      attempt = await generateSingleCoverAttempt(
        safeMovie,
        originalImageUrl,
        streamingPlatform,
        photoType,
        aspectRatio,
        gender,
        language,
        generateTitles,
        creativityLevel
      )

      if (attempt.success) {
        result.success = true
        result.image_url = attempt.image_url
        result.safe_prompt_used = true
      }
    }

    // 🎲 TENTATIVA 3: FILME DE BACKUP (se habilitado e ainda falhou)
    if (!result.success && useBackup) {
      console.log(`🎲 Trying backup movie for position ${position}`)
      
      // Buscar filmes já usados para evitar duplicação
      const { data: existingCovers } = await supabase
        .from('cover_generations')
        .select('generated_covers')
        .eq('id', generationId)
        .single()

      const usedMovies = existingCovers?.generated_covers?.map((cover: any) => cover.movie_title) || []
      
      // Filmes de backup (posições 13-20)
      const backupMovies = movies.slice(12)
      
      for (const backupMovie of backupMovies) {
        if (usedMovies.includes(backupMovie.title)) {
          console.log(`⏭️ Skipping used backup: ${backupMovie.title}`)
          continue
        }

        console.log(`🎲 Trying backup: ${backupMovie.title}`)
        
        // Tentar backup normal
        attempt = await generateSingleCoverAttempt(
          backupMovie,
          originalImageUrl,
          streamingPlatform,
          photoType,
          aspectRatio,
          gender,
          language,
          generateTitles,
          creativityLevel
        )

        if (attempt.success) {
          result.success = true
          result.image_url = attempt.image_url
          result.fallback_used = true
          result.fallback_movie = backupMovie.title
          result.movie_title = backupMovie.title
          break
        }
        // Se backup deu E005, tentar safe
        else if (!attempt.success && attempt.error?.includes('E005')) {
          console.log(`🛡️ Trying safe prompt for backup ${backupMovie.title}`)
          
          const safeBackupPrompt = createSafePrompt(backupMovie.prompt, photoType)
          const safeBackupMovie = { ...backupMovie, prompt: safeBackupPrompt }
          
          attempt = await generateSingleCoverAttempt(
            safeBackupMovie,
            originalImageUrl,
            streamingPlatform,
            photoType,
            aspectRatio,
            gender,
            language,
            generateTitles,
            creativityLevel
          )

          if (attempt.success) {
            result.success = true
            result.image_url = attempt.image_url
            result.fallback_used = true
            result.fallback_movie = backupMovie.title
            result.movie_title = backupMovie.title
            result.safe_prompt_used = true
            break
          }
        }
      }
    }

    // 🔥 CORREÇÃO: Salvar na tabela cover_generations (Series & Films) se sucesso
    if (result.success) {
      try {
        // Buscar a geração atual
        const { data: currentGeneration, error: fetchError } = await supabase
          .from('cover_generations')
          .select('generated_covers')
          .eq('id', generationId)
          .single()

        if (fetchError) {
          console.error('Error fetching generation:', fetchError)
          // Se não conseguir encontrar a geração, tentar criar uma nova
          console.log(`🔄 Creating new generation entry for ${generationId}`)
          
          const { error: createError } = await supabase
            .from('cover_generations')
            .insert({
              id: generationId,
              user_id: userId,
              original_image_url: originalImageUrl,
              photo_type: photoType,
              streaming_platform: streamingPlatform,
              generated_covers: [{
                movie_title: result.movie_title,
                image_url: result.image_url,
                streaming_platform: streamingPlatform,
                photo_type: photoType,
                aspect_ratio: aspectRatio,
                position: position,
                created_at: new Date().toISOString()
              }],
              status: 'processing'
            })
          
          if (createError) {
            console.error('Error creating new generation:', createError)
          } else {
            console.log(`✅ Position ${position} saved to NEW Series & Films entry: ${result.movie_title}`)
          }
        } else {
          // Adicionar ou ATUALIZAR a capa no array existente
          const currentCovers = currentGeneration.generated_covers || []
          const newCover = {
            movie_title: result.movie_title,
            image_url: result.image_url,
            streaming_platform: streamingPlatform,
            photo_type: photoType,
            aspect_ratio: aspectRatio,
            position: position,
            created_at: new Date().toISOString()
          }

          // Verificar se já existe uma capa para essa posição
          const existingCoverIndex = currentCovers.findIndex(c => c.position === position)

          if (existingCoverIndex !== -1) {
            // Se existir, substitui
            currentCovers[existingCoverIndex] = newCover
            console.log(`🔄 Position ${position} UPDATED in Series & Films: ${result.movie_title}`)
          } else {
            // Se não existir, adiciona
            currentCovers.push(newCover)
            console.log(`➕ Position ${position} ADDED to Series & Films: ${result.movie_title}`)
          }

          // Atualizar array de capas geradas
          const { error: updateError } = await supabase
            .from('cover_generations')
            .update({ generated_covers: currentCovers })
            .eq('id', generationId)

          if (updateError) {
            console.error('Error updating generated covers array:', updateError)
          } else {
            // Mensagem de sucesso unificada
            console.log(`✅ Position ${position} saved successfully to Series & Films.`)
          }
        }
      } catch (error) {
        console.error('Error saving to cover_generations:', error)
      }

      console.log(`✅ Position ${position} completed: ${result.movie_title}`)
    } else {
      result.error = attempt.error || 'All attempts failed'
      console.log(`❌ Position ${position} failed: ${result.error}`)
    }

    return new Response(
      JSON.stringify(result),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )

  } catch (error) {
    console.error('Error in generate-single-cover:', error)
    return new Response(
      JSON.stringify({ 
        success: false,
        error: error.message,
        generated_at: new Date().toISOString()
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
}) 