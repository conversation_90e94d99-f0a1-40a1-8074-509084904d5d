import React from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { X, RotateCcw, Sparkles } from 'lucide-react'
import { useTranslation } from 'react-i18next'

interface NewGenerationModalProps {
  isOpen: boolean
  onClose: () => void
  onCreateFromScratch: () => void
  onUseSameItems: () => void
  hasExistingData: boolean
}

export default function NewGenerationModal({ 
  isOpen, 
  onClose, 
  onCreateFromScratch, 
  onUseSameItems,
  hasExistingData 
}: NewGenerationModalProps) {
  const { t } = useTranslation()

  if (!isOpen) return null

  const handleContinueClick = () => {
    if (hasExistingData) {
      onUseSameItems()
      onClose()
    }
  }

  return (
    <AnimatePresence>
      <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 30 }}
          className="bg-brand-white rounded-xl shadow-brutal-lg border-2 border-brand-black w-full max-w-2xl"
        >
          {/* Header */}
          <div className="p-4 border-b-2 border-brand-black flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-black text-brand-black">{t('newGenerationModal.title')}</h2>
              <p className="text-brand-black/80">{t('newGenerationModal.subtitle')}</p>
            </div>
            <button onClick={onClose} className="p-2 rounded-lg border-2 border-brand-black bg-brand-white shadow-brutal-sm hover:shadow-brutal-hover active:shadow-brutal-pressed transition-all">
              <X className="w-5 h-5" />
            </button>
          </div>

          <div className="p-6 space-y-4">
            {/* Create from Scratch */}
            <button
              onClick={() => {
                onCreateFromScratch()
                onClose()
              }}
              className="w-full p-6 bg-brand-secondary border-2 border-brand-black rounded-lg shadow-brutal-sm hover:shadow-brutal-hover active:shadow-brutal-pressed transition-all duration-200 text-left group"
            >
              <div className="flex items-start space-x-4">
                <div className="w-12 h-12 bg-brand-accent rounded-lg flex items-center justify-center border-2 border-brand-black group-hover:scale-110 transition-transform">
                  <Sparkles className="w-6 h-6 text-white" />
                </div>
                <div className="flex-1">
                  <h3 className="text-lg font-bold text-brand-black mb-2">{t('newGenerationModal.fromScratch')}</h3>
                  <ul className="text-xs text-brand-black/60 space-y-1">
                    <li>{t('newGenerationModal.fromScratchDesc1')}</li>
                    <li>{t('newGenerationModal.fromScratchDesc2')}</li>
                    <li>{t('newGenerationModal.fromScratchDesc3')}</li>
                  </ul>
                </div>
              </div>
            </button>

            {/* Continue Editing */}
            <button
              onClick={handleContinueClick}
              disabled={!hasExistingData}
              className={`w-full p-6 border-2 border-brand-black rounded-lg shadow-brutal-sm transition-all duration-200 text-left group ${hasExistingData 
                ? 'bg-brand-white hover:shadow-brutal-hover active:shadow-brutal-pressed' 
                : 'bg-gray-100 cursor-not-allowed opacity-60'
              }`}
            >
              <div className="flex items-start space-x-4">
                <div className={`w-12 h-12 rounded-lg flex items-center justify-center border-2 border-brand-black transition-transform ${hasExistingData 
                    ? 'bg-brand-primary group-hover:scale-110' 
                    : 'bg-gray-300'
                }`}>
                  <RotateCcw className={`w-6 h-6 ${hasExistingData ? 'text-brand-black' : 'text-gray-500'}`} />
                </div>
                <div className="flex-1">
                  <h3 className={`text-lg font-bold mb-2 ${hasExistingData ? 'text-brand-black' : 'text-gray-500'}`}>
                    {t('newGenerationModal.continueEditing')}
                  </h3>
                  <p className={`text-sm mb-3 ${hasExistingData ? 'text-brand-black/70' : 'text-gray-400'}`}>
                    {hasExistingData 
                      ? t('newGenerationModal.continueEditingDesc1')
                      : t('newGenerationModal.noExistingDataDesc')
                    }
                  </p>
                  {hasExistingData && (
                    <ul className="text-xs text-brand-black/60 space-y-1">
                      <li>{t('newGenerationModal.continueEditingDesc2')}</li>
                      <li>{t('newGenerationModal.continueEditingDesc3')}</li>
                    </ul>
                  )}
                </div>
              </div>
            </button>

            {!hasExistingData && (
              <div className="bg-yellow-100 border-2 border-yellow-400 rounded-lg p-4">
                <p className="text-sm text-yellow-800">
                  <strong>💡 {t('newGenerationModal.tip')}:</strong> {t('newGenerationModal.tipDescription')}
                </p>
              </div>
            )}
          </div>
        </motion.div>
      </div>
    </AnimatePresence>
  )
} 