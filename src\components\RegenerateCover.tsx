import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { RotateCcw, Plus, Loader2, CheckCircle, XCircle } from 'lucide-react'
import { supabase } from '../lib/supabase'
import { showToast } from '../utils/toast'

interface RegenerateCoverProps {
  movieTitle: string
  originalImageUrl: string
  streamingPlatform: string
  photoType: string
  onNewCover?: (imageUrl: string) => void
  disabled?: boolean
  className?: string
}

export default function RegenerateCover({
  movieTitle,
  originalImageUrl,
  streamingPlatform,
  photoType,
  onNewCover,
  disabled = false,
  className = ""
}: RegenerateCoverProps) {
  const [isRegenerating, setIsRegenerating] = useState(false)
  const [lastGenerated, setLastGenerated] = useState<string | null>(null)
  const [error, setError] = useState<string | null>(null)

  const handleRegenerate = async () => {
    if (isRegenerating || disabled) return

    try {
      setIsRegenerating(true)
      setError(null)

      // Obter usuário atual
      const { data: { user }, error: userError } = await supabase.auth.getUser()
      if (userError || !user) {
        throw new Error('Usuário não autenticado')
      }

      console.log('Regenerating cover:', { movieTitle, streamingPlatform })

      // Chamar função de regeneração
      const { data, error } = await supabase.functions.invoke('regenerate-cover', {
        body: {
          movieTitle,
          originalImageUrl,
          streamingPlatform,
          photoType,
          userId: user.id,
          addToCurrent: true // Sempre adiciona nova capa
        }
      })

      if (error) {
        throw new Error(error.message || 'Erro ao regenerar capa')
      }

      if (!data.success) {
        throw new Error(data.error || 'Falha na regeneração')
      }

      console.log('Cover regenerated successfully:', data)

      setLastGenerated(data.image_url)
      onNewCover?.(data.image_url)
      
      showToast.success(`🎬 Nova versão de "${movieTitle}" gerada com sucesso!`)

    } catch (error) {
      console.error('Error regenerating cover:', error)
      const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido'
      setError(errorMessage)
      showToast.error(`Erro ao regenerar "${movieTitle}": ${errorMessage}`)
    } finally {
      setIsRegenerating(false)
    }
  }

  return (
    <div className={`space-y-3 ${className}`}>
      {/* Botão de Regeneração */}
      <button
        onClick={handleRegenerate}
        disabled={isRegenerating || disabled}
        className={`
          flex items-center space-x-2 px-4 py-2 rounded-lg border-2 border-brand-black font-semibold transition-all
          ${isRegenerating || disabled
            ? 'bg-gray-200 text-gray-500 cursor-not-allowed'
            : 'bg-brand-secondary shadow-brutal-sm hover:shadow-brutal-hover active:shadow-brutal-pressed'
          }
        `}
      >
        {isRegenerating ? (
          <>
            <Loader2 className="w-4 h-4 animate-spin" />
            <span>Gerando...</span>
          </>
        ) : (
          <>
            <Plus className="w-4 h-4" />
            <span>Nova Versão</span>
          </>
        )}
      </button>

      {/* Status da última geração */}
      {lastGenerated && (
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          className="flex items-center space-x-2 p-3 bg-green-50 border-2 border-green-300 rounded-lg"
        >
          <CheckCircle className="w-5 h-5 text-green-600" />
          <div className="flex-1">
            <p className="text-sm font-semibold text-green-800">
              Nova versão gerada!
            </p>
            <p className="text-xs text-green-600">
              A nova capa foi adicionada à sua galeria
            </p>
          </div>
        </motion.div>
      )}

      {/* Erro */}
      {error && (
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          className="flex items-center space-x-2 p-3 bg-red-50 border-2 border-red-300 rounded-lg"
        >
          <XCircle className="w-5 h-5 text-red-600" />
          <div className="flex-1">
            <p className="text-sm font-semibold text-red-800">
              Falha na regeneração
            </p>
            <p className="text-xs text-red-600">
              {error}
            </p>
          </div>
          <button
            onClick={() => setError(null)}
            className="text-red-600 hover:text-red-800"
          >
            ✕
          </button>
        </motion.div>
      )}

      {/* Info sobre regeneração */}
      <div className="text-xs text-gray-600 bg-gray-50 border border-gray-200 rounded-lg p-2">
        <p className="font-semibold mb-1">💡 Como funciona:</p>
        <ul className="space-y-1">
          <li>• Gera uma nova versão do mesmo filme/série</li>
          <li>• Adiciona à sua galeria (não substitui)</li>
          <li>• Usa variação do prompt original</li>
          <li>• Você pode escolher qual versão prefere</li>
        </ul>
      </div>
    </div>
  )
} 