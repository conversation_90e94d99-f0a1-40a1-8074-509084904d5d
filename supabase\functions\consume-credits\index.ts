import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from "https://esm.sh/@supabase/supabase-js@2"

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST',
}

const supabaseUrl = Deno.env.get('SUPABASE_URL')!
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Authenticate user and get their ID
    const authHeader = req.headers.get('Authorization')!
    const userClient = createClient(supabaseUrl, supabaseServiceKey, {
      global: { headers: { Authorization: authHeader } },
    })
    const { data: { user }, error: userError } = await userClient.auth.getUser()

    if (userError) {
      return new Response(JSON.stringify({ error: 'Falha na autenticação' }), { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } })
    }
    const userId = user.id

    const { amount, description, timestamp } = await req.json()

    if (!amount || amount <= 0) {
      return new Response(
        JSON.stringify({ error: 'amount (positivo) é obrigatório' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Verificar saldo atual
    const { data: purchases, error: purchasesError } = await supabase
      .from('credit_purchases')
      .select('credits')
      .eq('user_id', userId)
      .eq('status', 'completed')

    if (purchasesError) {
      console.error('Erro ao buscar compras de créditos:', purchasesError)
      return new Response(
        JSON.stringify({ error: 'Erro ao verificar saldo de créditos' }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    const { data: usage, error: usageError } = await supabase
      .from('credit_usage')
      .select('credits_used')
      .eq('user_id', userId)

    if (usageError) {
      console.error('Erro ao buscar uso de créditos:', usageError)
      return new Response(
        JSON.stringify({ error: 'Erro ao verificar uso de créditos' }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Calcular saldo disponível
    const totalCredits = purchases?.reduce((sum, purchase) => sum + purchase.credits, 0) || 0
    const usedCredits = usage?.reduce((sum, use) => sum + use.credits_used, 0) || 0
    const availableCredits = totalCredits - usedCredits

    if (availableCredits < amount) {
      return new Response(
        JSON.stringify({ 
          error: `Créditos insuficientes. Disponível: ${availableCredits}, necessário: ${amount}`,
          availableCredits,
          requiredCredits: amount
        }),
        { 
          status: 402, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Registrar o consumo
    const { data: consumeResult, error: consumeError } = await supabase
      .from('credit_usage')
      .insert({
        user_id: userId,
        credits_used: amount,
        description: description || `Consumo de ${amount} créditos`,
        created_at: timestamp || new Date().toISOString()
      })
      .select()
      .single()

    if (consumeError) {
      console.error('Erro ao registrar consumo de créditos:', consumeError)
      return new Response(
        JSON.stringify({ error: 'Erro ao registrar consumo de créditos' }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Calcular novo saldo
    const newAvailableCredits = availableCredits - amount

    return new Response(
      JSON.stringify({
        success: true,
        consumedCredits: amount,
        remainingCredits: newAvailableCredits,
        usageId: consumeResult.id,
        description: description || `Consumo de ${amount} créditos`
      }),
      { 
        status: 200, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )

  } catch (error) {
    console.error('Erro na função consume-credits:', error)
    return new Response(
      JSON.stringify({ error: 'Erro interno do servidor' }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
}) 