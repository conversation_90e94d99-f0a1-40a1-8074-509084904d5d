import { useTranslation } from 'react-i18next';
import { BrazilFlag, USFlag } from './icons'; // Supondo que você terá ícones de bandeira

const LanguageSwitcher = () => {
  const { i18n } = useTranslation();

  const changeLanguage = (lng: string) => {
    i18n.changeLanguage(lng);
  };

  const currentLanguage = i18n.language;

  return (
    <div className="flex items-center space-x-2">
      <button
        onClick={() => changeLanguage('pt-BR')}
        className={`p-1 rounded-full ${currentLanguage.startsWith('pt') ? 'ring-2 ring-offset-2 ring-blue-500' : ''}`}
        aria-label="Português (Brasil)"
      >
        <BrazilFlag className="w-6 h-6" />
      </button>
      <button
        onClick={() => changeLanguage('en')}
        className={`p-1 rounded-full ${currentLanguage === 'en' ? 'ring-2 ring-offset-2 ring-blue-500' : ''}`}
        aria-label="Switch to English"
      >
        <USFlag className="w-6 h-6" />
      </button>
    </div>
  );
};

export default LanguageSwitcher; 