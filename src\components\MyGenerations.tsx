import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Download, 
  Eye, 
  X, 
  Calendar,
  Tag,
  Grid,
  List,
  Film,
  ImageIcon,
  FileImage,
  User as UserIconLucide,
  Check
} from 'lucide-react'
import { supabase } from '../lib/supabase'
import { showToast } from '../utils/toast'
import { formatDate as formatDateUtil } from '../utils/dateUtils'
// Função local para determinar aspect ratio das capas
const getAspectRatio = (platform: string, index: number): string => {
  if (platform === 'disney') {
    // 🔥 DISNEY: 6 imagens em 16:9 e 3 imagens em 5:4
    return index < 6 ? '16:9' : '5:4';
  }
  return '3:4'; // Netflix e Amazon usam 3:4 (retrato)
}

interface GeneratedImage {
  id: string
  url: string
  title?: string
  createdAt: string
  type: 'series' | 'cover' | 'poster' | 'base'
  streamingPlatform?: string
  photoType?: string
  aspectRatio?: string
}

interface MyGenerationsProps {
  onSelectImages?: (images: string[]) => void
  selectionMode?: boolean
  maxSelection?: number
  onCancel?: () => void
  // 🔥 NOVOS FILTROS
  filterByStreaming?: string
  filterByAspectRatio?: boolean
  showAspectRatio?: boolean
}

export default function MyGenerations({ 
  onSelectImages, 
  selectionMode = false, 
  maxSelection, 
  onCancel,
  // 🔥 NOVOS FILTROS
  filterByStreaming,
  filterByAspectRatio = false,
  showAspectRatio = false
}: MyGenerationsProps) {
  const [activeTab, setActiveTab] = useState<'series' | 'covers' | 'posters' | 'base'>('series')
  const [seriesImages, setSeriesImages] = useState<GeneratedImage[]>([])
  const [coverImages, setCoverImages] = useState<GeneratedImage[]>([])
  const [posterImages, setPosterImages] = useState<GeneratedImage[]>([])
  const [baseImages, setBaseImages] = useState<GeneratedImage[]>([])
  const [selectedImages, setSelectedImages] = useState<Set<string>>(new Set())
  const [isLoading, setIsLoading] = useState(true)
  const [selectedCover, setSelectedCover] = useState<{ url: string; image: GeneratedImage } | null>(null)
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [filterPlatform, setFilterPlatform] = useState<string>('all')
  const [filterTitle, setFilterTitle] = useState<string>('all')
  const [sortBy, setSortBy] = useState<'date' | 'platform' | 'name'>('date')
  const [imageToDelete, setImageToDelete] = useState<{id: string, type: 'series' | 'cover' | 'poster' | 'base'} | null>(null)
  const [isDeleting, setIsDeleting] = useState(false)

  useEffect(() => {
    loadGenerations()
  }, [])

  // 🔥 NOVO: Resetar filtro de título quando mudar de aba
  useEffect(() => {
    setFilterTitle('all')
  }, [activeTab])

  const loadGenerations = async () => {
    try {
      setIsLoading(true)
      const { data: userData, error: userError } = await supabase.auth.getUser()
      
      if (userError || !userData.user) {
        showToast.error('Você precisa estar logado para ver suas gerações')
        return
      }

      // Load cover generations (series images) - CORRIGIDO para usar cover_images
      const { data: coverGenerations, error: coverError } = await supabase
        .from('cover_generations')
        .select('*')
        .eq('user_id', userData.user.id)
        .order('created_at', { ascending: false })

      console.log('Cover generations:', coverGenerations)
      console.log('Cover generations error:', coverError)
      console.log('Cover generations length:', coverGenerations?.length)

      if (!coverError && coverGenerations) {
        const series: GeneratedImage[] = []
        console.log('Processing cover generations:', coverGenerations.length)
        
        coverGenerations.forEach((gen, genIndex) => {
          console.log(`Processing generation ${genIndex}:`, gen)
          console.log(`Generated covers type:`, typeof gen.generated_covers)
          console.log(`Generated covers is array:`, Array.isArray(gen.generated_covers))
          console.log(`Generated covers:`, gen.generated_covers)
          
          // Verificar se generated_covers existe e é um array de objetos
          if (gen.generated_covers && Array.isArray(gen.generated_covers)) {
            console.log(`Processing ${gen.generated_covers.length} covers for generation ${genIndex}`)
            
            gen.generated_covers.forEach((cover: any, index: number) => {
              console.log(`Processing cover ${index}:`, cover)
              
                             // Se é string, usar como URL
               let url = typeof cover === 'string' ? cover : cover.image_url
               let title = typeof cover === 'string' ? `Capa ${index + 1}` : (cover.movie_title || `Capa ${index + 1}`)
              
              console.log(`URL: ${url}, Title: ${title}`)
              
              if (url) {
                // Usar configuração centralizada para aspect ratio
                const aspectRatio = gen.streaming_platform ? 
                  getAspectRatio(gen.streaming_platform, index) : 
                  '3:4' // fallback padrão

                const seriesItem = {
                  id: `${gen.id}-${index}`,
                  url,
                  title: `${gen.streaming_platform?.toUpperCase()} - ${title}`,
                  createdAt: gen.created_at,
                  type: 'series' as const,
                  streamingPlatform: gen.streaming_platform,
                  photoType: gen.photo_type,
                  aspectRatio
                }
                
                console.log(`Adding series item:`, seriesItem)
                
                // 🔥 APLICAR FILTROS AQUI
                let shouldInclude = true
                
                // Filtro por streaming
                if (filterByStreaming && gen.streaming_platform !== filterByStreaming) {
                  shouldInclude = false
                }
                
                // Filtro por aspect ratio (se ativado, só inclui imagens com proporção compatível)
                if (filterByAspectRatio && filterByStreaming) {
                  const requiredAspectRatio = getAspectRatio(filterByStreaming, index)
                  if (aspectRatio !== requiredAspectRatio) {
                    shouldInclude = false
                  }
                }
                
                if (shouldInclude) {
                  series.push(seriesItem)
                }
              } else {
                console.log(`No URL found for cover ${index}`)
              }
            })
          } else {
            console.log(`No generated_covers or not an array for generation ${genIndex}`)
          }
        })
        
        console.log(`Final series array (${series.length} items):`, series)
        setSeriesImages(series)
        console.log('Series images set:', series)
      }

      // Load cover images from database
      const { data: coverImagesData, error: coverImagesError } = await supabase
        .from('cover_images')
        .select('*')
        .eq('user_id', userData.user.id)
        .order('created_at', { ascending: false })

      const covers: GeneratedImage[] = []
      
      // Add database cover images
      if (!coverImagesError && coverImagesData) {        
        coverImagesData.forEach((cover, index) => {
          const isRegenerated = cover.image_url.includes('/regenerated/')
          const coverItem = {
            id: cover.id,
            url: cover.image_url,
            title: `${cover.streaming_platform?.toUpperCase()} - ${isRegenerated ? 'Regenerada' : 'Capa'} #${index + 1}`,
            createdAt: cover.created_at,
            type: 'cover' as const,
            streamingPlatform: cover.streaming_platform,
            photoType: cover.photo_type,
            aspectRatio: cover.aspect_ratio || '3:4'
          }
          
          // 🔥 APLICAR FILTROS AQUI TAMBÉM
          let shouldInclude = true
          
          // Filtro por streaming
          if (filterByStreaming && cover.streaming_platform !== filterByStreaming) {
            shouldInclude = false
          }
          
          // Filtro por aspect ratio
          if (filterByAspectRatio && filterByStreaming) {
            const requiredAspectRatio = getAspectRatio(filterByStreaming, 0) // Cover images usam proporção padrão
            if (coverItem.aspectRatio !== requiredAspectRatio) {
              shouldInclude = false
            }
          }
          
          if (shouldInclude) {
            covers.push(coverItem)
          }
        })
      }

      // Load poster images from database
      const { data: posterImagesData, error: posterImagesError } = await supabase
        .from('poster_images')
        .select('*')
        .eq('user_id', userData.user.id)
        .order('created_at', { ascending: false })

      const posters: GeneratedImage[] = []
      
      // Add database poster images
      if (!posterImagesError && posterImagesData) {        
        posterImagesData.forEach((poster, index) => {
          const posterItem = {
            id: poster.id,
            url: poster.poster_url,
            title: `${poster.streaming_platform?.toUpperCase()} - Poster Final ${index + 1}`,
            createdAt: poster.created_at,
            type: 'poster' as const,
            streamingPlatform: poster.streaming_platform,
            photoType: poster.photo_type,
            aspectRatio: '21:9'
          }
          
          // 🔥 APLICAR FILTROS AQUI TAMBÉM
          let shouldInclude = true
          
          // Filtro por streaming
          if (filterByStreaming && poster.streaming_platform !== filterByStreaming) {
            shouldInclude = false
          }
          
          // Posters sempre têm proporção fixa, então não filtramos por aspect ratio
          
          if (shouldInclude) {
            posters.push(posterItem)
          }
        })
      }

      // Load base images
      const { data: baseImagesData, error: baseError } = await supabase
        .from('base_images')
        .select('*')
        .eq('user_id', userData.user.id)
        .order('created_at', { ascending: false })

      if (!baseError && baseImagesData) {
        const bases: GeneratedImage[] = baseImagesData.map(base => ({
          id: base.id,
          url: base.original_image_url,
          title: `Foto Base - ${base.file_name}`,
          createdAt: base.created_at,
          type: 'base',
          aspectRatio: 'original'
        }))
        setBaseImages(bases)
      } else {
        setBaseImages([])
      }

      setCoverImages(covers)
      setPosterImages(posters)

      // Debug log para verificar quantas imagens foram carregadas
      console.log('=== DEBUG MYGENERATION ===')
      console.log('Series images loaded:', seriesImages.length)
      console.log('Cover images loaded:', covers.length)
      console.log('Poster images loaded:', posters.length)
      console.log('Base images loaded:', baseImages.length)
      console.log('Cover images data:', covers)

    } catch (error) {
      console.error('Error loading generations:', error)
      showToast.error('Erro ao carregar suas gerações')
    } finally {
      setIsLoading(false)
    }
  }

  const toggleImageSelection = (imageId: string, _url: string) => {
    if (!selectionMode) return

    const newSelection = new Set(selectedImages)
    if (newSelection.has(imageId)) {
      newSelection.delete(imageId)
    } else {
      if (maxSelection && newSelection.size >= maxSelection) {
        showToast.warning(`Você pode selecionar no máximo ${maxSelection} imagens`)
        return
      }
      newSelection.add(imageId)
    }
    setSelectedImages(newSelection)
  }

  const deleteImage = async () => {
    if (!imageToDelete) return
    
    setIsDeleting(true)
    try {
      const { id, type } = imageToDelete
      
      if (type === 'series') {
        // Para imagens de série, precisamos remover do array no banco
        const [generationId, imageIndex] = id.split('-')
        
        const { data: { session } } = await supabase.auth.getSession()
        if (!session) {
          showToast.error('Você precisa estar logado')
          setIsDeleting(false)
          return
        }

        // Chama a edge function para remover a imagem específica do array
        const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/remove-series-image`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${session.access_token}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            generationId,
            imageIndex: parseInt(imageIndex)
          })
        })

        if (!response.ok) {
          throw new Error('Erro ao remover imagem do banco')
        }

        setSeriesImages(prev => prev.filter(img => img.id !== id))
        showToast.success('Imagem excluída com sucesso!')
        setImageToDelete(null)
        setIsDeleting(false)
        return
      }

      // Para outros tipos, excluir do banco de dados
      const tableMap = {
        'cover': 'cover_images',
        'poster': 'poster_images',
        'base': 'base_images'
      }

      const table = tableMap[type]
      if (!table) throw new Error('Tipo de imagem inválido')

      // Para estes tipos, o ID é direto do banco
      const { error } = await supabase
        .from(table)
        .delete()
        .eq('id', id)

      if (error) throw error

      // Atualiza o estado removendo a imagem excluída
      const updateState = {
        'cover': setCoverImages,
        'poster': setPosterImages,
        'base': setBaseImages
      }[type]

      updateState(prev => prev.filter(img => img.id !== id))
      showToast.success('Imagem excluída com sucesso!')
      setImageToDelete(null)
    } catch (error) {
      console.error('Erro ao excluir imagem:', error)
      showToast.error('Erro ao excluir imagem')
    } finally {
      setIsDeleting(false)
    }
  }

  const handleConfirmSelection = () => {
    if (!onSelectImages) return

    // Get selected URLs
    const allImages = [...seriesImages, ...coverImages, ...posterImages, ...baseImages]
    const selectedUrls = Array.from(selectedImages).map(id => {
      const image = allImages.find(img => img.id === id)
      return image?.url || ''
    }).filter(Boolean)

    // 🔥 REMOVIDO: Validação de número exato - usuário pode escolher quantas quiser
    // if (maxSelection && selectedUrls.length !== maxSelection) {
    //   showToast.warning(`Você deve selecionar exatamente ${maxSelection} imagens`)
    //   return
    // }

    onSelectImages(selectedUrls)
  }

  const downloadCover = async (url: string, image: GeneratedImage) => {
    try {
      // 🔥 TRATAMENTO ESPECIAL PARA POSTERS
      if (image.type === 'poster') {
        // Verificar se é URL de download direto ou link do Canva
        if (url.includes('export-download.canva.com') || url.match(/\.(png|jpg|jpeg|webp)(\?|$)/i)) {
          // URL de download direto - fazer download normal
          showToast.loading('Baixando poster...')
          
          const response = await fetch(url)
          const blob = await response.blob()
          const downloadUrl = window.URL.createObjectURL(blob)
          
          const link = document.createElement('a')
          link.href = downloadUrl
          link.download = `poster-${image.streamingPlatform || 'streaming'}-${Date.now()}.png`
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
          
          window.URL.revokeObjectURL(downloadUrl)
          showToast.success('Poster baixado com sucesso! 📥')
        } else {
          // Link do Canva - abrir em nova aba
          window.open(url, '_blank')
          showToast.success('Poster aberto no Canva! Use Ctrl+S ou o botão de download do Canva.')
        }
      } else {
        // Comportamento normal para outros tipos de imagem
        showToast.loading('Baixando imagem...')
        
        const response = await fetch(url)
        const blob = await response.blob()
        const downloadUrl = window.URL.createObjectURL(blob)
        
        const link = document.createElement('a')
        link.href = downloadUrl
        link.download = `${image.type}-${image.streamingPlatform || 'streaming'}-${Date.now()}.jpg`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        
        window.URL.revokeObjectURL(downloadUrl)
        showToast.success('Imagem baixada com sucesso! 📥')
      }
    } catch (error) {
      console.error('Error downloading image:', error)
      showToast.error('Erro ao baixar imagem')
    }
  }

  // 🔥 Usar função utilitária para formatação consistente
  const formatDate = formatDateUtil

  const getPlatformColor = (platform: string) => {
    switch (platform) {
      case 'netflix': return 'bg-red-500'
      case 'disney': return 'bg-blue-500'
      case 'amazon': return 'bg-teal-500'
      default: return 'bg-gray-500'
    }
  }

  const getPlatformName = (platform: string) => {
    switch (platform) {
      case 'netflix': return 'Netflix'
      case 'disney': return 'Disney+'
      case 'amazon': return 'Amazon Prime'
      default: return platform?.charAt(0).toUpperCase() + platform?.slice(1)
    }
  }

  // 🔥 NOVA FUNÇÃO: Extrair títulos únicos das imagens
  const getUniqueTitles = () => {
    let images: GeneratedImage[] = []
    switch (activeTab) {
      case 'series': images = seriesImages; break
      case 'covers': images = coverImages; break
      case 'posters': images = posterImages; break
      case 'base': images = baseImages; break
      default: images = []
    }

    const titles = new Set<string>()
    images.forEach(image => {
      if (image.title) {
        // Extrair o título do filme/série removendo prefixos da plataforma
        let cleanTitle = image.title
        // Remove prefixos como "NETFLIX - ", "DISNEY - ", "AMAZON - "
        cleanTitle = cleanTitle.replace(/^(NETFLIX|DISNEY|AMAZON)\s*-\s*/i, '')
        // Remove sufixos como "- Capa #1", "- Regenerada #1", etc.
        cleanTitle = cleanTitle.replace(/\s*-\s*(Capa|Regenerada|Poster Final).*$/i, '')
        
        if (cleanTitle.trim()) {
          titles.add(cleanTitle.trim())
        }
      }
    })
    
    return Array.from(titles).sort()
  }

  const tabsConfig = [
    { id: 'series', label: 'Series & Films', icon: Film, count: seriesImages.length },
    { id: 'covers', label: 'Cover Images', icon: ImageIcon, count: coverImages.length },
    { id: 'posters', label: 'Posters', icon: FileImage, count: posterImages.length },
    { id: 'base', label: 'Fotos Base', icon: UserIconLucide, count: baseImages.length }
  ]

  const getCurrentImages = () => {
    let images: GeneratedImage[] = []
    switch (activeTab) {
      case 'series': images = seriesImages; break
      case 'covers': images = coverImages; break
      case 'posters': images = posterImages; break
      case 'base': images = baseImages; break
      default: images = []
    }

    // Filter by platform and title
    return images
      .filter(image => {
        const platformMatch = filterPlatform === 'all' || image.streamingPlatform === filterPlatform
        
        let titleMatch = true
        if (filterTitle !== 'all' && image.title) {
          // Extrair o título limpo da imagem
          let cleanTitle = image.title
          cleanTitle = cleanTitle.replace(/^(NETFLIX|DISNEY|AMAZON)\s*-\s*/i, '')
          cleanTitle = cleanTitle.replace(/\s*-\s*(Capa|Regenerada|Poster Final).*$/i, '')
          titleMatch = cleanTitle.trim() === filterTitle
        }
        
        return platformMatch && titleMatch
      })
      .sort((a, b) => {
        switch (sortBy) {
          case 'date':
            return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
          case 'platform':
            return (a.streamingPlatform || '').localeCompare(b.streamingPlatform || '')
          case 'name':
            return (a.title || '').localeCompare(b.title || '')
          default:
            return 0
        }
      })
  }

  if (isLoading) {
    return (
      <div className="w-full py-12 text-center">
        <div className="animate-spin w-8 h-8 border-2 border-primary-500 border-t-transparent rounded-full mx-auto mb-4"></div>
        <p className="text-gray-600">Carregando suas gerações...</p>
      </div>
    )
  }

  const currentImages = getCurrentImages()

  return (
    <div className="w-full space-y-6 relative">
      {/* Confirmation Dialog */}
      <AnimatePresence>
        {imageToDelete && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
            <motion.div 
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="bg-brand-white border-2 border-brand-black p-6 rounded-lg shadow-brutal-lg max-w-md w-full"
            >
              <h3 className="text-xl font-bold mb-4">Confirmar Exclusão</h3>
              <p className="mb-6">
                {imageToDelete?.type === 'series' 
                  ? 'Tem certeza que deseja remover esta imagem da visualização? A imagem será removida apenas da interface, mas permanecerá no banco de dados.'
                  : 'Tem certeza que deseja excluir esta imagem permanentemente? Esta ação não pode ser desfeita e a imagem será removida do banco de dados.'
                }
              </p>
              
              <div className="flex justify-end gap-3">
                <button
                  onClick={() => setImageToDelete(null)}
                  className="px-4 py-2 border-2 border-brand-black bg-brand-white shadow-brutal-sm hover:shadow-brutal-hover active:shadow-brutal-pressed transition-all font-bold"
                  disabled={isDeleting}
                >
                  Cancelar
                </button>
                <button
                  onClick={deleteImage}
                  className="px-4 py-2 border-2 border-brand-black bg-brand-accent text-white shadow-brutal-sm hover:shadow-brutal-hover active:shadow-brutal-pressed transition-all font-bold flex items-center gap-2"
                  disabled={isDeleting}
                >
                  {isDeleting ? (
                    <>
                      <svg className="animate-spin h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      {imageToDelete?.type === 'series' ? 'Removendo...' : 'Excluindo...'}
                    </>
                  ) : (
                    imageToDelete?.type === 'series' ? 'Remover' : 'Excluir'
                  )}
                </button>
              </div>
            </motion.div>
          </div>
        )}
      </AnimatePresence>
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-800">
            {selectionMode ? 'Escolher da Galeria' : 'Minhas Gerações'}
          </h2>
          {/* 🔥 NOVO: Mostrar filtros ativos */}
          {(filterByStreaming || filterByAspectRatio) && (
            <div className="mt-1 flex flex-wrap gap-2 text-xs">
              {filterByStreaming && (
                <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded border">
                  📺 {filterByStreaming.toUpperCase()}
                </span>
              )}
              {filterByAspectRatio && (
                <span className="px-2 py-1 bg-green-100 text-green-800 rounded border">
                  📏 Proporção Compatível
                </span>
              )}
              {showAspectRatio && (
                <span className="px-2 py-1 bg-purple-100 text-purple-800 rounded border">
                  🔍 Mostrando Proporções
                </span>
              )}
            </div>
          )}
        </div>
        {selectionMode && (
          <div className="text-sm text-gray-600">
            {selectedImages.size} de {maxSelection || '∞'} selecionadas
          </div>
        )}
      </div>

      {/* Tabs */}
      <div className="flex flex-wrap sm:flex-nowrap gap-2 sm:space-x-1 bg-brand-white rounded-2xl p-1 border-2 border-brand-black shadow-brutal">
        {tabsConfig.map((tab) => {
          const Icon = tab.icon
          return (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`
                flex-1 min-w-0 flex items-center justify-center space-x-1 sm:space-x-2 py-2 sm:py-3 px-2 sm:px-4 rounded-xl transition-all duration-200 border-2 border-transparent font-bold text-sm sm:text-base
                ${activeTab === tab.id
                  ? 'bg-brand-primary text-brand-black border-brand-black shadow-brutal-sm'
                  : 'text-brand-text hover:bg-brand-white hover:shadow-brutal-hover'
                }
              `}
            >
              <Icon size={16} className="sm:w-[18px] sm:h-[18px]" />
              <span className="font-medium truncate">{tab.label}</span>
              <span className={`
                px-1 sm:px-2 py-1 rounded-full text-xs font-bold border-2 border-brand-black
                ${activeTab === tab.id ? 'bg-brand-white text-brand-black' : 'bg-brand-secondary text-brand-black'}
              `}>
                {tab.count}
              </span>
            </button>
          )
        })}
      </div>

      {/* Controls */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex items-center space-x-4">
          <h3 className="text-lg font-semibold text-gray-900">
            {currentImages.length} {activeTab === 'series' ? 'capas' : activeTab === 'covers' ? 'imagens' : activeTab === 'posters' ? 'posters' : 'imagens base'}
          </h3>
          
          {/* View Mode Toggle */}
          <div className="flex bg-gray-100 rounded-lg p-1">
            <button
              onClick={() => setViewMode('grid')}
              className={`p-2 rounded-md transition-colors ${
                viewMode === 'grid' 
                  ? 'bg-white text-purple-600 shadow-sm' 
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <Grid className="w-4 h-4" />
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`p-2 rounded-md transition-colors ${
                viewMode === 'list' 
                  ? 'bg-white text-purple-600 shadow-sm' 
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <List className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* Filters */}
        <div className="flex flex-wrap gap-3">
          <select
            value={filterPlatform}
            onChange={(e) => setFilterPlatform(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-sm"
          >
            <option value="all">Todas as Plataformas</option>
            <option value="netflix">Netflix</option>
            <option value="disney">Disney+</option>
            <option value="amazon">Amazon Prime</option>
          </select>

          <select
            value={filterTitle}
            onChange={(e) => setFilterTitle(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-sm"
          >
            <option value="all">Todos os Títulos</option>
            {getUniqueTitles().map(title => (
              <option key={title} value={title}>{title}</option>
            ))}
          </select>

          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as 'date' | 'platform' | 'name')}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-sm"
          >
            <option value="date">Ordenar por Data</option>
            <option value="platform">Ordenar por Plataforma</option>
            <option value="name">Ordenar por Nome</option>
          </select>
        </div>
      </div>

      {/* Content */}
      <div className="min-h-[400px]">
        {currentImages.length === 0 ? (
          <div className="text-center py-12">
            <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4">
              {activeTab === 'series' && <Film size={24} className="text-gray-400" />}
              {activeTab === 'covers' && <ImageIcon size={24} className="text-gray-400" />}
              {activeTab === 'posters' && <FileImage size={24} className="text-gray-400" />}
              {activeTab === 'base' && <UserIconLucide size={24} className="text-gray-400" />}
            </div>
            <h3 className="text-xl font-bold text-brand-text">
              Nenhuma imagem encontrada
            </h3>
            <p className="text-sm text-brand-text/60 mt-2">
                              {activeTab === 'series' && 'Covers de filmes e séries gerados aparecem aqui.'}
                                  {activeTab === 'covers' && 'Imagens de capa principais (21:9) geradas aparecem aqui.'}
              {activeTab === 'posters' && 'Posters finais criados no Canva aparecem aqui.'}
              {activeTab === 'base' && 'Suas fotos originais enviadas para geração aparecem aqui.'}
            </p>
          </div>
        ) : viewMode === 'grid' ? (
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4">
            {currentImages.map((image, index) => (
              <motion.div
                key={image.id}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: index * 0.05 }}
                className={`group relative aspect-[2/3] bg-brand-white rounded-lg overflow-hidden cursor-pointer border-2 border-brand-black transition-all duration-200 shadow-brutal-sm hover:shadow-brutal-hover hover:translate-x-[-2px] hover:translate-y-[-2px] ${
                  selectionMode && selectedImages.has(image.id) 
                    ? 'bg-brand-primary scale-95' 
                    : 'bg-brand-white'
                }`}
                onClick={() => selectionMode ? toggleImageSelection(image.id, image.url) : setSelectedCover({ url: image.url, image })}
              >
                {/* 🔥 TRATAMENTO ESPECIAL PARA POSTERS */}
                {image.type === 'poster' && !image.url.includes('export-download.canva.com') && !image.url.match(/\.(png|jpg|jpeg|webp)(\?|$)/i) ? (
                  // Poster que é link do Canva - mostrar placeholder
                  <div className="w-full h-full bg-brand-secondary flex items-center justify-center">
                    <div className="text-center p-4">
                      <FileImage size={32} className="text-brand-black mx-auto mb-2" />
                      <p className="text-xs font-bold text-brand-black">Poster no Canva</p>
                      <p className="text-xs text-brand-black/70">Clique para abrir</p>
                    </div>
                  </div>
                ) : (
                  // Imagem normal ou poster com URL de download direto
                  <img
                    src={image.url}
                    alt={image.title || 'Imagem gerada'}
                    className="w-full h-full object-cover transition-transform duration-200 group-hover:scale-105"
                    loading="lazy"
                    onError={(e) => {
                      // Se a imagem falhar e for um poster, mostrar placeholder
                      if (image.type === 'poster') {
                        e.currentTarget.style.display = 'none';
                        const parent = e.currentTarget.parentElement;
                        if (parent) {
                          parent.innerHTML = `
                            <div class="w-full h-full bg-brand-secondary flex items-center justify-center">
                              <div class="text-center p-4">
                                <svg class="w-8 h-8 text-brand-black mx-auto mb-2" fill="currentColor" viewBox="0 0 24 24">
                                  <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8l-6-6z"/>
                                  <polyline points="14,2 14,8 20,8"/>
                                  <line x1="16" y1="13" x2="8" y2="13"/>
                                  <line x1="16" y1="17" x2="8" y2="17"/>
                                  <polyline points="10,9 9,9 8,9"/>
                                </svg>
                                <p class="text-xs font-bold text-brand-black">Poster no Canva</p>
                                <p class="text-xs text-brand-black/70">Clique para abrir</p>
                              </div>
                            </div>
                          `;
                        }
                      }
                    }}
                  />
                )}
                
                {/* Aspect Ratio Tag - 🔥 NOVA FUNCIONALIDADE */}
                {showAspectRatio && (
                  <div className="absolute top-2 left-2">
                    <span className="px-2 py-1 bg-black bg-opacity-70 text-white text-xs font-medium rounded">
                      {image.aspectRatio || '3:4'}
                    </span>
                  </div>
                )}

                {/* Platform Badge */}
                {image.streamingPlatform && (
                  <div className="absolute top-2 right-2">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium text-white ${getPlatformColor(image.streamingPlatform)}`}>
                      {image.streamingPlatform.toUpperCase()}
                    </span>
                  </div>
                )}
                
                {/* Selection indicator */}
                {selectionMode && selectedImages.has(image.id) && (
                  <div className="absolute inset-0 bg-brand-primary bg-opacity-20 flex items-center justify-center">
                    <div className="w-12 h-12 bg-brand-primary rounded-full flex items-center justify-center border-2 border-white shadow-lg">
                      <Check size={24} className="text-white" />
                    </div>
                  </div>
                )}
                
                {/* Hover overlay - Brutalist style */}
                {!selectionMode && (
                  <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-80 transition-all duration-200 flex items-center justify-center opacity-0 group-hover:opacity-100 p-2">
                    <div className="flex flex-col gap-2">
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          setSelectedCover({ url: image.url, image })
                        }}
                        className="p-2 bg-brand-white border-2 border-brand-black shadow-brutal-sm hover:shadow-brutal-hover active:shadow-brutal-pressed transition-all flex items-center justify-center gap-1 text-sm font-bold rounded-md"
                        title="Ver em tamanho completo"
                      >
                        <Eye className="w-4 h-4" />
                        <span className="hidden sm:inline">Ver</span>
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          downloadCover(image.url, image)
                        }}
                        className="p-2 bg-brand-primary border-2 border-brand-black shadow-brutal-sm hover:shadow-brutal-hover active:shadow-brutal-pressed transition-all flex items-center justify-center gap-1 text-sm font-bold rounded-md"
                        title="Baixar"
                      >
                        <Download className="w-4 h-4" />
                        <span className="hidden sm:inline">Baixar</span>
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          setImageToDelete({ id: image.id, type: image.type })
                        }}
                        className="p-2 bg-brand-accent text-white border-2 border-brand-black shadow-brutal-sm hover:shadow-brutal-hover active:shadow-brutal-pressed transition-all flex items-center justify-center gap-1 text-sm font-bold rounded-md"
                        title="Excluir"
                      >
                        <X className="w-4 h-4" />
                        <span className="hidden sm:inline">Excluir</span>
                      </button>
                    </div>
                  </div>
                )}
              </motion.div>
            ))}
          </div>
        ) : (
          <div className="space-y-4">
            {currentImages.map((image, index) => (
              <motion.div
                key={image.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.05 }}
                className="bg-brand-white border-2 border-brand-black rounded-xl p-4 shadow-brutal-sm hover:shadow-brutal-hover transition-all"
              >
                <div className="flex items-center space-x-4">
                  <div className="flex-shrink-0">
                    <img
                      src={image.url}
                      alt={image.title || 'Imagem gerada'}
                      className="w-16 h-24 object-cover rounded-lg border-2 border-brand-black shadow-brutal-sm cursor-pointer hover:scale-105 hover:shadow-brutal-hover transition-all"
                      onClick={() => setSelectedCover({ url: image.url, image })}
                    />
                  </div>
                  
                  <div className="flex-grow">
                    <div className="flex items-start justify-between">
                      <div>
                        <h4 className="font-semibold text-gray-900 flex items-center space-x-2">
                          <UserIconLucide className="w-4 h-4 text-gray-500" />
                          <span>{image.title}</span>
                        </h4>
                        <div className="flex items-center space-x-4 mt-1 text-sm text-gray-600">
                          {image.streamingPlatform && (
                            <div className="flex items-center space-x-1">
                              <Tag className="w-3 h-3" />
                              <span className={`px-2 py-0.5 rounded-full text-xs font-medium text-white ${getPlatformColor(image.streamingPlatform)}`}>
                                {getPlatformName(image.streamingPlatform)}
                              </span>
                            </div>
                          )}
                          <div className="flex items-center space-x-1">
                            <Calendar className="w-3 h-3" />
                            <span>{formatDate(image.createdAt)}</span>
                          </div>
                          <span className="capitalize">{image.photoType}</span>
                          <span className="px-2 py-0.5 bg-gray-100 text-gray-700 rounded text-xs font-medium">
                            {image.aspectRatio || '5:4'}
                          </span>
                        </div>
                      </div>
                      
                      <div className="flex space-x-2">
                        <button
                          onClick={() => setSelectedCover({ url: image.url, image })}
                          className="p-2 bg-brand-white border-2 border-brand-black shadow-brutal-sm hover:shadow-brutal-hover active:shadow-brutal-pressed transition-all rounded-md"
                          title="Ver em tamanho completo"
                        >
                          <Eye className="w-4 h-4 text-brand-black" />
                        </button>
                        <button
                          onClick={() => downloadCover(image.url, image)}
                          className="p-2 bg-brand-primary border-2 border-brand-black shadow-brutal-sm hover:shadow-brutal-hover active:shadow-brutal-pressed transition-all rounded-md"
                          title="Baixar"
                        >
                          <Download className="w-4 h-4 text-brand-black" />
                        </button>
                        <button
                          onClick={() => setImageToDelete({ id: image.id, type: image.type })}
                          className="p-2 bg-brand-accent text-white border-2 border-brand-black shadow-brutal-sm hover:shadow-brutal-hover active:shadow-brutal-pressed transition-all rounded-md"
                          title="Excluir"
                        >
                          <X className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        )}
      </div>

      {/* Selection Actions - Fixo na tela */}
      {selectionMode && (
        <div className="fixed bottom-0 left-0 right-0 bg-brand-white border-t-4 border-brand-black shadow-brutal-lg z-50 p-4">
          <div className="max-w-6xl mx-auto">
            <div className="flex items-center justify-between">
              <div className="text-sm text-brand-text">
                <span className="font-bold text-lg">{selectedImages.size}</span> {selectedImages.size === 1 ? 'imagem selecionada' : 'imagens selecionadas'}
                {maxSelection && (
                  <span className="text-gray-600 ml-2">(máximo recomendado: {maxSelection})</span>
                )}
              </div>
              <div className="flex space-x-3">
                <button
                  onClick={() => setSelectedImages(new Set())}
                  className="px-4 py-2 bg-gray-300 text-brand-black rounded-lg border-2 border-brand-black hover:shadow-brutal-hover transition-colors"
                  disabled={selectedImages.size === 0}
                >
                  Limpar Seleção
                </button>
                {onCancel && (
                  <button
                    onClick={onCancel}
                    className="px-4 py-2 bg-gray-300 text-brand-black rounded-lg border-2 border-brand-black hover:shadow-brutal-hover transition-colors"
                  >
                    Cancelar
                  </button>
                )}
                <button
                  onClick={handleConfirmSelection}
                  disabled={selectedImages.size === 0}
                  className={`px-6 py-2 rounded-lg border-2 border-brand-black font-bold transition-colors ${
                    selectedImages.size > 0
                      ? 'bg-brand-primary text-brand-black hover:shadow-brutal-hover shadow-brutal-sm'
                      : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  }`}
                >
                  ✨ Usar Selecionadas ({selectedImages.size})
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Espaço extra no final quando o painel está fixo */}
      {selectionMode && <div className="h-20" />}

      {/* Modal - Brutalist style */}
      <AnimatePresence>
        {selectedCover && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-80 flex items-center justify-center z-50 p-4"
            onClick={() => setSelectedCover(null)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0, y: 20 }}
              animate={{ scale: 1, opacity: 1, y: 0 }}
              exit={{ scale: 0.9, opacity: 0, y: 20 }}
              className="relative max-w-4xl max-h-full bg-brand-white border-4 border-brand-black shadow-brutal-lg rounded-xl overflow-hidden"
              onClick={(e) => e.stopPropagation()}
            >
              {/* Header with close button */}
              <div className="bg-brand-primary border-b-4 border-brand-black p-4 flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-brand-white border-2 border-brand-black rounded-lg flex items-center justify-center">
                    <Eye className="w-4 h-4 text-brand-black" />
                  </div>
                  <h3 className="font-black text-lg text-brand-black">Visualização da Imagem</h3>
                </div>
                <button
                  onClick={() => setSelectedCover(null)}
                  className="p-2 bg-brand-accent text-white border-2 border-brand-black shadow-brutal-sm hover:shadow-brutal-hover active:shadow-brutal-pressed transition-all rounded-md"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>
              
              {/* Image container */}
              <div className="p-6 bg-brand-white">
                {/* 🔥 TRATAMENTO ESPECIAL PARA POSTERS NO MODAL */}
                {selectedCover.image.type === 'poster' && !selectedCover.url.includes('export-download.canva.com') && !selectedCover.url.match(/\.(png|jpg|jpeg|webp)(\?|$)/i) ? (
                  // Poster que é link do Canva - mostrar placeholder grande
                  <div className="max-w-full max-h-[70vh] mx-auto border-2 border-brand-black rounded-lg shadow-brutal-sm bg-brand-secondary flex items-center justify-center min-h-[400px]">
                    <div className="text-center p-8">
                      <FileImage size={64} className="text-brand-black mx-auto mb-4" />
                      <h4 className="text-xl font-bold text-brand-black mb-2">Poster no Canva</h4>
                      <p className="text-brand-black/70 mb-4">Este poster está disponível no Canva</p>
                      <button
                        onClick={() => window.open(selectedCover.url, '_blank')}
                        className="px-6 py-3 bg-brand-primary text-brand-black border-2 border-brand-black shadow-brutal-sm hover:shadow-brutal-hover active:shadow-brutal-pressed transition-all font-bold rounded-lg flex items-center space-x-2 mx-auto"
                      >
                        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M18 13v6a2 2 0 01-2 2H5a2 2 0 01-2-2V8a2 2 0 012-2h6m4-3h6v6m-11 5L21 3"/>
                        </svg>
                        <span>Abrir no Canva</span>
                      </button>
                    </div>
                  </div>
                ) : (
                  // Imagem normal ou poster com URL de download direto
                  <img
                    src={selectedCover.url}
                    alt={selectedCover.image.title || 'Imagem gerada'}
                    className="max-w-full max-h-[70vh] object-contain mx-auto border-2 border-brand-black rounded-lg shadow-brutal-sm"
                    onError={(e) => {
                      // Se a imagem falhar e for um poster, mostrar placeholder
                      if (selectedCover.image.type === 'poster') {
                        e.currentTarget.style.display = 'none';
                        const parent = e.currentTarget.parentElement;
                        if (parent) {
                          parent.innerHTML = `
                            <div class="max-w-full max-h-[70vh] mx-auto border-2 border-brand-black rounded-lg shadow-brutal-sm bg-brand-secondary flex items-center justify-center min-h-[400px]">
                              <div class="text-center p-8">
                                <svg class="w-16 h-16 text-brand-black mx-auto mb-4" fill="currentColor" viewBox="0 0 24 24">
                                  <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8l-6-6z"/>
                                  <polyline points="14,2 14,8 20,8"/>
                                  <line x1="16" y1="13" x2="8" y2="13"/>
                                  <line x1="16" y1="17" x2="8" y2="17"/>
                                  <polyline points="10,9 9,9 8,9"/>
                                </svg>
                                <h4 class="text-xl font-bold text-brand-black mb-2">Poster no Canva</h4>
                                <p class="text-brand-black/70 mb-4">Este poster está disponível no Canva</p>
                                <button onclick="window.open('${selectedCover.url}', '_blank')" class="px-6 py-3 bg-brand-primary text-brand-black border-2 border-brand-black shadow-brutal-sm hover:shadow-brutal-hover active:shadow-brutal-pressed transition-all font-bold rounded-lg flex items-center space-x-2 mx-auto">
                                  <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M18 13v6a2 2 0 01-2 2H5a2 2 0 01-2-2V8a2 2 0 012-2h6m4-3h6v6m-11 5L21 3"/>
                                  </svg>
                                  <span>Abrir no Canva</span>
                                </button>
                              </div>
                            </div>
                          `;
                        }
                      }
                    }}
                  />
                )}
              </div>
              
              {/* Info Bar - Brutalist style */}
              <div className="bg-brand-secondary border-t-4 border-brand-black p-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-2">
                    <h4 className="font-bold text-brand-black text-lg">{selectedCover.image.title}</h4>
                    <div className="flex items-center space-x-3 text-sm">
                      {selectedCover.image.streamingPlatform && (
                        <span className={`px-3 py-1 rounded-full text-white font-bold border-2 border-brand-black ${getPlatformColor(selectedCover.image.streamingPlatform)}`}>
                          {getPlatformName(selectedCover.image.streamingPlatform)}
                        </span>
                      )}
                      <span className="px-3 py-1 bg-brand-white border-2 border-brand-black rounded-md font-bold text-brand-black">
                        📅 {formatDate(selectedCover.image.createdAt)}
                      </span>
                      <span className="px-3 py-1 bg-brand-white border-2 border-brand-black rounded-md font-bold text-brand-black">
                        📐 {selectedCover.image.aspectRatio || '5:4'}
                      </span>
                    </div>
                  </div>
                  <div className="flex space-x-3">
                    <button
                      onClick={() => setImageToDelete({ id: selectedCover.image.id, type: selectedCover.image.type })}
                      className="px-4 py-2 bg-brand-accent text-white border-2 border-brand-black shadow-brutal-sm hover:shadow-brutal-hover active:shadow-brutal-pressed transition-all font-bold rounded-md flex items-center space-x-2"
                    >
                      <X className="w-4 h-4" />
                      <span>Excluir</span>
                    </button>
                    <button
                      onClick={() => downloadCover(selectedCover.url, selectedCover.image)}
                      className="px-4 py-2 bg-brand-primary text-brand-black border-2 border-brand-black shadow-brutal-sm hover:shadow-brutal-hover active:shadow-brutal-pressed transition-all font-bold rounded-md flex items-center space-x-2"
                    >
                      <Download className="w-4 h-4" />
                      <span>Download</span>
                    </button>
                  </div>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
} 