import { useState } from 'react'
import { 
  createCreditPaymentIntent, 
  cancelPaymentIntent,
  checkUserCreditBalance, 
  stripePromise,
  CREDIT_PLANS 
} from '../lib/stripe'
import { showToast } from '../utils/toast'

export function usePayment() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // 🔥 Create payment intent for credits
  const createPaymentIntent = async (planId: string, userId: string) => {
    setLoading(true)
    setError(null)

    try {
      const plan = CREDIT_PLANS[planId as keyof typeof CREDIT_PLANS]
      if (!plan) {
        throw new Error('Plano de créditos não encontrado')
      }

      console.log('🔥 Criando payment intent para créditos:', { planId, userId, plan })
      
      const result = await createCreditPaymentIntent(planId, userId)
      
      console.log('✅ Payment intent criado com sucesso:', result)
      return result
    } catch (err) {
      console.error('❌ Erro ao criar payment intent:', err)
      const errorMessage = err instanceof Error ? err.message : 'Erro desconhecido'
      setError(errorMessage)
      showToast.error(`Erro ao processar pagamento: ${errorMessage}`)
      throw err
    } finally {
      setLoading(false)
    }
  }

  // 🔥 Cancel payment intent
  const cancelPayment = async (paymentIntentId: string, userId: string) => {
    setLoading(true)
    setError(null)
    
    try {
      console.log('🚫 Cancelando payment intent:', paymentIntentId)
      
      const result = await cancelPaymentIntent(paymentIntentId, userId)
      
      console.log('✅ Payment intent cancelado com sucesso:', result)
      showToast.info('Pagamento cancelado')
      
      return result
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred'
      console.error('❌ Erro ao cancelar payment intent:', errorMessage)
      setError(errorMessage)
      // Don't show error toast for cancellation failures, as it's not critical
      
      throw err
    } finally {
      setLoading(false)
    }
  }

  // 🔥 Check credit balance
  const checkBalance = async (userId: string) => {
    setLoading(true)
    setError(null)

    try {
      console.log('🔥 Verificando saldo de créditos para:', userId)
      
      const balance = await checkUserCreditBalance(userId)
      
      console.log('✅ Saldo de créditos obtido:', balance)
      return balance
    } catch (err) {
      console.error('❌ Erro ao verificar saldo de créditos:', err)
      const errorMessage = err instanceof Error ? err.message : 'Erro desconhecido'
      setError(errorMessage)
      showToast.error(`Erro ao verificar saldo: ${errorMessage}`)
      throw err
    } finally {
      setLoading(false)
    }
  }

  // 🔥 Process payment success
  const processPaymentSuccess = async (userId: string) => {
    try {
      console.log('🔥 Processando sucesso do pagamento para:', userId)
      
      // Recarregar saldo de créditos
      const updatedBalance = await checkUserCreditBalance(userId)
      
      console.log('✅ Saldo atualizado após pagamento:', updatedBalance)
      
      return updatedBalance
    } catch (err) {
      console.error('❌ Erro ao processar sucesso do pagamento:', err)
      showToast.error('Pagamento processado, mas houve um erro ao atualizar o saldo. Contate o suporte.')
      throw err
    }
  }

  return {
    loading,
    error,
    createPaymentIntent,
    cancelPayment,
    checkBalance,
    processPaymentSuccess,
    stripePromise,
    clearError: () => setError(null)
  }
} 