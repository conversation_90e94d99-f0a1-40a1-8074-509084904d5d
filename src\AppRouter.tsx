import { useEffect, useState } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate, useNavigate, useLocation } from 'react-router-dom';
import { supabase } from './lib/supabase';
import { logger } from './utils/logger';
import App from './App';
import AuthCallback from './pages/AuthCallback';
import CanvaPosterPage from './pages/CanvaPosterPage';
import CanvaPosterHistory from './pages/CanvaPosterHistory';
import LandingPage from './pages/LandingPage';
import NewLandingPage from './pages/NewLandingPage';
import AdminPage from './pages/AdminPage';
import AdminPromptTester from './pages/AdminPromptTester';
import AdminMovieStats from './pages/AdminMovieStats';
import AdminMovies from './pages/AdminMovies';

// Wrapper component to handle auth state and routing
function AppRoutes() {
  const [user, setUser] = useState<{ id: string; email?: string; user_metadata?: { role?: string } } | null>(null);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();
  const location = useLocation();
  useEffect(() => {
    // Check for existing session
    const { data } = supabase.auth.onAuthStateChange(
      async (_, session) => {
        if (session?.user) {
          // The user object from the session should contain the role
          setUser(session.user);
        } else {
          setUser(null);
        }
        setLoading(false);

        // Don't redirect if we're on the landing page
        if (window.location.pathname === '/') {
          return;
        }

        // Only handle auth redirects for auth-related pages
        if (session?.user) {
          if (['/login', '/signup'].includes(window.location.pathname)) {
            navigate('/dashboard');
          }
        } else if (!['/login', '/signup', '/auth/callback'].includes(window.location.pathname)) {
          // Only redirect to login if trying to access protected routes
          navigate('/');
        }
      }
    );

    // Initial session check
    const checkSession = async () => {
      try {
        const { data: { session } } = await supabase.auth.getSession();
        setUser(session?.user || null);
      } catch (error) {
        logger.error('Error checking session:', error);
      } finally {
        setLoading(false);
      }
    };

    checkSession();

    return () => {
      data?.subscription?.unsubscribe();
    };
  }, [navigate]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Carregando...</p>
        </div>
      </div>
    );
  }

  return (
    <Routes>
      <Route path="/" element={<LandingPage />} />
      <Route path="/new" element={<NewLandingPage />} />
      <Route path="/dashboard" element={user ? <App /> : <Navigate to="/" state={{ from: location }} replace />} />
      <Route path="/auth/callback" element={<AuthCallback />} />
      <Route path="/login" element={user ? <Navigate to="/dashboard" /> : <App />} />
      <Route path="/signup" element={user ? <Navigate to="/dashboard" /> : <App />} />
      <Route path="/canva-poster" element={user ? <CanvaPosterPage /> : <Navigate to="/" state={{ from: location }} replace />} />
      <Route path="/canva-history" element={user ? <CanvaPosterHistory /> : <Navigate to="/" state={{ from: location }} replace />} />
      <Route 
        path="/admin" 
        element={
          user && user.user_metadata?.role === 'admin'
            ? <AdminPage /> 
            : <Navigate to="/" replace />
        } 
      />
      <Route 
        path="/admin/prompt-tester" 
        element={
          user && user.user_metadata?.role === 'admin'
            ? <AdminPromptTester /> 
            : <Navigate to="/" replace />
        } 
      />
      <Route 
        path="/admin/movie-stats" 
        element={
          user && user.user_metadata?.role === 'admin'
            ? <AdminMovieStats /> 
            : <Navigate to="/" replace />
        } 
      />
      <Route 
        path="/admin/movies" 
        element={
          user && user.user_metadata?.role === 'admin'
            ? <AdminMovies /> 
            : <Navigate to="/" replace />
        } 
      />
      <Route path="*" element={<Navigate to="/" replace />} />
    </Routes>
  );
}

// Main App Router component
export default function AppRouter() {
  return (
    <Router>
      <AppRoutes />
    </Router>
  );
}
