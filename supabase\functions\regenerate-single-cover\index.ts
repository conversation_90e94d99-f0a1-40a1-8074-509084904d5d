import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { movieDatabase } from '../_shared/movieDatabase.ts'
import { 
  buildPrompt, 
  createSafePrompt, 
  pollForResult 
} from '../_shared/promptBuilder.ts'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
}

interface RegenerateSingleCoverRequest {
  movieTitle: string
  originalImageUrl: string
  streamingPlatform: string
  photoType: string
  aspectRatio: string
  userId: string
  gender?: string
  language?: string
  generateTitles?: boolean
  creativityLevel?: 'criativo' | 'rostoFiel' | 'estrito'
}

async function downloadAndUploadToSupabase(
  replicateImageUrl: string, 
  movieTitle: string, 
  streamingPlatform: string
): Promise<string | null> {
  try {
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceRoleKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const supabase = createClient(supabaseUrl, supabaseServiceRoleKey)

    console.log(`Downloading image from Replicate for ${movieTitle}...`)
    
    const imageResponse = await fetch(replicateImageUrl)
    if (!imageResponse.ok) {
      throw new Error(`Failed to download image: ${imageResponse.status}`)
    }
    
    const imageBlob = await imageResponse.blob()
    const imageBuffer = await imageBlob.arrayBuffer()
    
    const timestamp = Date.now()
    const sanitizedTitle = movieTitle.toLowerCase().replace(/[^a-z0-9]/g, '-')
    const filename = `covers/${timestamp}-${sanitizedTitle}-${streamingPlatform}-regenerated.jpg`
    
    console.log(`Uploading to Supabase Storage: ${filename}`)
    
    const { data, error } = await supabase.storage
      .from('generated-images')
      .upload(filename, imageBuffer, {
        contentType: 'image/jpeg',
        upsert: false
      })
    
    if (error) {
      console.error(`Supabase upload error for ${movieTitle}:`, error)
      return null
    }
    
    const { data: { publicUrl } } = supabase.storage
      .from('generated-images')
      .getPublicUrl(filename)
    
    console.log(`Successfully uploaded regenerated ${movieTitle} to Supabase: ${publicUrl}`)
    return publicUrl
    
  } catch (error) {
    console.error(`Error downloading/uploading ${movieTitle}:`, error)
    return null
  }
}

async function regenerateSingleCoverAttempt(
  movie: any,
  originalImageUrl: string,
  streamingPlatform: string,
  photoType: string,
  aspectRatio: string,
  gender?: string,
  language: string = 'portuguese',
  generateTitles: boolean = false,
  creativityLevel: 'criativo' | 'rostoFiel' | 'estrito' = 'rostoFiel'
): Promise<{ success: boolean, image_url?: string, error?: string }> {
  
  try {
    const replicateToken = Deno.env.get('REPLICATE_API_TOKEN')
    if (!replicateToken) {
      throw new Error('REPLICATE_API_TOKEN not found')
    }

    // 🔥 CORRIGIDO: Usar estrutura do banco unified_movies_series
    const moviePrompt = movie.base_prompt || movie.poster_style_prompt || movie.safe_prompt

    console.log(`🔄 Regenerating ${movie.title}`)
    console.log(`📋 Gender: ${gender}, Titles: ${generateTitles}, Creativity: ${creativityLevel}`)
    console.log(`🎬 Movie object:`, JSON.stringify(movie, null, 2))
    console.log(`🎯 Movie prompt (${language}):`, moviePrompt)
    
    if (!moviePrompt) {
      throw new Error(`Movie prompt not found for ${movie.title} in language ${language}`)
    }

    // Usar buildPrompt com variation=1 para regeneração (diferente da geração inicial)
    const optimizedPrompt = buildPrompt(moviePrompt, {
      language: language as 'english' | 'portuguese',
      photoType: photoType as 'individual' | 'casal',
      gender: gender as 'male' | 'female' | undefined,
      addTitle: generateTitles,
      movieTitle: movie.title,
      coverIndex: Math.floor(Math.random() * 12),
      creativityLevel: creativityLevel,
      variation: 1 // Para criar uma versão diferente
    })

    console.log(`🔍 Regeneration prompt tokens estimados: ${optimizedPrompt ? optimizedPrompt.split(' ').length : 0}`)
    console.log(`🔍 Regeneration prompt: ${optimizedPrompt ? optimizedPrompt.substring(0, 200) + '...' : 'PROMPT VAZIO'}`)
    
    // Converter aspect ratios antigos para os suportados pelo Flux Kontext
    const validAspectRatio = aspectRatio === '3:5' ? '3:4' : 
                            aspectRatio === '5:3' ? '4:3' : 
                            aspectRatio === '6:5' ? '5:4' : 
                            aspectRatio === '5:4' ? '5:4' : 
                            aspectRatio === '9:10' ? '3:4' :  // NOVO: converter 9:10 para 3:4
                            aspectRatio

    const replicatePayload = {
      input: {
        input_image: originalImageUrl,
        prompt: optimizedPrompt,
        aspect_ratio: validAspectRatio,
        output_format: 'jpg',
        safety_tolerance: 5,
        num_inference_steps: 28,
        guidance_scale: 3.5
      }
    }

    const prediction = await fetch('https://api.replicate.com/v1/models/black-forest-labs/flux-kontext-max/predictions', {
      method: 'POST',
      headers: {
        'Authorization': `Token ${replicateToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(replicatePayload)
    })

    if (!prediction.ok) {
      const errorText = await prediction.text()
      throw new Error(`Replicate API error: ${errorText}`)
    }

    const predictionData = await prediction.json()
    console.log(`🚀 Started regeneration for ${movie.title}: ${predictionData.status}`)

    // Polling para resultado
    const pollResult = await pollForResult(predictionData.id, movie.title, replicateToken)
    
    if (pollResult.imageUrl) {
      // Download e upload para Supabase
      const supabaseImageUrl = await downloadAndUploadToSupabase(pollResult.imageUrl, movie.title, streamingPlatform)
      
      if (supabaseImageUrl) {
        return { success: true, image_url: supabaseImageUrl }
      } else {
        return { success: false, error: 'Failed to upload to Supabase Storage' }
      }
    } else {
      // 🔥 PRESERVAR O ERRO ORIGINAL (incluindo E005)
      return { success: false, error: pollResult.error || 'Unknown polling error' }
    }

  } catch (error) {
    console.error(`❌ Error regenerating ${movie.title}:`, error)
    return { success: false, error: error.message }
  }
}

serve(async (req) => {
  // 🔥 CORS PREFLIGHT - DEVE VIR ANTES DA AUTENTICAÇÃO
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  // 1. AUTHENTICATION
  const authHeader = req.headers.get('Authorization')
  if (!authHeader) {
    return new Response(JSON.stringify({ error: 'Falta o cabeçalho de autenticação' }), { status: 401, headers: corsHeaders })
  }
  
  const supabase = createClient(
    Deno.env.get('SUPABASE_URL')!,
    Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!,
  )
  
  const { data: { user } } = await supabase.auth.getUser(authHeader.replace('Bearer ', ''));
  if (!user) {
    return new Response(JSON.stringify({ error: 'Token inválido ou expirado' }), { status: 401, headers: corsHeaders })
  }

  try {
    const {
      movieTitle,
      originalImageUrl,
      streamingPlatform,
      photoType,
      aspectRatio,
      gender,
      language = 'portuguese',
      generateTitles = false,
      creativityLevel = 'rostoFiel'
    }: RegenerateSingleCoverRequest = await req.json()
    
    // O userId agora vem do token, não do corpo da requisição
    const userId = user.id;

    // 🔥 CHECAR CRÉDITOS ANTES DE REGENERAR
    const creditsNeeded = 1; // Regeneração custa 1 crédito
    
    // Verificar saldo de créditos
    const { data: purchases } = await supabase
      .from('credit_purchases')
      .select('credits')
      .eq('user_id', user.id)
      .eq('status', 'completed')

    const { data: usage } = await supabase
      .from('credit_usage')
      .select('credits_used')
      .eq('user_id', user.id)

    const totalCredits = purchases?.reduce((sum, p) => sum + p.credits, 0) || 0
    const usedCredits = usage?.reduce((sum, u) => sum + u.credits_used, 0) || 0
    const availableCredits = totalCredits - usedCredits

    if (availableCredits < creditsNeeded) {
      return new Response(
        JSON.stringify({ 
          error: 'Créditos insuficientes para regenerar capa',
          availableCredits,
          requiredCredits: creditsNeeded,
          message: 'Compre mais créditos para continuar'
        }),
        { status: 402, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }
    
    console.log(`💳 Usuário tem ${availableCredits} créditos. Necessário: ${creditsNeeded}`);
    
    console.log(`🔄 Starting single cover regeneration: ${movieTitle}`)

    // 🔥 BUSCAR FILME NO BANCO DE DADOS unified_movies_series
    const { data: movie, error: movieError } = await supabase
      .from('unified_movies_series')
      .select('*')
      .eq('title', movieTitle)
      .eq('streaming_platform', streamingPlatform)
      .eq('is_active', true)
      .single()

    if (movieError || !movie) {
      console.error(`Movie not found in database:`, movieError)
      throw new Error(`Movie ${movieTitle} not found in ${streamingPlatform} database`)
    }

    console.log(`🎬 Found movie in database:`, movie.title)

    // 🔄 TENTATIVA 1: REGENERAÇÃO NORMAL
    let attempt = await regenerateSingleCoverAttempt(
      movie,
      originalImageUrl,
      streamingPlatform,
      photoType,
      aspectRatio,
      gender,
      language,
      generateTitles,
      creativityLevel
    )

    let safePromptUsed = false

    // 🛡️ TENTATIVA 2: PROMPT SAFE (se E005) - CORREÇÃO: só se falhou na primeira tentativa
    if (!attempt.success && attempt.error?.includes('E005')) {
      console.log(`🛡️ Trying safe prompt for regeneration of ${movieTitle}`)
      
      // 🔥 USAR SAFE_PROMPT DO BANCO DE DADOS
      const safeMovie = { 
        ...movie, 
        base_prompt: movie.safe_prompt || movie.base_prompt // Usar safe_prompt se disponível
      }
      
      attempt = await regenerateSingleCoverAttempt(
        safeMovie,
        originalImageUrl,
        streamingPlatform,
        photoType,
        aspectRatio,
        gender,
        language,
        generateTitles,
        creativityLevel
      )

      if (attempt.success) {
        safePromptUsed = true
      }
    }

    // 🔥 CORREÇÃO: Para regeneração individual, criar nova geração ou adicionar à existente
    if (attempt.success) {
      // 🔥 CONSUMIR CRÉDITO APENAS APÓS SUCESSO
      console.log(`💳 Consumindo ${creditsNeeded} crédito pela regeneração...`)
      
      const consumeResponse = await supabase.functions.invoke('consume-credits', {
        body: { 
          amount: creditsNeeded, 
          description: `Regeneração de capa: ${movieTitle}` 
        },
        headers: { Authorization: authHeader }
      })

      if (consumeResponse.error || !consumeResponse.data?.success) {
        console.error('❌ Erro ao consumir crédito após sucesso:', consumeResponse.error)
        // Não falhar a geração por causa disso, apenas logar
      } else {
        console.log(`✅ Crédito consumido com sucesso para regeneração`)
      }
      
      try {
        // 🔥 SALVAR NA TABELA cover_images (para compatibilidade com sistema antigo)
        const { error: coverImageError } = await supabase
          .from('cover_images')
          .insert({
            user_id: userId,
            image_url: attempt.image_url,
            movie_title: movieTitle,
            streaming_platform: streamingPlatform,
            photo_type: photoType,
            aspect_ratio: aspectRatio,
            is_regenerated: true, // 🔥 MARCAR COMO REGENERAÇÃO
            generation_id: null // Não associar a uma geração específica
          })

        if (coverImageError) {
          console.error('Error saving to cover_images:', coverImageError)
        } else {
          console.log(`✅ Regeneration saved to cover_images: ${movieTitle}`)
        }

        // 🔥 TAMBÉM CRIAR UMA NOVA ENTRADA DE GERAÇÃO SEPARADA (para Series & Films)
        const { data: newGeneration, error: insertError } = await supabase
          .from('cover_generations')
          .insert({
            user_id: userId,
            original_image_url: originalImageUrl,
            photo_type: photoType,
            streaming_platform: streamingPlatform,
            generated_covers: [{
              movie_title: movieTitle,
              image_url: attempt.image_url,
              streaming_platform: streamingPlatform,
              photo_type: photoType,
              aspect_ratio: aspectRatio,
              regenerated: true,
              created_at: new Date().toISOString()
            }],
            status: 'completed',
            completed_at: new Date().toISOString(),
            total_covers: 1 // Apenas 1 capa regenerada
          })
          .select()
          .single()

        if (insertError) {
          console.error('Error creating regeneration entry:', insertError)
        } else {
          console.log(`✅ Regeneration saved to Series & Films: ${movieTitle}`)
        }
      } catch (error) {
        console.error('Error saving regeneration:', error)
      }

      console.log(`✅ Regeneration completed: ${movieTitle}`)

      return new Response(
        JSON.stringify({
          success: true,
          image_url: attempt.image_url,
          movie_title: movieTitle,
          safe_prompt_used: safePromptUsed,
          generated_at: new Date().toISOString()
        }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    } else {
      console.log(`❌ Regeneration failed: ${attempt.error}`)

      return new Response(
        JSON.stringify({
          success: false,
          error: attempt.error || 'Regeneration failed',
          movie_title: movieTitle,
          generated_at: new Date().toISOString()
        }),
        { 
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

  } catch (error) {
    console.error('Error in regenerate-single-cover:', error)
    return new Response(
      JSON.stringify({ 
        success: false,
        error: error.message,
        generated_at: new Date().toISOString()
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
}) 