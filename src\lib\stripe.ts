import { loadStripe } from '@stripe/stripe-js'
import { supabase } from './supabase'
import { logger } from '../utils/logger'

// Stripe publishable keys
const stripePublishableKey = import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY || 'pk_test_51PlGJpDOvhLb9hmWCxdc7fFHVqc3Cifn2KCfgLjhFYwiQKy8sxgYdqCdcdUFENebYdTueAbOrfVP2vrNzVUf5auY00mIuAepTw'

// Initialize Stripe
export const stripePromise = loadStripe(stripePublishableKey)

// 🔥 Stripe Product IDs para Créditos
const STRIPE_CREDIT_PRODUCT_IDS = {
  starter_credits: import.meta.env.VITE_STRIPE_STARTER_CREDITS_ID || 'prod_SVeFgga19mY0Gl', // Test ID
  popular_credits: import.meta.env.VITE_STRIPE_POPULAR_CREDITS_ID || 'prod_SVeHwRl5tn4cP6', // Test ID
  master_credits: import.meta.env.VITE_STRIPE_MASTER_CREDITS_ID || 'prod_SVeIPaf5R93PYg' // Test ID
}

// 🔥 Planos de Créditos
export const CREDIT_PLANS = {
  starter_credits: {
    id: 'starter_credits',
    name: 'Starter Credits',
    credits: 30,
    price: 4990, // R$ 49,90 in cents
    priceDisplay: 'R$ 49,90',
    pricePerCredit: 'R$ 1,66',
    stripeProductId: STRIPE_CREDIT_PRODUCT_IDS.starter_credits,
    features: [
      '30 créditos para usar como quiser',
      'Exemplo: 27 capas + 1 poster (30 créditos)',
      'Ou 30 capas individuais',
      'Ou 10 posters completos',
      'Download em alta qualidade',
      'Suporte por email'
    ],
    description: 'Perfeito para começar'
  },
  popular_credits: {
    id: 'popular_credits',
    name: 'Popular Credits',
    credits: 100,
    price: 9990, // R$ 99,90 in cents
    priceDisplay: 'R$ 99,90',
    pricePerCredit: 'R$ 1,00',
    stripeProductId: STRIPE_CREDIT_PRODUCT_IDS.popular_credits,
    features: [
      '100 créditos para usar como quiser',
      'Exemplo: 85 capas + 5 posters (100 créditos)',
      'Ou 100 capas individuais',
      'Ou 33 posters completos',
      'Download em alta qualidade',
      'Suporte prioritário',
      'Economia de R$ 66,00'
    ],
    description: 'Mais popular - Melhor custo-benefício',
    popular: true
  },
  master_credits: {
    id: 'master_credits',
    name: 'Master Credits',
    credits: 300,
    price: 24990, // R$ 249,90 in cents
    priceDisplay: 'R$ 249,90',
    pricePerCredit: 'R$ 0,83',
    stripeProductId: STRIPE_CREDIT_PRODUCT_IDS.master_credits,
    features: [
      '300 créditos para usar como quiser',
      'Exemplo: 255 capas + 15 posters (300 créditos)',
      'Ou 300 capas individuais',
      'Ou 100 posters completos',
      'Download em alta qualidade',
      'Suporte prioritário',
      'Acesso antecipado a novos recursos',
      'Economia de R$ 248,00'
    ],
    description: 'Para uso profissional ou múltiplos projetos'
  }
}

// 🔥 Create payment intent para créditos
export async function createCreditPaymentIntent(planId: string, userId: string) {
  const plan = CREDIT_PLANS[planId as keyof typeof CREDIT_PLANS]
  
  if (!plan) {
    throw new Error('Invalid plan ID')
  }

  // Payment intent creation logs removed for production

  const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/create-payment-intent`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`,
    },
    body: JSON.stringify({
      amount: plan.price,
      currency: 'brl',
      planId,
      userId,
      credits: plan.credits,
      productId: `credit_pack_${planId}`,
      planType: 'credits',
      metadata: {
        planName: plan.name,
      }
    }),
  })

  if (!response.ok) {
    const errorData = await response.json()
    logger.error('❌ Error creating payment intent:', errorData)
    throw new Error(errorData.error || 'Failed to create payment intent')
  }

  const data = await response.json()
  logger.log('✅ Payment intent created:', data)
  return data
}

// 🔥 Cancel payment intent
export async function cancelPaymentIntent(paymentIntentId: string, userId: string) {
  logger.log('🚫 Canceling payment intent:', { paymentIntentId, userId })

  const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/cancel-payment-intent`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`,
    },
    body: JSON.stringify({
      paymentIntentId,
      userId
    }),
  })

  if (!response.ok) {
    const errorData = await response.json()
    logger.error('❌ Error canceling payment intent:', errorData)
    throw new Error(errorData.error || 'Failed to cancel payment intent')
  }

  const data = await response.json()
  logger.log('✅ Payment intent cancellation response:', data)
  return data
}

// 🔥 Check user credit balance
export async function checkUserCreditBalance(userId: string) {
  try {
    const { data: { session }, error: sessionError } = await supabase.auth.getSession()
    
    if (sessionError) {
      logger.error('Erro na sessão:', sessionError)
      throw new Error('Erro na autenticação')
    }
    
    if (!session?.access_token) {
      logger.error('Sessão ou token não encontrado')
      throw new Error('Usuário não autenticado')
    }

    const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/check-credit-balance`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${session.access_token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ userId }),
    })

    if (!response.ok) {
      const errorText = await response.text()
      logger.error('Erro na resposta:', errorText)
      throw new Error(`Erro ao verificar saldo de créditos: ${response.status} ${response.statusText}`)
    }

    const data = await response.json()
    
    return {
      totalCredits: data.totalCredits || 0,
      availableCredits: data.availableCredits || 0,
      usedCredits: data.usedCredits || 0,
      creditHistory: data.creditHistory || [],
      recentUsage: data.recentUsage || []
    }
  } catch (error) {
    logger.error('Erro ao verificar saldo de créditos:', error)
    return {
      totalCredits: 0,
      availableCredits: 0,
      usedCredits: 0,
      creditHistory: [],
      recentUsage: []
    }
  }
}

// 🔥 Consume credits
export async function consumeCredits(userId: string, amount: number, description: string) {
  try {
    const { data: { session } } = await supabase.auth.getSession()
    if (!session) {
      throw new Error('Usuário não autenticado')
    }

    // 🔥 REMOVIDO - Créditos são consumidos apenas nas Edge Functions quando sucesso
  console.log('💳 Compra de créditos não consome créditos - seria redundante')
  return
  const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/consume-credits`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${session.access_token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ 
        userId, 
        amount, 
        description,
        timestamp: new Date().toISOString()
      }),
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.error || 'Erro ao consumir créditos')
    }

    return await response.json()
  } catch (error) {
    logger.error('Erro ao consumir créditos:', error)
    throw error
  }
}

// 🔥 Check if user has active subscription (apenas créditos)
export async function checkUserSubscription(userId: string) {
  const creditBalance = await checkUserCreditBalance(userId)
  return {
    hasSubscription: creditBalance.availableCredits > 0,
    plan: creditBalance.availableCredits > 0 ? 'credit_based' : null,
    balance: creditBalance,
    credits: creditBalance
  }
}

// 🔥 Calculate credits needed for streaming platform
export function calculateCreditsNeeded(streamingPlatform: string, includesPoster: boolean = true): number {
  const coverCredits = {
    'netflix': 12,
    'disney': 9,
    'amazon': 11
  }
  
  const coversNeeded = coverCredits[streamingPlatform as keyof typeof coverCredits] || 12
  const posterCredits = includesPoster ? 3 : 0
  
  return coversNeeded + posterCredits
}