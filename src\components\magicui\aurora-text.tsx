import React from "react";

interface AuroraTextProps {
  children: React.ReactNode;
  className?: string;
  colors?: string[];
  speed?: number;
}

export function AuroraText({
  children,
  className = "",
  colors = ["#FF0080", "#7928CA", "#0070F3"],
  speed = 1,
}: AuroraTextProps) {
  const animationDuration = 8 / speed;

  return (
    <span 
      className={`relative inline-block font-inherit ${className}`}
      style={{
        background: `linear-gradient(120deg, ${colors.join(", ")})`,
        backgroundSize: "200% 200%",
        animation: `aurora ${animationDuration}s ease-in-out infinite`,
        WebkitBackgroundClip: "text",
        WebkitTextFillColor: "transparent",
        backgroundClip: "text",
        color: "transparent",
      }}
    >
      {children}
      
      <style>{`
        @keyframes aurora {
          0%, 100% {
            background-position: 0% 50%;
          }
          50% {
            background-position: 100% 50%;
          }
        }
      `}</style>
    </span>
  );
} 