import React, { useState } from 'react'
import { X } from 'lucide-react'
import { supabase } from '../lib/supabase'
import { showToast } from '../utils/toast'

interface SetPasswordModalProps {
  isOpen: boolean
  onClose: () => void
  userEmail: string
}

export default function SetPasswordModal({ isOpen, onClose, userEmail }: SetPasswordModalProps) {
  const [password, setPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [loading, setLoading] = useState(false)

  if (!isOpen) return null

  const handleSetPassword = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (password !== confirmPassword) {
      showToast.error('As senhas não coincidem')
      return
    }

    if (password.length < 6) {
      showToast.error('A senha deve ter pelo menos 6 caracteres')
      return
    }

    setLoading(true)
    try {
      // Update user password
      const { error } = await supabase.auth.updateUser({
        password: password
      })

      if (error) throw error

      showToast.success('Senha definida com sucesso! Agora você pode fazer login com email e senha.')
      onClose()
      setPassword('')
      setConfirmPassword('')
    } catch (error: any) {
      console.error('Error setting password:', error)
      showToast.error(error.message || 'Erro ao definir senha')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white border-2 border-black shadow-brutal-lg max-w-md w-full p-6 relative">
        {/* Decorative elements */}
        <div className="absolute -top-2 -left-2 w-4 h-4 bg-yellow-400 border-2 border-black"></div>
        <div className="absolute -bottom-2 -right-2 w-4 h-4 bg-pink-500 border-2 border-black"></div>
        
        {/* Close button */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 p-1 hover:bg-gray-100 rounded"
        >
          <X size={20} />
        </button>

        <div className="mb-6">
          <h2 className="text-2xl font-bold text-black mb-2">
            🔐 Definir Senha
          </h2>
          <p className="text-gray-600">
            Defina uma senha para poder fazer login com <strong>{userEmail}</strong> usando email e senha.
          </p>
        </div>

        <form onSubmit={handleSetPassword} className="space-y-4">
          <div>
            <label className="block text-sm font-bold text-black mb-2">
              Nova Senha
            </label>
            <input
              type="password"
              placeholder="Digite sua nova senha"
              value={password}
              onChange={e => setPassword(e.target.value)}
              required
              minLength={6}
              className="w-full px-4 py-3 bg-white border-2 border-black focus:ring-2 focus:ring-pink-500 focus:border-transparent text-black placeholder-gray-500 font-medium
              shadow-[4px_4px_0_0_#000] focus:shadow-[2px_2px_0_0_#000] focus:outline-none transition-all"
            />
          </div>

          <div>
            <label className="block text-sm font-bold text-black mb-2">
              Confirmar Senha
            </label>
            <input
              type="password"
              placeholder="Digite novamente sua senha"
              value={confirmPassword}
              onChange={e => setConfirmPassword(e.target.value)}
              required
              minLength={6}
              className="w-full px-4 py-3 bg-white border-2 border-black focus:ring-2 focus:ring-pink-500 focus:border-transparent text-black placeholder-gray-500 font-medium
              shadow-[4px_4px_0_0_#000] focus:shadow-[2px_2px_0_0_#000] focus:outline-none transition-all"
            />
          </div>

          <div className="flex gap-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 bg-gray-200 text-black font-bold py-3 px-4 border-2 border-black 
              shadow-[4px_4px_0_0_#000] hover:shadow-[2px_2px_0_0_#000] 
              active:shadow-[0px_0px_0_0_#000] active:translate-x-1 active:translate-y-1
              transition-all duration-150"
            >
              Cancelar
            </button>
            
            <button
              type="submit"
              disabled={loading || !password || !confirmPassword}
              className="flex-1 bg-brand-primary text-brand-black font-bold py-3 px-4 border-2 border-brand-black 
              shadow-[4px_4px_0_0_#000] hover:shadow-[2px_2px_0_0_#000] 
              active:shadow-[0px_0px_0_0_#000] active:translate-x-1 active:translate-y-1
              transition-all duration-150 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? (
                <div className="flex items-center justify-center gap-2">
                  <div className="w-4 h-4 border-2 border-black border-t-transparent rounded-full animate-spin"></div>
                  Definindo...
                </div>
              ) : (
                'Definir Senha'
              )}
            </button>
          </div>
        </form>

        <div className="mt-4 p-3 bg-blue-50 border-2 border-blue-300 rounded">
          <p className="text-sm text-blue-800">
            <strong>💡 Dica:</strong> Após definir a senha, você poderá fazer login tanto com Google quanto com email e senha.
          </p>
        </div>
      </div>
    </div>
  )
} 