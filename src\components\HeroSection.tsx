import React from 'react'
import { useTranslation } from 'react-i18next'
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Star, Play } from 'lucide-react'

interface HeroSectionProps {
  onGetStarted: () => void
}

export default function HeroSection({ onGetStarted }: HeroSectionProps) {
  const { t } = useTranslation()

  return (
    <section className="relative py-20 px-4 sm:px-6 lg:px-8 overflow-hidden">
      <div className="max-w-7xl mx-auto">
        <div className="text-center">
          {/* Badge */}
          <div className="inline-flex items-center space-x-2 bg-neu-base rounded-full px-6 py-3 shadow-neu-sm mb-8 animate-float">
            <div className="w-2 h-2 bg-primary-500 rounded-full animate-pulse-soft"></div>
            <span className="text-sm font-medium text-gray-700">{t('hero.badge')}</span>
          </div>

          {/* Main Heading */}
          <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-800 mb-6 leading-tight">
            {t('hero.title')}
            <span className="block text-primary-600">{t('hero.titleSpan')}</span>
          </h1>

          {/* Subtitle */}
          <p className="text-xl text-gray-600 mb-12 max-w-3xl mx-auto leading-relaxed">
            {t('hero.subtitle')}
          </p>

          {/* CTA Button */}
          <div className="flex flex-col sm:flex-row items-center justify-center gap-4 mb-16">
            <button
              onClick={onGetStarted}
              className="group bg-neu-base rounded-2xl px-8 py-4 shadow-neu hover:shadow-neu-pressed transition-all duration-200 flex items-center space-x-3 text-lg font-semibold text-gray-800 active:scale-95"
            >
              <div className="w-10 h-10 bg-primary-100 rounded-xl flex items-center justify-center group-hover:bg-primary-200 transition-colors">
                <Sparkles className="w-5 h-5 text-primary-600" />
              </div>
              <span>{t('hero.startCreating')}</span>
            </button>

            <button className="group bg-neu-base rounded-2xl px-8 py-4 shadow-neu-sm hover:shadow-neu transition-all duration-200 flex items-center space-x-3 text-lg font-medium text-gray-600">
              <div className="w-10 h-10 bg-gray-100 rounded-xl flex items-center justify-center group-hover:bg-gray-200 transition-colors">
                <Play className="w-5 h-5 text-gray-600" />
              </div>
              <span>{t('hero.watchDemo')}</span>
            </button>
          </div>

          {/* Features Grid */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
            <div className="bg-neu-base rounded-2xl p-6 shadow-neu-sm hover:shadow-neu transition-all duration-300 group">
              <div className="w-12 h-12 bg-primary-100 rounded-xl flex items-center justify-center mx-auto mb-4 group-hover:bg-primary-200 transition-colors">
                <Zap className="w-6 h-6 text-primary-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-800 mb-2">{t('hero.lightningFast')}</h3>
              <p className="text-gray-600">{t('hero.lightningFastDesc')}</p>
            </div>

            <div className="bg-neu-base rounded-2xl p-6 shadow-neu-sm hover:shadow-neu transition-all duration-300 group">
              <div className="w-12 h-12 bg-accent-100 rounded-xl flex items-center justify-center mx-auto mb-4 group-hover:bg-accent-200 transition-colors">
                <Star className="w-6 h-6 text-accent-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-800 mb-2">{t('hero.premiumQuality')}</h3>
              <p className="text-gray-600">{t('hero.premiumQualityDesc')}</p>
            </div>

            <div className="bg-neu-base rounded-2xl p-6 shadow-neu-sm hover:shadow-neu transition-all duration-300 group">
              <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center mx-auto mb-4 group-hover:bg-green-200 transition-colors">
                <Sparkles className="w-6 h-6 text-green-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-800 mb-2">{t('hero.multipleStyles')}</h3>
              <p className="text-gray-600">{t('hero.multipleStylesDesc')}</p>
            </div>
          </div>

          {/* Stats */}
          <div className="mt-16 grid grid-cols-2 md:grid-cols-4 gap-8 max-w-2xl mx-auto">
            <div className="text-center">
              <div className="bg-neu-base rounded-xl p-4 shadow-neu-sm mb-2">
                <p className="text-2xl font-bold text-primary-600">10K+</p>
              </div>
              <p className="text-sm text-gray-600">{t('hero.coversCreated')}</p>
            </div>
            <div className="text-center">
              <div className="bg-neu-base rounded-xl p-4 shadow-neu-sm mb-2">
                <p className="text-2xl font-bold text-accent-600">3</p>
              </div>
              <p className="text-sm text-gray-600">{t('hero.platforms')}</p>
            </div>
            <div className="text-center">
              <div className="bg-neu-base rounded-xl p-4 shadow-neu-sm mb-2">
                <p className="text-2xl font-bold text-green-600">98%</p>
              </div>
              <p className="text-sm text-gray-600">{t('hero.satisfaction')}</p>
            </div>
            <div className="text-center">
              <div className="bg-neu-base rounded-xl p-4 shadow-neu-sm mb-2">
                <p className="text-2xl font-bold text-orange-600">2min</p>
              </div>
              <p className="text-sm text-gray-600">{t('hero.avgTime')}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Background Elements */}
      <div className="absolute top-20 left-10 w-20 h-20 bg-primary-200 rounded-full opacity-20 animate-float"></div>
      <div className="absolute bottom-20 right-10 w-16 h-16 bg-accent-200 rounded-full opacity-20 animate-float" style={{ animationDelay: '2s' }}></div>
      <div className="absolute top-1/2 left-1/4 w-12 h-12 bg-green-200 rounded-full opacity-20 animate-float" style={{ animationDelay: '4s' }}></div>
    </section>
  )
} 