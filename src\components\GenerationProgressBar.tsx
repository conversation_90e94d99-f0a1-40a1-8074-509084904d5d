import React from 'react'
import { useTranslation } from 'react-i18next'
import { motion } from 'framer-motion'
import { Loader2, CheckCircle, XCircle, Clock } from 'lucide-react'

interface GenerationProgressBarProps {
  progress: {
    total: number
    completed: number
    failed: number
    inProgress: number
  }
  isGenerating: boolean
  currentMovie?: string | null
}

export default function GenerationProgressBar({ progress, isGenerating, currentMovie }: GenerationProgressBarProps) {
  const { t } = useTranslation()
  const { total, completed, failed, inProgress } = progress
  const completedPercentage = total > 0 ? (completed / total) * 100 : 0
  const failedPercentage = total > 0 ? (failed / total) * 100 : 0
  const inProgressPercentage = total > 0 ? (inProgress / total) * 100 : 0

  if (!isGenerating && total === 0) return null

  return (
    <div className="w-full bg-brand-white border-2 border-brand-black shadow-brutal-sm p-4 mb-6">
      <div className="flex items-center justify-between mb-3">
        <div className="flex flex-col">
          <h3 className="text-lg font-bold text-brand-black flex items-center gap-2">
            {isGenerating ? (
              <>
                <Loader2 className="h-5 w-5 animate-spin text-brand-red" />
                {t('progressBar.generatingCovers')}
              </>
            ) : (
              <>
                <CheckCircle className="h-5 w-5 text-green-600" />
                {t('progressBar.generationComplete')}
              </>
            )}
          </h3>
          {isGenerating && currentMovie && (
            <motion.p 
              className="text-sm text-gray-600 mt-1"
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              key={currentMovie}
            >
              {t('progressBar.processing')}: <span className="font-semibold text-brand-primary">{currentMovie}</span>
            </motion.p>
          )}
        </div>
        <div className="text-sm font-bold text-brand-black">
          {completed + failed} / {total}
        </div>
      </div>

      {/* Progress Bar */}
      <div className="w-full h-4 bg-gray-200 border-2 border-brand-black shadow-brutal-xs mb-3 overflow-hidden">
        <div className="h-full flex">
          {/* Completed */}
          <motion.div
            className="bg-green-500 border-r border-brand-black"
            style={{ width: `${completedPercentage}%` }}
            initial={{ width: 0 }}
            animate={{ width: `${completedPercentage}%` }}
            transition={{ duration: 0.5, ease: "easeOut" }}
          />
          
          {/* In Progress */}
          <motion.div
            className="bg-yellow-400 border-r border-brand-black relative overflow-hidden"
            style={{ width: `${inProgressPercentage}%` }}
            initial={{ width: 0 }}
            animate={{ width: `${inProgressPercentage}%` }}
            transition={{ duration: 0.3 }}
          >
            {inProgress > 0 && (
              <motion.div
                className="absolute inset-0 bg-yellow-300"
                animate={{ x: ['-100%', '100%'] }}
                transition={{ repeat: Infinity, duration: 1.5, ease: "linear" }}
              />
            )}
          </motion.div>
          
          {/* Failed */}
          <motion.div
            className="bg-red-500"
            style={{ width: `${failedPercentage}%` }}
            initial={{ width: 0 }}
            animate={{ width: `${failedPercentage}%` }}
            transition={{ duration: 0.5, ease: "easeOut" }}
          />
        </div>
      </div>

      {/* Status Details */}
      <div className="flex justify-between text-sm font-bold">
        <div className="flex items-center gap-4">
          {completed > 0 && (
            <div className="flex items-center gap-1 text-green-600">
              <CheckCircle className="h-4 w-4" />
              <span>{completed} {t('progressBar.completed')}</span>
            </div>
          )}
          
          {inProgress > 0 && (
            <div className="flex items-center gap-1 text-yellow-600">
              <Clock className="h-4 w-4" />
              <span>{inProgress} {t('progressBar.inProgress')}</span>
            </div>
          )}
          
          {failed > 0 && (
            <div className="flex items-center gap-1 text-red-600">
              <XCircle className="h-4 w-4" />
              <span>{failed} {t('progressBar.failed')}</span>
            </div>
          )}
        </div>
        
        <div className="text-brand-black">
          {completedPercentage.toFixed(0)}% {t('progressBar.complete')}
        </div>
      </div>
    </div>
  )
} 