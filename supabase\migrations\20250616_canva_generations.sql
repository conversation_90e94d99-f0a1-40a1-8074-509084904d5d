-- Create canva_generations table for tracking poster generation
CREATE TABLE IF NOT EXISTS public.canva_generations (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id),
  status TEXT NOT NULL CHECK (status IN ('processing', 'completed', 'failed')),
  template_id TEXT NOT NULL,
  request_data JSONB NOT NULL,
  design_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  completed_at TIMESTAMP WITH TIME ZONE,
  error_message TEXT
);

-- Add RLS policies
ALTER TABLE public.canva_generations ENABLE ROW LEVEL SECURITY;

-- Policy for users to view only their own generations
CREATE POLICY "Users can view their own generations" 
  ON public.canva_generations 
  FOR SELECT 
  USING (auth.uid() = user_id);

-- Policy for users to insert their own generations
CREATE POLICY "Users can add their own generations" 
  ON public.canva_generations 
  FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

-- Policy for users to update only their own generations
CREATE POLICY "Users can update their own generations" 
  ON public.canva_generations 
  FOR UPDATE 
  USING (auth.uid() = user_id);

-- Add indexes
CREATE INDEX IF NOT EXISTS idx_canva_generations_user_id ON public.canva_generations (user_id);
CREATE INDEX IF NOT EXISTS idx_canva_generations_status ON public.canva_generations (status);
CREATE INDEX IF NOT EXISTS idx_canva_generations_created_at ON public.canva_generations (created_at);
