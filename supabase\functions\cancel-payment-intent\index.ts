import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import Stripe from "https://esm.sh/stripe?target=deno&deno-std=0.132.0";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { paymentIntentId, userId } = await req.json()

    console.log('🔥 Cancel payment intent request:', { paymentIntentId, userId })

    if (!paymentIntentId || !userId) {
      console.error('❌ Missing required fields:', { paymentIntentId, userId })
      return new Response(
        JSON.stringify({ error: 'Missing paymentIntentId or userId' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // 🔥 Detectar ambiente e usar chaves apropriadas
    const isLocal = req.headers.get('origin')?.includes('localhost') || req.headers.get('referer')?.includes('localhost')
    
    // Usar chaves de teste para local, produção para deploy
    const stripeSecretKey = isLocal 
      ? Deno.env.get('STRIPE_TEST_SECRET_KEY')  // Variável de ambiente para teste
      : Deno.env.get('STRIPE_SECRET_KEY')       // Variável de ambiente para produção
    
    if (!stripeSecretKey) {
      console.error('❌ Stripe secret key not found')
      return new Response(
        JSON.stringify({ error: 'Stripe configuration error' }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }
    
    console.log('🔑 Environment:', isLocal ? 'LOCAL (test mode)' : 'PRODUCTION')
    console.log('🔑 Using Stripe key:', stripeSecretKey.substring(0, 15) + '...')

    // Initialize Stripe with the correct key
    const stripe = new Stripe(stripeSecretKey, {
      httpClient: Stripe.createFetchHttpClient(),
      apiVersion: '2024-06-20',
    });

    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    try {
      // Get the payment intent from Stripe
      const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId)
      
      console.log('📋 Payment intent status:', paymentIntent.status)

      // Only cancel if the payment intent is in a cancelable state
      if (paymentIntent.status === 'requires_payment_method' || 
          paymentIntent.status === 'requires_confirmation' ||
          paymentIntent.status === 'requires_action') {
        
        // Cancel the payment intent
        const canceledIntent = await stripe.paymentIntents.cancel(paymentIntentId)
        console.log('✅ Payment intent canceled:', canceledIntent.id)

        // Update the database record to mark as cancelled
        const { error: updateError } = await supabase
          .from('credit_purchases')
          .update({ 
            status: 'cancelled'
          })
          .eq('stripe_payment_intent_id', paymentIntentId)
          .eq('user_id', userId)

        if (updateError) {
          console.error('❌ Error updating credit purchase status:', updateError)
          return new Response(
            JSON.stringify({ error: 'Failed to update payment record' }),
            { 
              status: 500, 
              headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
            }
          )
        }

        console.log('✅ Credit purchase marked as cancelled')

        return new Response(
          JSON.stringify({ 
            success: true, 
            status: 'cancelled',
            message: 'Payment intent cancelled successfully' 
          }),
          { 
            status: 200, 
            headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
          }
        )

      } else {
        // Payment intent cannot be canceled (already succeeded, failed, or canceled)
        console.log('ℹ️ Payment intent cannot be canceled, current status:', paymentIntent.status)
        
        return new Response(
          JSON.stringify({ 
            success: false, 
            status: paymentIntent.status,
            message: `Payment intent cannot be canceled. Current status: ${paymentIntent.status}` 
          }),
          { 
            status: 200, 
            headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
          }
        )
      }

    } catch (stripeError) {
      console.error('❌ Stripe error:', stripeError.message)
      
      // If payment intent not found, mark as cancelled in database anyway
      if (stripeError.code === 'resource_missing') {
        const { error: updateError } = await supabase
          .from('credit_purchases')
          .update({ 
            status: 'cancelled'
          })
          .eq('stripe_payment_intent_id', paymentIntentId)
          .eq('user_id', userId)

        if (!updateError) {
          console.log('✅ Credit purchase marked as cancelled (payment intent not found)')
          return new Response(
            JSON.stringify({ 
              success: true, 
              status: 'cancelled',
              message: 'Payment record cancelled (intent not found)' 
            }),
            { 
              status: 200, 
              headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
            }
          )
        }
      }

      return new Response(
        JSON.stringify({ 
          error: 'Failed to cancel payment intent', 
          details: stripeError.message 
        }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

  } catch (error) {
    console.error('❌ Error canceling payment intent:', error)
    return new Response(
      JSON.stringify({ error: 'Internal server error', details: error.message }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})
