-- Populate movies table with existing data from movieDatabase.ts
-- This script should be run after the tables are created

-- Netflix movies
INSERT INTO movies (title, streaming_platform, base_prompt, safe_prompt, gender_male_prompt, gender_female_prompt, couple_prompt) VALUES
('Round 6 (Squid Game)', 'netflix', 'Inspired by the person in the photo, create an artistic version where they are depicted as part of the poster''s universe. Change the clothes to a teal-green numbered tracksuit from Squid Game with a number on the front and back, while preserving the person''s exact facial features.', 'A person wearing a green tracksuit with numbers, in the style of a modern TV series poster.', 'Inspired by the person in the photo, create an artistic version where they are depicted as part of the poster''s universe. Change the clothes to a male teal-green numbered tracksuit from Squid Game with a number on the front and back, while preserving the person''s exact facial features.', 'Inspired by the person in the photo, create an artistic version where they are depicted as part of the poster''s universe. Change the clothes to a female teal-green numbered tracksuit from Squid Game with a number on the front and back, while preserving the person''s exact facial features.', 'Inspired by the couple in the photo, create an artistic version where they are depicted as part of the poster''s universe. Change their clothes to matching teal-green numbered tracksuits from Squid Game with numbers on the front and back, while preserving their exact facial features.'),

('Stranger Things', 'netflix', 'Inspired by the person in the photo, create an artistic version where they are depicted as part of the poster''s universe. Change the clothes to 1980s retro attire: denim jacket, vintage t-shirt and jeans, while preserving the person''s exact facial features.', 'A person wearing 1980s retro clothing in the style of a supernatural TV series poster.', 'Inspired by the person in the photo, create an artistic version where they are depicted as part of the poster''s universe. Change the clothes to male 1980s retro attire: denim jacket, vintage t-shirt and jeans, while preserving the person''s exact facial features.', 'Inspired by the person in the photo, create an artistic version where they are depicted as part of the poster''s universe. Change the clothes to female 1980s retro attire: denim jacket, vintage t-shirt and jeans, while preserving the person''s exact facial features.', 'Inspired by the couple in the photo, create an artistic version where they are depicted as part of the poster''s universe. Change their clothes to matching 1980s retro attire: denim jackets, vintage t-shirts and jeans, while preserving their exact facial features.'),

('Dark', 'netflix', 'Inspired by the person in the photo, create an artistic version where they are depicted as part of the poster''s universe. Change the clothes to Winden attire: a dark hoodie, worn jeans and a yellow raincoat, while preserving the person''s exact facial features.', 'A person wearing dark casual clothing with a yellow raincoat in the style of a mystery TV series poster.', 'Inspired by the person in the photo, create an artistic version where they are depicted as part of the poster''s universe. Change the clothes to a male Winden attire: a dark hoodie, worn jeans and a yellow raincoat, while preserving the person''s exact facial features.', 'Inspired by the person in the photo, create an artistic version where they are depicted as part of the poster''s universe. Change the clothes to a female Winden attire: a dark hoodie, worn jeans and a yellow raincoat, while preserving the person''s exact facial features.', 'Inspired by the couple in the photo, create an artistic version where they are depicted as part of the poster''s universe. Change their clothes to matching Winden attire: dark hoodies, worn jeans and yellow raincoats, while preserving their exact facial features.'),

('The Witcher', 'netflix', 'Inspired by the person in the photo, create an artistic version where they are depicted as part of the poster''s universe. Change the clothes to medieval fantasy attire: leather armor, dark cloak and medieval accessories, while preserving the person''s exact facial features.', 'A person wearing medieval fantasy clothing in the style of a fantasy TV series poster.', 'Inspired by the person in the photo, create an artistic version where they are depicted as part of the poster''s universe. Change the clothes to male medieval fantasy attire: leather armor, dark cloak and medieval accessories, while preserving the person''s exact facial features.', 'Inspired by the person in the photo, create an artistic version where they are depicted as part of the poster''s universe. Change the clothes to female medieval fantasy attire: leather armor, dark cloak and medieval accessories, while preserving the person''s exact facial features.', 'Inspired by the couple in the photo, create an artistic version where they are depicted as part of the poster''s universe. Change their clothes to matching medieval fantasy attire: leather armor, dark cloaks and medieval accessories, while preserving their exact facial features.'),

('Money Heist (La Casa de Papel)', 'netflix', 'Inspired by the person in the photo, create an artistic version where they are depicted as part of the poster''s universe. Change the clothes to red jumpsuit with a Salvador Dalí mask, while preserving the person''s exact facial features.', 'A person wearing a red jumpsuit in the style of a heist TV series poster.', 'Inspired by the person in the photo, create an artistic version where they are depicted as part of the poster''s universe. Change the clothes to male red jumpsuit with a Salvador Dalí mask, while preserving the person''s exact facial features.', 'Inspired by the person in the photo, create an artistic version where they are depicted as part of the poster''s universe. Change the clothes to female red jumpsuit with a Salvador Dalí mask, while preserving the person''s exact facial features.', 'Inspired by the couple in the photo, create an artistic version where they are depicted as part of the poster''s universe. Change their clothes to matching red jumpsuits with Salvador Dalí masks, while preserving their exact facial features.');

-- Disney movies  
INSERT INTO movies (title, streaming_platform, base_prompt, safe_prompt, gender_male_prompt, gender_female_prompt, couple_prompt) VALUES
('The Mandalorian', 'disney', 'Inspired by the person in the photo, create an artistic version where they are depicted as part of the poster''s universe. Change the clothes to Mandalorian armor with beskar steel appearance, while preserving the person''s exact facial features.', 'A person wearing futuristic armor in the style of a space adventure series poster.', 'Inspired by the person in the photo, create an artistic version where they are depicted as part of the poster''s universe. Change the clothes to male Mandalorian armor with beskar steel appearance, while preserving the person''s exact facial features.', 'Inspired by the person in the photo, create an artistic version where they are depicted as part of the poster''s universe. Change the clothes to female Mandalorian armor with beskar steel appearance, while preserving the person''s exact facial features.', 'Inspired by the couple in the photo, create an artistic version where they are depicted as part of the poster''s universe. Change their clothes to matching Mandalorian armor with beskar steel appearance, while preserving their exact facial features.'),

('WandaVision', 'disney', 'Inspired by the person in the photo, create an artistic version where they are depicted as part of the poster''s universe. Change the clothes to 1950s suburban attire with a vintage aesthetic, while preserving the person''s exact facial features.', 'A person wearing 1950s vintage clothing in the style of a superhero TV series poster.', 'Inspired by the person in the photo, create an artistic version where they are depicted as part of the poster''s universe. Change the clothes to male 1950s suburban attire with a vintage aesthetic, while preserving the person''s exact facial features.', 'Inspired by the person in the photo, create an artistic version where they are depicted as part of the poster''s universe. Change the clothes to female 1950s suburban attire with a vintage aesthetic, while preserving the person''s exact facial features.', 'Inspired by the couple in the photo, create an artistic version where they are depicted as part of the poster''s universe. Change their clothes to matching 1950s suburban attire with a vintage aesthetic, while preserving their exact facial features.'),

('Loki', 'disney', 'Inspired by the person in the photo, create an artistic version where they are depicted as part of the poster''s universe. Change the clothes to Asgardian royal attire with green and gold colors, while preserving the person''s exact facial features.', 'A person wearing royal fantasy clothing in green and gold colors in the style of a superhero series poster.', 'Inspired by the person in the photo, create an artistic version where they are depicted as part of the poster''s universe. Change the clothes to male Asgardian royal attire with green and gold colors, while preserving the person''s exact facial features.', 'Inspired by the person in the photo, create an artistic version where they are depicted as part of the poster''s universe. Change the clothes to female Asgardian royal attire with green and gold colors, while preserving the person''s exact facial features.', 'Inspired by the couple in the photo, create an artistic version where they are depicted as part of the poster''s universe. Change their clothes to matching Asgardian royal attire with green and gold colors, while preserving their exact facial features.');

-- Amazon movies
INSERT INTO movies (title, streaming_platform, base_prompt, safe_prompt, gender_male_prompt, gender_female_prompt, couple_prompt) VALUES
('The Boys', 'amazon', 'Inspired by the person in the photo, create an artistic version where they are depicted as part of the poster''s universe. Change the clothes to modern superhero costume with a dark and gritty aesthetic, while preserving the person''s exact facial features.', 'A person wearing a modern superhero costume in the style of a dark superhero series poster.', 'Inspired by the person in the photo, create an artistic version where they are depicted as part of the poster''s universe. Change the clothes to male modern superhero costume with a dark and gritty aesthetic, while preserving the person''s exact facial features.', 'Inspired by the person in the photo, create an artistic version where they are depicted as part of the poster''s universe. Change the clothes to female modern superhero costume with a dark and gritty aesthetic, while preserving the person''s exact facial features.', 'Inspired by the couple in the photo, create an artistic version where they are depicted as part of the poster''s universe. Change their clothes to matching modern superhero costumes with a dark and gritty aesthetic, while preserving their exact facial features.'),

('The Lord of the Rings: The Rings of Power', 'amazon', 'Inspired by the person in the photo, create an artistic version where they are depicted as part of the poster''s universe. Change the clothes to Middle-earth fantasy attire: elven robes, medieval armor or hobbit clothing, while preserving the person''s exact facial features.', 'A person wearing fantasy medieval clothing in the style of an epic fantasy series poster.', 'Inspired by the person in the photo, create an artistic version where they are depicted as part of the poster''s universe. Change the clothes to male Middle-earth fantasy attire: elven robes, medieval armor or hobbit clothing, while preserving the person''s exact facial features.', 'Inspired by the person in the photo, create an artistic version where they are depicted as part of the poster''s universe. Change the clothes to female Middle-earth fantasy attire: elven robes, medieval armor or hobbit clothing, while preserving the person''s exact facial features.', 'Inspired by the couple in the photo, create an artistic version where they are depicted as part of the poster''s universe. Change their clothes to matching Middle-earth fantasy attire: elven robes, medieval armor or hobbit clothing, while preserving their exact facial features.'),

('Invincible', 'amazon', 'Inspired by the person in the photo, create an artistic version where they are depicted as part of the poster''s universe. Change the clothes to animated superhero costume with bright colors and comic book aesthetic, while preserving the person''s exact facial features.', 'A person wearing a colorful superhero costume in the style of an animated superhero series poster.', 'Inspired by the person in the photo, create an artistic version where they are depicted as part of the poster''s universe. Change the clothes to male animated superhero costume with bright colors and comic book aesthetic, while preserving the person''s exact facial features.', 'Inspired by the person in the photo, create an artistic version where they are depicted as part of the poster''s universe. Change the clothes to female animated superhero costume with bright colors and comic book aesthetic, while preserving the person''s exact facial features.', 'Inspired by the couple in the photo, create an artistic version where they are depicted as part of the poster''s universe. Change their clothes to matching animated superhero costumes with bright colors and comic book aesthetic, while preserving their exact facial features.'); 