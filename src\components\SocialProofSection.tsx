import React from 'react';
import { Star } from 'lucide-react';

const SocialProofSection: React.FC = () => {
  const testimonials = [
    {
      quote: "Nunca pensei que fosse tão fácil e divertido! Transformei uma foto aborrecida numa capa de filme épica. Os meus amigos adoraram!",
      name: '<PERSON><PERSON>',
      role: '<PERSON><PERSON><PERSON> Conteúdo',
    },
    {
      quote: "A qualidade é incrível. Parece um póster de cinema a sério. Usei-o para um presente de aniversário e foi um sucesso absoluto.",
      name: '<PERSON>',
      role: 'Designer <PERSON><PERSON><PERSON><PERSON><PERSON>',
    },
    {
      quote: "Isto é viciante! Já criei pósteres para toda a minha família. A funcionalidade de escolher o estilo é a minha favorita.",
      name: '<PERSON>',
      role: 'Fã de Cinema',
    },
  ];

  const posters = [
    'https://image.tmdb.org/t/p/w500/qJ2tW6WMUDux911r6m7haRef0WH.jpg',
    'https://image.tmdb.org/t/p/w500/pB8BM7pdSp6B6Ih7QZ4DrQ3PmJK.jpg',
    'https://image.tmdb.org/t/p/w500/rCzpDGLbOoPwLjy3OAm5NUPOTrC.jpg',
    'https://image.tmdb.org/t/p/w500/8OKmBV5BUFzmor4p5UdS1s3dO3i.jpg',
    'https://image.tmdb.org/t/p/w500/d5iIlFn5s0ImszYzrKYOFTl8rhp.jpg',
    'https://image.tmdb.org/t/p/w500/uDO8zWDhfWwoFdKS4fzkUJt0Rf0.jpg',
  ];

  return (
    <section className="py-20 px-8 bg-black">
      <div className="max-w-6xl mx-auto text-center">
        <h2 className="text-4xl md:text-5xl font-bold mb-4 uppercase text-white">Amado por Criativos e Fãs de Cinema</h2>
        <p className="text-lg text-gray-400 mb-12 max-w-3xl mx-auto">
          Veja o que os nossos utilizadores estão a criar e a dizer sobre a plataforma.
        </p>

        {/* Gallery */}
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-20">
          {posters.map((poster, index) => (
            <div key={index} className="bg-gray-800 border-2 border-black shadow-[4px_4px_0px_#000000]">
              <img src={poster} alt={`Poster de exemplo ${index + 1}`} className="w-full h-full object-cover" />
            </div>
          ))}
        </div>

        {/* Testimonials */}
        <div className="grid md:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <div key={index} className="bg-[#1a1a1a] p-8 border-2 border-black shadow-[8px_8px_0px_rgba(255,255,255,0.1)] text-left">
              <div className="flex mb-4">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-5 h-5 text-yellow-400" fill="currentColor" />
                ))}
              </div>
              <p className="text-gray-300 mb-6 italic">\"{testimonial.quote}\"</p>
              <div className="font-bold text-white">{testimonial.name}</div>
              <div className="text-sm text-yellow-400">{testimonial.role}</div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default SocialProofSection;
