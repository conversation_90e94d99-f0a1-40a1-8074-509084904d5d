import React, { useState, useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { motion, useAnimation, useInView } from 'framer-motion';
import { 
  Sparkles, 
  Zap, 
  Star, 
  ArrowRight, 
  Play, 
  Check, 
  Users, 
  Award, 
  TrendingUp,
  Menu,
  X,
  ChevronRight,
  Quote,
  Moon,
  Sun,
  Globe,
  LogOut,
  User as UserIcon
} from 'lucide-react';
import { BrazilFlag, USFlag } from '../components/icons';
import { supabase } from '../lib/supabase';
import PricingModal from '../components/PricingModal';
import AuthModal from '../components/AuthModal';
import CheckoutForm from '../components/CheckoutForm';
import { usePayment } from '../hooks/usePayment';
import { useCreditBalance } from '../hooks/useCreditBalance';
import { useVoucher } from '../hooks/useVoucher';
import { showToast } from '../utils/toast';
import { logger } from '../utils/logger';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Toaster } from 'sonner';

// Magic UI Components
import { SparklesText } from '../components/magicui/sparkles-text';
import { VelocityScroll } from "../components/magicui/scroll-based-velocity";
import { BorderBeam } from '../components/magicui/border-beam';
import { NumberTicker } from '../components/magicui/number-ticker';
import { AnimatedGradientText } from '../components/magicui/animated-gradient-text';
import { WarpBackground } from '../components/magicui/warp-background';
import { AnimatedBeam } from '../components/magicui/animated-beam';

// Demo Component
import BeforeAfterDemo from '../components/BeforeAfterDemo';

// Create a QueryClient for the landing page
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 30 * 1000,
      gcTime: 5 * 60 * 1000,
      retry: 3,
    },
  },
});

// Utility function
function cn(...classes: string[]) {
  return classes.filter(Boolean).join(' ');
}

// Button component
const Button = React.forwardRef<
  HTMLButtonElement,
  React.ButtonHTMLAttributes<HTMLButtonElement> & {
    variant?: 'default' | 'outline' | 'ghost';
    size?: 'sm' | 'lg' | 'default';
  }
>(({ className, variant = 'default', size = 'default', ...props }, ref) => {
  const baseClasses = 'inline-flex items-center justify-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background';
  
  const variants = {
    default: 'bg-primary text-primary-foreground hover:bg-primary/90',
    outline: 'border border-input hover:bg-accent hover:text-accent-foreground',
    ghost: 'hover:bg-accent hover:text-accent-foreground'
  };
  
  const sizes = {
    sm: 'h-9 px-3 text-sm',
    default: 'h-10 py-2 px-4',
    lg: 'h-11 px-8 text-lg'
  };
  
  return (
    <button
      className={cn(baseClasses, variants[variant], sizes[size], className)}
      ref={ref}
      {...props}
    />
  );
});
Button.displayName = 'Button';

// Avatar components
const Avatar = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn('relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full', className)}
    {...props}
  />
));
Avatar.displayName = 'Avatar';

const AvatarImage = React.forwardRef<
  HTMLImageElement,
  React.ImgHTMLAttributes<HTMLImageElement>
>(({ className, ...props }, ref) => (
  <img
    ref={ref}
    className={cn('aspect-square h-full w-full', className)}
    {...props}
  />
));
AvatarImage.displayName = 'AvatarImage';

const AvatarFallback = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn('flex h-full w-full items-center justify-center rounded-full bg-muted', className)}
    {...props}
  />
));
AvatarFallback.displayName = 'AvatarFallback';

// Separator component
const Separator = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn('shrink-0 bg-border h-[1px] w-full', className)}
    {...props}
  />
));
Separator.displayName = 'Separator';

// Glow component
const glowVariants = {
  top: "top-0",
  above: "-top-[128px]",
  bottom: "bottom-0",
  below: "-bottom-[128px]",
  center: "top-[50%]",
};

const Glow = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & { variant?: keyof typeof glowVariants }
>(({ className, variant = "top", ...props }, ref) => (
  <div
    ref={ref}
    className={cn(glowVariants[variant], "absolute w-full", className)}
    {...props}
  >
    <div
      className={cn(
        "absolute left-1/2 h-[256px] w-[60%] -translate-x-1/2 scale-[2.5] rounded-[50%] bg-gradient-radial from-blue-500/50 via-purple-500/30 to-transparent sm:h-[512px]",
        variant === "center" && "-translate-y-1/2",
      )}
    />
    <div
      className={cn(
        "absolute left-1/2 h-[128px] w-[40%] -translate-x-1/2 scale-[2] rounded-[50%] bg-gradient-radial from-blue-400/30 via-purple-400/20 to-transparent sm:h-[256px]",
        variant === "center" && "-translate-y-1/2",
      )}
    />
  </div>
));
Glow.displayName = "Glow";

// Mockup component
const Mockup = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & { type?: "mobile" | "responsive" }
>(({ className, type = "responsive", children, ...props }, ref) => (
  <div
    ref={ref}
    className={cn(
      "flex relative z-10 overflow-hidden shadow-2xl border border-gray-200 dark:border-gray-800",
      type === "mobile" ? "rounded-[48px] max-w-[350px]" : "rounded-lg",
      className
    )}
    {...props}
  >
    {children}
  </div>
));
Mockup.displayName = "Mockup";

// Testimonial interface
interface Testimonial {
  id: number;
  name: string;
  role: string;
  company: string;
  content: string;
  rating: number;
  avatar: string;
}

// Animated Testimonials component
function AnimatedTestimonials({
  title,
  subtitle,
  badgeText,
  testimonials,
  autoRotateInterval = 6000,
  trustedCompanies = ["Netflix", "Warner Bros", "Universal", "Sony Pictures", "Disney"],
  trustedCompaniesTitle,
  className,
}: {
  title?: string;
  subtitle?: string;
  badgeText?: string;
  testimonials?: Testimonial[];
  autoRotateInterval?: number;
  trustedCompanies?: string[];
  trustedCompaniesTitle?: string;
  className?: string;
}) {
  const { t } = useTranslation();
  
  // Set default values using the t function
  const defaultTitle = title || t('landing.testimonials.title', 'Amado pela comunidade');
  const defaultSubtitle = subtitle || t('landing.testimonials.subtitle', 'Veja o que nossos usuários dizem sobre nossa plataforma de geração de posters cinematográficos com IA.');
  const defaultBadgeText = badgeText || t('landing.testimonials.badge', 'Confiado por criadores');
  const defaultTrustedCompaniesTitle = trustedCompaniesTitle || t('landing.testimonials.trustedTitle', 'Confiado por profissionais de empresas renomadas');
  const defaultTestimonials = testimonials || [
    {
      id: 1,
      name: t('landing.testimonials.ana.name', 'Ana Silva'),
      role: t('landing.testimonials.ana.role', 'Diretora de Arte'),
      company: t('landing.testimonials.ana.company', 'Estúdio Criativo'),
      content: t('landing.testimonials.ana.content', 'A qualidade dos posters gerados é impressionante. Consegui criar materiais profissionais em minutos que antes levariam horas.'),
      rating: 5,
      avatar: "https://randomuser.me/api/portraits/women/32.jpg",
    },
    {
      id: 2,
      name: t('landing.testimonials.carlos.name', 'Carlos Santos'),
      role: t('landing.testimonials.carlos.role', 'Produtor'),
      company: t('landing.testimonials.carlos.company', 'FilmeCorp'),
      content: t('landing.testimonials.carlos.content', 'Revolucionou nosso processo criativo. A IA entende perfeitamente o estilo cinematográfico que buscamos.'),
      rating: 5,
      avatar: "https://randomuser.me/api/portraits/men/44.jpg",
    },
    {
      id: 3,
      name: t('landing.testimonials.marina.name', 'Marina Costa'),
      role: t('landing.testimonials.marina.role', 'Designer Gráfica'),
      company: t('landing.testimonials.marina.company', 'Visual Studio'),
      content: t('landing.testimonials.marina.content', 'Ferramenta indispensável para qualquer profissional da área. A variedade de estilos e a qualidade são excepcionais.'),
      rating: 5,
      avatar: "https://randomuser.me/api/portraits/women/46.jpg",
    },
  ];
  const [activeIndex, setActiveIndex] = useState(0);
  const sectionRef = useRef(null);
  const isInView = useInView(sectionRef, { once: true, amount: 0.2 });
  const controls = useAnimation();

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
        ease: "easeOut",
      },
    },
  };

  useEffect(() => {
    if (isInView) {
      controls.start("visible");
    }
  }, [isInView, controls]);

  useEffect(() => {
    if (autoRotateInterval <= 0 || defaultTestimonials.length <= 1) return;

    const interval = setInterval(() => {
      setActiveIndex((current) => (current + 1) % defaultTestimonials.length);
    }, autoRotateInterval);

    return () => clearInterval(interval);
  }, [autoRotateInterval, defaultTestimonials.length]);

  if (defaultTestimonials.length === 0) {
    return null;
  }

  return (
    <section ref={sectionRef} id="testimonials" className={`py-24 overflow-hidden bg-gray-50 dark:bg-gray-900/50 ${className || ""}`}>
      <div className="px-4 md:px-6 max-w-7xl mx-auto">
        <motion.div
          initial="hidden"
          animate={controls}
          variants={containerVariants}
          className="grid grid-cols-1 gap-16 w-full md:grid-cols-2 lg:gap-24"
        >
          <motion.div variants={itemVariants} className="flex flex-col justify-center">
            <div className="space-y-6">
              {defaultBadgeText && (
                <div className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                  <Star className="mr-1 h-3.5 w-3.5 fill-current" />
                  <span>{defaultBadgeText}</span>
                </div>
              )}

              <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">{defaultTitle}</h2>

              <p className="max-w-[600px] text-gray-600 dark:text-gray-400 md:text-xl/relaxed">{defaultSubtitle}</p>

              <div className="flex items-center gap-3 pt-4">
                {defaultTestimonials.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => setActiveIndex(index)}
                    className={`h-2.5 rounded-full transition-all duration-300 ${
                      activeIndex === index ? "w-10 bg-blue-600" : "w-2.5 bg-gray-300 dark:bg-gray-600"
                    }`}
                    aria-label={`View testimonial ${index + 1}`}
                  />
                ))}
              </div>
            </div>
          </motion.div>

          <motion.div variants={itemVariants} className="relative h-full mr-10 min-h-[300px] md:min-h-[400px]">
            {defaultTestimonials.map((testimonial, index) => (
              <motion.div
                key={testimonial.id}
                className="absolute inset-0"
                initial={{ opacity: 0, x: 100 }}
                animate={{
                  opacity: activeIndex === index ? 1 : 0,
                  x: activeIndex === index ? 0 : 100,
                  scale: activeIndex === index ? 1 : 0.9,
                }}
                transition={{ duration: 0.5, ease: "easeInOut" }}
                style={{ zIndex: activeIndex === index ? 10 : 0 }}
              >
                <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 shadow-lg rounded-xl p-8 h-full flex flex-col">
                  <div className="mb-6 flex gap-2">
                    {Array(testimonial.rating)
                      .fill(0)
                      .map((_, i) => (
                        <Star key={i} className="h-5 w-5 fill-yellow-500 text-yellow-500" />
                      ))}
                  </div>

                  <div className="relative mb-6 flex-1">
                    <Quote className="absolute -top-2 -left-2 h-8 w-8 text-blue-500/20 rotate-180" />
                    <p className="relative z-10 text-lg font-medium leading-relaxed">"{testimonial.content}"</p>
                  </div>

                  <Separator className="my-4" />

                  <div className="flex items-center gap-4">
                    <Avatar className="h-12 w-12 border">
                      <AvatarImage src={testimonial.avatar} alt={testimonial.name} />
                      <AvatarFallback>{testimonial.name.charAt(0)}</AvatarFallback>
                    </Avatar>
                    <div>
                      <h3 className="font-semibold">{testimonial.name}</h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {testimonial.role}, {testimonial.company}
                      </p>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}

            <div className="absolute -bottom-6 -left-6 h-24 w-24 rounded-xl bg-blue-500/5"></div>
            <div className="absolute -top-6 -right-6 h-24 w-24 rounded-xl bg-blue-500/5"></div>
          </motion.div>
        </motion.div>

        {trustedCompanies.length > 0 && (
          <motion.div variants={itemVariants} initial="hidden" animate={controls} className="mt-24 text-center">
            <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-8">{defaultTrustedCompaniesTitle}</h3>
            <div className="flex flex-wrap justify-center gap-x-12 gap-y-8">
              {trustedCompanies.map((company) => (
                <div key={company} className="text-2xl font-semibold text-gray-400 dark:text-gray-600">
                  {company}
                </div>
              ))}
            </div>
          </motion.div>
        )}
      </div>
    </section>
  );
}

// Main Landing Page Component
const NewLandingPageContent: React.FC = () => {
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const [isDark, setIsDark] = useState(false);
  const [user, setUser] = useState<{ id: string; email?: string } | null>(null);
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [scrollY, setScrollY] = useState(0);
  
  // Modal states
  const [showPricingModal, setShowPricingModal] = useState(false);
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [showCheckoutModal, setShowCheckoutModal] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState<string | null>(null);
  const [paymentIntent, setPaymentIntent] = useState<{ clientSecret: string; paymentIntentId: string } | null>(null);
  const [pendingVoucherCode, setPendingVoucherCode] = useState<string | null>(null);
  
  // Hooks
  const { createPaymentIntent, loading: paymentLoading, stripePromise } = usePayment();
  const { creditBalance, refetch: refetchCredits } = useCreditBalance(user?.id || null);
  const { redeemVoucher } = useVoucher();

  // Apply theme to document
  useEffect(() => {
    if (isDark) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  }, [isDark]);

  // Check if user is logged in
  useEffect(() => {
    const checkUser = async () => {
      const { data: { session } } = await supabase.auth.getSession();
      setUser(session?.user || null);
    };

    checkUser();

    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (_, session) => {
        setUser(session?.user || null);
      }
    );

    return () => subscription.unsubscribe();
  }, []);

  useEffect(() => {
    const handleScroll = () => {
      setScrollY(window.scrollY);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const handleLanguageChange = (lang: string) => {
    i18n.changeLanguage(lang);
  };

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const handleGetStarted = () => {
    if (user) {
      navigate('/dashboard');
    } else {
      setShowPricingModal(true);
    }
  };

  const handleSelectPlan = async (planId: string) => {
    if (!user) {
      setSelectedPlan(planId);
      setShowAuthModal(true);
      return;
    }

    await proceedWithPayment(planId);
  };

  const proceedWithPayment = async (planId: string) => {
    try {
      const result = await createPaymentIntent(planId, user!.id);
      setPaymentIntent(result);
      setSelectedPlan(planId);
      setShowPricingModal(false);
      setShowCheckoutModal(true);
    } catch (error) {
      logger.error('Error creating payment intent:', error);
    }
  };

  const handleAuthSuccess = async (authenticatedUser: any) => {
    setUser(authenticatedUser);
    setShowAuthModal(false);
    
    if (pendingVoucherCode) {
      try {
        const result = await redeemVoucher(pendingVoucherCode, authenticatedUser.id, authenticatedUser.email);
        if (result.success) {
          showToast.success(result.message);
          refetchCredits();
        } else {
          showToast.error(result.message);
        }
      } catch (error) {
        showToast.error('Erro ao resgatar voucher após login.');
      } finally {
        setPendingVoucherCode(null);
      }
      return;
    }
    
    if (selectedPlan) {
      await proceedWithPayment(selectedPlan);
      return;
    }
    
    navigate('/dashboard');
  };

  const handleVoucherAuthRequired = (voucherCode: string) => {
    setPendingVoucherCode(voucherCode);
    setShowAuthModal(true);
  };

  const handlePaymentSuccess = () => {
    setShowCheckoutModal(false);
    setPaymentIntent(null);
    setSelectedPlan(null);
    refetchCredits();
    showToast.success(t('payment.success', 'Pagamento realizado com sucesso! Seus créditos foram adicionados.'));
  };

  const handlePaymentCancel = () => {
    setShowCheckoutModal(false);
    setPaymentIntent(null);
    setSelectedPlan(null);
    setShowPricingModal(true);
  };

  const closeAllModals = () => {
    setShowPricingModal(false);
    setShowAuthModal(false);
    setShowCheckoutModal(false);
    setPaymentIntent(null);
    setSelectedPlan(null);
  };

  const fadeIn = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6 },
    },
  };

  const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemFadeIn = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 },
    },
  };

  return (
    <div className="flex min-h-screen flex-col bg-gradient-to-br from-white via-gray-50 to-blue-50 dark:from-gray-900 dark:via-gray-900 dark:to-blue-950">
      {/* Header */}
      <motion.header
        initial={{ y: -100 }}
        animate={{ y: 0 }}
        transition={{ duration: 0.5 }}
        className={`sticky top-0 z-50 w-full border-b border-gray-200/50 dark:border-gray-800/50 bg-white/95 dark:bg-gray-900/95 backdrop-blur supports-[backdrop-filter]:bg-white/60 dark:supports-[backdrop-filter]:bg-gray-900/60 ${scrollY > 50 ? "shadow-md" : ""}`}
      >
        <div className="max-w-7xl mx-auto flex h-16 items-center justify-between px-4 md:px-6">
          <div className="flex items-center gap-3">
            <div className="flex items-center space-x-3">
              <motion.div
                whileHover={{ rotate: 5, scale: 1.1 }}
                transition={{ type: "spring", stiffness: 400, damping: 10 }}
                className="h-10 w-10 rounded-3xl bg-blue-600 flex items-center justify-center"
              >
                <Sparkles className="h-5 w-5 text-white" />
              </motion.div>
              <span className="font-bold text-xl">{t('landing.brand', 'PosterFlix')}</span>
            </div>
          </div>
          
          <nav className="hidden md:flex gap-6">
            <a href="#features" className="text-sm font-medium transition-colors hover:text-blue-600">
              {t('landing.nav.features', 'Recursos')}
            </a>
            <a href="#how-it-works" className="text-sm font-medium transition-colors hover:text-blue-600">
              {t('landing.nav.howItWorks', 'Como Funciona')}
            </a>
            <a href="#testimonials" className="text-sm font-medium transition-colors hover:text-blue-600">
              {t('landing.nav.testimonials', 'Depoimentos')}
            </a>
            <a href="#demo-section" className="text-sm font-medium transition-colors hover:text-blue-600">
              {t('landing.nav.demo', 'Demo')}
            </a>
          </nav>
          
          <div className="hidden md:flex items-center gap-3">
            {/* Language Switcher */}
            <div className="flex items-center border-0 dark:border-0 p-1 rounded-lg bg-gray-50 dark:bg-gray-800">
              <button
                onClick={() => handleLanguageChange('pt-BR')}
                className={`p-2 transition-all duration-200 rounded ${
                  i18n.language === 'pt-BR' 
                    ? 'bg-green-500' 
                    : 'hover:bg-gray-200 dark:hover:bg-gray-700'
                }`}
              >
                <BrazilFlag className="w-5 h-5" />
              </button>
              <button
                onClick={() => handleLanguageChange('en')}
                className={`p-2 transition-all duration-200 rounded ${
                  i18n.language === 'en' 
                    ? 'bg-blue-500' 
                    : 'hover:bg-gray-200 dark:hover:bg-gray-700'
                }`}
              >
                <USFlag className="w-5 h-5" />
              </button>
            </div>

            {/* Theme Toggle */}
            <button
              onClick={() => setIsDark(!isDark)}
              className="p-3 border-0 dark:border-0 transition-all duration-200 rounded-lg bg-gray-50 dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700"
            >
              {isDark ? <Sun className="w-5 h-5" /> : <Moon className="w-5 h-5" />}
            </button>

            {!user ? (
              <>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="rounded-3xl"
                  onClick={() => setShowAuthModal(true)}
                >
                  {t('landing.nav.login', 'Entrar')}
                </Button>
                <Button 
                  size="sm" 
                  className="rounded-3xl bg-blue-600 hover:bg-blue-700"
                  onClick={handleGetStarted}
                >
                  {t('landing.hero.cta', 'Começar Grátis')}
                </Button>
              </>
            ) : (
              <Button 
                size="sm" 
                className="rounded-3xl bg-blue-600 hover:bg-blue-700"
                onClick={() => navigate('/dashboard')}
              >
                {t('landing.nav.dashboard', 'Dashboard')}
              </Button>
            )}
          </div>
          
          <button className="flex md:hidden" onClick={toggleMenu}>
            <Menu className="h-6 w-6" />
            <span className="sr-only">Toggle menu</span>
          </button>
        </div>
      </motion.header>

      {/* Mobile Menu */}
      {isMenuOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-50 bg-white dark:bg-gray-900 md:hidden"
        >
          <div className="max-w-7xl mx-auto flex h-16 items-center justify-between px-4">
            <div className="flex items-center gap-3">
              <div className="flex items-center space-x-3">
                <div className="h-10 w-10 rounded-3xl bg-blue-600 flex items-center justify-center">
                  <Sparkles className="h-5 w-5 text-white" />
                </div>
                <span className="font-bold text-xl">{t('landing.brand', 'PosterFlix')}</span>
              </div>
            </div>
            <button onClick={toggleMenu}>
              <X className="h-6 w-6" />
              <span className="sr-only">Close menu</span>
            </button>
          </div>
          <motion.nav
            variants={staggerContainer}
            initial="hidden"
            animate="visible"
            className="max-w-7xl mx-auto grid gap-3 pb-8 pt-6 px-4"
          >
            {[
              { key: 'features', label: t('landing.nav.features', 'Recursos') },
              { key: 'how-it-works', label: t('landing.nav.howItWorks', 'Como Funciona') },
              { key: 'testimonials', label: t('landing.nav.testimonials', 'Depoimentos') },
              { key: 'demo', label: t('landing.nav.demo', 'Demo') }
            ].map((item, index) => (
              <motion.div key={index} variants={itemFadeIn}>
                <a
                  href={item.key === 'demo' ? '#demo-section' : `#${item.key}`}
                  className="flex items-center justify-between rounded-3xl px-3 py-2 text-lg font-medium hover:bg-gray-100 dark:hover:bg-gray-800"
                  onClick={toggleMenu}
                >
                  {item.label}
                  <ChevronRight className="h-4 w-4" />
                </a>
              </motion.div>
            ))}
            <motion.div variants={itemFadeIn} className="flex flex-col gap-3 pt-4">
              {!user ? (
                <>
                  <Button 
                    variant="outline" 
                    className="w-full rounded-3xl"
                    onClick={() => {
                      setShowAuthModal(true);
                      toggleMenu();
                    }}
                  >
                    {t('landing.nav.login', 'Entrar')}
                  </Button>
                  <Button 
                    className="w-full rounded-3xl bg-blue-600 hover:bg-blue-700"
                    onClick={() => {
                      handleGetStarted();
                      toggleMenu();
                    }}
                  >
                    {t('landing.nav.getStarted', 'Começar Grátis')}
                  </Button>
                </>
              ) : (
                <Button 
                  className="w-full rounded-3xl bg-blue-600 hover:bg-blue-700"
                  onClick={() => {
                    navigate('/dashboard');
                    toggleMenu();
                  }}
                >
                  {t('landing.nav.dashboard', 'Dashboard')}
                </Button>
              )}
            </motion.div>
          </motion.nav>
        </motion.div>
      )}

      <main className="flex-1">
        {/* Hero Section */}
        <section className="relative w-full py-12 md:py-24 lg:py-32 xl:py-48 overflow-hidden">
          <div className="max-w-7xl mx-auto px-4 md:px-6">
            <div className="relative z-10 flex flex-col items-center gap-6 pt-8 md:pt-16 text-center lg:gap-12">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.7 }}
                className="text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl lg:text-7xl xl:text-8xl leading-[1.1] sm:leading-[1.1]"
              >
                <SparklesText 
                  text={t('landing.hero.title', 'Crie Posters Cinematográficos Incríveis com IA')}
                  className="bg-gradient-to-b from-gray-900 via-gray-800 to-gray-600 dark:from-gray-100 dark:via-gray-200 dark:to-gray-400 bg-clip-text text-transparent"
                />
              </motion.div>

              <motion.p
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.7, delay: 0.15 }}
                className="max-w-[550px] text-base sm:text-lg md:text-xl text-gray-600 dark:text-gray-300 font-medium"
              >
                {t('landing.hero.subtitle', 'Transforme suas ideias em posters cinematográficos profissionais em segundos. Nossa IA avançada entende estilos, gêneros e elementos visuais para criar arte única.')}
              </motion.p>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.7, delay: 0.3 }}
                className="relative z-10 flex flex-wrap justify-center gap-4"
              >
                <Button
                  size="lg"
                  className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-lg transition-all duration-300 rounded-3xl"
                  onClick={handleGetStarted}
                >
                  {t('landing.hero.cta', 'Começar Grátis')}
                </Button>

                <Button
                  size="lg"
                  variant="ghost"
                  className="text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-800 transition-all duration-300 rounded-3xl border-0 dark:border-0 bg-gray-50 dark:bg-gray-800"
                  onClick={() => {
                    const demoSection = document.getElementById('demo-section');
                    if (demoSection) {
                      demoSection.scrollIntoView({ behavior: 'smooth' });
                    }
                  }}
                >
                  <Play className="mr-2 h-4 w-4" />
                  {t('landing.hero.demo', 'Ver Demo')}
                </Button>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.7, delay: 0.7 }}
                className="relative w-full pt-12 px-4 sm:px-6 lg:px-8"
              >
                <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
                  {[
                    {
                      src: "/img/template-netflix.png",
                      hoverSrc: "/example/netflix_1_1.png",
                      alt: "Template Netflix - PosterFlix AI",
                      title: "Netflix",
                      delay: 0
                    },
                    {
                      src: "/img/template-amazon.png",
                      hoverSrc: "/example/amazon_1.png",
                      alt: "Template Amazon Prime - PosterFlix AI",
                      title: "Prime Video",
                      delay: 0.2
                    },
                    {
                      src: "/img/template-disney.png",
                      hoverSrc: "/example/disney_1.png",
                      alt: "Template Disney+ - PosterFlix AI",
                      title: "Disney+",
                      delay: 0.4
                    }
                  ].map((template, index) => {
                    const [isHovered, setIsHovered] = useState(false);
                    
                    return (
                      <motion.div
                        key={index}
                        initial={{ opacity: 0, y: 20 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.6, delay: template.delay }}
                        viewport={{ once: true }}
                        className="text-center"
                        onMouseEnter={() => setIsHovered(true)}
                        onMouseLeave={() => setIsHovered(false)}
                      >
                        <Mockup className="shadow-[0_0_30px_-12px_rgba(0,0,0,0.3)] dark:shadow-[0_0_30px_-12px_rgba(59,130,246,0.3)] border-0 dark:border-0 mx-auto max-w-sm relative mb-4 overflow-hidden cursor-pointer transform transition-all duration-500 hover:scale-105 hover:shadow-[0_0_50px_-12px_rgba(59,130,246,0.5)]">
                          <BorderBeam size={200} duration={12} delay={9 + template.delay} />
                          <div className="relative w-full h-auto">
                            <motion.img
                              src={template.src}
                              alt={template.alt}
                              className="w-full h-auto transition-opacity duration-500"
                              loading="lazy"
                              decoding="async"
                              animate={{ opacity: isHovered ? 0 : 1 }}
                              transition={{ duration: 0.5 }}
                            />
                            <motion.img
                              src={template.hoverSrc}
                              alt={`Exemplo ${template.title} - PosterFlix AI`}
                              className="absolute top-0 left-0 w-full h-auto transition-opacity duration-500"
                              loading="lazy"
                              decoding="async"
                              animate={{ opacity: isHovered ? 1 : 0 }}
                              transition={{ duration: 0.5 }}
                            />
                          </div>
                        </Mockup>
                        <motion.h3 
                          className="text-lg font-semibold text-gray-900 dark:text-white transition-colors duration-300"
                          animate={{ color: isHovered ? '#3b82f6' : undefined }}
                        >
                          {template.title}
                        </motion.h3>
                      </motion.div>
                    );
                  })}
                </div>
              </motion.div>
            </div>
          </div>

          <div className="absolute inset-0 overflow-hidden pointer-events-none">
            <Glow
              variant="above"
              className="animate-pulse opacity-30"
            />
          </div>
        </section>

        {/* Features Section */}
        <section id="features" className="py-24 bg-white dark:bg-gray-900">
          <div className="max-w-7xl mx-auto px-4 md:px-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl mb-4 text-gray-900 dark:text-white">
                {t('landing.features.title', 'Recursos Poderosos')}
              </h2>
              <p className="text-gray-600 dark:text-gray-300 text-lg max-w-2xl mx-auto">
                {t('landing.features.subtitle', 'Tudo que você precisa para criar posters cinematográficos profissionais')}
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                {
                  icon: <Sparkles className="h-8 w-8 text-blue-600" />,
                  title: t('landing.features.items.ai.title', 'IA Avançada'),
                  description: t('landing.features.items.ai.description', 'Algoritmos de última geração que entendem estilos cinematográficos e criam arte única.')
                },
                {
                  icon: <Zap className="h-8 w-8 text-yellow-500" />,
                  title: t('landing.features.items.speed.title', 'Geração Rápida'),
                  description: t('landing.features.items.speed.description', 'Crie posters profissionais em segundos, não em horas.')
                },
                {
                  icon: <Star className="h-8 w-8 text-purple-600" />,
                  title: t('landing.features.items.quality.title', 'Qualidade Premium'),
                  description: t('landing.features.items.quality.description', 'Resolução alta e qualidade profissional para todos os seus projetos.')
                },
                {
                  icon: <Users className="h-8 w-8 text-green-600" />,
                  title: t('landing.features.items.ease.title', 'Fácil de Usar'),
                  description: t('landing.features.items.ease.description', 'Interface intuitiva que qualquer pessoa pode usar, sem conhecimento técnico.')
                },
                {
                  icon: <Award className="h-8 w-8 text-red-600" />,
                  title: t('landing.features.items.styles.title', 'Estilos Variados'),
                  description: t('landing.features.items.styles.description', 'Centenas de estilos cinematográficos, desde clássicos até modernos.')
                },
                {
                  icon: <TrendingUp className="h-8 w-8 text-blue-500" />,
                  title: t('landing.features.items.updates.title', 'Sempre Atualizado'),
                  description: t('landing.features.items.updates.description', 'Novos recursos e melhorias constantemente adicionados.')
                }
              ].map((feature, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="bg-gray-50 dark:bg-gray-800 p-6 rounded-xl border border-gray-200 dark:border-gray-700 hover:shadow-lg dark:hover:shadow-blue-500/10 transition-all duration-300"
                >
                  <div className="mb-4">{feature.icon}</div>
                  <h3 className="text-xl font-semibold mb-2 text-gray-900 dark:text-white">{feature.title}</h3>
                  <p className="text-gray-600 dark:text-gray-300">{feature.description}</p>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* How It Works Section */}
        <section id="how-it-works" className="py-24 bg-gray-50 dark:bg-gray-900/50">
          <div className="max-w-7xl mx-auto px-4 md:px-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl mb-4 text-gray-900 dark:text-white">
                {t('landing.howItWorks.title', 'Como Funciona')}
              </h2>
              <p className="text-gray-600 dark:text-gray-300 text-lg max-w-2xl mx-auto">
                {t('landing.howItWorks.subtitle', 'Três passos simples para criar seu poster cinematográfico')}
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {[
                {
                  step: "1",
                  title: t('landing.howItWorks.steps.upload.title', 'Faça Upload'),
                  description: t('landing.howItWorks.steps.upload.description', 'Envie sua foto ou descreva sua ideia para o poster.')
                },
                {
                  step: "2",
                  title: t('landing.howItWorks.steps.style.title', 'Escolha o Estilo'),
                  description: t('landing.howItWorks.steps.style.description', 'Selecione entre centenas de estilos cinematográficos disponíveis.')
                },
                {
                  step: "3",
                  title: t('landing.howItWorks.steps.download.title', 'Baixe seu Poster'),
                  description: t('landing.howItWorks.steps.download.description', 'Receba seu poster profissional em alta resolução em segundos.')
                }
              ].map((step, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.2 }}
                  viewport={{ once: true }}
                  className="text-center"
                >
                  <div className="w-16 h-16 bg-blue-600 text-white rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-4">
                    <NumberTicker value={parseInt(step.step)} className="text-white" />
                  </div>
                  <h3 className="text-xl font-semibold mb-2 text-gray-900 dark:text-white">{step.title}</h3>
                  <p className="text-gray-600 dark:text-gray-300">{step.description}</p>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Demo Section */}
        <section id="demo-section" className="py-24 bg-white dark:bg-gray-900">
          <div className="max-w-7xl mx-auto px-4 md:px-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl mb-4 text-gray-900 dark:text-white">
                {t('landing.demo.title', 'Veja a Transformação')}
              </h2>
              <p className="text-gray-600 dark:text-gray-300 text-lg max-w-2xl mx-auto">
                {t('landing.demo.subtitle', 'Compare o antes e depois de uma foto comum transformada em poster cinematográfico')}
              </p>
            </motion.div>
            
            <BeforeAfterDemo />
          </div>
        </section>

        {/* Stats Section */}
        <section className="py-24 bg-gradient-to-r from-blue-600 to-purple-700">
          <div className="max-w-7xl mx-auto px-4 md:px-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8 text-center text-white">
              {[
                { number: 50000, label: t('landing.stats.posters', 'Posters Criados'), suffix: "+" },
                { number: 10000, label: t('landing.stats.users', 'Usuários Ativos'), suffix: "+" },
                { number: 99, label: t('landing.stats.satisfaction', 'Satisfação'), suffix: "%" },
                { number: 24, label: t('landing.stats.support', 'Suporte'), suffix: "/7" }
              ].map((stat, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="text-center"
                >
                  <div className="text-4xl font-bold mb-2">
                    <NumberTicker value={stat.number} className="text-white" />
                    {stat.suffix}
                  </div>
                  <p className="text-blue-100">{stat.label}</p>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Testimonials Section */}
        <AnimatedTestimonials />

        {/* Platforms Section */}
         <section className="py-24 bg-white dark:bg-gray-900">
           <div className="max-w-7xl mx-auto px-4 md:px-6">
             <motion.div
               initial={{ opacity: 0, y: 20 }}
               whileInView={{ opacity: 1, y: 0 }}
               transition={{ duration: 0.6 }}
               viewport={{ once: true }}
               className="text-center mb-16"
             >
               <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl mb-4 text-gray-900 dark:text-white">
                 {t('landing.platforms.title', 'Escolha Seu Universo Cinematográfico')}
               </h2>
               <p className="text-gray-600 dark:text-gray-300 text-lg max-w-3xl mx-auto">
                 {t('landing.platforms.subtitle', 'Cada plataforma tem sua identidade visual única. Nossa IA domina os códigos estéticos de cada uma.')}
               </p>
             </motion.div>

             <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
               {[
                 {
                   platform: 'Netflix',
                   title: 'NETFLIX',
                   subtitle: t('landing.platforms.netflix.subtitle', 'Drama & Suspense'),
                   description: t('landing.platforms.netflix.description', 'Estética cinematográfica sombria e intensa. Tons dramáticos que capturam a essência dos grandes sucessos da plataforma.'),
                   color: 'bg-red-600',
                   features: [
                     t('landing.platforms.netflix.features.covers', '12 capas únicas'),
                     t('landing.platforms.netflix.features.style', 'Estilo noir premium'),
                     t('landing.platforms.netflix.features.typography', 'Tipografia icônica')
                   ]
                 },
                 {
                   platform: 'Disney+',
                   title: 'DISNEY+',
                   subtitle: t('landing.platforms.disney.subtitle', 'Fantasia & Aventura'),
                   description: t('landing.platforms.disney.description', 'Magia cinematográfica com cores vibrantes e composições épicas. O visual que desperta o herói que existe em você.'),
                   color: 'bg-blue-600',
                   features: [
                     t('landing.platforms.disney.features.covers', '9 capas mágicas'),
                     t('landing.platforms.disney.features.style', 'Estilo épico'),
                     t('landing.platforms.disney.features.colors', 'Cores vibrantes')
                   ]
                 },
                 {
                   platform: 'Amazon',
                   title: 'PRIME VIDEO',
                   subtitle: t('landing.platforms.amazon.subtitle', 'Ação & Thriller'),
                   description: t('landing.platforms.amazon.description', 'Visual cinematográfico premium com foco em ação e intensidade. A estética dos blockbusters modernos.'),
                   color: 'bg-cyan-600',
                   features: [
                     t('landing.platforms.amazon.features.covers', '11 capas dinâmicas'),
                     t('landing.platforms.amazon.features.style', 'Estilo blockbuster'),
                     t('landing.platforms.amazon.features.composition', 'Composição premium')
                   ]
                 }
               ].map((platform, index) => (
                 <motion.div
                   key={index}
                   initial={{ opacity: 0, y: 20 }}
                   whileInView={{ opacity: 1, y: 0 }}
                   transition={{ duration: 0.6, delay: index * 0.1 }}
                   viewport={{ once: true }}
                   className="group relative p-8 bg-white dark:bg-gray-800 rounded-2xl shadow-lg hover:shadow-xl dark:hover:shadow-blue-500/10 transition-all duration-300 border border-gray-200 dark:border-gray-700"
                 >
                   <div className={`absolute -top-3 left-6 px-4 py-2 ${platform.color} text-white font-bold text-sm rounded-full`}>
                     {platform.platform}
                   </div>
                   
                   <div className="mt-6">
                     <h3 className="text-2xl font-bold mb-2 text-gray-900 dark:text-white">
                       {platform.title}
                     </h3>
                     <p className={`text-lg font-semibold mb-4 ${
                       platform.color.replace('bg-red-600', 'text-red-500 dark:text-red-400')
                                     .replace('bg-blue-600', 'text-blue-500 dark:text-blue-400')
                                     .replace('bg-cyan-600', 'text-cyan-500 dark:text-cyan-400')
                     }`}>
                       {platform.subtitle}
                     </p>
                     <p className="text-gray-600 dark:text-gray-300 mb-6 leading-relaxed">
                       {platform.description}
                     </p>

                     <ul className="space-y-3">
                       {platform.features.map((feature, featureIndex) => (
                         <li key={featureIndex} className="flex items-center space-x-3">
                           <Check className="w-5 h-5 text-green-500" />
                           <span className="text-gray-700 dark:text-gray-200">
                             {feature}
                           </span>
                         </li>
                       ))}
                     </ul>
                   </div>
                 </motion.div>
               ))}
             </div>
           </div>
         </section>

         {/* Features Section */}
         <section className="py-24 bg-gray-50 dark:bg-gray-900/50">
           <div className="max-w-7xl mx-auto px-4 md:px-6">
             <motion.div
               initial={{ opacity: 0, y: 20 }}
               whileInView={{ opacity: 1, y: 0 }}
               transition={{ duration: 0.6 }}
               viewport={{ once: true }}
               className="text-center mb-16"
             >
               <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl mb-4 text-gray-900 dark:text-white">
                 {t('landing.features2.title')}
               </h2>
               <p className="text-gray-600 dark:text-gray-300 text-lg max-w-2xl mx-auto">
                 {t('landing.features2.subtitle')}
               </p>
             </motion.div>

             <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
               {[
                 {
                   icon: <Sparkles className="w-12 h-12" />,
                   title: t('landing.features2.advanced.title'),
                   description: t('landing.features2.advanced.description'),
                   color: 'bg-purple-500'
                 },
                 {
                   icon: <Zap className="w-12 h-12" />,
                   title: t('landing.features2.fast.title'),
                   description: t('landing.features2.fast.description'),
                   color: 'bg-yellow-500'
                 },
                 {
                   icon: <Star className="w-12 h-12" />,
                   title: t('landing.features2.quality.title'),
                   description: t('landing.features2.quality.description'),
                   color: 'bg-green-500'
                 },
                 {
                   icon: <Check className="w-12 h-12" />,
                   title: t('landing.features2.easy.title'),
                   description: t('landing.features2.easy.description'),
                   color: 'bg-blue-500'
                 }
               ].map((feature, index) => (
                 <motion.div
                   key={index}
                   initial={{ opacity: 0, y: 20 }}
                   whileInView={{ opacity: 1, y: 0 }}
                   transition={{ duration: 0.6, delay: index * 0.1 }}
                   viewport={{ once: true }}
                   className="group relative p-8 bg-white dark:bg-gray-800 rounded-2xl shadow-lg hover:shadow-xl dark:hover:shadow-blue-500/10 transition-all duration-300 border border-gray-200 dark:border-gray-700"
                 >
                   <div className={`w-16 h-16 ${feature.color} text-white rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`}>
                     {feature.icon}
                   </div>
                   
                   <h3 className="text-2xl font-bold mb-4 text-gray-900 dark:text-white">
                     {feature.title}
                   </h3>
                   
                   <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                     {feature.description}
                   </p>
                 </motion.div>
               ))}
             </div>
           </div>
         </section>

         {/* Velocity Scroll Section */}
          <section className="py-12 bg-gray-50 dark:bg-gray-900/50">
            <VelocityScroll
              defaultVelocity={0.5}
              className="font-display text-center text-4xl font-bold tracking-[-0.02em] text-gray-900 drop-shadow-sm dark:text-gray-100 md:text-7xl md:leading-[5rem]"
            >
              {t('landing.velocity.text')}
            </VelocityScroll>
          </section>

         {/* FAQ Section */}
         <section className="py-24 bg-white dark:bg-gray-900">
           <div className="max-w-7xl mx-auto px-4 md:px-6">
             <motion.div
               initial={{ opacity: 0, y: 20 }}
               whileInView={{ opacity: 1, y: 0 }}
               transition={{ duration: 0.6 }}
               viewport={{ once: true }}
               className="text-center mb-16"
             >
               <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl mb-4 text-gray-900 dark:text-white">
                 {t('landing.faq.title')}
               </h2>
               <p className="text-gray-600 dark:text-gray-300 text-lg max-w-2xl mx-auto">
                 {t('landing.faq.subtitle')}
               </p>
             </motion.div>

             <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-5xl mx-auto">
               {[
                 {
                   question: t('landing.faq.quality.question'),
                   answer: t('landing.faq.quality.answer'),
                   color: 'bg-purple-500'
                 },
                 {
                   question: t('landing.faq.security.question'),
                   answer: t('landing.faq.security.answer'),
                   color: 'bg-pink-500'
                 },
                 {
                   question: t('landing.faq.commercial.question'),
                   answer: t('landing.faq.commercial.answer'),
                   color: 'bg-orange-500'
                 },
                 {
                   question: t('landing.faq.refund.question'),
                   answer: t('landing.faq.refund.answer'),
                   color: 'bg-cyan-500'
                 }
               ].map((faq, index) => (
                 <motion.div
                   key={index}
                   initial={{ opacity: 0, y: 20 }}
                   whileInView={{ opacity: 1, y: 0 }}
                   transition={{ duration: 0.6, delay: index * 0.1 }}
                   viewport={{ once: true }}
                   className="group relative p-6 bg-white dark:bg-gray-800 rounded-2xl shadow-lg hover:shadow-xl dark:hover:shadow-blue-500/10 transition-all duration-300 border-0 dark:border-0"
                 >
                   <div className={`absolute -top-3 -left-3 w-8 h-8 ${faq.color} rounded-full flex items-center justify-center text-white font-bold text-lg`}>
                     ?
                   </div>
                   
                   <h4 className="text-xl font-bold mb-4 text-gray-900 dark:text-white">
                     {faq.question}
                   </h4>
                   <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                     {faq.answer}
                   </p>
                 </motion.div>
               ))}
             </div>
           </div>
         </section>

        {/* CTA Section */}
        <section className="py-24 bg-gradient-to-r from-blue-600 to-purple-700 dark:from-blue-700 dark:to-purple-800 relative overflow-hidden">
          <WarpBackground className="absolute inset-0" />
          <div className="max-w-7xl mx-auto px-4 md:px-6 text-center relative z-10">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
            >
              <AnimatedGradientText className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl text-white dark:text-gray-100 mb-4">
                {t('landing.cta.title')}
              </AnimatedGradientText>
              <p className="text-blue-100 dark:text-blue-200 text-lg max-w-2xl mx-auto mb-8">
                {t('landing.cta.subtitle')}
              </p>
              <Button
                size="lg"
                className="bg-white text-blue-600 hover:bg-gray-100 dark:bg-gray-100 dark:text-blue-700 dark:hover:bg-white shadow-lg transition-all duration-300 rounded-3xl"
                onClick={handleGetStarted}
              >
                {t('landing.cta.button')}
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </motion.div>
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="bg-gray-900 dark:bg-gray-950 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 md:px-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center space-x-3 mb-4">
                <div className="h-8 w-8 rounded-2xl bg-blue-600 dark:bg-blue-500 flex items-center justify-center">
                  <Sparkles className="h-4 w-4 text-white" />
                </div>
                <span className="font-bold text-lg text-white dark:text-gray-100">{t('landing.brand', 'PosterFlix')}</span>
              </div>
              <p className="text-gray-400 dark:text-gray-300">
                {t('landing.footer.description')}
              </p>
            </div>
            
            <div>
              <h3 className="font-semibold mb-4 text-white dark:text-gray-100">{t('landing.footer.product')}</h3>
              <ul className="space-y-2 text-gray-400 dark:text-gray-300">
                <li><a href="#features" className="hover:text-white dark:hover:text-gray-100 transition-colors">{t('landing.nav.features')}</a></li>
                <li><a href="#testimonials" className="hover:text-white dark:hover:text-gray-100 transition-colors">{t('landing.nav.testimonials')}</a></li>
                <li><a href="#demo-section" className="hover:text-white dark:hover:text-gray-100 transition-colors">{t('landing.nav.demo')}</a></li>
              </ul>
            </div>
            
            <div>
              <h3 className="font-semibold mb-4 text-white dark:text-gray-100">{t('landing.footer.navigation')}</h3>
              <ul className="space-y-2 text-gray-400 dark:text-gray-300">
                <li><a href="#how-it-works" className="hover:text-white dark:hover:text-gray-100 transition-colors">{t('landing.nav.howItWorks')}</a></li>
                <li><a href="#features" className="hover:text-white dark:hover:text-gray-100 transition-colors">{t('landing.nav.features')}</a></li>
                <li><a href="#testimonials" className="hover:text-white dark:hover:text-gray-100 transition-colors">{t('landing.nav.testimonials')}</a></li>
              </ul>
            </div>
            
            <div>
              <h3 className="font-semibold mb-4 text-white dark:text-gray-100">{t('landing.footer.about')}</h3>
              <ul className="space-y-2 text-gray-400 dark:text-gray-300">
                <li><span className="text-gray-500 dark:text-gray-400">{t('landing.footer.aboutLine1')}</span></li>
                <li><span className="text-gray-500 dark:text-gray-400">{t('landing.footer.aboutLine2')}</span></li>
                <li><span className="text-gray-500 dark:text-gray-400">{t('landing.footer.aboutLine3')}</span></li>
              </ul>
            </div>
          </div>
          
          <div className="border-t border-gray-800 dark:border-gray-700 mt-8 pt-8 text-center text-gray-400 dark:text-gray-300">
            <p>{t('landing.footer.copyright')}</p>
          </div>
        </div>
      </footer>

      {/* Modals */}
      {showPricingModal && (
        <PricingModal
          isOpen={showPricingModal}
          onClose={() => setShowPricingModal(false)}
          onSelectPlan={handleSelectPlan}
          onVoucherAuthRequired={handleVoucherAuthRequired}
        />
      )}

      {showAuthModal && (
        <AuthModal
          isOpen={showAuthModal}
          onClose={() => setShowAuthModal(false)}
          onSuccess={handleAuthSuccess}
        />
      )}

      {showCheckoutModal && paymentIntent && stripePromise && (
        <CheckoutForm
          isOpen={showCheckoutModal}
          onClose={handlePaymentCancel}
          paymentIntent={paymentIntent}
          stripePromise={stripePromise}
          onSuccess={handlePaymentSuccess}
          planId={selectedPlan}
        />
      )}

      <Toaster position="top-right" />
    </div>
  );
};

// Main component with QueryClient provider
const NewLandingPage: React.FC = () => {
  return (
    <QueryClientProvider client={queryClient}>
      <NewLandingPageContent />
    </QueryClientProvider>
  );
};

export default NewLandingPage;