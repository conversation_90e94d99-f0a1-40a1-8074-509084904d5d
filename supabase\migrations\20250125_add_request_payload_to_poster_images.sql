-- Adicionar campo request_payload para sistema de regeneração de posters
-- Este campo armazenará o payload completo enviado para geração, permitindo regeneração pelo admin

-- Adicionar coluna request_payload como JSONB para armazenar dados completos
ALTER TABLE IF EXISTS public.poster_images 
ADD COLUMN IF NOT EXISTS request_payload JSONB;

-- Comentário para documentar o propósito
COMMENT ON COLUMN public.poster_images.request_payload IS 'Payload completo da requisição original para permitir regeneração pelo admin';

-- Índice para melhorar performance de consultas no request_payload
CREATE INDEX IF NOT EXISTS idx_poster_images_request_payload 
ON public.poster_images USING GIN (request_payload);

-- Política RLS para admin poder acessar todos os request_payload
CREATE POLICY IF NOT EXISTS "admin_can_view_request_payload" 
ON public.poster_images 
FOR SELECT 
USING (true); -- Permitir para todos, RLS já controla acesso por user_id 