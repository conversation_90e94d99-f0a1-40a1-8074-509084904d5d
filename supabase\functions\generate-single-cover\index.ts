import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

// Declaração de tipo para o Deno global
declare const Deno: {
  env: {
    get(key: string): string | undefined;
  };
};

// Função para fazer polling do resultado da predição
async function pollForResult(predictionId: string, replicateToken: string): Promise<{ status: string, output?: string[], error?: string }> {
  const maxAttempts = 30;
  const delayMs = 10000;

  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    await new Promise(resolve => setTimeout(resolve, delayMs));

    try {
      const response = await fetch(`https://api.replicate.com/v1/predictions/${predictionId}`, {
        headers: {
          'Authorization': `Token ${replicateToken}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        console.error(`Poll attempt ${attempt}: HTTP ${response.status}`);
        continue;
      }

      const result = await response.json();
      console.log(`Poll attempt ${attempt}: ${result.status}`);

      if (result.status === 'succeeded' || result.status === 'failed' || result.status === 'canceled') {
        return result;
      }
    } catch (error) {
      console.error(`Poll attempt ${attempt} failed:`, error);
    }
  }

  return { status: 'failed', error: 'Polling timeout' };
}

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
}

// Função para download e upload da imagem para o Supabase Storage
async function downloadAndUploadToSupabase(replicateImageUrl: string, movieTitle: string, streamingPlatform: string) {
  try {
    console.log(`🔄 Starting download and upload process for ${movieTitle}...`);
    console.log(`📥 Replicate URL: ${replicateImageUrl}`);
    
    const supabaseUrl = Deno.env.get('SUPABASE_URL') || '';
    const supabaseServiceRoleKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || '';
    
    if (!supabaseUrl || !supabaseServiceRoleKey) {
      console.error('❌ Missing Supabase credentials');
      return null;
    }
    
    const supabase = createClient(supabaseUrl, supabaseServiceRoleKey);

    console.log(`📥 Downloading image from Replicate for ${movieTitle}...`);
    const imageResponse = await fetch(replicateImageUrl);
    if (!imageResponse.ok) {
      console.error(`❌ Failed to download image from Replicate: ${imageResponse.status} ${imageResponse.statusText}`);
      return null;
    }

    console.log(`📦 Image downloaded successfully, converting to buffer...`);
    const imageBlob = await imageResponse.blob();
    const imageBuffer = await imageBlob.arrayBuffer();
    
    console.log(`📦 Image buffer size: ${imageBuffer.byteLength} bytes`);

    const timestamp = Date.now();
    const sanitizedTitle = movieTitle.toLowerCase().replace(/[^a-z0-9]/g, '-');
    const filename = `covers/${timestamp}-${sanitizedTitle}-${streamingPlatform}.jpg`;

    console.log(`📤 Uploading to Supabase Storage: ${filename}`);
    const { data, error } = await supabase.storage.from('generated-images').upload(filename, imageBuffer, {
      contentType: 'image/jpeg',
      upsert: false
    });

    if (error) {
      console.error(`❌ Supabase upload error for ${movieTitle}:`, error);
      console.error(`❌ Error details:`, JSON.stringify(error, null, 2));
      return null;
    }

    console.log(`✅ Upload successful, getting public URL...`);
    const { data: { publicUrl } } = supabase.storage.from('generated-images').getPublicUrl(filename);
    console.log(`✅ Successfully uploaded ${movieTitle} to Supabase: ${publicUrl}`);
    return publicUrl;
  } catch (error) {
    console.error(`❌ Critical error downloading/uploading ${movieTitle}:`, error);
    console.error(`❌ Error stack:`, error.stack);
    return null;
  }
}

// Salva capa em cover_generations para aparecer em "Minhas Gerações"
async function saveToSeriesAndFilms(supabase: any, userId: string, generationId: string, imageUrl: string, movieTitle: string, streamingPlatform: string, photoType: string, aspectRatio: string, position: number, originalImageUrl: string, userName: string, gender?: string, language: string = 'portuguese', generateTitles: boolean = false, creativityLevelName: string = 'rostoFiel') {
  try {
    console.log(`💾 Saving to Series & Films: ${movieTitle} (${generationId})`);

    // Buscar a geração atual
    const { data: currentGeneration, error: fetchError } = await supabase.from('cover_generations').select('generated_covers').eq('id', generationId).single();

    if (fetchError) {
      console.error('Error fetching current generation:', fetchError);
      // Se a geração não existe, criar uma nova
      const { error: createError } = await supabase.from('cover_generations').insert({
        id: generationId,
        user_id: userId || '',
        original_image_url: originalImageUrl || 'https://placeholder.com/300x400',
        streaming_platform: streamingPlatform,
        photo_type: photoType, // Usar photo_type original (agora aceita 'cover')
        user_name: userName,
        gender: gender,
        language: language || 'portuguese',
        generate_titles: generateTitles || false,
        creativity_level: creativityLevelName || 'rostoFiel', // Usar o nome do nível, não "Rosto Fiel"
        generated_covers: [
          {
            movie_title: movieTitle,
            image_url: imageUrl,
            streaming_platform: streamingPlatform,
            photo_type: photoType, // Manter o tipo original no JSON
            aspect_ratio: aspectRatio,
            position: position,
            success: true, // 🔥 ADICIONAR PROPRIEDADE SUCCESS
            created_at: new Date().toISOString()
          }
        ],
        status: 'completed',
        completed_at: new Date().toISOString()
      });

      if (createError) {
        console.error('Error creating new generation:', createError);
        return;
      } else {
        console.log(`✅ Created new generation and saved cover: ${movieTitle}`);
        return;
      }
    }

    // Adicionar nova capa ao array existente
    const currentCovers = currentGeneration.generated_covers || [];
    const newCover = {
      movie_title: movieTitle,
      image_url: imageUrl,
      streaming_platform: streamingPlatform,
      photo_type: photoType,
      aspect_ratio: aspectRatio,
      position: position,
      success: true, // 🔥 ADICIONAR PROPRIEDADE SUCCESS
      created_at: new Date().toISOString()
    };

    currentCovers.push(newCover);

    // Atualizar array de capas geradas
    const { error: updateError } = await supabase.from('cover_generations').update({
      generated_covers: currentCovers,
      created_at: new Date().toISOString(), // 🔥 ATUALIZAR DATA DA GERAÇÃO PARA QUE APAREÇA NO TOPO
      completed_at: new Date().toISOString() // 🔥 ATUALIZAR TAMBÉM completed_at
    }).eq('id', generationId);

    if (updateError) {
      console.error('Error updating generated covers array:', updateError);
    } else {
      console.log(`✅ Cover saved to Series & Films: ${movieTitle} - ${imageUrl}`);
    }
  } catch (error) {
    console.error('Error in saveToSeriesAndFilms:', error);
  }
}

// Salva capa principal em cover_images para aparecer na aba "Cover Images"
async function saveCoverToImages(supabase: any, userId: string, imageUrl: string, movieTitle: string, streamingPlatform: string, photoType: string, aspectRatio: string, generationId: string) {
  try {
    console.log(`💾 Saving to Cover Images: ${movieTitle}`);

    const { error } = await supabase.from('cover_images').insert({
      user_id: userId,
      image_url: imageUrl,
      movie_title: movieTitle,
      streaming_platform: streamingPlatform,
      photo_type: photoType,
      aspect_ratio: aspectRatio || '21:9',
      is_regenerated: false,
      generation_id: generationId,
      created_at: new Date().toISOString()
    });

    if (error) {
      console.error('Error saving to cover_images:', error);
    } else {
      console.log(`✅ Cover saved to Cover Images: ${movieTitle} - ${imageUrl}`);
    }
  } catch (error) {
    console.error('Error in saveCoverToImages:', error);
  }
}

interface GeneratedCover {
  movie_title: string;
  image_url?: string;
  success: boolean;
  error?: string;
  fallback_used?: boolean;
  fallback_movie?: string;
  no_title_used?: boolean;
  safe_prompt_used?: boolean;
  generated_at: string;
  promptInfo?: {
    promptUsed: string;
    attempts: number;
    fallbackUsed: boolean;
    safePromptUsed: boolean;
    lastPromptTried?: string;
  };
}

// Função auxiliar para gerar uma tentativa de capa
async function generateSingleCoverAttempt(
  supabase: any,
  movie: any,
  originalImageUrl: string,
  streamingPlatform: string,
  photoType: string,
  aspectRatio: string,
  creativityLevelId: string,
  customPrompt?: string,
  gender?: string,
  language: string = 'portuguese',
  generateTitles: boolean = true,
  skipCreativity: boolean = false
): Promise<{
  success: boolean;
  image_url?: string;
  error?: string;
  promptUsed?: string;
  creativityLevel?: any;
}> {
  try {
    const replicateToken = Deno.env.get('REPLICATE_API_TOKEN');
    if (!replicateToken) {
      throw new Error('REPLICATE_API_TOKEN não configurado');
    }

    // Buscar nível de criatividade
    console.log(`🔍 Buscando nível de criatividade com ID: ${creativityLevelId}`);
    const { data: creativityLevel, error: creativityError } = await supabase
      .from('creativity_levels')
      .select('*')
      .eq('id', creativityLevelId)
      .single();

    let finalCreativityLevel;

    if (creativityError || !creativityLevel) {
      console.error('Erro ao buscar nível de criatividade:', creativityError);
      console.log(`⚠️ Tentando buscar todos os níveis para encontrar um padrão...`);
      
      // Tentar buscar todos os níveis e usar um como padrão
      const { data: allLevels, error: allError } = await supabase
        .from('creativity_levels')
        .select('*');
      
      if (allError || !allLevels || allLevels.length === 0) {
        console.error('Erro ao buscar todos os níveis:', allError);
        // Usar configuração mínima para continuar
        console.log(`⚠️ Usando configuração de criatividade básica como último recurso`);
        finalCreativityLevel = {
          id: 'fallback',
          name: 'rostoFiel',
          prompt_enhancement: 'Keeping the person\'s face faithful to the photo, but with creative freedom for the setting',
          max_tokens: 2000,
          temperature: 0.5
        };
      } else {
        // Usar o primeiro nível disponível como padrão
        console.log(`✅ Usando primeiro nível disponível como fallback:`, allLevels[0]);
        finalCreativityLevel = allLevels[0];
      }
    } else {
      console.log(`✅ Nível de criatividade encontrado:`, creativityLevel);
      finalCreativityLevel = creativityLevel;
    }

    // Construir prompt
    let basePrompt = '';
    let promptSource = '';
    let finalPrompt = '';

    if (customPrompt) {
      console.log('📝 Using custom prompt...');
      basePrompt = customPrompt;
      promptSource = 'custom_prompt';
    } else {
      console.log('📝 Building prompt from database...');
      basePrompt = movie.base_prompt;
      
      // Usar prompts específicos resolvidos do banco de dados
      if (photoType === 'cover') {
        basePrompt = `${movie.base_prompt} Position the person on the right side of the image. Wide cinematic shot with balanced composition. Make sure the person is not centered but positioned to the right third of the image. Place the title '${movie.title}' in the bottom right corner of the image with an elegant, readable gold font.`;
        promptSource = 'cover_prompt';
      } else if (photoType === 'casal' && movie.couple_prompt) {
        basePrompt = movie.couple_prompt;
        promptSource = 'couple_prompt';
      } else if (photoType === 'individual' && gender === 'male' && movie.gender_male_prompt) {
        basePrompt = movie.gender_male_prompt;
        promptSource = 'gender_male_prompt';
      } else if (photoType === 'individual' && gender === 'female' && movie.gender_female_prompt) {
        basePrompt = movie.gender_female_prompt;
        promptSource = 'gender_female_prompt';
      } else {
        promptSource = 'base_prompt';
      }
    }

    // Instrução de título
    const titleInstruction = generateTitles 
      ? `IMPORTANTE: Adicione o título "${movie.title}" na parte inferior da imagem com fonte elegante e legível. `
      : '';

    // Montagem final do prompt
    if (skipCreativity) {
      finalPrompt = movie.title;
    } else {
      finalPrompt = (finalCreativityLevel.prompt_enhancement || '') + titleInstruction + basePrompt;
    }

    console.log(`📝 Prompt source: ${promptSource}`);
    console.log(`📝 Final prompt (${finalPrompt.length} chars): ${finalPrompt.substring(0, 200)}...`);

    const replicatePayload = {
      input: {
        input_image: originalImageUrl,
        prompt: finalPrompt,
        aspect_ratio: aspectRatio || '3:4',
        output_format: 'jpg',
        safety_tolerance: 2,
        num_inference_steps: 28,
        guidance_scale: 2,
      }
    };

    const prediction = await fetch('https://api.replicate.com/v1/models/black-forest-labs/flux-kontext-max/predictions', {
      method: 'POST',
      headers: {
        'Authorization': `Token ${replicateToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(replicatePayload)
    });

    if (!prediction.ok) {
      const errorText = await prediction.text();
      console.log(`❌ Replicate API error: ${errorText}`);
      throw new Error(`Replicate API error: ${errorText}`);
    }

    const predictionData = await prediction.json();
    console.log(`🚀 Started generation for ${movie.title}: ${predictionData.status}`);

    // Polling para resultado
    const pollResult = await pollForResult(predictionData.id, replicateToken);
    
    if (pollResult.status === 'succeeded' && pollResult.output) {
      const replicateImageUrl = Array.isArray(pollResult.output) ? pollResult.output[0] : pollResult.output;

      if (replicateImageUrl) {
        console.log(`✅ Generation successful for ${movie.title}, downloading and uploading to Supabase...`);
        
        // Download da imagem do Replicate e upload para o Supabase Storage
        console.log(`🔄 Attempting to upload ${movie.title} from Replicate URL: ${replicateImageUrl}`);
        const supabaseImageUrl = await downloadAndUploadToSupabase(replicateImageUrl, movie.title, streamingPlatform);
        
        if (supabaseImageUrl) {
          console.log(`✅ SUCCESS: Image uploaded to Supabase Storage for ${movie.title}: ${supabaseImageUrl}`);
          return {
            success: true,
            image_url: supabaseImageUrl, // Usar a URL do Supabase, não do Replicate
            promptUsed: finalPrompt,
            creativityLevel: finalCreativityLevel
          };
        } else {
          console.log(`⚠️ FALLBACK: Failed to upload ${movie.title} to Supabase, using Replicate URL as fallback: ${replicateImageUrl}`);
          return {
            success: true,
            image_url: replicateImageUrl, // Fallback para URL do Replicate
            promptUsed: finalPrompt,
            creativityLevel: finalCreativityLevel,
            warning: 'Failed to upload to Supabase Storage, using temporary Replicate URL'
          };
        }
      }
    }

    const errorMessage = pollResult.error || `Generation failed with status: ${pollResult.status}`;
    console.log(`❌ Generation failed for ${movie.title}: ${errorMessage}`);
    
    return {
      success: false,
      error: errorMessage,
      promptUsed: finalPrompt,
      creativityLevel: finalCreativityLevel
    };

  } catch (error) {
    console.error(`❌ Error in generateSingleCoverAttempt for ${movie.title}:`, error);
    return {
      success: false,
      error: (error as Error).message || 'Unknown error',
      promptUsed: customPrompt || movie.base_prompt
    };
  }
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  console.log('🚀 generate-single-cover: Starting function execution...');

  // 1. AUTHENTICATION
  const authHeader = req.headers.get('Authorization');
  if (!authHeader) {
    console.error('❌ Missing authorization header');
    return new Response(JSON.stringify({
      error: 'Falta o cabeçalho de autenticação'
    }), {
      status: 401,
      headers: corsHeaders
    });
  }

  console.log('🔐 Authentication header found, creating Supabase client...');
  
  try {
    const supabaseUrl = Deno.env.get('SUPABASE_URL') || '';
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || '';
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    console.log('🔍 Getting user from token...');
    const authToken = authHeader.replace('Bearer ', '');
    const authResponse = await supabase.auth.getUser(authToken || '');
    const user = authResponse.data.user;
    
    if (!user) {
      console.error('❌ Invalid or expired token');
      return new Response(JSON.stringify({
        error: 'Token inválido ou expirado'
      }), {
        status: 401,
        headers: corsHeaders
      });
    }

    console.log(`✅ User authenticated: ${user.id}`);
    
    console.log('📝 Parsing request body...');
    const { 
      movieTitle, 
      originalImageUrl, 
      streamingPlatform, 
      photoType, 
      aspectRatio, 
      position, 
      creativityLevel,
      customPrompt,
      gender,
      language = 'portuguese',
      generateTitles = true,
      testMode = false,
      generationId,
      useBackup = true,
      moviesToExclude = [],
      isManualRegeneration = false,
      isRegeneration = false,
      userName
    } = await req.json();
    
    const userId = user.id;
    
    console.log(`🎯 Processing: ${movieTitle} for ${streamingPlatform} (position ${position})`);
    console.log(`🧪 Test mode: ${testMode} ${testMode ? '(NO CREDITS/LOGS)' : '(WILL CONSUME CREDITS/LOGS)'}`);
    
    // 🔥 VERIFICAR TÍTULOS JÁ GERADOS (apenas para geração automática)
    if (!isManualRegeneration && generationId) {
      console.log(`🔍 Checking for existing titles in generation ${generationId}...`);
      const { data: existingGeneration, error: checkError } = await supabase.from('cover_generations').select('generated_covers').eq('id', generationId).single();
      
      if (!checkError && existingGeneration && existingGeneration.generated_covers) {
        const existingTitles = existingGeneration.generated_covers
          .filter((cover: any) => cover.image_url) // Apenas capas com sucesso
          .map((cover: any) => cover.movie_title);
          
        console.log(`📋 Existing successful titles: ${existingTitles.join(', ')}`);
        
        // Se o título já foi gerado com sucesso, pular
        if (existingTitles.includes(movieTitle)) {
          console.log(`⚠️ Title "${movieTitle}" already generated successfully, skipping...`);
          return new Response(JSON.stringify({
            success: false,
            error: `Title "${movieTitle}" already generated in this batch`,
            movie_title: movieTitle,
            skip_reason: 'duplicate_title'
          }), {
            headers: {
              ...corsHeaders,
              'Content-Type': 'application/json'
            }
          });
        }
      }
    }
    
    // 💰 Verificar créditos disponíveis
    console.log('💰 Checking credits...');
    const creditsNeeded = 1;
    
    if (!testMode) {
      // Verificar saldo de créditos
      const { data: purchases } = await supabase.from('credit_purchases').select('credits').eq('user_id', userId).eq('status', 'completed');
      const { data: usage } = await supabase.from('credit_usage').select('credits_used').eq('user_id', userId);
      
      const totalCredits = purchases?.reduce((sum: number, p: any) => sum + p.credits, 0) || 0;
      const usedCredits = usage?.reduce((sum: number, u: any) => sum + u.credits_used, 0) || 0;
      const availableCredits = totalCredits - usedCredits;
      
      console.log(`💳 Credits: ${availableCredits} available, ${creditsNeeded} needed`);
      
      if (availableCredits < creditsNeeded) {
        console.log('❌ Insufficient credits');
        return new Response(JSON.stringify({
          error: 'Créditos insuficientes para gerar capa',
          availableCredits,
          requiredCredits: creditsNeeded,
          message: 'Compre mais créditos para continuar'
        }), {
          status: 402,
          headers: {
            ...corsHeaders,
            'Content-Type': 'application/json'
          }
        });
      }
    }

    console.log(`🎬 Generating cover for: ${movieTitle} (${streamingPlatform})`);

    // Buscar níveis de criatividade diretamente do banco
    let creativityMap: { [key: string]: string } = {};
    
    try {
      console.log(`🎨 Buscando níveis de criatividade do banco...`);
      const { data: allCreativityLevels, error: levelsError } = await supabase
        .from('creativity_levels')
        .select('id,name,prompt_enhancement');

      if (!levelsError && allCreativityLevels && allCreativityLevels.length > 0) {
        console.log(`🎨 Níveis encontrados no banco:`, allCreativityLevels);
        
        // Mapear com os nomes exatos do banco
        allCreativityLevels.forEach((level: any) => {
          const normalizedName = level.name.toLowerCase().replace(/[^a-z]/g, '');
          creativityMap[normalizedName] = level.id;
          creativityMap[level.name] = level.id; // Mapear também o nome original
        });
        
        // Definir padrão baseado no que existe no banco
        const defaultLevel = allCreativityLevels.find((l: any) => l.name === 'rostoFiel');
        if (defaultLevel) {
          creativityMap['default'] = defaultLevel.id;
        } else {
          creativityMap['default'] = allCreativityLevels[0].id; // Usar o primeiro como padrão
        }
      } else {
        console.log(`❌ Erro ao buscar níveis do banco:`, levelsError);
        throw new Error('Creativity levels not found in database');
      }
    } catch (err) {
      console.error(`❌ Erro crítico ao buscar níveis de criatividade:`, err);
      throw new Error('Failed to load creativity levels from database');
    }

    const normalizedCreativityId = creativityLevel?.toLowerCase() || 'rostofiel';
    let finalCreativityLevelId;
    
    // Tentar encontrar o ID mapeado
    if (creativityMap[normalizedCreativityId]) {
      finalCreativityLevelId = creativityMap[normalizedCreativityId];
    } else {
      // Fallback para rostoFiel se não encontrar
      finalCreativityLevelId = creativityMap['rostofiel'];
    }

    console.log(`🎨 Creativity level received: "${creativityLevel}"`);
    console.log(`🎨 Normalized to: "${normalizedCreativityId}"`);
    console.log(`🎨 Using creativity level ID: ${finalCreativityLevelId}`);
    console.log(`🎨 Available mapping:`, creativityMap);

    // Buscar filme no banco de dados
    const { data: movies, error: moviesError } = await supabase
      .from('movies_with_prompts')
      .select('*, default_creativity_level')
      .eq('streaming_platform', streamingPlatform)
      .eq('is_active', true);

    if (moviesError) {
      console.error('Error loading movies from database:', moviesError);
      throw new Error(`Failed to load movies: ${moviesError.message}`);
    }

    if (!movies || movies.length === 0) {
      throw new Error(`No active movies found for platform: ${streamingPlatform}`);
    }

    console.log(`🎬 Looking for movie: "${movieTitle}"`);
    console.log(`📋 Available movies: ${movies.map((m: any) => m.title).join(', ')}`);

    // Tentar busca exata primeiro
    let targetMovie = movies.find((m: any) => m.title === movieTitle);
    
    // Se não encontrar, tentar busca parcial (contém o título)
    if (!targetMovie) {
      targetMovie = movies.find((m: any) => 
        m.title.toLowerCase().includes(movieTitle.toLowerCase()) ||
        movieTitle.toLowerCase().includes(m.title.toLowerCase())
      );
      if (targetMovie) {
        console.log(`✅ Found partial match: "${targetMovie.title}" for "${movieTitle}"`);
      }
    }
    
    // Se ainda não encontrar, tentar busca por palavras-chave
    if (!targetMovie) {
      const movieWords = movieTitle.toLowerCase().split(/\s+/);
      targetMovie = movies.find((m: any) => {
        const titleWords = m.title.toLowerCase().split(/\s+/);
        return movieWords.some(word => titleWords.some(titleWord => 
          titleWord.includes(word) || word.includes(titleWord)
        ));
      });
      if (targetMovie) {
        console.log(`✅ Found keyword match: "${targetMovie.title}" for "${movieTitle}"`);
      }
    }
    
    if (!targetMovie && !customPrompt) {
      console.log(`❌ Movie "${movieTitle}" not found for platform: ${streamingPlatform}`);
      console.log(`📋 Available titles: ${movies.map((m: any) => m.title).join(', ')}`);
      throw new Error(`Movie "${movieTitle}" not found for platform: ${streamingPlatform}`);
    }

    // Fallback para filme genérico se não encontrado
    if (!targetMovie) {
      targetMovie = {
        title: movieTitle,
        base_prompt: customPrompt || 'A cinematic poster',
        safe_prompt: customPrompt || 'A cinematic poster',
        gender_male_prompt: customPrompt || 'A cinematic poster',
        gender_female_prompt: customPrompt || 'A cinematic poster',
        couple_prompt: customPrompt || 'A cinematic poster',
        default_creativity_level: 'rostoFiel'
      };
    }

    console.log(`🎬 Target movie: ${targetMovie.title}`);
    console.log(`📝 Available prompts: base=${!!targetMovie.base_prompt}, safe=${!!targetMovie.safe_prompt}, male=${!!targetMovie.gender_male_prompt}, female=${!!targetMovie.gender_female_prompt}, couple=${!!targetMovie.couple_prompt}`);

    // 🔥 SISTEMA DE FALLBACK REORGANIZADO CONFORME ESPECIFICAÇÃO
    let attemptCount = 0;
    let lastError = '';

    const result: GeneratedCover = {
      movie_title: movieTitle, // USAR O TÍTULO ORIGINAL DA REQUISIÇÃO (que vai para o prompt)
      success: false,
      generated_at: new Date().toISOString(),
      promptInfo: {
        promptUsed: '',
        attempts: 0,
        fallbackUsed: false,
        safePromptUsed: false
      }
    };

    // 🎯 DEFINIR SEQUÊNCIA DE TENTATIVAS BASEADA NO TIPO DE FOTO E GÊNERO
    const attempts = [];

    if (photoType === 'cover') {
      // COVER/BANNER: 2 tentativas iniciais + tentativas específicas por gênero + 3 tentativas de fallback
      attempts.push(
        { prompt: targetMovie.base_prompt, withTitle: true, withPositioning: true, description: 'base_prompt + criatividade + instrução do título + posicionamento' },
        { prompt: targetMovie.base_prompt, withTitle: false, withPositioning: true, description: 'base_prompt + criatividade + posicionamento (sem título)' }
      );
      
      // Adicionar tentativas específicas por gênero após as 2 primeiras falharem
      if (gender === 'male' && targetMovie.gender_male_prompt) {
        attempts.push(
          { prompt: targetMovie.gender_male_prompt, withTitle: true, withPositioning: true, description: 'gender_male_prompt + criatividade + instrução do título + posicionamento' },
          { prompt: targetMovie.gender_male_prompt, withTitle: false, withPositioning: true, description: 'gender_male_prompt + criatividade + posicionamento (sem título)' }
        );
      } else if (gender === 'female' && targetMovie.gender_female_prompt) {
        attempts.push(
          { prompt: targetMovie.gender_female_prompt, withTitle: true, withPositioning: true, description: 'gender_female_prompt + criatividade + instrução do título + posicionamento' },
          { prompt: targetMovie.gender_female_prompt, withTitle: false, withPositioning: true, description: 'gender_female_prompt + criatividade + posicionamento (sem título)' }
        );
      } else if (gender === 'casal' && targetMovie.couple_prompt) {
        attempts.push(
          { prompt: targetMovie.couple_prompt, withTitle: true, withPositioning: true, description: 'couple_prompt + criatividade + instrução do título + posicionamento' },
          { prompt: targetMovie.couple_prompt, withTitle: false, withPositioning: true, description: 'couple_prompt + criatividade + posicionamento (sem título)' }
        );
      }
      
      // Adicionar tentativas finais de fallback
       attempts.push(
         { prompt: `${targetMovie.title}`, withTitle: false, withPositioning: true, description: 'apenas o título + posicionamento (sem criatividade)', skipCreativity: true },
         { prompt: targetMovie.safe_prompt, withTitle: true, withPositioning: true, description: 'safe_prompt + criatividade + instrução do título + posicionamento' },
         { prompt: targetMovie.safe_prompt, withTitle: false, withPositioning: true, description: 'safe_prompt + criatividade + posicionamento (sem título)' }
       );
    } else if (photoType === 'individual' && gender === 'male') {
      // HOMEM INDIVIDUAL: 8 tentativas
      attempts.push(
        { prompt: targetMovie.base_prompt, withTitle: true, withPositioning: false, description: 'base_prompt + criatividade + instrução do título' },
        { prompt: targetMovie.base_prompt, withTitle: false, withPositioning: false, description: 'base_prompt + criatividade' },
        { prompt: targetMovie.gender_male_prompt, withTitle: true, withPositioning: false, description: 'gender_male_prompt + criatividade + instrução do título' },
        { prompt: targetMovie.gender_male_prompt, withTitle: false, withPositioning: false, description: 'gender_male_prompt + criatividade' },
        { prompt: `Inspired in a series ${targetMovie.title} make a poster film about it`, withTitle: true, withPositioning: false, description: 'generic prompt + criatividade + instrução do título' },
        { prompt: `Inspired in a series ${targetMovie.title} make a poster film about it`, withTitle: false, withPositioning: false, description: 'generic prompt + criatividade' },
        { prompt: targetMovie.safe_prompt, withTitle: true, withPositioning: false, description: 'safe_prompt + criatividade + instrução do título' },
        { prompt: targetMovie.safe_prompt, withTitle: false, withPositioning: false, description: 'safe_prompt + criatividade' }
      );
    } else if (photoType === 'individual' && gender === 'female') {
      // MULHER INDIVIDUAL: 8 tentativas
      attempts.push(
        { prompt: targetMovie.base_prompt, withTitle: true, withPositioning: false, description: 'base_prompt + criatividade + instrução do título' },
        { prompt: targetMovie.base_prompt, withTitle: false, withPositioning: false, description: 'base_prompt + criatividade' },
        { prompt: targetMovie.gender_female_prompt, withTitle: true, withPositioning: false, description: 'gender_female_prompt + criatividade + instrução do título' },
        { prompt: targetMovie.gender_female_prompt, withTitle: false, withPositioning: false, description: 'gender_female_prompt + criatividade' },
        { prompt: `Inspired in a series ${targetMovie.title} make a poster film about it`, withTitle: true, withPositioning: false, description: 'generic prompt + criatividade + instrução do título' },
        { prompt: `Inspired in a series ${targetMovie.title} make a poster film about it`, withTitle: false, withPositioning: false, description: 'generic prompt + criatividade' },
        { prompt: targetMovie.safe_prompt, withTitle: true, withPositioning: false, description: 'safe_prompt + criatividade + instrução do título' },
        { prompt: targetMovie.safe_prompt, withTitle: false, withPositioning: false, description: 'safe_prompt + criatividade' }
      );
    } else if (photoType === 'casal') {
      // CASAL: 8 tentativas
      attempts.push(
        { prompt: targetMovie.base_prompt, withTitle: true, withPositioning: false, description: 'base_prompt + criatividade + instrução do título' },
        { prompt: targetMovie.base_prompt, withTitle: false, withPositioning: false, description: 'base_prompt + criatividade' },
        { prompt: targetMovie.couple_prompt, withTitle: true, withPositioning: false, description: 'couple_prompt + criatividade + instrução do título' },
        { prompt: targetMovie.couple_prompt, withTitle: false, withPositioning: false, description: 'couple_prompt + criatividade' },
        { prompt: `Inspired in a series ${targetMovie.title} make a poster film about it`, withTitle: true, withPositioning: false, description: 'generic prompt + criatividade + instrução do título' },
        { prompt: `Inspired in a series ${targetMovie.title} make a poster film about it`, withTitle: false, withPositioning: false, description: 'generic prompt + criatividade' },
        { prompt: targetMovie.safe_prompt, withTitle: true, withPositioning: false, description: 'safe_prompt + criatividade + instrução do título' },
        { prompt: targetMovie.safe_prompt, withTitle: false, withPositioning: false, description: 'safe_prompt + criatividade' }
      );
    } else {
      // FALLBACK PADRÃO: individual sem gênero definido
      attempts.push(
        { prompt: targetMovie.base_prompt, withTitle: true, withPositioning: false, description: 'base_prompt + criatividade + instrução do título' },
        { prompt: targetMovie.base_prompt, withTitle: false, withPositioning: false, description: 'base_prompt + criatividade' },
        { prompt: `Inspired in a series ${targetMovie.title} make a poster film about it`, withTitle: true, withPositioning: false, description: 'generic prompt + criatividade + instrução do título' },
        { prompt: `Inspired in a series ${targetMovie.title} make a poster film about it`, withTitle: false, withPositioning: false, description: 'generic prompt + criatividade' },
        { prompt: targetMovie.safe_prompt, withTitle: true, withPositioning: false, description: 'safe_prompt + criatividade + instrução do título' },
        { prompt: targetMovie.safe_prompt, withTitle: false, withPositioning: false, description: 'safe_prompt + criatividade' }
      );
    }

    // Filtrar tentativas com prompts válidos
    const validAttempts = attempts.filter(attempt => attempt.prompt && attempt.prompt.trim().length > 0);
    
    console.log(`🎯 Prepared ${validAttempts.length} fallback attempts for ${photoType} ${gender || 'unspecified'}`);

    // 🔄 EXECUTAR TENTATIVAS SEQUENCIALMENTE - MAS PARAR NO PRIMEIRO SUCESSO
    for (const attempt of validAttempts) {
      attemptCount++;
      console.log(`🚀 Attempt ${attemptCount}: ${attempt.description}`);
      
      // Preparar prompt customizado para esta tentativa
      let customPromptForAttempt = attempt.prompt;
      
      // Adicionar instruções de posicionamento para cover/banner
      if (attempt.withPositioning) {
        customPromptForAttempt += ` Position the person on the right side of the image. Wide cinematic shot with balanced composition. Make sure the person is not centered but positioned to the right third of the image. Place the title '${movieTitle}' in the bottom right corner of the image with an elegant, readable gold font.`;
      }

      const attemptResult = await generateSingleCoverAttempt(
        supabase,
        { ...targetMovie, base_prompt: customPromptForAttempt, title: movieTitle }, // ✅ USAR TÍTULO ORIGINAL NO PROMPT
        originalImageUrl,
        streamingPlatform,
        photoType,
        aspectRatio,
        finalCreativityLevelId,
        undefined,
        gender,
        language,
        attempt.withTitle,
        attempt.skipCreativity || false
      );

      result.promptInfo!.attempts = attemptCount;
      result.promptInfo!.promptUsed = attemptResult.promptUsed || '';

      if (attemptResult.success) {
        console.log(`✅ Attempt ${attemptCount} SUCCESS: ${attempt.description}`);
        result.success = true;
        result.image_url = attemptResult.image_url || '';
        result.movie_title = movieTitle; // ✅ USAR O TÍTULO ORIGINAL QUE FOI PARA O PROMPT
        result.promptInfo!.fallbackUsed = attemptCount > 1;

        // 💾 Salvar capa em cover_generations se não for modo de teste
        if (!testMode && generationId) {
          // 🔥 LÓGICA BASEADA NO PHOTO_TYPE (independente se é geração ou regeneração)
          if (photoType !== 'cover') {
            // Filmes e séries → cover_generations → "Series & Films" tab
            await saveToSeriesAndFilms(
              supabase,
              userId || '',
              generationId || '',
              result.image_url || '',
              movieTitle, // ✅ USAR TÍTULO ORIGINAL
              streamingPlatform || '',
              photoType || '',
              aspectRatio || '',
              position || 0,
              originalImageUrl || '',
              userName || '',
              gender || '',
              language || 'portuguese',
              !!generateTitles,
              attemptResult.creativityLevel?.name || 'rostoFiel' // Usar o nome do banco
            );
          } else {
            // Capa principal → cover_images → "Cover Images" tab
            await saveCoverToImages(
              supabase,
              userId || '',
              result.image_url || '',
              movieTitle,
              streamingPlatform || '',
              photoType || '',
              aspectRatio || '',
              generationId || ''
            );
          }
        } else {
          console.log(`⚠️ Skipping database insert: testMode=${testMode}, generationId=${generationId}`);
        }

        // Consumir créditos apenas se não for modo de teste
        if (!testMode) {
          console.log(`💳 Consuming ${creditsNeeded} credits for ${movieTitle}...`);
          const { error: creditError } = await supabase.from('credit_usage').insert({
            user_id: userId || '',
            credits_used: creditsNeeded,
            description: `${movieTitle} - ${streamingPlatform?.toUpperCase() || 'UNKNOWN'} (Posição ${position || 0})`,
            generation_id: generationId || null
          });

          if (creditError) {
            console.error('❌ Error consuming credits:', creditError);
          } else {
            console.log(`✅ Successfully consumed ${creditsNeeded} credits for ${movieTitle}`);
          }
        } else {
          console.log(`🧪 Test mode: NOT consuming credits for ${movieTitle}`);
        }

        // Atualizar estatísticas do filme (sucesso)
        if (!testMode) {
          try {
            await supabase.rpc('update_movie_stats', {
              p_movie_title: movieTitle, // ✅ USAR TÍTULO ORIGINAL
              p_streaming_platform: streamingPlatform || 'unknown',
              p_success: true,
              p_error_message: null
            });
          } catch (statsError) {
            console.error('Error updating movie stats:', statsError);
          }
        }

        // 🎯 RETORNAR IMEDIATAMENTE APÓS SUCESSO - NÃO CONTINUAR TENTATIVAS
        return new Response(JSON.stringify(result), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        });
      }

      lastError = attemptResult.error || `Attempt ${attemptCount} failed`;
      console.log(`❌ Attempt ${attemptCount} failed: ${lastError}`);
    }

    // 🔥 SE TODAS AS TENTATIVAS FALHARAM - TENTAR FILMES BACKUP
    console.log(`❌ All ${attemptCount} attempts failed for ${movieTitle}`);
    
    // 🎯 SISTEMA DE FALLBACK PARA OUTROS TÍTULOS (apenas se useBackup=true e não for regeneração manual)
    if (useBackup && !isManualRegeneration && generationId) {
      console.log(`🔄 Trying backup movies since ${movieTitle} failed...`);
      
      // Buscar títulos já gerados com sucesso nesta geração
      let existingTitles: string[] = [];
      try {
        const { data: currentGeneration, error: checkError } = await supabase.from('cover_generations').select('generated_covers').eq('id', generationId).single();
        if (!checkError && currentGeneration && currentGeneration.generated_covers) {
          existingTitles = currentGeneration.generated_covers
            .filter((cover: any) => cover.image_url && cover.success) // Apenas capas com sucesso
            .map((cover: any) => cover.movie_title);
        }
      } catch (err) {
        console.log(`⚠️ Could not fetch existing titles, proceeding with backup attempt...`);
      }
      
      // Encontrar filmes backup disponíveis (mesma plataforma, não usado ainda)
      const excludeList = [...(moviesToExclude || []), movieTitle, ...existingTitles];
      const backupMovies = movies
        .filter((m: any) => !excludeList.includes(m.title))
        .slice(0, 3); // Tentar até 3 backups
      
      console.log(`🎯 Available backup movies: ${backupMovies.map((m: any) => m.title).join(', ')}`);
      console.log(`🚫 Excluding: ${excludeList.join(', ')}`);
      
      // Tentar cada filme backup com a MESMA LÓGICA COMPLETA do título original
      for (const backupMovie of backupMovies) {
        console.log(`🔄 Trying backup movie: ${backupMovie.title}`);
        
        // 🎯 DEFINIR SEQUÊNCIA DE TENTATIVAS PARA BACKUP (MESMA LÓGICA DO TÍTULO ORIGINAL)
        const backupAttempts = [];

        if (photoType === 'cover') {
          // COVER/BANNER: 2 tentativas iniciais + tentativas específicas por gênero + 3 tentativas de fallback
          backupAttempts.push(
            { prompt: backupMovie.base_prompt, withTitle: true, withPositioning: true, description: `BACKUP ${backupMovie.title}: base_prompt + criatividade + instrução do título + posicionamento` },
            { prompt: backupMovie.base_prompt, withTitle: false, withPositioning: true, description: `BACKUP ${backupMovie.title}: base_prompt + criatividade + posicionamento (sem título)` }
          );
          
          // Adicionar tentativas específicas por gênero
          if (gender === 'male' && backupMovie.gender_male_prompt) {
            backupAttempts.push(
              { prompt: backupMovie.gender_male_prompt, withTitle: true, withPositioning: true, description: `BACKUP ${backupMovie.title}: gender_male_prompt + criatividade + instrução do título + posicionamento` },
              { prompt: backupMovie.gender_male_prompt, withTitle: false, withPositioning: true, description: `BACKUP ${backupMovie.title}: gender_male_prompt + criatividade + posicionamento (sem título)` }
            );
          } else if (gender === 'female' && backupMovie.gender_female_prompt) {
            backupAttempts.push(
              { prompt: backupMovie.gender_female_prompt, withTitle: true, withPositioning: true, description: `BACKUP ${backupMovie.title}: gender_female_prompt + criatividade + instrução do título + posicionamento` },
              { prompt: backupMovie.gender_female_prompt, withTitle: false, withPositioning: true, description: `BACKUP ${backupMovie.title}: gender_female_prompt + criatividade + posicionamento (sem título)` }
            );
          } else if (gender === 'casal' && backupMovie.couple_prompt) {
            backupAttempts.push(
              { prompt: backupMovie.couple_prompt, withTitle: true, withPositioning: true, description: `BACKUP ${backupMovie.title}: couple_prompt + criatividade + instrução do título + posicionamento` },
              { prompt: backupMovie.couple_prompt, withTitle: false, withPositioning: true, description: `BACKUP ${backupMovie.title}: couple_prompt + criatividade + posicionamento (sem título)` }
            );
          }
          
          // Tentativas finais de fallback
          backupAttempts.push(
            { prompt: `${backupMovie.title}`, withTitle: false, withPositioning: true, description: `BACKUP ${backupMovie.title}: apenas o título + posicionamento (sem criatividade)`, skipCreativity: true },
            { prompt: backupMovie.safe_prompt, withTitle: true, withPositioning: true, description: `BACKUP ${backupMovie.title}: safe_prompt + criatividade + instrução do título + posicionamento` },
            { prompt: backupMovie.safe_prompt, withTitle: false, withPositioning: true, description: `BACKUP ${backupMovie.title}: safe_prompt + criatividade + posicionamento (sem título)` }
          );
        } else if (photoType === 'individual' && gender === 'male') {
          // HOMEM INDIVIDUAL: 8 tentativas
          backupAttempts.push(
            { prompt: backupMovie.base_prompt, withTitle: true, withPositioning: false, description: `BACKUP ${backupMovie.title}: base_prompt + criatividade + instrução do título` },
            { prompt: backupMovie.base_prompt, withTitle: false, withPositioning: false, description: `BACKUP ${backupMovie.title}: base_prompt + criatividade` },
            { prompt: backupMovie.gender_male_prompt, withTitle: true, withPositioning: false, description: `BACKUP ${backupMovie.title}: gender_male_prompt + criatividade + instrução do título` },
            { prompt: backupMovie.gender_male_prompt, withTitle: false, withPositioning: false, description: `BACKUP ${backupMovie.title}: gender_male_prompt + criatividade` },
            { prompt: `Inspired in a series ${backupMovie.title} make a poster film about it`, withTitle: true, withPositioning: false, description: `BACKUP ${backupMovie.title}: generic prompt + criatividade + instrução do título` },
            { prompt: `Inspired in a series ${backupMovie.title} make a poster film about it`, withTitle: false, withPositioning: false, description: `BACKUP ${backupMovie.title}: generic prompt + criatividade` },
            { prompt: backupMovie.safe_prompt, withTitle: true, withPositioning: false, description: `BACKUP ${backupMovie.title}: safe_prompt + criatividade + instrução do título` },
            { prompt: backupMovie.safe_prompt, withTitle: false, withPositioning: false, description: `BACKUP ${backupMovie.title}: safe_prompt + criatividade` }
          );
        } else if (photoType === 'individual' && gender === 'female') {
          // MULHER INDIVIDUAL: 8 tentativas
          backupAttempts.push(
            { prompt: backupMovie.base_prompt, withTitle: true, withPositioning: false, description: `BACKUP ${backupMovie.title}: base_prompt + criatividade + instrução do título` },
            { prompt: backupMovie.base_prompt, withTitle: false, withPositioning: false, description: `BACKUP ${backupMovie.title}: base_prompt + criatividade` },
            { prompt: backupMovie.gender_female_prompt, withTitle: true, withPositioning: false, description: `BACKUP ${backupMovie.title}: gender_female_prompt + criatividade + instrução do título` },
            { prompt: backupMovie.gender_female_prompt, withTitle: false, withPositioning: false, description: `BACKUP ${backupMovie.title}: gender_female_prompt + criatividade` },
            { prompt: `Inspired in a series ${backupMovie.title} make a poster film about it`, withTitle: true, withPositioning: false, description: `BACKUP ${backupMovie.title}: generic prompt + criatividade + instrução do título` },
            { prompt: `Inspired in a series ${backupMovie.title} make a poster film about it`, withTitle: false, withPositioning: false, description: `BACKUP ${backupMovie.title}: generic prompt + criatividade` },
            { prompt: backupMovie.safe_prompt, withTitle: true, withPositioning: false, description: `BACKUP ${backupMovie.title}: safe_prompt + criatividade + instrução do título` },
            { prompt: backupMovie.safe_prompt, withTitle: false, withPositioning: false, description: `BACKUP ${backupMovie.title}: safe_prompt + criatividade` }
          );
        } else if (photoType === 'casal') {
          // CASAL: 8 tentativas
          backupAttempts.push(
            { prompt: backupMovie.base_prompt, withTitle: true, withPositioning: false, description: `BACKUP ${backupMovie.title}: base_prompt + criatividade + instrução do título` },
            { prompt: backupMovie.base_prompt, withTitle: false, withPositioning: false, description: `BACKUP ${backupMovie.title}: base_prompt + criatividade` },
            { prompt: backupMovie.couple_prompt, withTitle: true, withPositioning: false, description: `BACKUP ${backupMovie.title}: couple_prompt + criatividade + instrução do título` },
            { prompt: backupMovie.couple_prompt, withTitle: false, withPositioning: false, description: `BACKUP ${backupMovie.title}: couple_prompt + criatividade` },
            { prompt: `Inspired in a series ${backupMovie.title} make a poster film about it`, withTitle: true, withPositioning: false, description: `BACKUP ${backupMovie.title}: generic prompt + criatividade + instrução do título` },
            { prompt: `Inspired in a series ${backupMovie.title} make a poster film about it`, withTitle: false, withPositioning: false, description: `BACKUP ${backupMovie.title}: generic prompt + criatividade` },
            { prompt: backupMovie.safe_prompt, withTitle: true, withPositioning: false, description: `BACKUP ${backupMovie.title}: safe_prompt + criatividade + instrução do título` },
            { prompt: backupMovie.safe_prompt, withTitle: false, withPositioning: false, description: `BACKUP ${backupMovie.title}: safe_prompt + criatividade` }
          );
        } else {
          // FALLBACK PADRÃO: individual sem gênero definido
          backupAttempts.push(
            { prompt: backupMovie.base_prompt, withTitle: true, withPositioning: false, description: `BACKUP ${backupMovie.title}: base_prompt + criatividade + instrução do título` },
            { prompt: backupMovie.base_prompt, withTitle: false, withPositioning: false, description: `BACKUP ${backupMovie.title}: base_prompt + criatividade` },
            { prompt: `Inspired in a series ${backupMovie.title} make a poster film about it`, withTitle: true, withPositioning: false, description: `BACKUP ${backupMovie.title}: generic prompt + criatividade + instrução do título` },
            { prompt: `Inspired in a series ${backupMovie.title} make a poster film about it`, withTitle: false, withPositioning: false, description: `BACKUP ${backupMovie.title}: generic prompt + criatividade` },
            { prompt: backupMovie.safe_prompt, withTitle: true, withPositioning: false, description: `BACKUP ${backupMovie.title}: safe_prompt + criatividade + instrução do título` },
            { prompt: backupMovie.safe_prompt, withTitle: false, withPositioning: false, description: `BACKUP ${backupMovie.title}: safe_prompt + criatividade` }
          );
        }

        // Filtrar tentativas com prompts válidos
        const validBackupAttempts = backupAttempts.filter(attempt => attempt.prompt && attempt.prompt.trim().length > 0);
        console.log(`🎯 Prepared ${validBackupAttempts.length} backup attempts for ${backupMovie.title} (${photoType} ${gender || 'unspecified'})`);
        
        // 🔄 EXECUTAR TENTATIVAS DO BACKUP SEQUENCIALMENTE - PARAR NO PRIMEIRO SUCESSO
        for (const backupAttempt of validBackupAttempts) {
          attemptCount++;
          console.log(`🚀 Backup Attempt ${attemptCount}: ${backupAttempt.description}`);
          
          // Preparar prompt customizado para esta tentativa
          let customPromptForAttempt = backupAttempt.prompt;
          
          // Adicionar instruções de posicionamento para cover/banner
          if (backupAttempt.withPositioning) {
            customPromptForAttempt += ` Position the person on the right side of the image. Wide cinematic shot with balanced composition. Make sure the person is not centered but positioned to the right third of the image. Place the title '${backupMovie.title}' in the bottom right corner of the image with an elegant, readable gold font.`;
          }

          const backupResult = await generateSingleCoverAttempt(
            supabase,
            { ...backupMovie, base_prompt: customPromptForAttempt, title: backupMovie.title }, // ✅ USAR TÍTULO DO BACKUP
            originalImageUrl,
            streamingPlatform,
            photoType,
            aspectRatio,
            finalCreativityLevelId,
            undefined,
            gender,
            language,
            backupAttempt.withTitle,
            backupAttempt.skipCreativity || false
          );
          
          if (backupResult.success) {
            console.log(`✅ BACKUP SUCCESS with ${backupMovie.title}!`);
            
            // Atualizar resultado com filme backup
            result.success = true;
            result.image_url = backupResult.image_url || '';
            result.movie_title = backupMovie.title; // 🔥 USAR TÍTULO DO FILME BACKUP
            result.fallback_used = true;
            result.fallback_movie = backupMovie.title;
            result.promptInfo!.attempts = attemptCount;
            result.promptInfo!.fallbackUsed = true;
            result.promptInfo!.promptUsed = backupResult.promptUsed || '';
            
            // Salvar no banco usando o título do backup
            if (!testMode && generationId) {
              // 🔥 LÓGICA BASEADA NO PHOTO_TYPE (independente se é geração ou regeneração)
              if (photoType !== 'cover') {
                // Filmes e séries → cover_generations → "Series & Films" tab
                await saveToSeriesAndFilms(
                  supabase,
                  userId || '',
                  generationId || '',
                  result.image_url || '',
                  backupMovie.title, // 🔥 USAR TÍTULO DO BACKUP
                  streamingPlatform || '',
                  photoType || '',
                  aspectRatio || '',
                  position || 0,
                  originalImageUrl || '',
                  userName || '',
                  gender || '',
                  language || 'portuguese',
                  !!generateTitles,
                  backupResult.creativityLevel?.name || 'rostoFiel'
                );
              } else {
                // Capa principal → cover_images → "Cover Images" tab
                await saveCoverToImages(
                  supabase,
                  userId || '',
                  result.image_url || '',
                  backupMovie.title, // 🔥 USAR TÍTULO DO BACKUP
                  streamingPlatform || '',
                  photoType || '',
                  aspectRatio || '',
                  generationId || ''
                );
              }
            }
            
            // Consumir créditos
            if (!testMode) {
              console.log(`💳 Consuming ${creditsNeeded} credits for backup ${backupMovie.title}...`);
              const { error: creditError } = await supabase.from('credit_usage').insert({
                user_id: userId || '',
                credits_used: creditsNeeded,
                description: `${backupMovie.title} - ${streamingPlatform?.toUpperCase() || 'UNKNOWN'} (Posição ${position || 0}) - BACKUP for ${movieTitle}`,
                generation_id: generationId || null
              });
              
              if (creditError) {
                console.error('❌ Error consuming credits for backup:', creditError);
              } else {
                console.log(`✅ Successfully consumed ${creditsNeeded} credits for backup ${backupMovie.title}`);
              }
            }
            
            // Atualizar estatísticas (sucesso do backup)
            if (!testMode) {
              try {
                await supabase.rpc('update_movie_stats', {
                  p_movie_title: backupMovie.title,
                  p_streaming_platform: streamingPlatform || 'unknown',
                  p_success: true,
                  p_error_message: null
                });
              } catch (statsError) {
                console.error('Error updating backup movie stats:', statsError);
              }
            }
            
            // 🎯 RETORNAR SUCESSO COM FILME BACKUP
            return new Response(JSON.stringify(result), {
              headers: { ...corsHeaders, 'Content-Type': 'application/json' }
            });
          }
          
          console.log(`❌ Backup attempt ${attemptCount} failed: ${backupResult.error}`);
        }
        
        console.log(`❌ All backup attempts failed for ${backupMovie.title}`);
      }
      
      console.log(`❌ All backup movies failed. Original error: ${lastError}`);
    }
    
    // 🔥 SE TODOS OS BACKUPS TAMBÉM FALHARAM
    result.promptInfo!.attempts = attemptCount;
    result.error = `All ${attemptCount} attempts failed (including ${useBackup && !isManualRegeneration ? 'backup movies' : 'fallback prompts'}). Last error: ${lastError}`;
    
    // Atualizar estatísticas do filme original (falha)
    if (!testMode) {
      try {
        await supabase.rpc('update_movie_stats', {
          p_movie_title: movieTitle, // ✅ USAR TÍTULO ORIGINAL
          p_streaming_platform: streamingPlatform,
          p_success: false,
          p_error_message: lastError
        });
      } catch (statsError) {
        console.error('Error updating movie stats:', statsError);
      }
    }

    return new Response(JSON.stringify(result), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('❌ Error in generate-single-cover:', error);
    return new Response(
      JSON.stringify({ 
        error: (error as Error).message || 'Unknown error',
        movie_title: 'Unknown',
        success: false,
        generated_at: new Date().toISOString()
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );
  }
});