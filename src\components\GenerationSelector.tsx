import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { X, Calendar, Film, Loader2, CheckCircle } from 'lucide-react'
import { supabase } from '../lib/supabase'
import { toast as showToast } from 'sonner'
import { formatDateTime } from '../utils/dateUtils'

interface Generation {
  id: string
  created_at: string
  streaming_platform: string
  photo_type: string
  user_name: string
  generated_covers: any[]
  language?: string
  generate_titles?: boolean
  gender?: string
  creativity_level?: string
}

interface GenerationSelectorProps {
  isOpen: boolean
  onClose: () => void
  onSelectGeneration: (generation: Generation) => void
}

export default function GenerationSelector({ isOpen, onClose, onSelectGeneration }: GenerationSelectorProps) {
  const [generations, setGenerations] = useState<Generation[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [selectedGeneration, setSelectedGeneration] = useState<Generation | null>(null)

  useEffect(() => {
    if (isOpen) {
      loadGenerations()
    }
  }, [isOpen])

  const loadGenerations = async () => {
    try {
      setIsLoading(true)
      const { data: { session } } = await supabase.auth.getSession()
      
      if (!session) {
        showToast.error('Você precisa estar logado')
        return
      }

      const { data: generations, error } = await supabase
        .from('cover_generations')
        .select('*')
        .eq('user_id', session.user.id)
        .order('created_at', { ascending: false })
        .limit(10)

      if (error) {
        console.error('Erro ao carregar gerações:', error)
        showToast.error('Erro ao carregar suas gerações')
        return
      }

      setGenerations(generations || [])
    } catch (error) {
      console.error('Erro ao carregar gerações:', error)
      showToast.error('Erro ao carregar suas gerações')
    } finally {
      setIsLoading(false)
    }
  }

  // 🔥 Usar função utilitária para formatação consistente com hora
  const formatDate = formatDateTime

  const getPlatformBrand = (platform: string) => {
    const brands = {
      netflix: { name: 'Netflix', color: 'bg-red-600' },
      disney: { name: 'Disney+', color: 'bg-blue-600' },
      amazon: { name: 'Prime Video', color: 'bg-yellow-600' }
    }
    return brands[platform as keyof typeof brands] || { name: platform, color: 'bg-gray-600' }
  }

  const handleSelectGeneration = () => {
    if (selectedGeneration) {
      onSelectGeneration(selectedGeneration)
      onClose()
    }
  }

  if (!isOpen) return null

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
        onClick={onClose}
      >
        <motion.div
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.9, opacity: 0 }}
          className="bg-brand-white rounded-2xl border-2 border-brand-black shadow-brutal-lg max-w-4xl w-full max-h-[80vh] overflow-hidden"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="bg-brand-primary border-b-2 border-brand-black p-6">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-2xl font-black text-brand-black">Carregar Geração</h2>
                <p className="text-brand-black/80">Selecione uma geração anterior para continuar de onde parou</p>
              </div>
              <button
                onClick={onClose}
                className="p-2 bg-brand-white rounded-lg border-2 border-brand-black shadow-brutal-sm hover:shadow-brutal-hover transition-all duration-200"
              >
                <X size={20} className="text-brand-black" />
              </button>
            </div>
          </div>

          {/* Content */}
          <div className="p-6 overflow-y-auto max-h-[60vh]">
            {isLoading ? (
              <div className="flex items-center justify-center py-12">
                <Loader2 className="w-8 h-8 animate-spin text-brand-primary" />
                <span className="ml-2 text-brand-text">Carregando suas gerações...</span>
              </div>
            ) : generations.length === 0 ? (
              <div className="text-center py-12">
                <Film className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-xl font-bold text-brand-text mb-2">Nenhuma geração encontrada</h3>
                <p className="text-brand-text/70">Você ainda não criou nenhuma geração de capas.</p>
              </div>
            ) : (
              <div className="space-y-4">
                {generations.map((generation) => {
                  const platform = getPlatformBrand(generation.streaming_platform)
                  const coversCount = generation.generated_covers ? generation.generated_covers.length : 0
                  const isSelected = selectedGeneration?.id === generation.id

                  return (
                    <motion.div
                      key={generation.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      className={`
                        p-4 rounded-lg border-2 border-brand-black shadow-brutal-sm cursor-pointer transition-all duration-200
                        ${isSelected 
                          ? 'bg-brand-secondary shadow-brutal-pressed' 
                          : 'bg-brand-white hover:shadow-brutal-hover'
                        }
                      `}
                      onClick={() => setSelectedGeneration(generation)}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-3 mb-2">
                            <span className={`px-3 py-1 rounded-full text-white text-sm font-bold ${platform.color}`}>
                              {platform.name}
                            </span>
                            <span className="text-sm font-semibold text-brand-text capitalize">
                              {generation.photo_type === 'individual' ? 'Individual' : 'Casal'}
                            </span>
                            {generation.user_name && (
                              <span className="text-sm text-brand-text/70">
                                Nome: {generation.user_name}
                              </span>
                            )}
                          </div>
                          <div className="flex items-center space-x-4 text-sm text-brand-text/70">
                            <div className="flex items-center space-x-1">
                              <Calendar className="w-4 h-4" />
                              <span>{formatDate(generation.created_at)}</span>
                            </div>
                            <div className="flex items-center space-x-1">
                              <Film className="w-4 h-4" />
                              <span>{coversCount} covers gerados</span>
                            </div>
                            {generation.language && (
                              <span>Idioma: {generation.language === 'portuguese' ? 'Português' : 'Inglês'}</span>
                            )}
                          </div>
                          {generation.creativity_level && (
                            <div className="mt-2">
                              <span className="text-xs bg-gray-100 px-2 py-1 rounded">
                                Criatividade: {generation.creativity_level}
                              </span>
                            </div>
                          )}
                        </div>
                        <div className="flex items-center">
                          {isSelected && (
                            <CheckCircle className="w-6 h-6 text-brand-primary" />
                          )}
                        </div>
                      </div>
                    </motion.div>
                  )
                })}
              </div>
            )}
          </div>

          {/* Footer */}
          {!isLoading && generations.length > 0 && (
            <div className="border-t-2 border-brand-black p-6 bg-gray-50">
              <div className="flex items-center justify-between">
                <div className="text-sm text-brand-text/70">
                  {selectedGeneration 
                    ? `Selecionada: ${getPlatformBrand(selectedGeneration.streaming_platform).name} - ${formatDate(selectedGeneration.created_at)}`
                    : 'Selecione uma geração para continuar'
                  }
                </div>
                <div className="flex space-x-3">
                  <button
                    onClick={onClose}
                    className="px-4 py-2 bg-gray-300 text-brand-black rounded-lg border-2 border-brand-black shadow-brutal-sm hover:shadow-brutal-hover transition-all font-bold"
                  >
                    Cancelar
                  </button>
                  <button
                    onClick={handleSelectGeneration}
                    disabled={!selectedGeneration}
                    className={`
                      px-6 py-2 rounded-lg border-2 border-brand-black shadow-brutal-sm hover:shadow-brutal-hover transition-all font-bold
                      ${selectedGeneration 
                        ? 'bg-brand-primary text-brand-black' 
                        : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                      }
                    `}
                  >
                    Carregar Geração
                  </button>
                </div>
              </div>
            </div>
          )}
        </motion.div>
      </motion.div>
    </AnimatePresence>
  )
} 