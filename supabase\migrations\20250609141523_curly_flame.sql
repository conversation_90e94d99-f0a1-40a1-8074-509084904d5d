/*
  # Fix RLS policies for cover_generations table

  1. Security Updates
    - Update RLS policies to allow anonymous users to insert records
    - Ensure proper access control for cover generation functionality
    - Allow public access for cover generation while maintaining data security

  2. Changes
    - Modify INSERT policy to allow anonymous users
    - Keep existing SELECT and UPDATE policies for authenticated users
    - Add policy for anonymous SELECT access to their own generations
*/

-- Drop existing policies
DROP POLICY IF EXISTS "cover_generations_insert_policy" ON cover_generations;
DROP POLICY IF EXISTS "cover_generations_select_policy" ON cover_generations;
DROP POLICY IF EXISTS "cover_generations_update_policy" ON cover_generations;

-- Create new policies that allow anonymous access
CREATE POLICY "cover_generations_insert_policy"
  ON cover_generations
  FOR INSERT
  TO public
  WITH CHECK (true);

CREATE POLICY "cover_generations_select_policy"
  ON cover_generations
  FOR SELECT
  TO public
  USING (
    CASE 
      WHEN auth.uid() IS NOT NULL THEN user_id = auth.uid()
      ELSE user_id IS NULL
    END
  );

CREATE POLICY "cover_generations_update_policy"
  ON cover_generations
  FOR UPDATE
  TO public
  USING (
    CASE 
      WHEN auth.uid() IS NOT NULL THEN user_id = auth.uid()
      ELSE user_id IS NULL
    END
  );