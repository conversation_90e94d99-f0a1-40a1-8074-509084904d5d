# 🚀 Configuração de Produção - Stripe

## Variáveis de Ambiente para Produção

### 1. Stripe Configuration (OBRIGATÓRIO ALTERAR)

```env
# Stripe Live Keys (substitua pelas chaves reais de produção)
VITE_STRIPE_PUBLISHABLE_KEY=pk_live_SEU_LIVE_KEY_AQUI

# Stripe Credit Products - PRODUCTION IDs (IDs reais que você criou)
VITE_STRIPE_STARTER_CREDITS_ID=prod_SadnLW125oNEVc
VITE_STRIPE_POPULAR_CREDITS_ID=prod_SadnvZanedY48s
VITE_STRIPE_MASTER_CREDITS_ID=prod_Sadn6aBJvTKEz

# Legacy Pack Products (se ainda usar)
VITE_STRIPE_ONE_PACK_PRODUCT_ID=prod_NOVO_LIVE_ONE_PACK_ID
VITE_STRIPE_COMPLETE_PACK_PRODUCT_ID=prod_NOVO_LIVE_COMPLETE_PACK_ID
VITE_STRIPE_MASTER_PACK_PRODUCT_ID=prod_NOVO_LIVE_MASTER_PACK_ID
```

### 2. Supabase Environment Variables (Configurar no Dashboard)

```env
# Stripe Secret Keys (CONFIGURAR NO SUPABASE DASHBOARD)
STRIPE_SECRET_KEY=sk_live_SEU_SECRET_KEY_AQUI
STRIPE_WEBHOOK_SECRET=whsec_SEU_WEBHOOK_SECRET_AQUI

# Email Service
RESEND_API_KEY=re_SUA_RESEND_KEY_AQUI

# Supabase URLs
SUPABASE_URL=https://SEU-PROJETO-PROD.supabase.co
SUPABASE_SERVICE_ROLE_KEY=SUA_SERVICE_ROLE_KEY
```

### 3. Frontend Environment Variables

```env
# Supabase Production
VITE_SUPABASE_URL=https://SEU-PROJETO-PROD.supabase.co
VITE_SUPABASE_ANON_KEY=SUA_ANON_KEY_DE_PRODUCAO
```

## 📋 Checklist para Produção

### ✅ Stripe Setup
- [ ] Ativar modo Live no Stripe
- [ ] ✅ **Produtos já criados** (você já tem os IDs):
  - [ ] ✅ Starter Credits (30 créditos - R$ 49,90) - `prod_SadnLW125oNEVc`
  - [ ] ✅ Popular Credits (100 créditos - R$ 99,90) - `prod_SadnvZanedY48s`
  - [ ] ✅ Master Credits (300 créditos - R$ 249,90) - `prod_Sadn6aBJvTKEz`
- [ ] Configurar webhook de produção
- [ ] Obter chave pública live (pk_live_...)
- [ ] Obter chave secreta live (sk_live_...)
- [ ] Obter webhook secret (whsec_...)

### ✅ Supabase Setup  
- [ ] Configurar variáveis de ambiente no Supabase:
  - [ ] STRIPE_SECRET_KEY (live key)
  - [ ] STRIPE_WEBHOOK_SECRET (webhook secret)
  - [ ] RESEND_API_KEY (para emails)
- [ ] Fazer deploy das Edge Functions
- [ ] Aplicar migrações do banco

### ✅ Webhook Update (IMPORTANTE!)
- [ ] Atualizar stripe-webhook para usar sistema de créditos
- [ ] Testar webhook com pagamentos reais
- [ ] Monitorar logs do webhook

### ✅ Deployment
- [ ] Atualizar variáveis de ambiente no servidor
- [ ] Testar pagamentos com cartão real
- [ ] Verificar webhooks funcionando
- [ ] Monitorar logs de erro

## 🔧 Comandos para Deploy

```bash
# 1. Deploy Edge Functions
npx supabase functions deploy check-credit-balance
npx supabase functions deploy consume-credits  
npx supabase functions deploy create-payment-intent
npx supabase functions deploy stripe-webhook
npx supabase functions deploy send-contact-email

# 2. Aplicar migrações
npx supabase db push

# 3. Build para produção
npm run build
```

## ⚠️ IMPORTANTE - Webhook Needs Update

O webhook atual ainda usa o sistema antigo de packs. Para produção, você precisará:

1. **Atualizar o stripe-webhook** para processar créditos em vez de packs
2. **Inserir na tabela `credit_purchases`** em vez de `user_packs`
3. **Usar os novos metadados** do sistema de créditos

## 🎯 URLs de Webhook para Produção

```
Webhook URL: https://SEU-DOMINIO.supabase.co/functions/v1/stripe-webhook
Events: payment_intent.succeeded, payment_intent.payment_failed
```

## 💳 Diferenças Test vs Live

| Aspecto | Test Mode (Atual) | Live Mode (Produção) |
|---------|-------------------|----------------------|
| Chave Pública | pk_test_... | pk_live_... |
| Chave Secreta | sk_test_... | sk_live_... |
| **Starter Credits** | `prod_SVeFgga19mY0Gl` | `prod_SadnLW125oNEVc` |
| **Popular Credits** | `prod_SVeHwRl5tn4cP6` | `prod_SadnvZanedY48s` |
| **Master Credits** | `prod_SVeIPaf5R93PYg` | `prod_Sadn6aBJvTKEz` |
| Webhook Secret | whsec_test_... | whsec_live_... |
| Pagamentos | Simulados | Reais |
| Cartões | 4242 4242 4242 4242 | Cartões reais |

## 🔄 Migração Test → Live

1. **✅ Produtos já criados** (você já tem os IDs de produção)
2. **Configurar variáveis de ambiente** com os IDs de produção
3. **Atualizar chaves** do Stripe (test → live)
4. **Redeployar** todas as functions
5. **Testar** com pagamento real pequeno

## 🎯 **Configuração Atual vs Produção**

### Desenvolvimento (Atual):
```env
# Usa IDs de teste como fallback
VITE_STRIPE_STARTER_CREDITS_ID=prod_SVeFgga19mY0Gl
```

### Produção (Configurar):
```env
# Usar seus IDs de produção
VITE_STRIPE_STARTER_CREDITS_ID=prod_SadnLW125oNEVc
VITE_STRIPE_POPULAR_CREDITS_ID=prod_SadnvZanedY48s
VITE_STRIPE_MASTER_CREDITS_ID=prod_Sadn6aBJvTKEz
``` 