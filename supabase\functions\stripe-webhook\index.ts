import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import Stripe from "https://esm.sh/stripe?target=deno&deno-std=0.132.0";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type, stripe-signature',
}

const stripe = new Stripe(Deno.env.get('STRIPE_SECRET_KEY')!, {
  httpClient: Stripe.createFetchHttpClient(),
  apiVersion: '2024-06-20',
});

serve(async (req) => {
  console.log('🔥 Webhook received:', req.method, req.url)
  
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    console.log('✅ CORS preflight request')
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    console.log('📥 Processing webhook request...')
    const signature = req.headers.get('stripe-signature')
    const body = await req.text()

    console.log('🔍 Signature present:', !!signature)
    console.log('📝 Body length:', body.length)

    if (!signature) {
      console.error('❌ Missing stripe-signature header')
      return new Response('Missing stripe-signature header', { status: 400 })
    }

    const webhookSecret = Deno.env.get('STRIPE_WEBHOOK_SECRET')
    console.log('🔐 Webhook secret present:', !!webhookSecret)
    
    if (!webhookSecret) {
      console.error('❌ Missing STRIPE_WEBHOOK_SECRET environment variable')
      return new Response('Server configuration error', { status: 500 })
    }
    
    // Parse the event
    let event: Stripe.Event
    try {
      console.log('🔄 Constructing event...')
      event = await stripe.webhooks.constructEventAsync(
        body,
        signature,
        webhookSecret,
        undefined,
        Stripe.createSubtleCryptoProvider()
      );
      console.log('✅ Event constructed successfully:', event.type)
    } catch (err) {
      console.error(`❌ Webhook signature verification failed: ${err.message}`)
      return new Response(`Webhook Error: ${err.message}`, { status: 400 })
    }

    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')
    
    console.log('🔗 Supabase URL present:', !!supabaseUrl)
    console.log('🔑 Service key present:', !!supabaseServiceKey)
    
    if (!supabaseUrl || !supabaseServiceKey) {
      console.error('❌ Missing Supabase configuration')
      return new Response('Server configuration error', { status: 500 })
    }
    
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Handle the event
    switch (event.type) {
      case 'payment_intent.succeeded':
        console.log('💰 Processing payment_intent.succeeded')
        const paymentIntent = event.data.object
        const userId = paymentIntent.metadata.userId
        const planId = paymentIntent.metadata.planId
        const planType = paymentIntent.metadata.planType
        const productId = paymentIntent.metadata.productId
        const planName = paymentIntent.metadata.planName

        console.log(`✅ Payment succeeded for user ${userId}:`, {
          planId,
          planName,
          productId,
          planType,
          amount: paymentIntent.amount,
          paymentIntentId: paymentIntent.id
        })

        // 🔥 Only process credit payments
        if (userId && planId && planType === 'credits') {
          const credits = parseInt(paymentIntent.metadata.credits) || 0
          
          console.log(`📊 Processing credit purchase for payment intent: ${paymentIntent.id}`)
          
          // Update credit_purchases table to completed
          const { error: updateError } = await supabase
            .from('credit_purchases')
            .update({ 
              status: 'completed',
              completed_at: new Date().toISOString()
            })
            .eq('stripe_payment_intent_id', paymentIntent.id)

          if (updateError) {
            console.error('❌ Error updating credit purchase status:', updateError)
          } else {
            console.log(`✅ Credit purchase marked as completed for payment intent: ${paymentIntent.id}`)
            
            // 🔥 Agora adicionar os créditos ao usuário
            console.log(`💳 Adding ${credits} credits to user ${userId}`)
            
            // Primeiro, garantir que o usuário tem um registro na tabela user_credits
            const { data: existingCredits, error: checkError } = await supabase
              .from('user_credits')
              .select('*')
              .eq('user_id', userId)
              .single()

            if (checkError && checkError.code === 'PGRST116') {
              // Usuário não tem registro, vamos criar um
              console.log('🔥 Creating user_credits record for user:', userId)
              const { error: insertError } = await supabase
                .from('user_credits')
                .insert({
                  user_id: userId,
                  total_credits: credits,
                  available_credits: credits,
                  used_credits: 0
                })

              if (insertError) {
                console.error('❌ Error creating user_credits record:', insertError)
              } else {
                console.log(`✅ Created user_credits record with ${credits} credits`)
              }
            } else if (!checkError) {
              // Usuário já tem registro, vamos atualizar
              console.log('🔄 Updating existing user_credits record')
              const { error: updateCreditsError } = await supabase
                .from('user_credits')
                .update({
                  total_credits: (existingCredits.total_credits || 0) + credits,
                  available_credits: (existingCredits.available_credits || 0) + credits,
                  updated_at: new Date().toISOString()
                })
                .eq('user_id', userId)

              if (updateCreditsError) {
                console.error('❌ Error updating user_credits:', updateCreditsError)
              } else {
                console.log(`✅ Added ${credits} credits to user ${userId}`)
              }
            }
            
            console.log(`✅ Successfully completed credit purchase for user ${userId}: ${credits} credits (plan: ${planId}, product: ${productId})`)
          }
        } else {
          console.warn('⚠️ Invalid payment metadata or unsupported plan type:', { userId, planId, planType })
        }
        break

      case 'payment_intent.payment_failed':
        console.log('❌ Processing payment_intent.payment_failed')
        const failedPayment = event.data.object
        const failedUserId = failedPayment.metadata.userId
        const failedPlanType = failedPayment.metadata.planType
        
        console.log(`❌ Payment failed for payment intent ${failedPayment.id}:`, {
          userId: failedUserId,
          planId: failedPayment.metadata.planId,
          productId: failedPayment.metadata.productId,
          planType: failedPlanType,
          amount: failedPayment.amount
        })
        
        // 🔥 Only update credit purchases
        if (failedPlanType === 'credits') {
          console.log(`📊 Updating failed credit purchase for payment intent: ${failedPayment.id}`)
          
          const { error: updateError } = await supabase
            .from('credit_purchases')
            .update({ status: 'failed' })
            .eq('stripe_payment_intent_id', failedPayment.id)

          if (updateError) {
            console.error('❌ Error updating failed credit purchase:', updateError)
          } else {
            console.log(`✅ Updated failed credit purchase for payment intent ${failedPayment.id}`)
          }
        } else {
          console.warn('⚠️ Unsupported plan type for failed payment:', failedPlanType)
        }
        break

      case 'payment_intent.canceled':
        console.log('🚫 Processing payment_intent.canceled')
        const canceledPayment = event.data.object
        const canceledUserId = canceledPayment.metadata.userId
        const canceledPlanType = canceledPayment.metadata.planType
        
        console.log(`🚫 Payment canceled for payment intent ${canceledPayment.id}:`, {
          userId: canceledUserId,
          planId: canceledPayment.metadata.planId,
          productId: canceledPayment.metadata.productId,
          planType: canceledPlanType,
          amount: canceledPayment.amount
        })
        
        // 🔥 Only update credit purchases
        if (canceledPlanType === 'credits') {
          console.log(`📊 Updating cancelled credit purchase for payment intent: ${canceledPayment.id}`)
          
          const { error: updateError } = await supabase
            .from('credit_purchases')
            .update({ 
              status: 'cancelled'
            })
            .eq('stripe_payment_intent_id', canceledPayment.id)

          if (updateError) {
            console.error('❌ Error updating cancelled credit purchase:', updateError)
          } else {
            console.log(`✅ Updated cancelled credit purchase for payment intent ${canceledPayment.id}`)
          }
        } else {
          console.warn('⚠️ Unsupported plan type for cancelled payment:', canceledPlanType)
        }
        break

      default:
        console.log(`ℹ️ Unhandled event type: ${event.type}`)
    }

    console.log('✅ Webhook processed successfully')
    return new Response(JSON.stringify({ received: true }), {
      status: 200,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })

  } catch (error) {
    console.error('💥 Webhook error:', error)
    return new Response(
      JSON.stringify({ error: 'Webhook handler failed' }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
}) 