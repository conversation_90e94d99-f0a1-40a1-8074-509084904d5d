import { useState, useCallback, useEffect } from 'react'
import { supabase } from '../lib/supabase'
import { PhotoType, StreamingPlatform, Language } from '../types'
import { showToast } from '../utils/toast'

interface GenerationProgress {
  total: number
  completed: number
  failed: number
  inProgress: number
}

interface GenerationParams {
  file: File
  photoType: PhotoType
  gender: 'male' | 'female' | null
  streamingPlatform: StreamingPlatform
  language: Language
  quantity: number
  generateTitles: boolean
  creativityLevel: 'criativo' | 'rostoFiel' | 'estrito'
  imageUrl: string
}

export function useCoverGeneration() {
  const [isGenerating, setIsGenerating] = useState(false)
  const [generatedCovers, setGeneratedCovers] = useState<string[]>([])
  const [failedIndices, setFailedIndices] = useState<Set<number>>(new Set())
  const [error, setError] = useState<string | null>(null)
  const [regeneratingIndex, setRegeneratingIndex] = useState<number | null>(null)
  const [regeneratingIndices, setRegeneratingIndices] = useState<Set<number>>(new Set())
  const [progress, setProgress] = useState<GenerationProgress>({ total: 0, completed: 0, failed: 0, inProgress: 0 })
  const [originalImageUrl, setOriginalImageUrl] = useState<string | null>(null)
  const [currentMovie, setCurrentMovie] = useState<string | null>(null)
  const [currentGenerationId, setCurrentGenerationId] = useState<string | null>(null)
  const [lastGenerationParams, setLastGenerationParams] = useState<GenerationParams | null>(null)
  const [hasExistingGenerations, setHasExistingGenerations] = useState(false)

  // 🔥 NOVA FUNÇÃO: Obter título do filme por índice do BANCO DE DADOS
  const getMovieTitleForIndex = async (index: number, platform: string): Promise<string | undefined> => {
    try {
      console.log(`🔍 Buscando título para index ${index} em ${platform} no banco de dados...`)

      // Buscar filmes da plataforma no banco de dados
      const { data: movies, error } = await supabase
        .from('unified_movies_series')
        .select('title')
        .eq('streaming_platform', platform)
        .eq('is_active', true)
        .order('created_at', { ascending: true }) // Ordem consistente

      if (error) {
        console.error('Erro ao buscar filmes do banco:', error)
        return undefined
      }

      if (movies && movies[index]) {
        const title = movies[index].title
        console.log(`🎯 Título encontrado no banco para index ${index} em ${platform}: ${title}`)
        return title
      }

      console.log(`❌ Título não encontrado no banco para index ${index} em ${platform}`)
      return undefined
    } catch (error) {
      console.error('Erro ao obter título do filme do banco:', error)
      return undefined
    }
  }

  // 🔥 NOVA FUNÇÃO: Verificar se existem gerações anteriores
  const checkExistingGenerations = async () => {
    try {
      const { data: { session } } = await supabase.auth.getSession()
      if (!session) return false

      const { data: generations, error } = await supabase
        .from('cover_generations')
        .select('id')
        .eq('user_id', session.user.id)
        .limit(1)

      if (!error && generations && generations.length > 0) {
        setHasExistingGenerations(true)
        return true
      } else {
        setHasExistingGenerations(false)
        return false
      }
    } catch (error) {
      console.error('Erro ao verificar gerações existentes:', error)
      setHasExistingGenerations(false)
      return false
    }
  }

  // 🔥 NOVA FUNÇÃO: Carregar geração específica por dados da geração
  const loadSpecificGeneration = async (generationData: any) => {
    try {
      console.log('🔄 Carregando geração específica completa:', generationData)

      // Buscar TODOS os dados desta geração específica
      const { data: generation, error } = await supabase
        .from('cover_generations')
        .select('*') // Buscar TODOS os campos
        .eq('id', generationData.id)
        .single()

      if (!error && generation) {
        console.log('📦 Dados completos da geração:', generation)

        // Extrair capas se existirem
        const covers = generation.generated_covers
          ? generation.generated_covers
            .filter((cover: any) => cover.image_url)
            .map((cover: any) => cover.image_url)
          : []

        if (covers.length > 0) {
          console.log(`🎉 Carregadas ${covers.length} capas da geração específica!`)
          setGeneratedCovers(covers)
          setProgress({
            total: covers.length,
            completed: covers.length,
            failed: 0,
            inProgress: 0
          })

          // Definir a imagem original e ID da geração
          if (generation.original_image_url) {
            setOriginalImageUrl(generation.original_image_url)
          }
          setCurrentGenerationId(String(generation.id))

          showToast.success(`📚 ${covers.length} capas carregadas da geração selecionada!`)
        }

        // Retornar TODOS os dados da geração para preencher o wizard
        return {
          covers,
          // Usar dados da query completa primeiro, fallback para dados passados
          photo_type: generation.photo_type || generationData.photo_type || 'individual',
          streaming_platform: generation.streaming_platform || generationData.streaming_platform || 'netflix',
          language: generation.language || generationData.language || 'portuguese',
          generate_titles: generation.generate_titles !== undefined ? generation.generate_titles : (generationData.generate_titles !== undefined ? generationData.generate_titles : true),
          user_name: generation.user_name || generationData.user_name || '',
          gender: generation.gender || generationData.gender || null,
          creativity_level: generation.creativity_level || generationData.creativity_level || 'rostoFiel',
          original_image_url: generation.original_image_url || generationData.original_image_url || null
        }
      } else {
        console.error('❌ Erro ao buscar geração:', error)
        showToast.error('Erro ao carregar dados da geração.')
        return null
      }
    } catch (error) {
      console.error('❌ Erro ao carregar geração específica:', error)
      showToast.error('Ocorreu um erro ao carregar a geração.')
      return null
    }
  }

  // 🔥 FUNÇÃO ATUALIZADA: Limpar geração (manter ou remover configurações)
  const clearGeneration = (keepSettings: boolean = false) => {
    console.log(`🧹 Limpando geração (keepSettings: ${keepSettings})`)

    // Limpar TODAS as capas e estado de geração
    setGeneratedCovers([])
    setFailedIndices(new Set())
    setError(null)
    setOriginalImageUrl(null)
    setCurrentGenerationId(null)
    setCurrentMovie(null)
    setProgress({ total: 0, completed: 0, failed: 0, inProgress: 0 })
    setRegeneratingIndex(null)
    setRegeneratingIndices(new Set())

    if (!keepSettings) {
      // Limpar TUDO se for "criar do zero"
      setLastGenerationParams(null)
      setHasExistingGenerations(false)
      console.log('🔥 Limpeza completa realizada - tudo resetado')
    } else {
      console.log('📋 Configurações mantidas para reutilização')
    }
  }

  // Polling do progresso em tempo real
  const pollProgress = useCallback(async (generationId: string) => {
    const maxAttempts = 120 // 10 minutos máximo (3s * 120)
    let attempts = 0

    const poll = async () => {
      try {
        const { data: { session } } = await supabase.auth.getSession()
        if (!session) return

        console.log(`🔄 Polling attempt ${attempts + 1} for generation ${generationId}`)

        // Buscar dados da geração
        const { data: generation, error } = await supabase
          .from('cover_generations')
          .select('progress, status, current_movie, generated_covers')
          .eq('id', generationId)
          .single()

        if (!error && generation) {
          const progressPercent = generation.progress || 0
          setCurrentMovie(generation.current_movie)

          console.log(`📊 Progress: ${progressPercent}%, Status: ${generation.status}, Current movie: ${generation.current_movie}`)

          // Atualizar progresso
          setProgress(prev => ({
            ...prev,
            completed: Math.round((progressPercent / 100) * prev.total),
            inProgress: generation.status === 'processing' ? 1 : 0
          }))

          // 🔥 BUSCAR CAPAS EM TEMPO REAL DA TABELA CORRETA (cover_generations)
          const { data: realTimeGeneration } = await supabase
            .from('cover_generations')
            .select('generated_covers')
            .eq('id', generationId)
            .single()

          if (realTimeGeneration && realTimeGeneration.generated_covers) {
            const coverUrls = realTimeGeneration.generated_covers
              .filter((cover: any) => cover.image_url)
              .map((cover: any) => cover.image_url)

            if (coverUrls.length > 0) {
              console.log(`🎬 REAL-TIME: Found ${coverUrls.length} covers in cover_generations`)
              console.log('📸 Cover URLs:', coverUrls)

              // 🔥 LOG ESPECÍFICO: Detectar nova imagem adicionada
              const currentCount = generatedCovers.length
              if (coverUrls.length > currentCount) {
                const newCovers = coverUrls.slice(currentCount)
                console.log(`🆕 NEW COVERS DETECTED: ${newCovers.length} new covers!`, newCovers)
              }

              // 🔥 FORÇA ATUALIZAÇÃO EM TEMPO REAL - cada imagem aparece assim que termina
              setGeneratedCovers([...coverUrls]) // Nova referência para forçar re-render

              // Atualizar progresso baseado nas capas realmente salvas
              setProgress(prev => ({
                ...prev,
                completed: coverUrls.length,
                inProgress: generation.status === 'processing' ? Math.max(0, prev.total - coverUrls.length) : 0
              }))
            } else {
              console.log(`❌ No covers found in cover_generations for generation ${generationId}`)
            }
          }

          // Verificar se tem capas geradas no JSON (fallback)
          if (generation.generated_covers && Array.isArray(generation.generated_covers)) {
            const successfulCovers = generation.generated_covers
              .filter((cover: any) => cover.success)
              .map((cover: any) => cover.image_url)

            // Só usar se não temos capas da tabela cover_images
            if (successfulCovers.length > 0 && (!realTimeGeneration || realTimeGeneration.generated_covers.length === 0)) {
              console.log(`📦 Using covers from generated_covers JSON: ${successfulCovers.length}`)
              setGeneratedCovers(successfulCovers)
            }
          }

          // Se completou ou falhou, parar polling
          if (generation.status === 'completed' || generation.status === 'failed') {
            console.log(`✅ Generation ${generation.status}! Final covers count: ${realTimeGeneration?.generated_covers?.length || 0}`)

            // BUSCA FINAL das capas quando completa
            if (generation.status === 'completed') {
              console.log('🔄 Fetching final covers from cover_generations...')
              const { data: finalCovers, error: finalError } = await supabase
                .from('cover_generations')
                .select('generated_covers')
                .eq('user_id', session.user.id)
                .eq('id', generationId)
                .single()

              if (!finalError && finalCovers && finalCovers.generated_covers) {
                const finalCoverUrls = finalCovers.generated_covers
                  .filter((cover: any) => cover.image_url)
                  .map((cover: any) => cover.image_url)
                console.log(`🎬 FINAL: Found ${finalCoverUrls.length} covers in cover_generations`)

                // ATUALIZAÇÃO FINAL
                setGeneratedCovers(finalCoverUrls)

                setProgress(prev => ({
                  ...prev,
                  completed: finalCoverUrls.length,
                  inProgress: 0
                }))
                showToast.success(`🎉 Geração completa! ${finalCoverUrls.length} capas criadas com sucesso!`)

                // 🔥 NOTIFICAR OUTROS COMPONENTES QUE NOVAS CAPAS FORAM CRIADAS
                setTimeout(() => {
                  console.log('🔄 Triggering Series & Films refresh...')
                  // Disparar evento customizado para atualizar a página Series & Films
                  window.dispatchEvent(new CustomEvent('coversGenerated', {
                    detail: { covers: finalCoverUrls, generationId }
                  }))
                }, 1000)
              } else {
                console.error('❌ Failed to fetch covers from cover_generations:', finalError)
                showToast.warning('Geração completa, mas houve problema ao carregar as capas.')
              }
            } else if (generation.status === 'failed') {
              // 🔥 TRATAMENTO DE ERRO MELHORADO
              console.log('❌ Generation failed! Checking for partial success...')

              // Buscar capas que podem ter sido geradas mesmo com falha em cover_generations
              const { data: partialGeneration } = await supabase
                .from('cover_generations')
                .select('generated_covers')
                .eq('id', generationId)
                .single()

              if (partialGeneration && partialGeneration.generated_covers && partialGeneration.generated_covers.length > 0) {
                const partialCoverUrls = partialGeneration.generated_covers
                  .filter((cover: any) => cover.image_url)
                  .map((cover: any) => cover.image_url)
                console.log(`🎬 PARTIAL: Found ${partialCoverUrls.length} covers even with failed status`)

                setGeneratedCovers(partialCoverUrls)
                setProgress(prev => ({
                  ...prev,
                  completed: partialCoverUrls.length,
                  failed: prev.total - partialCoverUrls.length,
                  inProgress: 0
                }))

                showToast.warning(`⚠️ Geração parcial: ${partialCoverUrls.length} capas foram geradas. Você pode tentar novamente.`)
              } else {
                showToast.error('❌ Geração falhou. Tente novamente com configurações diferentes.')
                setProgress(prev => ({
                  ...prev,
                  failed: prev.total,
                  inProgress: 0
                }))
              }
            }

            // 🔥 SEMPRE PERMITIR NOVA TENTATIVA
            setIsGenerating(false)
            setCurrentGenerationId(null)
            setCurrentMovie(null)
            return
          }
        } else if (error) {
          console.error('❌ Error fetching generation data:', error)
        }

        // Continuar polling se ainda processando
        attempts++
        if (attempts < maxAttempts && generation?.status === 'processing') {
          // 🔥 POLLING MAIS AGRESSIVO NO FINAL - 1s quando próximo do fim, 3s normal
          const progressPercent = generation.progress || 0
          const isNearEnd = progressPercent >= 80 // 80% completo
          const pollDelay = isNearEnd ? 1000 : 3000 // 1s no final, 3s normal

          console.log(`🔄 Next poll in ${pollDelay}ms (${isNearEnd ? 'FAST' : 'NORMAL'} mode, ${progressPercent}% done)`)
          setTimeout(poll, pollDelay)
        } else {
          console.log(`⏹️ Stopping polling after ${attempts} attempts`)

          // 🔥 TIMEOUT: Buscar capas geradas até agora em cover_generations
          if (attempts >= maxAttempts) {
            console.log('⏰ Polling timeout - checking for partial results...')

            const { data: timeoutGeneration } = await supabase
              .from('cover_generations')
              .select('generated_covers')
              .eq('id', generationId)
              .single()

            if (timeoutGeneration && timeoutGeneration.generated_covers && timeoutGeneration.generated_covers.length > 0) {
              const timeoutCoverUrls = timeoutGeneration.generated_covers
                .filter((cover: any) => cover.image_url)
                .map((cover: any) => cover.image_url)
              setGeneratedCovers(timeoutCoverUrls)
              setProgress(prev => ({
                ...prev,
                completed: timeoutCoverUrls.length,
                failed: prev.total - timeoutCoverUrls.length,
                inProgress: 0
              }))
              showToast.warning(`⏰ Timeout: ${timeoutCoverUrls.length} capas geradas. Você pode tentar novamente.`)
            } else {
              showToast.error('⏰ Timeout na geração. Tente novamente.')
            }
          }

          setIsGenerating(false)
          setCurrentGenerationId(null)
          setCurrentMovie(null)
        }
      } catch (error) {
        console.error('❌ Erro no polling de progresso:', error)
        attempts++
        if (attempts < maxAttempts) {
          setTimeout(poll, 3000)
        }
      }
    }

    // Começar polling imediatamente
    poll()
  }, [])

  const loadPreviousGeneration = async () => {
    try {
      const { data: { session } } = await supabase.auth.getSession()
      if (!session) {
        showToast.error('Você precisa estar logado para carregar uma geração anterior.')
        return null
      }

      const toastId = showToast.loading('Carregando sua última geração...')

      // 🔥 BUSCAR DA TABELA CORRETA: cover_generations
      const { data: generations, error: generationError } = await supabase
        .from('cover_generations')
        .select('generated_covers, created_at, streaming_platform, status')
        .eq('user_id', session.user.id)
        .order('created_at', { ascending: false })
        .limit(1)
        .single()

      showToast.dismiss(String(toastId))

      if (!generationError && generations && generations.generated_covers) {
        const covers = generations.generated_covers
          .filter((cover: any) => cover.image_url)
          .map((cover: any) => cover.image_url)

        if (covers.length > 0) {
          console.log(`🎉 Carregadas ${covers.length} capas da última geração!`)
          setGeneratedCovers(covers)
          setProgress({
            total: covers.length,
            completed: covers.length,
            failed: 0,
            inProgress: 0
          })

          showToast.success(`📚 ${covers.length} capas da sua última geração foram carregadas!`)
          return { covers } // Retorna os dados da geração
        } else {
          showToast.info('Sua última geração não tem capas válidas.')
          return null
        }
      } else {
        showToast.info('Nenhuma geração anterior foi encontrada.')
        return null
      }
    } catch (error) {
      console.error('Erro ao carregar geração anterior:', error)
      showToast.error('Ocorreu um erro ao carregar seus dados.')
      return null
    }
  }

  const generateCovers = async (
    file: File,
    photoType: PhotoType,
    gender: 'male' | 'female' | null,
    streamingPlatform: StreamingPlatform,
    language: Language,
    quantity: number,
    generateTitles: boolean,
    creativityLevel: 'criativo' | 'rostoFiel' | 'estrito' = 'rostoFiel'
  ) => {
    try {
      setIsGenerating(true)
      setGeneratedCovers([])
      setFailedIndices(new Set())
      setProgress({ total: quantity, completed: 0, failed: 0, inProgress: 0 })
      setCurrentMovie('')

      const loadingToast = showToast.loading('Preparando geração...')

      // Upload da imagem para o Storage
      const uploadToast = showToast.loading('Fazendo upload da imagem...')

      const fileExt = file.name.split('.').pop()
      const fileName = `${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`;

      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('cover-images')
        .upload(`uploads/${fileName}`, file)

      if (uploadError) {
        showToast.dismiss(String(loadingToast))
        showToast.dismiss(String(uploadToast))
        throw new Error('Falha no upload da imagem: ' + uploadError.message)
      }

      showToast.dismiss(String(loadingToast))
      showToast.dismiss(String(uploadToast))
      showToast.success('Imagem enviada com sucesso!')

      const { data: { publicUrl } } = supabase.storage
        .from('cover-images')
        .getPublicUrl(`uploads/${fileName}`)

      setOriginalImageUrl(publicUrl)

      // 🔥 SALVAR FOTO ORIGINAL NA TABELA BASE_IMAGES PARA APARECER EM "FOTOS BASE"
      const { data: { session } } = await supabase.auth.getSession()
      if (session?.user) {
        try {
          const { error: baseImageError } = await supabase
            .from('base_images')
            .insert({
              user_id: session.user.id,
              original_image_url: publicUrl,
              file_name: file.name,
              file_size: file.size,
              content_type: file.type,
              photo_type: photoType,
              streaming_platform: streamingPlatform
            })

          if (baseImageError) {
            console.error('Erro ao salvar imagem base:', baseImageError)
          } else {
            console.log('✅ Imagem original salva em base_images para aparecer em Fotos Base')
            showToast.success('Foto salva em "Fotos Base"! 📸')
          }
        } catch (error) {
          console.error('Erro ao salvar base image:', error)
        }
      }

      setLastGenerationParams({
        file,
        photoType,
        gender,
        streamingPlatform,
        language,
        quantity,
        generateTitles,
        creativityLevel,
        imageUrl: publicUrl
      })

      showToast.dismiss(String(loadingToast))
      const generationToast = showToast.loading(`Gerando ${quantity} capas em paralelo...`)

      if (!session) {
        showToast.error('Erro: Você não está autenticado.')
        setIsGenerating(false)
        return
      }
      const accessToken = session.access_token

      // Iniciar geração (sem await para não bloquear)
      const generationPromise = fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/generate-covers`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          originalImageUrl: publicUrl,
          photoType,
          gender,
          streamingPlatform,
          language,
          quantity,
          generateTitles,
          creativityLevel,
          userId: session.user.id
        })
      })

      // Aguardar resposta inicial para obter generation_id
      const response = await generationPromise
      const initialData = await response.json()

      // Se temos generation_id, iniciar polling
      if (initialData.generation_id) {
        setCurrentGenerationId(initialData.generation_id)
        showToast.dismiss(String(generationToast))
        showToast.success('Geração iniciada! Acompanhe o progresso em tempo real.')

        // Iniciar polling de progresso
        pollProgress(initialData.generation_id)
        return // Sair aqui para deixar o polling cuidar do resto
      }

      // Se não tem generation_id, processar como antes (fallback)
      const data = initialData

      if (!response.ok) {
        const errorMessage = data.error || `Erro na geração das capas: ${response.status}`
        const details = data.details ? data.details.join(', ') : ''
        const fullError = details ? `${errorMessage} - ${details}` : errorMessage

        console.error('Erro detalhado na geração de capas:', data)
        showToast.dismiss(String(generationToast))

        // Se há capas que foram geradas mesmo com erro, usar elas
        if (data.covers && data.covers.length > 0) {
          const successfulCoversWithError = data.covers
            .filter((cover: any) => cover.success)
            .map((cover: any) => cover.image_url)

          console.log(`🎉 Encontradas ${successfulCoversWithError.length} capas geradas mesmo com erro!`)
          setGeneratedCovers(successfulCoversWithError)
          setProgress({
            total: quantity,
            completed: successfulCoversWithError.length,
            failed: quantity - successfulCoversWithError.length,
            inProgress: 0
          })

          showToast.warning(`⚠️ Geração parcial: ${successfulCoversWithError.length} de ${quantity} capas foram geradas. ${data.debug?.failed || 0} falharam.`)

          // Marcar índices que falharam
          const failedSet = new Set<number>()
          for (let i = successfulCoversWithError.length; i < quantity; i++) {
            failedSet.add(i)
          }
          setFailedIndices(failedSet)

          // Não lançar erro se algumas capas foram geradas
          setIsGenerating(false)
          return
        }

        // Se é erro de token da Replicate, mostrar instruções específicas
        if (data.instructions) {
          showToast.error(`${fullError}\n\n💡 ${data.instructions}`)
        } else {
          showToast.error(fullError)
        }

        // Tentar buscar capas que podem ter sido salvas no banco
        console.log('🔍 Tentando buscar capas geradas salvas no banco...')

        try {
          const { data: { session: coversSession } } = await supabase.auth.getSession()
          if (!coversSession) {
            console.log('Usuário não autenticado para buscar capas salvas.')
            throw new Error(fullError)
          }

          // 🔥 BUSCAR DA TABELA CORRETA: cover_generations
          const { data: savedGenerations, error: savedError } = await supabase
            .from('cover_generations')
            .select('generated_covers, created_at')
            .eq('user_id', coversSession.user.id)
            .order('created_at', { ascending: false })
            .limit(3)

          if (!savedError && savedGenerations && savedGenerations.length > 0) {
            // Coletar capas de gerações recentes
            const allSavedCovers: string[] = []

            for (const generation of savedGenerations) {
              if (generation.generated_covers && Array.isArray(generation.generated_covers)) {
                const covers = generation.generated_covers
                  .filter((cover: any) => cover.image_url)
                  .map((cover: any) => cover.image_url)
                allSavedCovers.push(...covers)
              }
            }

            if (allSavedCovers.length > 0) {
              console.log(`🎉 Encontradas ${allSavedCovers.length} capas salvas no banco!`)
              setGeneratedCovers(allSavedCovers)
              setProgress({
                total: quantity,
                completed: allSavedCovers.length,
                failed: quantity - allSavedCovers.length,
                inProgress: 0
              })

              showToast.success(`🎬 ${allSavedCovers.length} capas foram encontradas e estão disponíveis!`)
              setIsGenerating(false)
              return // Não lance erro se encontrou capas
            }
          }
        } catch (fetchError) {
          console.error('Erro ao buscar capas geradas:', fetchError)
        }

        throw new Error(fullError)
      }

      // Extrair apenas as URLs das capas bem-sucedidas
      const successfulCovers = data.covers
        ? data.covers.filter((cover: any) => cover.success).map((cover: any) => cover.image_url)
        : []

      // Mostrar mensagem de sucesso ou aviso baseado na resposta
      showToast.dismiss(String(generationToast))

      if (data.message) {
        console.log('Mensagem da edge function:', data.message)
      }

      if (data.debug) {
        console.log('Debug info:', data.debug)

        if (data.debug.failed > 0) {
          showToast.warning(`⚠️ ${data.debug.successful} de ${data.debug.requested} capas geradas. ${data.debug.failed} falharam.`)

          // Marcar índices que falharam
          const failedSet = new Set<number>()
          for (let i = data.debug.successful; i < data.debug.requested; i++) {
            failedSet.add(i)
          }
          setFailedIndices(failedSet)
        } else {
          showToast.success(`🎬 Todas as ${data.debug.successful} capas geradas com sucesso!`)
        }
      } else if (successfulCovers.length > 0) {
        showToast.success(`🎬 ${successfulCovers.length} capas geradas com sucesso!`)
      }

      setGeneratedCovers(successfulCovers)
      setProgress({
        total: quantity,
        completed: successfulCovers.length,
        failed: quantity - successfulCovers.length,
        inProgress: 0
      })

      // 🔥 DISPARAR EVENTO PARA INVALIDAR CACHE DE CRÉDITOS
      if (successfulCovers.length > 0) {
        window.dispatchEvent(new CustomEvent('creditConsumed'))
      }

      /* try {
        const { data: { session: coverSession } } = await supabase.auth.getSession()
        if (!coverSession) {
          console.warn('Não foi possível gerar a capa principal: usuário não autenticado.')
          return
        }
        const coverAuthToken = coverSession.access_token
        
        const coverResponse = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/generate-cover-image`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${coverAuthToken}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            imageUrl: publicUrl,
            photoType,
            gender,
            streamingPlatform,
            aspectRatio: '21:9'
          })
        })
        if (coverResponse.ok) {
          const coverData = await coverResponse.json()
          if (coverData.coverUrl) {
            // localStorage.setItem('generatedCover', coverData.coverUrl)
          }
        }
      } catch (coverError) {
        console.error('Error generating cover image:', coverError)
      } */

    } catch (err) {
      console.error('Generation error:', err)
      const errorMessage = err instanceof Error ? err.message : 'Falha ao gerar capas'
      setError(errorMessage)
      showToast.error(errorMessage)
      setProgress({ total: quantity, completed: 0, failed: 0, inProgress: 0 })
    } finally {
      setIsGenerating(false)
    }
  }

  const regenerateCoverImage = async () => {
    if (!lastGenerationParams) {
      showToast.error('Parâmetros da última geração não encontrados.')
      return
    }

    const { imageUrl, photoType, gender, streamingPlatform } = lastGenerationParams
    const toastId = showToast.loading('Regenerando capa principal...')

    try {
      const { data: { session } } = await supabase.auth.getSession()
      if (!session) {
        showToast.error('Erro: Você não está autenticado.')
        return
      }
      const authToken = session.access_token

      const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/generate-cover-image`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          imageUrl,
          photoType,
          gender,
          streamingPlatform,
          aspectRatio: '21:9',
        }),
      })

      if (!response.ok) throw new Error(`Erro na regeneração da capa: ${response.status}`)

      const data = await response.json()
      if (data.coverUrl) {
        // localStorage.setItem('generatedCover', data.coverUrl)
        showToast.dismiss(String(toastId))
        showToast.success('Nova capa principal gerada com sucesso!')
        window.location.reload();
      } else {
        throw new Error('URL da capa regenerada não encontrada na resposta.')
      }
    } catch (error) {
      console.error('Erro ao regenerar a capa principal:', error)
      const message = error instanceof Error ? error.message : 'Erro desconhecido'
      showToast.dismiss(String(toastId))
      showToast.error(`Falha ao regenerar: ${message}`)
    }
  }

  const regenerateIndividualCover = async (index: number, addCoverCallback?: (url: string) => void) => {
    if (!lastGenerationParams) {
      showToast.error('Não foi possível regenerar: parâmetros não encontrados.')
      return
    }

    const { imageUrl, photoType, streamingPlatform, language, generateTitles, gender, creativityLevel } = lastGenerationParams

    setRegeneratingIndex(index)
    setRegeneratingIndices(prev => new Set(prev).add(index))

    try {
      const { data: { session } } = await supabase.auth.getSession()
      if (!session) {
        showToast.error('Erro: Você não está autenticado.')
        return
      }
      const authToken = session.access_token

      const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/generate-single-cover`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          movieTitle: await getMovieTitleForIndex(index, streamingPlatform), // 🔥 TÍTULO PRIMEIRO
          originalImageUrl: imageUrl,
          streamingPlatform,
          photoType,
          aspectRatio: '2:3', // Aspect ratio padrão
          userId: session.user.id,
          generationId: crypto.randomUUID(), // 🔥 GERAR ID ÚNICO PARA REGENERAÇÃO
          position: index + 1, // 🔥 POSIÇÃO (1-indexed)
          gender,
          language,
          generateTitles,
          creativityLevelId: creativityLevel === 'criativo' ? 3 : creativityLevel === 'estrito' ? 1 : 2, // 🔥 MAPEAR PARA ID
          isRegeneration: true, // 🔥 MARCAR COMO REGENERAÇÃO
          isManualRegeneration: true, // 🔥 REGENERAÇÃO MANUAL
          useBackup: false // 🔥 NÃO USAR BACKUP NA REGENERAÇÃO
        })
      })

      if (!response.ok) throw new Error('Falha ao regenerar a capa.')

      const data = await response.json()
      if (data.success && data.image_url) {
        console.log(`🎉 SUCESSO INDIVIDUAL: ${data.movie_title} - ${data.image_url}`)

        // 🔥 ATUALIZAÇÃO IMEDIATA DO STATE - FORÇA INTERFACE A ATUALIZAR
        setGeneratedCovers(prevCovers => {
          const newCovers = [...prevCovers]

          // Se o index existe, substitui
          if (index < newCovers.length) {
            newCovers[index] = data.image_url
            console.log(`🔄 Substituindo posição ${index} com nova capa`)
          } else {
            // Se não existe, adiciona no final
            newCovers.push(data.image_url)
            console.log(`🔄 Adicionando nova capa na posição ${newCovers.length - 1}`)
          }

          console.log(`🎬 NOVO STATE: ${newCovers.length} capas, última: ${newCovers[newCovers.length - 1]}`)
          return newCovers
        })

        // 🔥 FORÇA ATUALIZAÇÃO DO PROGRESSO
        setProgress(prev => ({
          ...prev,
          completed: prev.completed + 1
        }))

        // Add the new cover using the callback if provided
        if (addCoverCallback) {
          addCoverCallback(data.image_url)
        }

        // 🔥 POLLING AGRESSIVO PARA GARANTIR SINCRONIZAÇÃO
        setTimeout(async () => {
          console.log('🔄 Polling agressivo após sucesso individual...')
          await refreshCurrentGeneration()
        }, 500)

        // 🔥 SEGUNDO POLLING DE SEGURANÇA
        setTimeout(async () => {
          console.log('🔄 Segundo polling de segurança...')
          await refreshCurrentGeneration()
        }, 2000)

        showToast.success(`🎉 ${data.movie_title} gerada com sucesso!`)
        console.log(`✅ Capa regenerada e salva no Supabase: ${data.image_url}`)
      } else {
        throw new Error('Nenhuma URL de capa foi retornada')
      }
    } catch (error) {
      console.error('Error regenerating cover:', error)
      showToast.error(`Erro ao regenerar a capa #${index + 1}.`)
    } finally {
      setRegeneratingIndex(null)
      setRegeneratingIndices(prev => {
        const newSet = new Set(prev)
        newSet.delete(index)
        return newSet
      })
    }
  }

  const retryFailedCover = (index: number) => {
    setFailedIndices(prev => {
      const newSet = new Set(prev)
      newSet.delete(index)
      return newSet
    })
    regenerateIndividualCover(index)
  }

  // 🔥 FUNÇÃO REMOVIDA: clearGeneration movida para cima com parâmetro keepSettings

  // 🔥 CORREÇÃO: Função para forçar carregamento das capas de cover_generations
  const forceLoadCovers = async () => {
    try {
      const { data: { session } } = await supabase.auth.getSession()
      if (!session) {
        showToast.error('Você precisa estar logado para carregar capas.')
        return
      }

      const toastId = showToast.loading('Forçando carregamento das capas...')

      // 🔥 BUSCAR DA TABELA CORRETA: cover_generations
      const { data: generations, error } = await supabase
        .from('cover_generations')
        .select('generated_covers, created_at, streaming_platform')
        .eq('user_id', session.user.id)
        .order('created_at', { ascending: false })
        .limit(10) // Últimas 10 gerações

      if (!error && generations && generations.length > 0) {
        // Coletar todas as capas de todas as gerações
        const allCovers: string[] = []

        for (const generation of generations) {
          if (generation.generated_covers && Array.isArray(generation.generated_covers)) {
            const covers = generation.generated_covers
              .filter((cover: any) => cover.image_url)
              .map((cover: any) => cover.image_url)
            allCovers.push(...covers)
          }
        }

        if (allCovers.length > 0) {
          console.log(`🔄 Force loaded ${allCovers.length} covers from cover_generations`)
          setGeneratedCovers([...allCovers])
          setProgress({
            total: allCovers.length,
            completed: allCovers.length,
            failed: 0,
            inProgress: 0
          })

          showToast.dismiss(String(toastId))
          showToast.success(`✅ ${allCovers.length} capas carregadas com sucesso!`)
        } else {
          showToast.dismiss(String(toastId))
          showToast.warning('Nenhuma capa encontrada nas suas gerações.')
        }
      } else {
        showToast.dismiss(String(toastId))
        showToast.warning('Nenhuma geração encontrada no seu histórico.')
      }
    } catch (error) {
      console.error('Erro ao forçar carregamento:', error)
      showToast.error('Erro ao carregar capas.')
    }
  }

  // 🔥 CORREÇÃO: Atualizar capas de cover_generations (não cover_images)
  const refreshCovers = async () => {
    try {
      const { data: { session } } = await supabase.auth.getSession()
      if (!session) return

      console.log('🔄 Refreshing covers from cover_generations...')

      // 🔥 BUSCAR DA TABELA CORRETA: cover_generations
      const { data: generations, error } = await supabase
        .from('cover_generations')
        .select('generated_covers, created_at')
        .eq('user_id', session.user.id)
        .order('created_at', { ascending: false })
        .limit(5) // Últimas 5 gerações

      if (error) {
        console.error('Erro ao buscar gerações:', error)
        return
      }

      if (generations && generations.length > 0) {
        // Coletar todas as capas de todas as gerações recentes
        const allCovers: string[] = []

        for (const generation of generations) {
          if (generation.generated_covers && Array.isArray(generation.generated_covers)) {
            const covers = generation.generated_covers
              .filter((cover: any) => cover.image_url)
              .map((cover: any) => cover.image_url)
            allCovers.push(...covers)
          }
        }

        if (allCovers.length > 0) {
          console.log(`🎬 Found ${allCovers.length} covers from cover_generations, updating state...`)
          setGeneratedCovers([...allCovers]) // Force new array reference
          setProgress(prev => ({
            ...prev,
            completed: allCovers.length
          }))
        } else {
          console.log('❌ No covers found in any recent generation')
        }
      }
    } catch (error) {
      console.error('Erro ao atualizar capas:', error)
    }
  }

  // 🔥 NOVA FUNÇÃO: GERAÇÃO CONTROLADA PELO FRONTEND
  const generateCoversDirectly = async (
    file: File,
    photoType: PhotoType,
    gender: 'male' | 'female' | null,
    streamingPlatform: StreamingPlatform,
    language: Language,
    quantity: number,
    generateTitles: boolean,
    creativityLevel: 'criativo' | 'rostoFiel' | 'estrito' = 'rostoFiel',
    testMode: boolean = false // 🔥 PARÂMETRO PARA CONTROLAR TESTE
  ) => {
    try {
      setIsGenerating(true)
      setGeneratedCovers([])
      setFailedIndices(new Set())
      setProgress({ total: quantity, completed: 0, failed: 0, inProgress: 0 })
      setCurrentMovie('')

      const loadingToast = showToast.loading('Preparando geração...')

      // Upload da imagem
      const uploadToast = showToast.loading('Fazendo upload da imagem...')

      const fileExt = file.name.split('.').pop()
      const fileName = `${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`;

      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('cover-images')
        .upload(`uploads/${fileName}`, file)

      if (uploadError) throw new Error('Falha no upload da imagem: ' + uploadError.message)

      showToast.dismiss(String(uploadToast))
      showToast.success('Imagem enviada com sucesso!')

      const { data: { publicUrl } } = supabase.storage
        .from('cover-images')
        .getPublicUrl(`uploads/${fileName}`)

      setOriginalImageUrl(publicUrl)

      // Salvar foto original
      const { data: { session } } = await supabase.auth.getSession()
      if (session?.user) {
        try {
          const { error: baseImageError } = await supabase
            .from('base_images')
            .insert({
              user_id: session.user.id,
              original_image_url: publicUrl,
              file_name: file.name,
              file_size: file.size,
              content_type: file.type,
              photo_type: photoType,
              streaming_platform: streamingPlatform
            })

          if (!baseImageError) {
            console.log('✅ Imagem original salva em base_images')
            showToast.success('Foto salva em "Fotos Base"! 📸')
          }
        } catch (error) {
          console.error('Erro ao salvar base image:', error)
        }
      }

      setLastGenerationParams({
        file,
        photoType,
        gender,
        streamingPlatform,
        language,
        quantity,
        generateTitles,
        creativityLevel,
        imageUrl: publicUrl
      })

      showToast.dismiss(String(loadingToast))
      showToast.success(`Iniciando geração de ${quantity} capas...`)

      if (!session) {
        showToast.error('Erro: Você não está autenticado.')
        setIsGenerating(false)
        return
      }

      // Criar generation_id para tracking
      const generationId = crypto.randomUUID()
      setCurrentGenerationId(generationId)

      // 🎬 FILMES DA PLATAFORMA
      const movieDatabase = {
        netflix: [
          'Round 6 (Squid Game)', 'Wednesday', 'Stranger Things 5', 'Elite',
          'Money Heist (La Casa de Papel)', 'The Witcher', 'Ozark', 'Dark',
          'Narcos', 'The Crown', 'Orange Is the New Black', 'Bridgerton'
        ],
        disney: [
          'The Mandalorian', 'Encanto', 'Loki', 'WandaVision',
          'The Falcon and the Winter Soldier', 'Hawkeye', 'Ahsoka', 'Andor',
          'Obi-Wan Kenobi'
        ],
        amazon: [
          'The Boys', 'The Marvelous Mrs. Maisel', 'The Wheel of Time', 'Invincible',
          'The Terminal List', 'Upload', 'Reacher', 'Tom Clancy\'s Jack Ryan',
          'Good Omens', 'The Rings of Power', 'Gen V'
        ]
      }

      const movies = movieDatabase[streamingPlatform] || []
      const primaryMovies = movies.slice(0, quantity)

      // Aspect ratios corretos
      const getAspectRatio = (platform: string, index: number) => {
        if (platform === 'netflix') return '3:4'
        if (platform === 'amazon') return '3:4'
        if (platform === 'disney') return index < 6 ? '16:9' : '5:4'
        return '3:4'
      }

      const accessToken = session.access_token
      const newCovers: string[] = []
      const failedIndices = new Set<number>()

      // 🔄 PROCESSAR UMA POR VEZ
      for (let i = 0; i < quantity; i++) {
        const position = i + 1
        const movieTitle = primaryMovies[i] || `Filme ${position}`
        const aspectRatio = getAspectRatio(streamingPlatform, i)

        console.log(`\n🎬 Frontend processing ${position}/${quantity}: ${movieTitle}`)
        setCurrentMovie(movieTitle)

        // Atualizar progresso
        setProgress(prev => ({
          ...prev,
          inProgress: 1
        }))

        try {
          // 🎯 CHAMAR GENERATE-SINGLE-COVER DIRETAMENTE
          const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/generate-single-cover`, {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${accessToken}`,
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              movieTitle,
              originalImageUrl: publicUrl,
              streamingPlatform,
              photoType,
              aspectRatio,
              userId: session.user.id,
              generationId,
              position,
              gender,
              language,
              generateTitles,
              creativityLevel,
              useBackup: true,
              testMode // 🔥 USAR PARÂMETRO testMode DINÂMICO
            })
          })

          if (!response.ok) {
            const errorText = await response.text()
            throw new Error(`API Error: ${errorText}`)
          }

          const result = await response.json()

          if (result.success) {
            newCovers.push(result.image_url)
            console.log(`✅ Position ${position} SUCCESS: ${result.movie_title}`)
            showToast.success(`✅ ${result.movie_title} gerada!`)

            // Atualizar covers em tempo real
            setGeneratedCovers([...newCovers])
            setProgress(prev => ({
              ...prev,
              completed: newCovers.length,
              inProgress: 0
            }))
          } else {
            failedIndices.add(i)
            console.log(`❌ Position ${position} FAILED: ${result.error}`)
            showToast.error(`❌ ${movieTitle}: ${result.error}`)

            // 🔥 CORRIGIR: Atualizar progresso com contagem correta de falhas
            setProgress(prev => ({
              ...prev,
              failed: failedIndices.size,
              inProgress: 0
            }))
          }

        } catch (error) {
          failedIndices.add(i)
          console.error(`❌ Frontend error for position ${position}:`, error)
          const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido'

          // Capturar a mensagem de erro real da API
          if (errorMessage.includes('API Error:')) {
            const apiErrorMatch = errorMessage.match(/API Error: (.+)/)
            if (apiErrorMatch) {
              // setLastError(apiErrorMatch[1]) // REMOVIDO
            }
          } else {
            // setLastError(errorMessage) // REMOVIDO
          }

          showToast.error(`❌ ${movieTitle}: ${errorMessage}`)

          setProgress(prev => ({
            ...prev,
            failed: failedIndices.size,
            inProgress: 0
          }))
        }

        // Pequena pausa entre gerações
        if (i < quantity - 1) {
          await new Promise(resolve => setTimeout(resolve, 2000))
        }
      }

      // 🔥 SALVAR LOG DE GERAÇÃO APENAS SE NÃO FOR TESTE
      if (newCovers.length > 0 && !testMode) {
        try {
          const { error: logError } = await supabase
            .from('generation_logs')
            .insert({
              user_id: session.user.id,
              streaming_platform: streamingPlatform,
              total_requested: quantity,
              total_generated: newCovers.length,
              success_rate: Math.round((newCovers.length / quantity) * 100),
              duration_seconds: 0,
              settings: {
                gender,
                language,
                generateTitles,
                creativityLevel
              }
            })

          if (logError) {
            console.error('❌ Erro ao salvar log de geração:', logError)
          } else {
            console.log('✅ Log de geração salvo com sucesso')
          }
        } catch (error) {
          console.error('❌ Erro ao processar log de geração:', error)
        }
      }

      // Finalizar
      setFailedIndices(failedIndices)
      setCurrentMovie(null)
      setCurrentGenerationId(null)

      if (newCovers.length > 0) {
        showToast.success(`🎉 ${newCovers.length} capas geradas com sucesso!`)
      } else {
        showToast.error('❌ Nenhuma capa foi gerada. Tente novamente.')
      }

    } catch (err) {
      console.error('Generation error:', err)
      const errorMessage = err instanceof Error ? err.message : 'Falha ao gerar capas'
      setError(errorMessage)
      showToast.error(errorMessage)
      setProgress({ total: quantity, completed: 0, failed: 0, inProgress: 0 })
    } finally {
      setIsGenerating(false)
      setCurrentMovie(null)
      setCurrentGenerationId(null)
    }
  }

  // 🔥 NOVA FUNÇÃO: Atualizar apenas a geração atual após regeneração individual
  const refreshCurrentGeneration = async (generationId?: string) => {
    try {
      const { data: { session } } = await supabase.auth.getSession()
      if (!session) return

      const targetGenerationId = generationId || currentGenerationId
      if (!targetGenerationId) {
        console.log('🔄 No generation ID, using refreshCovers fallback')
        await refreshCovers()
        return
      }

      console.log(`🔄 Refreshing current generation: ${targetGenerationId}`)

      // Buscar apenas a geração atual
      const { data: generation, error } = await supabase
        .from('cover_generations')
        .select('generated_covers')
        .eq('id', targetGenerationId)
        .single()

      if (!error && generation && generation.generated_covers) {
        const covers = generation.generated_covers
          .filter((cover: any) => cover.image_url)
          .map((cover: any) => cover.image_url)

        if (covers.length > 0) {
          console.log(`🎬 Updated current generation with ${covers.length} covers`)
          setGeneratedCovers([...covers]) // Force new array reference
          setProgress(prev => ({
            ...prev,
            completed: covers.length
          }))
        }
      } else {
        console.error('Error fetching current generation:', error)
        // Fallback para refresh geral
        await refreshCovers()
      }
    } catch (error) {
      console.error('Erro ao atualizar geração atual:', error)
      // Fallback para refresh geral
      await refreshCovers()
    }
  }

  return {
    isGenerating,
    generatedCovers,
    failedIndices,
    error,
    regeneratingIndex,
    regeneratingIndices,
    progress,
    originalImageUrl,
    currentMovie,
    currentGenerationId,
    generateCovers,
    regenerateIndividualCover,
    regenerateCoverImage,
    retryFailedCover,
    clearGeneration,
    lastGenerationParams,
    hasExistingGenerations, // 🔥 NOVA FUNÇÃO para verificar se há gerações
    loadPreviousGeneration,
    loadSpecificGeneration, // 🔥 NOVA FUNÇÃO para carregar geração específica
    checkExistingGenerations, // 🔥 NOVA FUNÇÃO para verificar gerações existentes
    forceLoadCovers,
    refreshCovers, // 🔥 NOVA FUNÇÃO para atualizar capas automaticamente
    generateCoversDirectly, // 🔥 AGORA COM PARÂMETRO testMode
    refreshCurrentGeneration
  }
}