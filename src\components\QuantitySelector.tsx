import React from 'react'
import { useTranslation } from 'react-i18next'
import { Image } from 'lucide-react'

interface QuantitySelectorProps {
  selectedQuantity: number | null
  onSelect: (quantity: number) => void
}

export default function QuantitySelector({ selectedQuantity, onSelect }: QuantitySelectorProps) {
  const { t } = useTranslation()

  const options = [
    {
      quantity: 2,
      label: `2 ${t('quantitySelector.covers')}`,
      description: t('quantitySelector.quickGeneration'),
      price: t('quantitySelector.free')
    },
    {
      quantity: 6,
      label: `6 ${t('quantitySelector.covers')}`,
      description: t('quantitySelector.moreVariety'),
      price: t('quantitySelector.premium')
    },
    {
      quantity: 10,
      label: `10 ${t('quantitySelector.covers')}`,
      description: t('quantitySelector.extendedCollection'),
      price: t('quantitySelector.premium')
    },
    {
      quantity: 12,
      label: `12 ${t('quantitySelector.covers')}`,
      description: t('quantitySelector.completeCollection'),
      price: t('quantitySelector.premium')
    }
  ]

  return (
    <div className="w-full">
      <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center space-x-2">
        <Image size={20} />
        <span>{t('quantitySelector.title')}</span>
      </h3>
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {options.map((option) => {
          const isSelected = selectedQuantity === option.quantity
          
          return (
            <button
              key={option.quantity}
              onClick={() => onSelect(option.quantity)}
              className={`
                p-4 rounded-lg border-2 transition-all duration-200 text-left
                ${isSelected 
                  ? 'border-blue-500 bg-blue-50 text-blue-800' 
                  : 'border-gray-200 hover:border-gray-300 bg-white'
                }
              `}
            >
              <div className="flex flex-col space-y-2">
                <div className={`
                  w-12 h-12 rounded-lg flex items-center justify-center text-white font-bold text-lg
                  ${isSelected ? 'bg-blue-600' : 'bg-gray-400'}
                `}>
                  {option.quantity}
                </div>
                <div>
                  <h4 className="font-semibold">{option.label}</h4>
                  <p className="text-sm opacity-75">{option.description}</p>
                  <span className={`text-xs font-medium px-2 py-1 rounded-full mt-1 inline-block ${
                    option.price === t('quantitySelector.free') ? 'bg-green-100 text-green-800' : 'bg-orange-100 text-orange-800'
                  }`}>
                    {option.price}
                  </span>
                </div>
              </div>
            </button>
          )
        })}
      </div>
    </div>
  )
}