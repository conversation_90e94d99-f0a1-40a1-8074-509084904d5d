# Sistema de Packs - AI Movie Cover Generator

## Visão Geral

O AI Movie Cover Generator agora utiliza um sistema baseado em **packs** para controlar o uso e monetização da plataforma. Cada pack permite a geração completa de capas para uma plataforma de streaming específica.

## Como Funciona

### O que é um Pack?

Um pack é uma unidade de uso que inclui:
- **Capas para 1 plataforma** (Netflix, Disney+ ou Amazon Prime)
- **Até 12 imagens cover** por pack
- **1 poster final** para impressão
- **Regeneração ilimitada** das imagens cover
- **1 geração única** do poster final

### Planos Disponíveis

| Plano | Quantidade | Preço Total | Preço por Pack | Economia |
|-------|------------|-------------|----------------|----------|
| **One Pack** | 1 pack | R$ 19,90 | R$ 19,90 | - |
| **Complete Pack** | 3 packs | R$ 59,90 | R$ 11,98 | R$ 23,80 |
| **Master Pack** | 15 packs | R$ 149,90 | R$ 9,99 | R$ 148,60 |

### Regras de Uso

#### ✅ Permitido (Ilimitado)
- **Regenerar imagens cover**: Você pode regenerar qualquer imagem cover quantas vezes quiser
- **Download das covers**: Baixe todas as imagens em alta qualidade
- **Visualização**: Veja suas imagens em tela cheia

#### ⚠️ Limitado (1 vez por pack)
- **Poster final**: Cada pack permite apenas 1 geração do poster final
- **Consumo do pack**: Uma vez usado para uma plataforma, o pack é consumido

## Fluxo de Uso

1. **Compra do Pack**: Usuário adquire um ou mais packs
2. **Seleção da Plataforma**: Escolhe Netflix, Disney+ ou Amazon
3. **Consumo do Pack**: Sistema consome 1 pack ao iniciar a geração
4. **Geração das Covers**: Até 12 imagens são geradas
5. **Regeneração Livre**: Usuário pode regenerar covers à vontade
6. **Poster Final**: Usuário pode gerar 1 poster final (opcional)

## Implementação Técnica

### Estrutura do Banco de Dados

#### Tabela `user_packs`
```sql
CREATE TABLE user_packs (
  id UUID PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id),
  pack_type TEXT NOT NULL, -- 'one_pack', 'complete_pack', 'master_pack'
  total_packs INTEGER NOT NULL,
  available_packs INTEGER NOT NULL,
  used_packs INTEGER NOT NULL,
  stripe_payment_intent_id TEXT,
  purchased_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE
);
```

#### Tabela `pack_usage`
```sql
CREATE TABLE pack_usage (
  id UUID PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id),
  user_pack_id UUID REFERENCES user_packs(id),
  streaming_platform TEXT NOT NULL,
  photo_type TEXT NOT NULL,
  cover_images_generated INTEGER DEFAULT 0,
  poster_generated BOOLEAN DEFAULT FALSE,
  poster_generated_at TIMESTAMP WITH TIME ZONE,
  original_image_url TEXT,
  generated_covers JSONB,
  generated_poster_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE
);
```

#### Tabela `payment_transactions`
```sql
CREATE TABLE payment_transactions (
  id UUID PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id),
  stripe_payment_intent_id TEXT UNIQUE NOT NULL,
  pack_type TEXT NOT NULL,
  packs_quantity INTEGER NOT NULL,
  amount INTEGER NOT NULL, -- em centavos
  currency TEXT NOT NULL DEFAULT 'brl',
  status TEXT NOT NULL DEFAULT 'pending',
  created_at TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE
);
```

### Funções Edge

#### 1. `check-pack-balance`
- **Endpoint**: `/functions/v1/check-pack-balance`
- **Método**: GET
- **Parâmetros**: `userId`
- **Retorna**: Saldo de packs do usuário

#### 2. `consume-pack`
- **Endpoint**: `/functions/v1/consume-pack`
- **Método**: POST
- **Payload**: `{ userId, streamingPlatform, photoType, originalImageUrl }`
- **Função**: Consome um pack e cria registro de uso

#### 3. `update-pack-usage`
- **Endpoint**: `/functions/v1/update-pack-usage`
- **Método**: POST
- **Payload**: `{ usageId, coverImages, posterUrl, posterGenerated }`
- **Função**: Atualiza o uso do pack com imagens geradas

### Componentes React

#### `PackBalance`
Exibe o saldo de packs do usuário:
- Packs disponíveis, usados e total
- Histórico de compras
- Uso recente
- Botão para comprar mais packs

#### `PricingModal`
Modal de seleção de planos:
- Três opções de packs
- Destaque para o plano mais popular
- Informações sobre economia

#### `PackInfo`
Componente informativo sobre como funcionam os packs:
- Regras de uso
- Limitações
- Dicas para o usuário

### Integração com Stripe

#### Webhook de Pagamento
Quando um pagamento é confirmado:
1. Atualiza status da transação
2. Adiciona packs à conta do usuário
3. Envia confirmação

#### Metadados do Payment Intent
```javascript
{
  planId: 'complete_pack',
  userId: 'user-uuid',
  packs: '3',
  planName: 'Complete Pack'
}
```

## Fluxo de Desenvolvimento

### 1. Verificação de Saldo
```javascript
const balance = await checkUserPackBalance(userId)
if (balance.availablePacks <= 0) {
  // Mostrar modal de compra
}
```

### 2. Consumo de Pack
```javascript
const result = await consumePack(platform, photoType, imageUrl)
if (result.needsPurchase) {
  // Usuário precisa comprar mais packs
}
```

### 3. Geração de Covers
```javascript
// Após consumir pack com sucesso
await generateCovers(params)
await updatePackUsage(usageId, coverImages)
```

### 4. Poster Final
```javascript
// Verificar se já foi gerado para este pack
if (!posterGenerated) {
  await generatePoster(params)
  await updatePackUsage(usageId, null, posterUrl, true)
}
```

## Vantagens do Sistema

### Para o Usuário
- **Transparência**: Sabe exatamente o que está comprando
- **Flexibilidade**: Pode usar os packs quando quiser
- **Economia**: Descontos progressivos em compras maiores
- **Controle**: Regeneração ilimitada das covers

### Para o Negócio
- **Previsibilidade**: Receita baseada em packs vendidos
- **Escalabilidade**: Fácil ajuste de preços e quantidades
- **Controle de Custos**: Limita uso de recursos caros (poster final)
- **Upsell**: Incentiva compra de packs maiores

## Monitoramento e Analytics

### Métricas Importantes
- **Taxa de Conversão**: Visitantes → Compradores
- **Pack Médio**: Valor médio por transação
- **Uso por Pack**: Quantas covers são realmente geradas
- **Regeneração**: Frequência de regeneração de covers
- **Poster Final**: % de usuários que geram poster

### Queries Úteis
```sql
-- Packs mais vendidos
SELECT pack_type, COUNT(*), SUM(total_packs) 
FROM user_packs 
GROUP BY pack_type;

-- Uso médio por pack
SELECT AVG(cover_images_generated) 
FROM pack_usage 
WHERE cover_images_generated > 0;

-- Taxa de geração de poster
SELECT 
  COUNT(CASE WHEN poster_generated THEN 1 END) * 100.0 / COUNT(*) as poster_rate
FROM pack_usage;
```

## Próximos Passos

1. **Analytics Dashboard**: Painel para acompanhar métricas
2. **Pack Expiration**: Sistema de expiração de packs (opcional)
3. **Referral System**: Programa de indicação
4. **Pack Gifts**: Possibilidade de presentear packs
5. **Enterprise Plans**: Planos para uso comercial

## Suporte e Troubleshooting

### Problemas Comuns

**Usuário não consegue gerar covers**
- Verificar saldo de packs
- Verificar se pack foi consumido corretamente
- Verificar logs de erro na geração

**Pagamento aprovado mas packs não creditados**
- Verificar webhook do Stripe
- Verificar tabela `payment_transactions`
- Executar creditação manual se necessário

**Poster não gera**
- Verificar se já foi gerado para este pack
- Verificar se todas as imagens necessárias estão disponíveis
- Verificar configuração do template

### Logs Importantes
- Supabase Edge Functions logs
- Stripe webhook logs
- Frontend console errors
- Database query performance 