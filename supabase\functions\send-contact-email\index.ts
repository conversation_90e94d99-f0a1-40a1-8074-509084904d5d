import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from "https://esm.sh/@supabase/supabase-js@2"
import { Ratelimit } from "https://esm.sh/@upstash/ratelimit@1.1.3"
import { Redis } from "https://esm.sh/@upstash/redis@1.31.5"

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

// Rate limiting
const ratelimit = new Ratelimit({
  redis: Redis.fromEnv(),
  limiter: Ratelimit.slidingWindow(10, "10 m"), // 10 requests per 10 minutes
  analytics: true,
  prefix: "@upstash/ratelimit",
});

const EMAILIT_API_KEY = Deno.env.get('EMAILIT_API_KEY')
const supabaseUrl = Deno.env.get('SUPABASE_URL')!
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!

interface ContactRequest {
  name: string
  email: string
  subject: string
  message: string
  type: 'bug' | 'suggestion' | 'criticism' | 'other'
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST',
        'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
      },
    })
  }

  try {
    // Rate limit by IP address
    const ip = req.headers.get("x-forwarded-for") ?? "127.0.0.1";
    const { success, limit, remaining, reset } = await ratelimit.limit(ip);

    if (!success) {
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: 'RATE_LIMIT_EXCEEDED',
          message: 'Você enviou muitas mensagens. Tente novamente mais tarde.' 
        }),
        { 
          status: 429, 
          headers: { 
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
            'X-RateLimit-Limit': limit,
            'X-RateLimit-Remaining': remaining,
            'X-RateLimit-Reset': reset,
          } 
        }
      )
    }

    const { name, email, subject, message, type }: ContactRequest = await req.json()

    if (!name || !email || !subject || !message || !type) {
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: 'MISSING_FIELDS',
          message: 'Todos os campos são obrigatórios' 
        }),
        { 
          status: 400, 
          headers: { 
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          } 
        }
      )
    }

    // Create Supabase client
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Get request metadata
    const userAgent = req.headers.get('user-agent') || 'Unknown'
    const forwardedFor = req.headers.get('x-forwarded-for')
    const realIp = req.headers.get('x-real-ip')
    const ipAddress = forwardedFor || realIp || 'Unknown'

    let emailSent = false
    let emailError = null

    // Try to send email if Emailit API key is configured
    if (EMAILIT_API_KEY) {
      try {
        const emailitResponse = await fetch('https://api.emailit.com/v1/emails', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${EMAILIT_API_KEY}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            from: 'PosterFlix <<EMAIL>>',
            to: '<EMAIL>',
            reply_to: email,
            subject: `[PosterFlix Contato] ${subject}`,
            html: `
              <h2>Nova mensagem de contato recebida</h2>
              <p><strong>Nome:</strong> ${name}</p>
              <p><strong>Email:</strong> ${email}</p>
              <p><strong>Tipo:</strong> ${type === 'bug' ? '🐛 Bug' : 
                                        type === 'suggestion' ? '💡 Sugestão' : 
                                        type === 'criticism' ? '📝 Feedback' : '📧 Outros'}</p>
              <p><strong>Assunto:</strong> ${subject}</p>
              <p><strong>Mensagem:</strong></p>
              <div style="background: #f5f5f5; padding: 15px; border-radius: 5px; margin: 10px 0;">
                ${message.replace(/\n/g, '<br>')}
              </div>
              <hr>
              <p><small>
                <strong>Informações técnicas:</strong><br>
                IP: ${ipAddress}<br>
                User Agent: ${userAgent}<br>
                Data: ${new Date().toLocaleString('pt-BR')}
              </small></p>
            `,
            text: `
Nova mensagem de contato recebida

Nome: ${name}
Email: ${email}
Tipo: ${type}
Assunto: ${subject}

Mensagem:
${message}

---
Informações técnicas:
IP: ${ipAddress}
User Agent: ${userAgent}
Data: ${new Date().toLocaleString('pt-BR')}
            `,
            headers: {
              'X-Contact-Type': type,
              'X-Contact-Email': email
            }
          }),
        })

        if (emailitResponse.ok) {
          emailSent = true
          console.log('✅ Email enviado com sucesso via Emailit')
        } else {
          const errorData = await emailitResponse.text()
          emailError = `Emailit API error: ${emailitResponse.status} - ${errorData}`
          console.error('❌ Erro no Emailit:', emailError)
        }
      } catch (error) {
        emailError = `Emailit request failed: ${error.message}`
        console.error('❌ Erro ao enviar email via Emailit:', error)
      }
    } else {
      console.log('⚠️ EMAILIT_API_KEY não configurado, email não será enviado')
    }

    // Always save message to database (even if email fails)
    const { data, error } = await supabase
      .from('contact_messages')
      .insert({
        name: name.trim(),
        email: email.trim().toLowerCase(),
        subject: subject.trim(),
        message: message.trim(),
        type,
        email_sent: emailSent,
        user_agent: userAgent,
        ip_address: ipAddress,
        status: 'pending'
      })
      .select()

    if (error) {
      console.error('❌ Erro ao salvar mensagem no banco:', error)
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: 'DATABASE_ERROR',
          message: 'Erro interno do servidor ao salvar mensagem' 
        }),
        { 
          status: 500, 
          headers: { 
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          } 
        }
      )
    }

    console.log(`✅ Mensagem salva no banco: ${data[0].id}`)

    // Return success response with details
    return new Response(
      JSON.stringify({ 
        success: true,
        message: emailSent 
          ? 'Mensagem enviada com sucesso! Responderemos em breve.'
          : 'Mensagem salva com sucesso! Nossa equipe será notificada e responderemos em breve.',
        email_sent: emailSent,
        message_id: data[0].id,
        ...(emailError && { email_error: emailError })
      }),
      { 
        status: 200, 
        headers: { 
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        } 
      }
    )

  } catch (error) {
    console.error('❌ Erro geral no edge function:', error)
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: 'INTERNAL_ERROR',
        message: 'Erro interno do servidor' 
      }),
      { 
        status: 500, 
        headers: { 
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        } 
      }
    )
  }
}) 