import { useState, useEffect } from 'react'
import { supabase } from '../lib/supabase'

export interface UserProfile {
  user_id: string
  full_name: string | null
  avatar_url: string | null
  preferred_language: string | null
  preferred_platform: string | null
  total_generations: number
  total_covers_created: number
  total_posters_created: number
  profile_created_at: string
  available_packs: number
  used_packs: number
  total_packs_purchased: number
  generations_last_30_days: number
  posters_last_30_days: number
  favorite_platform: string | null
  last_generation_date: string | null
}

export interface Generation {
  id: string
  original_image_url: string
  photo_type: 'individual' | 'casal'
  streaming_platform: 'netflix' | 'disney' | 'amazon'
  generated_covers: string[]
  status: 'processing' | 'completed' | 'failed'
  created_at: string
  completed_at: string | null
  total_count: number
}

export interface PaymentTransaction {
  id: string
  stripe_payment_intent_id: string
  pack_type: string
  packs_quantity: number
  amount: number
  currency: string
  status: string
  created_at: string
  total_count: number
}

export interface UserStats {
  totalGenerations: number
  totalCovers: number
  totalPosters: number
  availablePacks: number
  usedPacks: number
  generationsThisMonth: number
  postersThisMonth: number
  favoritePlatform: string | null
  lastGenerationDate: string | null
}

export function useUserData() {
  const [profile, setProfile] = useState<UserProfile | null>(null)
  const [generations, setGenerations] = useState<Generation[]>([])
  const [paymentHistory, setPaymentHistory] = useState<PaymentTransaction[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Fetch user dashboard data
  const fetchDashboardData = async () => {
    try {
      setLoading(true)
      setError(null)

      const { data, error: dashboardError } = await supabase
        .rpc('get_user_dashboard_data')

      if (dashboardError) {
        throw dashboardError
      }

      if (data && data.length > 0) {
        setProfile(data[0])
      }
    } catch (err) {
      console.error('Error fetching dashboard data:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch dashboard data')
    } finally {
      setLoading(false)
    }
  }

  // Fetch user generations with pagination and filters
  const fetchGenerations = async (
    pageSize = 20,
    pageOffset = 0,
    platformFilter?: string,
    sortBy = 'created_at',
    sortOrder = 'desc'
  ) => {
    try {
      const { data, error: generationsError } = await supabase
        .rpc('get_user_generations', {
          page_size: pageSize,
          page_offset: pageOffset,
          platform_filter: platformFilter || null,
          sort_by: sortBy,
          sort_order: sortOrder
        })

      if (generationsError) {
        throw generationsError
      }

      // Transform the data to match our interface
      const transformedData = data?.map((gen: any) => ({
        ...gen,
        generated_covers: Array.isArray(gen.generated_covers) 
          ? gen.generated_covers 
          : gen.generated_covers ? JSON.parse(gen.generated_covers) : []
      })) || []

      setGenerations(transformedData)
      return transformedData
    } catch (err) {
      console.error('Error fetching generations:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch generations')
      return []
    }
  }

  // Fetch payment history
  const fetchPaymentHistory = async (pageSize = 20, pageOffset = 0) => {
    try {
      const { data, error: paymentError } = await supabase
        .rpc('get_user_payment_history', {
          page_size: pageSize,
          page_offset: pageOffset
        })

      if (paymentError) {
        throw paymentError
      }

      setPaymentHistory(data || [])
      return data || []
    } catch (err) {
      console.error('Error fetching payment history:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch payment history')
      return []
    }
  }

  // Update user profile
  const updateProfile = async (updates: {
    full_name?: string
    avatar_url?: string
    preferred_language?: string
    preferred_platform?: string
  }) => {
    try {
      const { data, error: updateError } = await supabase
        .rpc('update_user_profile', {
          new_full_name: updates.full_name || null,
          new_avatar_url: updates.avatar_url || null,
          new_preferred_language: updates.preferred_language || null,
          new_preferred_platform: updates.preferred_platform || null
        })

      if (updateError) {
        throw updateError
      }

      if (data && data.length > 0 && data[0].success) {
        // Refresh dashboard data after update
        await fetchDashboardData()
        return { success: true, message: data[0].message }
      } else {
        throw new Error(data?.[0]?.message || 'Failed to update profile')
      }
    } catch (err) {
      console.error('Error updating profile:', err)
      const errorMessage = err instanceof Error ? err.message : 'Failed to update profile'
      setError(errorMessage)
      return { success: false, message: errorMessage }
    }
  }

  // Get or create user profile
  const getOrCreateProfile = async () => {
    try {
      const { data, error: profileError } = await supabase
        .rpc('get_or_create_user_profile')

      if (profileError) {
        throw profileError
      }

      return data?.[0] || null
    } catch (err) {
      console.error('Error getting/creating profile:', err)
      setError(err instanceof Error ? err.message : 'Failed to get profile')
      return null
    }
  }

  // Calculate user stats from profile data
  const getUserStats = (): UserStats => {
    if (!profile) {
      return {
        totalGenerations: 0,
        totalCovers: 0,
        totalPosters: 0,
        availablePacks: 0,
        usedPacks: 0,
        generationsThisMonth: 0,
        postersThisMonth: 0,
        favoritePlatform: null,
        lastGenerationDate: null
      }
    }

    return {
      totalGenerations: profile.total_generations,
      totalCovers: profile.total_covers_created,
      totalPosters: profile.total_posters_created,
      availablePacks: profile.available_packs,
      usedPacks: profile.used_packs,
      generationsThisMonth: profile.generations_last_30_days,
      postersThisMonth: profile.posters_last_30_days,
      favoritePlatform: profile.favorite_platform,
      lastGenerationDate: profile.last_generation_date
    }
  }

  // Download individual cover
  const downloadCover = async (coverUrl: string, filename?: string) => {
    try {
      const response = await fetch(coverUrl)
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      
      const link = document.createElement('a')
      link.href = url
      link.download = filename || `cover-${Date.now()}.jpg`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      
      window.URL.revokeObjectURL(url)
      return { success: true }
    } catch (err) {
      console.error('Error downloading cover:', err)
      return { success: false, error: err instanceof Error ? err.message : 'Download failed' }
    }
  }

  // Download all covers from a generation
  const downloadAllCovers = async (generation: Generation) => {
    try {
      for (let i = 0; i < generation.generated_covers.length; i++) {
        const coverUrl = generation.generated_covers[i]
        const filename = `${generation.streaming_platform}-cover-${i + 1}-${generation.id.slice(0, 8)}.jpg`
        await downloadCover(coverUrl, filename)
        
        // Small delay between downloads
        if (i < generation.generated_covers.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 500))
        }
      }
      return { success: true }
    } catch (err) {
      console.error('Error downloading all covers:', err)
      return { success: false, error: err instanceof Error ? err.message : 'Bulk download failed' }
    }
  }

  // Initialize data on mount
  useEffect(() => {
    const initializeData = async () => {
      try {
        await fetchDashboardData()
        await fetchGenerations()
      } catch (error) {
        console.error('Error initializing dashboard data:', error)
      }
    }
    
    initializeData()
  }, [])

  return {
    // Data
    profile,
    generations,
    paymentHistory,
    loading,
    error,
    
    // Computed
    userStats: getUserStats(),
    
    // Actions
    fetchDashboardData,
    fetchGenerations,
    fetchPaymentHistory,
    updateProfile,
    getOrCreateProfile,
    downloadCover,
    downloadAllCovers,
    
    // Utilities
    refreshData: () => {
      fetchDashboardData()
      fetchGenerations()
      fetchPaymentHistory()
    }
  }
} 