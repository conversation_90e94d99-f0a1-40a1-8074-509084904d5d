import React, { useState } from 'react'
import { User, ImageIcon, Package, LogOut, ChevronDown, CreditCard, Plus, Palette, Clock } from 'lucide-react'
import { Link } from 'react-router-dom'

interface AppHeaderProps {
  user: { id: string; email?: string } | null
  onLogout: () => void
  availablePacks?: number
  onOpenDashboard?: () => void
  onBuyPacks?: () => void
}

export default function AppHeader({ user, onLogout, availablePacks, onOpenDashboard, onBuyPacks }: AppHeaderProps) {
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  if (!user) return null

  const getInitials = (email: string) => {
    return email.split('@')[0].slice(0, 2).toUpperCase()
  }

  const isLowOnPacks = (availablePacks || 0) < 3

  return (
    <header className="bg-neu-base border-b border-neu-dark/20 sticky top-0 z-40">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <div className="flex items-center space-x-3">
            <img 
              src="/img/logo-final-svg.png" 
              alt="PosterFlix Online" 
              className="h-10 w-auto"
            />
          </div>

          {/* Center - Buy Packs Button (when low on packs) */}
          {isLowOnPacks && onBuyPacks && (
            <button
              onClick={onBuyPacks}
              className="flex items-center space-x-2 bg-gradient-to-r from-purple-500 to-pink-500 text-white px-6 py-2 rounded-xl hover:from-purple-600 hover:to-pink-600 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105"
            >
              <Plus className="w-4 h-4" />
              <span className="font-medium">Buy More Packs</span>
            </button>
          )}

          {/* Right Side */}
          <div className="flex items-center space-x-4">
            {/* Pack Balance */}
            {availablePacks !== undefined && (
              <div className={`
                flex items-center space-x-2 bg-neu-base rounded-xl px-4 py-2 shadow-neu-sm
                ${isLowOnPacks ? 'border-2 border-orange-200 bg-orange-50' : ''}
              `}>
                <div className={`
                  w-8 h-8 rounded-lg flex items-center justify-center
                  ${isLowOnPacks ? 'bg-orange-100' : 'bg-primary-100'}
                `}>
                  <Package className={`w-4 h-4 ${isLowOnPacks ? 'text-orange-600' : 'text-primary-600'}`} />
                </div>
                <div className="text-left">
                  <p className="text-xs text-gray-500">Available Packs</p>
                  <p className={`text-sm font-semibold ${isLowOnPacks ? 'text-orange-700' : 'text-gray-800'}`}>
                    {availablePacks}
                  </p>
                </div>
                {onBuyPacks && (
                  <button
                    onClick={onBuyPacks}
                    className="ml-2 w-6 h-6 bg-primary-100 hover:bg-primary-200 rounded-lg flex items-center justify-center transition-colors duration-200"
                    title="Buy more packs"
                  >
                    <Plus className="w-3 h-3 text-primary-600" />
                  </button>
                )}
              </div>
            )}

            {/* User Menu */}
            <div className="relative">
              <button
                onClick={() => setIsMenuOpen(!isMenuOpen)}
                className="flex items-center space-x-3 bg-neu-base rounded-xl px-4 py-2 shadow-neu-sm hover:shadow-neu-hover transition-all duration-200 focus:outline-none focus:shadow-neu-pressed"
              >
                {/* User Avatar */}
                <div className="flex items-center space-x-2">
                  <div className="w-8 h-8 bg-accent-100 rounded-lg flex items-center justify-center">
                    <span className="text-xs font-semibold text-accent-700">
                      {getInitials(user.email || 'U')}
                    </span>
                  </div>
                  <div className="text-left hidden sm:block">
                    <p className="text-sm font-medium text-gray-800">{user.email?.split('@')[0]}</p>
                    <p className="text-xs text-gray-500">Free Plan</p>
                  </div>
                </div>
                <ChevronDown className={`w-4 h-4 text-gray-500 transition-transform duration-200 ${isMenuOpen ? 'rotate-180' : ''}`} />
              </button>

              {/* Dropdown Menu */}
              {isMenuOpen && (
                <div className="absolute right-0 mt-2 w-64 bg-neu-base rounded-xl shadow-neu-lg border border-neu-dark/10 py-2 z-50">
                  <div className="px-4 py-3 border-b border-neu-dark/10">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-accent-100 rounded-lg flex items-center justify-center">
                        <span className="text-sm font-semibold text-accent-700">
                          {getInitials(user.email || 'U')}
                        </span>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-800">{user.email}</p>
                        <p className="text-xs text-gray-500">Free Plan</p>
                      </div>
                    </div>
                  </div>

                  <div className="py-2">
                    <Link
                      to="/dashboard"
                      onClick={() => setIsMenuOpen(false)}
                      className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-neu-dark/10 flex items-center space-x-3 transition-colors duration-150"
                    >
                      <div className="w-8 h-8 bg-primary-100 rounded-lg flex items-center justify-center">
                        <User className="w-4 h-4 text-primary-600" />
                      </div>
                      <div>
                        <p className="font-medium">Dashboard</p>
                        <p className="text-xs text-gray-500">Manage your account</p>
                      </div>
                    </Link>

                    <Link
                      to="/my-covers"
                      onClick={() => setIsMenuOpen(false)}
                      className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-neu-dark/10 flex items-center space-x-3 transition-colors duration-150"
                    >
                      <div className="w-8 h-8 bg-accent-100 rounded-lg flex items-center justify-center">
                        <ImageIcon className="w-4 h-4 text-accent-600" />
                      </div>
                      <div>
                        <p className="font-medium">My Covers</p>
                        <p className="text-xs text-gray-500">View all generations</p>
                      </div>
                    </Link>
                    
                    <Link
                      to="/canva-poster"
                      onClick={() => setIsMenuOpen(false)}
                      className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-neu-dark/10 flex items-center space-x-3 transition-colors duration-150"
                    >
                      <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                        <Palette className="w-4 h-4 text-green-600" />
                      </div>
                      <div>
                        <p className="font-medium">Canva Posters</p>
                        <p className="text-xs text-gray-500">Generate custom posters</p>
                      </div>
                    </Link>

                    <Link
                      to="/canva-history"
                      onClick={() => setIsMenuOpen(false)}
                      className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-neu-dark/10 flex items-center space-x-3 transition-colors duration-150"
                    >
                      <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                        <Clock className="w-4 h-4 text-blue-600" />
                      </div>
                      <div>
                        <p className="font-medium">Histórico de Pôsteres</p>
                        <p className="text-xs text-gray-500">Ver pôsteres anteriores</p>
                      </div>
                    </Link>

                    {onBuyPacks && (
                      <button
                        onClick={() => {
                          onBuyPacks()
                          setIsMenuOpen(false)
                        }}
                        className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-neu-dark/10 flex items-center space-x-3 transition-colors duration-150"
                      >
                        <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                          <CreditCard className="w-4 h-4 text-purple-600" />
                        </div>
                        <div>
                          <p className="font-medium">Buy Packs</p>
                          <p className="text-xs text-gray-500">Purchase more generations</p>
                        </div>
                      </button>
                    )}

                    <div className="border-t border-neu-dark/10 mt-2 pt-2">
                      <button
                        onClick={() => {
                          onLogout()
                          setIsMenuOpen(false)
                        }}
                        className="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 flex items-center space-x-3 transition-colors duration-150"
                      >
                        <div className="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center">
                          <LogOut className="w-4 h-4 text-red-600" />
                        </div>
                        <div>
                          <p className="font-medium">Sign out</p>
                          <p className="text-xs text-red-400">Logout from account</p>
                        </div>
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Click outside to close */}
      {isMenuOpen && (
        <div
          className="fixed inset-0 z-30"
          onClick={() => setIsMenuOpen(false)}
        />
      )}
    </header>
  )
}
