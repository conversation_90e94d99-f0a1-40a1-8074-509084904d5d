import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { 
  Mail, 
  User, 
  MessageSquare, 
  Send, 
  Bug, 
  Lightbulb, 
  FileText, 
  HelpCircle,
  Check,
  AlertCircle
} from 'lucide-react'
import { supabase } from '../lib/supabase'
import { showToast } from '../utils/toast'
import { useTranslation } from 'react-i18next'

interface ContactFormData {
  name: string
  email: string
  subject: string
  message: string
  type: 'bug' | 'suggestion' | 'criticism' | 'other'
}

export default function ContactForm() {
  const { t } = useTranslation()
  const [formData, setFormData] = useState<ContactFormData>({
    name: '',
    email: '',
    subject: '',
    message: '',
    type: 'other'
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSubmitted, setIsSubmitted] = useState(false)
  const [submissionResult, setSubmissionResult] = useState<{
    emailSent: boolean
    message: string
  } | null>(null)

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  const handleTypeChange = (type: ContactFormData['type']) => {
    setFormData(prev => ({ ...prev, type }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!formData.name || !formData.email || !formData.subject || !formData.message) {
      showToast.error('Todos os campos são obrigatórios')
      return
    }
    
    setIsSubmitting(true)
    
    try {
      const { data: { session } } = await supabase.auth.getSession()
      const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/send-contact-email`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': session ? `Bearer ${session.access_token}` : '',
        },
        body: JSON.stringify(formData)
      })
      
      const result = await response.json()
      
      if (response.ok && result.success) {
        // Salvar informações do resultado
        setSubmissionResult({
          emailSent: result.email_sent || false,
          message: result.message || 'Mensagem processada com sucesso!'
        })
        
        setIsSubmitted(true)
        
        // Mostrar mensagem específica baseada na resposta
        if (result.email_sent) {
          showToast.success('✅ Mensagem enviada com sucesso! Responderemos em breve.')
        } else {
          showToast.success('💾 Mensagem salva com sucesso! Nossa equipe será notificada e responderemos em breve.')
        }
        
        // Reset form after 4 seconds
        setTimeout(() => {
          setIsSubmitted(false)
          setSubmissionResult(null)
          setFormData({ name: '', email: '', subject: '', message: '', type: 'other' })
        }, 4000)
        
      } else {
        // Handle specific error messages
        const errorMessage = result.error || result.message || 'Erro ao enviar mensagem'
        
        // Show user-friendly error messages
        switch (result.error) {
          case 'Todos os campos são obrigatórios':
            showToast.error('❌ Todos os campos são obrigatórios')
            break
          case 'Erro ao salvar mensagem':
            showToast.error('❌ Erro ao salvar sua mensagem. Tente novamente.')
            break
          case 'Erro interno do servidor':
            showToast.error('❌ Erro interno. Tente novamente em alguns minutos.')
            break
          default:
            showToast.error(`❌ ${errorMessage}`)
            break
        }
        
        console.error('Erro na resposta da API:', result)
      }
    } catch (error) {
      console.error('Erro ao enviar formulário:', error)
      showToast.error('❌ Erro de conexão. Verifique sua internet e tente novamente.')
    } finally {
      setIsSubmitting(false)
    }
  }

  const contactTypes = [
    { id: 'bug' as const, label: t('contactForm.types.bug'), description: t('contactForm.types.bugDesc'), icon: Bug, color: 'bg-red-500' },
    { id: 'suggestion' as const, label: t('contactForm.types.suggestion'), description: t('contactForm.types.suggestionDesc'), icon: Lightbulb, color: 'bg-yellow-500' },
    { id: 'criticism' as const, label: t('contactForm.types.feedback'), description: t('contactForm.types.feedbackDesc'), icon: FileText, color: 'bg-blue-500' },
    { id: 'other' as const, label: t('contactForm.types.other'), description: t('contactForm.types.otherDesc'), icon: HelpCircle, color: 'bg-gray-500' }
  ]

  if (isSubmitted) {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className="max-w-2xl mx-auto p-8 bg-brand-white border-2 border-brand-black shadow-brutal rounded-lg text-center"
      >
        <div className={`w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 border-2 border-brand-black ${
          submissionResult?.emailSent ? 'bg-green-500' : 'bg-blue-500'
        }`}>
          {submissionResult?.emailSent ? (
            <Check className="w-8 h-8 text-white" />
          ) : (
            <Mail className="w-8 h-8 text-white" />
          )}
        </div>
        
        <h2 className="text-2xl font-bold text-brand-text mb-2">
          {submissionResult?.emailSent ? 
            '📧 Mensagem Enviada!' : 
            '💾 Mensagem Salva!'
          }
        </h2>
        
        <p className="text-brand-text/70 mb-4">
          {submissionResult?.message || 'Sua mensagem foi processada com sucesso!'}
        </p>
        
        <div className={`p-4 rounded-lg border-2 border-brand-black mb-4 ${
          submissionResult?.emailSent ? 'bg-green-50' : 'bg-blue-50'
        }`}>
          <div className="flex items-center justify-center space-x-2 text-sm">
            {submissionResult?.emailSent ? (
              <>
                <Check className="w-4 h-4 text-green-600" />
                <span className="text-green-800 font-medium">
                  Email enviado automaticamente para nossa equipe
                </span>
              </>
            ) : (
              <>
                <AlertCircle className="w-4 h-4 text-blue-600" />
                <span className="text-blue-800 font-medium">
                  Mensagem salva em nosso sistema - nossa equipe será notificada
                </span>
              </>
            )}
          </div>
        </div>
        
        <div className="text-sm text-brand-text/60">
          Retornando em alguns segundos...
        </div>
      </motion.div>
    )
  }

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-8">
      <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} className="text-center">
        <h1 className="text-4xl font-black text-brand-text mb-4">{t('contactForm.title')}</h1>
        <p className="text-lg text-brand-text/70 max-w-2xl mx-auto">
          {t('contactForm.subtitle')}
        </p>
      </motion.div>

      <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.1 }} className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {contactTypes.map((type) => {
          const Icon = type.icon
          return (
            <button key={type.id} onClick={() => handleTypeChange(type.id)} className={`p-4 rounded-lg border-2 border-brand-black transition-all duration-200 text-left ${formData.type === type.id ? 'bg-brand-primary shadow-brutal-pressed scale-95' : 'bg-brand-white shadow-brutal-sm hover:shadow-brutal-hover hover:translate-x-[-2px] hover:translate-y-[-2px]'}`}>
              <div className={`w-10 h-10 ${type.color} rounded-lg flex items-center justify-center mb-3 border-2 border-brand-black`}>
                <Icon className="w-5 h-5 text-white" />
              </div>
              <h3 className="font-bold text-brand-text text-sm mb-1">{type.label}</h3>
              <p className="text-xs text-brand-text/60">{type.description}</p>
            </button>
          )
        })}
      </motion.div>

      <motion.form initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.2 }} onSubmit={handleSubmit} className="bg-brand-white border-2 border-brand-black shadow-brutal rounded-lg p-6 space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label htmlFor="name" className="block text-sm font-bold text-brand-text mb-2">{t('contactForm.fullName')} *</label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              required
              className="w-full px-4 py-3 border-2 border-brand-black rounded-lg focus:ring-2 focus:ring-brand-accent focus:border-brand-accent transition-all shadow-brutal-sm"
              placeholder={t('contactForm.fullNamePlaceholder')}
            />
          </div>
          <div>
            <label htmlFor="email" className="block text-sm font-bold text-brand-text mb-2">{t('contactForm.email')} *</label>
            <input
              type="email"
              id="email"
              name="email"
              value={formData.email}
              onChange={handleInputChange}
              required
              className="w-full px-4 py-3 border-2 border-brand-black rounded-lg focus:ring-2 focus:ring-brand-accent focus:border-brand-accent transition-all shadow-brutal-sm"
              placeholder={t('contactForm.emailPlaceholder')}
            />
          </div>
        </div>
        <div>
          <label htmlFor="subject" className="block text-sm font-bold text-brand-text mb-2">{t('contactForm.subject')} *</label>
          <input
            type="text"
            id="subject"
            name="subject"
            value={formData.subject}
            onChange={handleInputChange}
            required
            className="w-full px-4 py-3 border-2 border-brand-black rounded-lg focus:ring-2 focus:ring-brand-accent focus:border-brand-accent transition-all shadow-brutal-sm"
            placeholder={t('contactForm.subjectPlaceholder')}
          />
        </div>
        <div>
          <label htmlFor="message" className="block text-sm font-bold text-brand-text mb-2">{t('contactForm.message')} *</label>
          <textarea
            id="message"
            name="message"
            value={formData.message}
            onChange={handleInputChange}
            required
            rows={6}
            className="w-full px-4 py-3 border-2 border-brand-black rounded-lg focus:ring-2 focus:ring-brand-accent focus:border-brand-accent transition-all shadow-brutal-sm resize-none"
            placeholder={t('contactForm.messagePlaceholder')}
          />
        </div>
        <div className="flex justify-end">
          <button
            type="submit"
            disabled={isSubmitting}
            className={`px-8 py-3 rounded-lg border-2 border-brand-black font-bold transition-all flex items-center space-x-2 ${isSubmitting ? 'bg-gray-300 text-gray-500 cursor-not-allowed' : 'bg-brand-primary text-brand-black shadow-brutal-sm hover:shadow-brutal-hover hover:translate-x-[-2px] hover:translate-y-[-2px] active:shadow-brutal-pressed active:translate-x-0 active:translate-y-0'}`}
          >
            {isSubmitting ? (
              <>
                <div className="w-5 h-5 border-2 border-gray-500 border-t-transparent rounded-full animate-spin" />
                <span>{t('contactForm.sending')}</span>
              </>
            ) : (
              <>
                <Send className="w-5 h-5" />
                <span>{t('contactForm.sendMessage')}</span>
              </>
            )}
          </button>
        </div>
      </motion.form>
    </div>
  )
} 