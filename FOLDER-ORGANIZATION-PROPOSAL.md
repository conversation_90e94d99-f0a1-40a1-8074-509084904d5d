# Proposta de Reorganização de Pastas - AI Streaming Poster

**Data da proposta:** 2025-07-15  
**Status:** Aguardando implementação

## Problema Atual

### Estrutura Atual - Problemas Identificados:
- **55+ componentes em estrutura plana** - Dificulta navegação e manutenção
- **Responsabilidades misturadas** - Landing page, admin, geração e UI juntos
- **Nomenclatura inconsistente** - Alguns componentes têm nomes genéricos
- **Falta separação de responsabilidades** - Componentes específicos espalhados
- **Dificuldade para encontrar arquivos** - Sem agrupamento lógico

## 🗂️ Estrutura Proposta

### 1. Reorganização dos Componentes

```
src/
├── components/
│   ├── ui/                     # Componentes reutilizáveis de UI
│   │   ├── common/             # Componentes comuns
│   │   │   ├── ErrorBoundary.tsx
│   │   │   ├── Footer.tsx
│   │   │   ├── AppHeader.tsx
│   │   │   ├── PatternBackground.tsx
│   │   │   ├── ToastProvider.tsx
│   │   │   └── icons.tsx
│   │   │
│   │   ├── forms/              # Formulários reutilizáveis
│   │   │   ├── FileUpload.tsx
│   │   │   ├── ContactForm.tsx
│   │   │   └── CheckoutForm.tsx
│   │   │
│   │   ├── selectors/          # Seletores/Inputs especializados
│   │   │   ├── PhotoTypeSelector.tsx
│   │   │   ├── TemplateSelector.tsx
│   │   │   ├── LanguageSelector.tsx
│   │   │   ├── LanguageSwitcher.tsx
│   │   │   ├── GenderSelector.tsx
│   │   │   ├── GenerationSelector.tsx
│   │   │   ├── QuantitySelector.tsx
│   │   │   ├── CreativityLevelSelector.tsx
│   │   │   └── BaseImageSelector.tsx
│   │   │
│   │   ├── progress/           # Componentes de progresso
│   │   │   ├── GenerationProgressBar.tsx
│   │   │   └── PosterStatus.tsx
│   │   │
│   │   ├── modals/             # Modais reutilizáveis
│   │   │   ├── AuthModal.tsx
│   │   │   ├── PricingModal.tsx
│   │   │   ├── NewGenerationModal.tsx
│   │   │   └── SetPasswordModal.tsx
│   │   │
│   │   └── magicui/            # Manter estrutura existente
│   │       ├── animated-beam.tsx
│   │       ├── animated-gradient-text.tsx
│   │       ├── border-beam.tsx
│   │       ├── number-ticker.tsx
│   │       ├── scroll-based-velocity.tsx
│   │       ├── sparkles-text.tsx
│   │       └── warp-background.tsx
│   │
│   ├── features/               # Componentes por funcionalidade
│   │   ├── auth/               # Autenticação
│   │   │   └── AuthForm.tsx
│   │   │
│   │   ├── generation/         # Geração de imagens
│   │   │   ├── CoverGenerationWizard.tsx
│   │   │   ├── CoverGrid.tsx
│   │   │   ├── MyGenerations.tsx
│   │   │   ├── RegenerateCover.tsx
│   │   │   ├── TitleGenerationToggle.tsx
│   │   │   ├── GenerationLogs.tsx
│   │   │   └── WizardStep.tsx
│   │   │
│   │   ├── canva/              # Integração Canva
│   │   │   ├── CanvaPosterGenerator.tsx
│   │   │   └── CanvaTestComponent.tsx
│   │   │
│   │   ├── payment/            # Sistema de pagamento
│   │   │   ├── CreditBalance.tsx
│   │   │   ├── PackBalance.tsx
│   │   │   └── PackInfo.tsx
│   │   │
│   │   ├── dashboard/          # Dashboard do usuário
│   │   │   ├── Dashboard.tsx
│   │   │   └── UserDashboard.tsx
│   │   │
│   │   └── landing/            # Landing page
│   │       ├── LandingHero.tsx
│   │       ├── HeroSection.tsx
│   │       ├── BeforeAfterDemo.tsx
│   │       ├── InteractiveDemo.tsx
│   │       ├── InteractiveDemoSection.tsx
│   │       ├── HowItWorksSection.tsx
│   │       ├── SocialProofSection.tsx
│   │       └── TrustAndTransparencySection.tsx
│   │
│   └── layout/                 # Componentes de layout (futuro)
│       └── (componentes de layout se necessário)
```

### 2. Reorganização dos Hooks

```
src/
├── hooks/
│   ├── api/                    # Hooks de API
│   │   ├── useCoverGeneration.ts
│   │   ├── useBaseImages.ts
│   │   └── useAdminVouchers.ts
│   │
│   ├── auth/                   # Hooks de autenticação
│   │   └── useUserData.ts
│   │
│   ├── payment/               # Hooks de pagamento
│   │   ├── usePayment.ts
│   │   ├── useCreditBalance.ts
│   │   └── useVoucher.ts
│   │
│   └── ui/                    # Hooks de estado de UI (futuro)
│       └── (hooks de UI futuros)
```

### 3. Reorganização das Pages

```
src/
├── pages/
│   ├── admin/                  # Páginas administrativas
│   │   ├── AdminPage.tsx
│   │   ├── AdminMovieStats.tsx
│   │   ├── AdminMovies.tsx
│   │   └── AdminPromptTester.tsx
│   │
│   ├── auth/                   # Páginas de autenticação
│   │   └── AuthCallback.tsx
│   │
│   ├── canva/                  # Páginas do Canva
│   │   ├── CanvaPosterPage.tsx
│   │   └── CanvaPosterHistory.tsx
│   │
│   ├── public/                 # Páginas públicas
│   │   └── LandingPage.tsx
│   │
│   └── api/                    # Manter estrutura existente
│       └── (arquivos existentes)
```

### 4. Reorganização da Lib

```
src/
├── lib/
│   ├── services/              # Integrações com serviços externos
│   │   ├── supabase.ts
│   │   ├── stripe.ts
│   │   ├── canvaService.ts
│   │   └── replicate.ts
│   │
│   ├── config/               # Arquivos de configuração
│   │   ├── i18n.ts
│   │   └── gtm.ts
│   │
│   ├── diagnostics/          # Debug e diagnósticos
│   │   └── supabase-diagnostics.ts
│   │
│   └── utils.ts              # Utilitários gerais
```

### 5. Reorganização dos Types

```
src/
├── types/
│   ├── api/                  # Types relacionados à API
│   │   └── database.types.ts
│   │
│   ├── integrations/         # Types de serviços externos
│   │   └── canva.ts
│   │
│   └── index.ts              # Types principais da aplicação
```

### 6. Reorganização dos Utils

```
src/
├── utils/
│   ├── date/
│   │   └── dateUtils.ts
│   │
│   ├── ui/
│   │   └── toast.ts
│   │
│   └── envCheck.ts
```

## 🎯 Benefícios da Nova Estrutura

### 1. **Organização por Funcionalidade**
- Componentes agrupados por feature que pertencem
- Mais fácil encontrar componentes relacionados
- Melhor para colaboração em equipe

### 2. **Separação de Responsabilidades**
- Componentes de UI separados de lógica de negócio
- Funcionalidade admin isolada
- Componentes da landing page agrupados

### 3. **Escalabilidade**
- Fácil adicionar novas features sem bagunçar diretórios existentes
- Padrões claros para onde novos componentes devem ir
- Melhor para code splitting e lazy loading

### 4. **Manutenibilidade**
- Mais fácil entender relações entre componentes
- Debug e desenvolvimento de features mais rápidos
- Modelo mental mais claro da aplicação

### 5. **Reutilização**
- Componentes de UI claramente marcados como reutilizáveis
- Componentes específicos contidos em seus domínios
- Padrões comuns mais fáceis de identificar

## 🚀 Estratégia de Migração

### Fase 1 - Componentes de UI (Baixo Risco)
```bash
# Criar estrutura de pastas
mkdir -p src/components/ui/{common,forms,selectors,progress,modals}
mkdir -p src/components/features/{auth,generation,canva,payment,dashboard,landing}

# Mover componentes de UI primeiro
# Estes são menos acoplados e mais fáceis de mover
```

### Fase 2 - Componentes por Feature (Médio Risco)
- Mover componentes de geração
- Mover componentes de landing page
- Mover componentes de pagamento
- Mover componentes de dashboard

### Fase 3 - Reorganizar Hooks e Pages (Alto Risco)
- Reorganizar hooks por categoria
- Reorganizar pages por funcionalidade
- Atualizar todos os imports

### Fase 4 - Lib e Utils (Baixo Risco)
- Reorganizar arquivos de lib
- Reorganizar types
- Reorganizar utils

## 📝 Arquivos Index para Imports Limpos

### Exemplo de arquivo index:
```typescript
// src/components/features/generation/index.ts
export { default as CoverGenerationWizard } from './CoverGenerationWizard'
export { default as CoverGrid } from './CoverGrid'
export { default as MyGenerations } from './MyGenerations'
export { default as RegenerateCover } from './RegenerateCover'

// Uso:
import { CoverGenerationWizard, CoverGrid } from '@/components/features/generation'
```

## ⚠️ Considerações de Migração

### 1. **Atualização de Imports**
- Todos os imports precisarão ser atualizados
- Usar find/replace para mudanças em lote
- Configurar path mapping no tsconfig.json para imports relativos mais limpos

### 2. **Possíveis Breaking Changes**
- Referências externas a estes componentes
- Configurações do sistema de build
- Localização de arquivos de teste

### 3. **Configuração de Path Mapping**
```json
// tsconfig.json
{
  "compilerOptions": {
    "baseUrl": ".",
    "paths": {
      "@/components/*": ["src/components/*"],
      "@/features/*": ["src/components/features/*"],
      "@/ui/*": ["src/components/ui/*"],
      "@/hooks/*": ["src/hooks/*"],
      "@/lib/*": ["src/lib/*"],
      "@/types/*": ["src/types/*"],
      "@/utils/*": ["src/utils/*"]
    }
  }
}
```

## 📋 Checklist de Implementação

### Preparação:
- [ ] Fazer backup do projeto
- [ ] Criar branch para reorganização
- [ ] Documentar estrutura atual

### Execução:
- [ ] Criar nova estrutura de pastas
- [ ] Mover componentes em fases
- [ ] Atualizar imports gradualmente  
- [ ] Criar arquivos index
- [ ] Configurar path mapping
- [ ] Testar funcionalidade após cada fase

### Finalização:
- [ ] Executar testes completos
- [ ] Verificar build de produção
- [ ] Atualizar documentação
- [ ] Code review

---

**Próximos passos:** Esta reorganização deve ser feita gradualmente, testando a cada fase para garantir que nada seja quebrado. O benefício será uma base de código muito mais organizada e fácil de manter.