import React, { useEffect, useRef } from "react";

interface SparklesTextProps {
  children: React.ReactNode;
  className?: string;
  sparklesCount?: number;
  colors?: {
    first: string;
    second: string;
  };
}

export function SparklesText({
  children,
  className = "",
  sparklesCount = 10,
  colors = {
    first: "#9E7AFF",
    second: "#FE8BBB",
  },
}: SparklesTextProps) {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const sparkles = Array.from({ length: sparklesCount }, (_, i) => {
      const sparkle = document.createElement("div");
      sparkle.className = "absolute pointer-events-none";
      sparkle.style.cssText = `
        width: 4px;
        height: 4px;
        background: ${i % 2 === 0 ? colors.first : colors.second};
        border-radius: 50%;
        opacity: 0;
        animation: sparkle 3s linear infinite;
        animation-delay: ${Math.random() * 3}s;
      `;
      
      // Random position within the text bounds
      sparkle.style.left = `${Math.random() * 100}%`;
      sparkle.style.top = `${Math.random() * 100}%`;
      
      container.appendChild(sparkle);
      return sparkle;
    });

    // Add sparkle animation styles
    const style = document.createElement("style");
    style.textContent = `
      @keyframes sparkle {
        0%, 20% {
          opacity: 0;
          transform: scale(0);
        }
        25% {
          opacity: 1;
          transform: scale(1);
        }
        90%, 100% {
          opacity: 0;
          transform: scale(0);
        }
      }
    `;
    document.head.appendChild(style);

    return () => {
      sparkles.forEach(sparkle => {
        if (sparkle.parentNode) {
          sparkle.parentNode.removeChild(sparkle);
        }
      });
      if (style.parentNode) {
        style.parentNode.removeChild(style);
      }
    };
  }, [sparklesCount, colors]);

  return (
    <div ref={containerRef} className={`relative inline-block ${className}`}>
      {children}
    </div>
  );
} 