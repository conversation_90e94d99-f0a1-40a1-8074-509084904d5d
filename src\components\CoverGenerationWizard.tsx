import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { CheckCircle, Circle, Sparkles, Upload, Image as ImageIcon, Palette, Globe, Type, Wand2, CreditCard, ChevronLeft, ChevronRight, AlertTriangle, FolderOpen } from 'lucide-react'
import FileUpload from './FileUpload'
import PhotoTypeSelector from './PhotoTypeSelector'
import TemplateSelector from './TemplateSelector'
import LanguageSelector from './LanguageSelector'
import TitleGenerationToggle from './TitleGenerationToggle'
import CreativityLevelSelector from './CreativityLevelSelector'
import PricingModal from './PricingModal'
import GenderSelector from './GenderSelector'
import { PhotoType, StreamingPlatform, Language } from '../types'
import { useTranslation } from 'react-i18next'

interface WizardStep {
  id: string
  title: string
  description: string
  icon: React.ComponentType<any>
  completed: boolean
}

interface CoverGenerationWizardProps {
  user: { id: string; email?: string } | null
  selectedFile: File | null
  setSelectedFile: (file: File | null) => void
  photoType: PhotoType | null
  setPhotoType: (type: PhotoType) => void
  gender: 'male' | 'female' | null
  setGender: (gender: 'male' | 'female') => void
  streamingPlatform: StreamingPlatform | null
  setStreamingPlatform: (platform: StreamingPlatform) => void
  language: Language | null
  setLanguage: (language: Language) => void
  generateTitles: boolean
  setGenerateTitles: (generate: boolean) => void
  creativityLevel: 'criativo' | 'rostoFiel' | 'estrito'
  setCreativityLevel: (level: 'criativo' | 'rostoFiel' | 'estrito') => void
  creativityLevelSelected?: boolean
  userName: string
  setUserName: (name: string) => void
  onGenerate: () => void
  onOpenGallery?: () => void  // Nova prop adicionada
  canGenerate: boolean
  isGenerating: boolean
  hasAvailableCredits?: boolean
  availableCredits?: number
  creditsNeeded?: number
  onSelectPlan?: (planId: string) => void
  paymentLoading?: boolean
  onStepChange: (step: number) => void
  onClose: () => void
  loadPreviousGeneration: () => Promise<any>
  hasExistingGenerations?: boolean
  initialStep?: number
  onStartFromScratch?: () => void
}

export default function CoverGenerationWizard({
  user,
  selectedFile,
  setSelectedFile,
  photoType,
  setPhotoType,
  gender,
  setGender,
  streamingPlatform,
  setStreamingPlatform,
  language,
  setLanguage,
  generateTitles,
  setGenerateTitles,
  creativityLevel,
  setCreativityLevel,
  creativityLevelSelected = false,
  userName,
  setUserName,
  onGenerate,
  onOpenGallery,  // Nova prop adicionada
  canGenerate,
  isGenerating,
  hasAvailableCredits = false,
  availableCredits = 0,
  creditsNeeded = 0,
  onSelectPlan,
  paymentLoading = false,
  onStepChange,
  onClose,
  loadPreviousGeneration,
  hasExistingGenerations = false,
  initialStep = 0,
  onStartFromScratch,
}: CoverGenerationWizardProps) {
  const { t } = useTranslation()
  const [currentStep, setCurrentStep] = useState(initialStep)
  const [showPricingModal, setShowPricingModal] = useState(false)
  const [error, setError] = useState('')

  // Force currentStep to match initialStep on mount
  useEffect(() => {
    console.log(`🔄 Inicializando wizard com step ${initialStep}`)
    setCurrentStep(initialStep)
  }, []) // Empty dependency array ensures this only runs on mount

  // Update currentStep when initialStep changes
  useEffect(() => {
    if (initialStep !== undefined) {
      console.log(`🔄 Atualizando step para ${initialStep} (externo)`)
      setCurrentStep(initialStep)
    }
  }, [initialStep])
  
  // Debug logging for render
  useEffect(() => {
    console.log(`🔄 Renderizando wizard - currentStep: ${currentStep}, initialStep: ${initialStep}`)
  }, [currentStep, initialStep])

  // Removido o avanço automático - o usuário deve clicar em "Próximo" manualmente
  // useEffect(() => {
  //   if (currentStep === 1 && selectedFile) {
  //     nextStep();
  //   }
  // }, [selectedFile, currentStep]);

  const handleLoadPrevious = async () => {
    const generationData = await loadPreviousGeneration();
    if (generationData) {
      setPhotoType(generationData.photo_type || 'individual');
      setStreamingPlatform(generationData.streaming_platform || 'netflix');
      setLanguage(generationData.language || 'portuguese');
      setGenerateTitles(generationData.generate_titles !== false);
      setUserName(generationData.user_name || user?.email?.split('@')[0] || '');
      setGender(generationData.gender || null);
      onClose();
    }
  };

  // Nova etapa inicial de escolha
  const InitialChoiceStep = () => (
    <div className="text-center flex flex-col items-center justify-center h-full w-full max-w-2xl mx-auto">
        <div className="flex flex-col items-center justify-center space-y-8">
          <h3 className="text-4xl font-black text-brand-text">{t('wizard.steps.initialChoice.howToStart')}</h3>
          <p className="text-brand-text/80 max-w-md mx-auto text-lg">
              {hasExistingGenerations 
                ? t('wizard.steps.initialChoice.withExisting')
                : t('wizard.steps.initialChoice.firstTime')
              }
          </p>
          <div className="flex flex-col md:flex-row gap-6 w-full max-w-lg">
              <button
                  onClick={() => {
                    // Call parent function to clear generation data
                    if (onStartFromScratch) {
                      onStartFromScratch();
                    }
                    
                    // Reset all form data when starting from scratch
                    setSelectedFile(null);
                    setPhotoType(null);
                    setGender(null);
                    setStreamingPlatform(null);
                    setLanguage(null);
                    setGenerateTitles(true);
                    setUserName('');
                    setCreativityLevel('criativo');
                    setCurrentStep(1);
                  }}
                  className="flex-1 bg-brand-primary text-brand-black font-bold py-6 px-8 rounded-lg border-2 border-brand-black shadow-brutal-lg hover:shadow-brutal-hover active:shadow-brutal-pressed transition-all text-xl"
              >
                  {t('wizard.steps.initialChoice.startFromScratch')}
              </button>
              {hasExistingGenerations && (
                <button
                    onClick={handleLoadPrevious}
                    className="flex-1 bg-brand-white text-brand-black font-bold py-6 px-8 rounded-lg border-2 border-brand-black shadow-brutal-lg hover:shadow-brutal-hover active:shadow-brutal-pressed transition-all text-xl"
                >
                    {t('wizard.steps.initialChoice.loadGeneration')}
                </button>
              )}
          </div>
          {!hasExistingGenerations && (
            <div className="p-4 bg-blue-100 border-2 border-blue-400 rounded-lg max-w-md">
              <p className="text-sm text-blue-800">
                💡 <strong>{t('wizard.steps.initialChoice.firstTimeNotice')}</strong> {t('wizard.steps.initialChoice.firstTimeDesc')}
              </p>
            </div>
          )}
        </div>
    </div>
  );

  const needsPayment = !hasAvailableCredits || availableCredits < creditsNeeded
  
  const steps: WizardStep[] = [
    {
      id: 'initial-choice',
      title: t('wizard.steps.initialChoice.title'),
      description: t('wizard.steps.initialChoice.description'),
      icon: Sparkles,
      completed: false
    },
    {
      id: 'upload',
      title: t('wizard.steps.upload.title'),
      description: t('wizard.steps.upload.description'),
      icon: Upload,
      completed: !!selectedFile
    },
    {
      id: 'photo-type',
      title: t('wizard.steps.photoType.title'),
      description: t('wizard.steps.photoType.description'),
      icon: ImageIcon,
      completed: !!photoType
    },
    ...(photoType === 'individual' ? [{
      id: 'gender',
      title: t('wizard.steps.gender.title'),
      description: t('wizard.steps.gender.description'),
      icon: Type,
      completed: !!gender,
    }] as WizardStep[] : []),
    {
      id: 'platform',
      title: t('wizard.steps.platform.title'),
      description: t('wizard.steps.platform.description'),
      icon: Palette,
      completed: !!streamingPlatform
    },
    {
      id: 'creativity',
      title: t('wizard.steps.creativity.title'),
      description: t('wizard.steps.creativity.description'),
      icon: Wand2,
      completed: creativityLevelSelected
    },
    {
      id: 'language',
      title: t('wizard.steps.language.title'),
      description: t('wizard.steps.language.description'),
      icon: Globe,
      completed: !!language
    },
    {
      id: 'titles',
      title: t('wizard.steps.titles.title'),
      description: t('wizard.steps.titles.description'),
      icon: Type,
      completed: !!userName.trim()
    },
    ...(needsPayment ? [{
      id: 'payment',
      title: t('wizard.steps.payment.title'),
      description: t('wizard.steps.payment.description'),
      icon: CreditCard,
      completed: hasAvailableCredits && availableCredits >= creditsNeeded
    }] : []),
    {
      id: 'generate',
      title: t('wizard.steps.generate.title'),
      description: t('wizard.steps.generate.description'),
      icon: Wand2,
      completed: false
    }
  ]

  const allStepsCompleted = steps.slice(0, -1).every(step => step.completed)
  const progressPercentage = (steps.filter(step => step.completed).length / (steps.length -1)) * 100

  const goToStep = (stepIndex: number) => {
    if (stepIndex <= currentStep || steps[stepIndex - 1]?.completed) {
      if (stepIndex >= 0 && stepIndex < steps.length) {
        setCurrentStep(stepIndex)
      }
    }
  }

  const nextStep = () => {
    if (currentStep < steps.length - 1) {
      if (steps[currentStep].completed) {
        setCurrentStep(currentStep + 1)
      }
    }
  }

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1)
    }
  }

  const canGoNext = () => {
    const currentStepData = steps[currentStep]
    return currentStepData?.completed || currentStep === steps.length - 1
  }

  const handlePlanSelect = (planId: string) => {
    if (onSelectPlan) {
      onSelectPlan(planId)
      setShowPricingModal(false)
    }
  }

  const getStepContent = () => {
    // Direct check for step 1 (upload) to ensure it's always shown when needed
    if (currentStep === 1) {
      console.log('🔄 Forçando exibição do passo de upload (step 1)')
      return (
        <div className="w-full">
          <FileUpload
            onFileSelect={(file) => setSelectedFile(file)}
            selectedFile={selectedFile}
            onRemoveFile={() => setSelectedFile(null)}
            userId={user?.id || null}
          />
        </div>
      )
    }
    
    const step = steps[currentStep]
    if (!step) return null

    switch (step.id) {
      case 'initial-choice':
        return <InitialChoiceStep key={currentStep} />;

      case 'upload':
        return (
          <div className="w-full">
            <FileUpload
              onFileSelect={(file) => setSelectedFile(file)}
              selectedFile={selectedFile}
              onRemoveFile={() => setSelectedFile(null)}
              userId={user?.id || null}
            />
          </div>
        )

      case 'photo-type':
        return (
          <div className="w-full">
            <PhotoTypeSelector
              selectedType={photoType}
              onSelect={setPhotoType}
            />
          </div>
        )

      case 'gender':
        return (
          <div className="w-full">
            <GenderSelector
              selectedGender={gender}
              onSelectGender={setGender}
            />
          </div>
        )

      case 'platform':
        return (
          <div className="w-full">
            <TemplateSelector
              selectedTemplate={streamingPlatform || ''}
              onTemplateChange={(template) => setStreamingPlatform(template as any)}
            />
          </div>
        )

      case 'creativity':
        return (
          <div className="w-full">
            <CreativityLevelSelector
              selectedLevel={creativityLevel}
              onSelect={setCreativityLevel}
            />
          </div>
        )

      case 'language':
        return (
          <div className="w-full">
            <LanguageSelector
              selectedLanguage={language}
              onSelect={setLanguage}
            />
          </div>
        )

      case 'titles':
        return (
          <motion.div initial={{ opacity: 0, x: 10 }} animate={{ opacity: 1, x: 0 }}>
            <div className="text-center mb-8">
              <div className="w-16 h-16 bg-brand-secondary mx-auto rounded-xl flex items-center justify-center border-2 border-brand-black mb-4">
                <Type className="w-8 h-8 text-brand-black" />
              </div>
              <h3 className="text-3xl font-black text-brand-text mb-2">{t('wizard.steps.titles.titleOptions')}</h3>
              <p className="text-brand-text/80">{t('wizard.steps.titles.configureTitle')}</p>
            </div>

            <div className="max-w-lg mx-auto space-y-6">
              <div>
                <label htmlFor="userName" className="text-lg font-bold text-brand-text mb-2 block">{t('wizard.steps.titles.yourName')}</label>
                <input
                  type="text"
                  id="userName"
                  value={userName}
                  onChange={(e) => setUserName(e.target.value)}
                  placeholder={t('wizard.steps.titles.namePlaceholder')}
                  className="w-full p-4 bg-brand-white border-2 border-brand-black rounded-lg text-lg font-semibold text-brand-text placeholder:text-brand-text/50 focus:ring-2 focus:ring-brand-accent focus:border-brand-accent shadow-brutal-sm"
                />
                <p className="text-sm text-brand-text/70 mt-2">{t('wizard.steps.titles.nameDescription')}</p>
              </div>

              <div className="bg-brand-white p-6 rounded-lg border-2 border-brand-black shadow-brutal-sm space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-bold text-brand-text text-lg">{t('wizard.steps.titles.titleGeneration')}</h4>
                    <p className="text-sm text-brand-text/80">{t('wizard.steps.titles.titleGenerationDesc')}</p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={generateTitles}
                      onChange={() => setGenerateTitles(!generateTitles)}
                      className="sr-only peer"
                    />
                    <div className="w-14 h-8 bg-gray-200 rounded-full peer peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-brand-accent/50 dark:peer-focus:ring-brand-accent dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-1 after:left-[4px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-6 after:w-6 after:transition-all dark:border-gray-600 peer-checked:bg-brand-accent"></div>
                  </label>
                </div>
                
                {generateTitles && (
                  <div className="bg-yellow-100 border-2 border-yellow-400 text-yellow-800 p-4 rounded-lg flex items-start space-x-3">
                    <AlertTriangle className="w-6 h-6 text-yellow-600 flex-shrink-0" />
                    <div>
                      <h5 className="font-bold">{t('wizard.steps.titles.importantNotice')}</h5>
                      <p className="text-sm">{t('wizard.steps.titles.aiWarning')}</p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </motion.div>
        )

      case 'payment':
        return (
          <motion.div initial={{ opacity: 0, x: 10 }} animate={{ opacity: 1, x: 0 }} className="text-center">
            <div className="w-24 h-24 bg-brand-secondary mx-auto rounded-2xl flex items-center justify-center border-2 border-brand-black mb-6 shadow-brutal">
              <CreditCard className="w-12 h-12 text-brand-black" />
            </div>
            <h3 className="text-4xl font-black text-brand-text mb-3">{t('wizard.steps.payment.buyCredits')}</h3>
            <p className="text-brand-text/80 max-w-md mx-auto mb-4">
              {t('wizard.steps.payment.needCredits', { count: creditsNeeded })}
            </p>
            <p className="text-brand-text/80 max-w-md mx-auto mb-8">
              {t('wizard.steps.payment.haveCredits', { count: availableCredits })}
            </p>
            <button
              onClick={() => setShowPricingModal(true)}
              className="bg-brand-primary text-brand-black font-bold py-4 px-8 rounded-lg border-2 border-brand-black shadow-brutal-lg hover:shadow-brutal-hover active:shadow-brutal-pressed transition-all text-xl"
            >
              {t('wizard.steps.payment.buyCredits')}
            </button>
            <p className="text-sm text-brand-text/70 mt-4">
              {t('wizard.steps.payment.securePayment')}
            </p>
          </motion.div>
        )

      case 'generate':
        return (
          <motion.div initial={{ opacity: 0, x: 10 }} animate={{ opacity: 1, x: 0 }} className="text-center">
            <div className="w-24 h-24 bg-brand-primary mx-auto rounded-2xl flex items-center justify-center border-2 border-brand-black mb-6 shadow-brutal">
              <Wand2 className="w-12 h-12 text-brand-black" />
            </div>
            <h3 className="text-4xl font-black text-brand-text mb-3">{t('wizard.steps.generate.readyToGenerate')}</h3>
            <p className="text-brand-text/80 max-w-md mx-auto mb-4">
              {t('wizard.steps.generate.allReady', { count: creditsNeeded })}
            </p>
            <p className="text-brand-text/80 max-w-md mx-auto mb-8">
              {t('wizard.steps.generate.haveCredits', { count: availableCredits })}
            </p>
            
            {/* Botões de Geração */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-6">
              <button
                onClick={onGenerate}
                disabled={!canGenerate || isGenerating}
                className="bg-brand-accent text-white font-bold py-4 px-8 rounded-lg border-2 border-brand-black shadow-brutal-lg hover:shadow-brutal-hover active:shadow-brutal-pressed transition-all text-xl flex items-center gap-3 min-w-[280px]"
              >
                <Wand2 className="w-6 h-6" />
                {isGenerating ? t('wizard.steps.generate.generating') : 'Gerar Cover com IA'}
              </button>
              
              <button
                onClick={() => {
                  if (onOpenGallery) {
                    onOpenGallery();
                  }
                }}
                disabled={isGenerating}
                className="bg-brand-secondary text-brand-black font-bold py-4 px-8 rounded-lg border-2 border-brand-black shadow-brutal-lg hover:shadow-brutal-hover active:shadow-brutal-pressed transition-all text-xl flex items-center gap-3 min-w-[280px]"
              >
                <FolderOpen className="w-6 h-6" />
                Gerar Cover com fotos da galeria
              </button>
            </div>
            
            <p className="text-sm text-brand-text/70 mt-4">
              {t('wizard.steps.generate.willGenerate', { count: streamingPlatform === 'netflix' ? 12 : streamingPlatform === 'disney' ? 9 : 11 })}
            </p>
          </motion.div>
        )

      default:
        return null
    }
  }

  return (
    <div className="w-full max-w-7xl mx-auto py-8">
      {/* Page Title */}
      <div className="text-center mb-12 px-4">
        <h2 className="text-4xl md:text-6xl font-black text-brand-text uppercase">
          {t('wizard.title')}
        </h2>
        <p className="text-lg text-brand-text/80 mt-2">
          {t('wizard.subtitle')}
        </p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-12 gap-8">
        {/* Left Nav */}
        <div className="md:col-span-3 lg:col-span-3">
          <div className="space-y-3 sticky top-24">
            {steps.map((step, index) => (
              <button
                key={step.id}
                onClick={() => goToStep(index)}
                disabled={index > currentStep && !steps[index - 1]?.completed}
                className={`w-full text-left p-4 rounded-lg border-2 border-brand-black transition-all duration-200 flex items-center space-x-4
                  ${currentStep === index
                    ? 'bg-brand-secondary shadow-brutal'
                    : 'bg-brand-white shadow-brutal-sm hover:shadow-brutal'
                  }
                  ${(index > currentStep && (!steps[index -1]?.completed)) ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
                `}
              >
                <div className={`w-10 h-10 rounded-md flex items-center justify-center border-2 border-brand-black
                  ${step.completed 
                    ? 'bg-brand-primary' 
                    : currentStep === index 
                      ? 'bg-brand-secondary' 
                      : 'bg-gray-200'
                  }
                `}>
                  {step.completed ? (
                    <CheckCircle className="w-6 h-6 text-brand-black"/>
                  ) : (
                    <step.icon className={`w-6 h-6 ${
                      step.completed 
                        ? 'text-brand-text' 
                        : currentStep === index 
                          ? 'text-brand-black' 
                          : 'text-gray-400'
                    }`} />
                  )}
                </div>
                <div>
                  <p className={`font-bold ${
                    step.completed 
                      ? 'text-brand-text' 
                      : currentStep === index 
                        ? 'text-brand-text' 
                        : 'text-gray-400'
                  }`}>{step.title}</p>
                  <p className={`text-sm ${
                    step.completed 
                      ? 'text-brand-text/70' 
                      : currentStep === index 
                        ? 'text-brand-text/70' 
                        : 'text-gray-400'
                  }`}>{step.description}</p>
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* Right Content */}
        <div className="md:col-span-9 lg:col-span-9">
          <div className="bg-brand-white rounded-2xl border-2 border-brand-black shadow-brutal-lg p-8 min-h-[700px] flex flex-col">
            {/* Progress Bar */}
            <div className="mb-8">
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-bold text-brand-text">{t('wizard.progress')}</span>
                <span className="text-sm font-bold text-brand-text">{Math.round(progressPercentage)}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-3 border-2 border-brand-black">
                <div 
                  className="bg-brand-primary h-full rounded-full transition-all duration-300 ease-out"
                  style={{ width: `${progressPercentage}%` }}
                ></div>
              </div>
            </div>

            {/* Step Content */}
            <div className="flex-grow flex items-center justify-center min-h-[400px] py-4">
              {getStepContent()}
            </div>

            {/* Navigation */}
            {currentStep > 0 && (
              <div className="flex justify-between items-center mt-8 pt-6 border-t-2 border-brand-black">
                <button
                  onClick={prevStep}
                  className="flex items-center space-x-2 bg-brand-white text-brand-text font-bold py-3 px-6 rounded-lg border-2 border-brand-black shadow-brutal-sm hover:shadow-brutal-hover active:shadow-brutal-pressed transition-all"
                >
                  <ChevronLeft className="w-5 h-5" />
                  <span>{t('wizard.previous')}</span>
                </button>

                {currentStep < steps.length - 1 && (
                  <button
                    onClick={nextStep}
                    disabled={!canGoNext()}
                    className="flex items-center space-x-2 bg-brand-primary text-brand-black font-bold py-3 px-6 rounded-lg border-2 border-brand-black shadow-brutal-sm hover:shadow-brutal-hover active:shadow-brutal-pressed transition-all disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <span>{t('wizard.next')}</span>
                    <ChevronRight className="w-5 h-5" />
                  </button>
                )}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Pricing Modal */}
      {showPricingModal && (
        <PricingModal
          isOpen={showPricingModal}
          onClose={() => setShowPricingModal(false)}
          onSelectPlan={handlePlanSelect}
          loading={paymentLoading}
          currentCredits={availableCredits}
          user={user}
          onVoucherRedeemed={() => {
            // Recarregar créditos após resgatar voucher
            window.location.reload();
          }}
        />
      )}
    </div>
  )
}