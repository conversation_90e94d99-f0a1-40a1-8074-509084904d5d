<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste de Pagamento Local</title>
    <script src="https://js.stripe.com/v3/"></script>
</head>
<body>
    <h1>Teste de Pagamento Local</h1>
    <button id="test-payment">Testar Pagamento</button>
    <div id="result"></div>

    <script>
        const stripe = Stripe('pk_test_51PlGJpDOvhLb9hmWCxdc7fFHVqc3Cifn2KCfgLjhFYwiQKy8sxgYdqCdcdUFENebYdTueAbOrfVP2vrNzVUf5auY00mIuAepTw');
        
        document.getElementById('test-payment').addEventListener('click', async () => {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testando...';
            
            try {
                // Teste 1: Verificar se o Supabase está acessível
                const supabaseUrl = 'https://uptmptfpumgrnlxukwau.supabase.co';
                const response = await fetch(`${supabaseUrl}/rest/v1/`, {
                    headers: {
                        'apikey': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVwdG1wdGZwdW1ncm5seHVrd2F1Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk0Nzc0MzgsImV4cCI6MjA2NTA1MzQzOH0.pm_3eXs3HkgrSMRMqq6Dw53kDKO5Xi_eL4LfksfvL7w'
                    }
                });
                
                if (response.ok) {
                    resultDiv.innerHTML += '<br>✅ Supabase acessível';
                } else {
                    resultDiv.innerHTML += '<br>❌ Erro no Supabase: ' + response.status;
                }
                
                // Teste 2: Verificar se a Edge Function está acessível
                const edgeFunctionUrl = `${supabaseUrl}/functions/v1/create-payment-intent`;
                const testPayload = {
                    amount: 4990,
                    currency: 'brl',
                    planId: 'starter_credits',
                    userId: 'test-user-id',
                    credits: 30,
                    productId: 'prod_SVeFgga19mY0Gl',
                    planType: 'credits',
                    metadata: {
                        planName: 'Starter Credits',
                        userId: 'test-user-id',
                        credits: '30',
                        productId: 'prod_SVeFgga19mY0Gl',
                        planType: 'credits'
                    }
                };
                
                const edgeResponse = await fetch(edgeFunctionUrl, {
                    method: 'POST',
                    headers: {
                        'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVwdG1wdGZwdW1ncm5seHVrd2F1Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk0Nzc0MzgsImV4cCI6MjA2NTA1MzQzOH0.pm_3eXs3HkgrSMRMqq6Dw53kDKO5Xi_eL4LfksfvL7w',
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(testPayload)
                });
                
                if (edgeResponse.ok) {
                    const edgeData = await edgeResponse.json();
                    resultDiv.innerHTML += '<br>✅ Edge Function acessível';
                    resultDiv.innerHTML += '<br>📝 Client Secret: ' + (edgeData.clientSecret ? 'Recebido' : 'Não recebido');
                } else {
                    const errorText = await edgeResponse.text();
                    resultDiv.innerHTML += '<br>❌ Erro na Edge Function: ' + edgeResponse.status + ' - ' + errorText;
                }
                
            } catch (error) {
                resultDiv.innerHTML += '<br>❌ Erro geral: ' + error.message;
            }
        });
    </script>
</body>
</html>