import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { supabase } from '../lib/supabase';
import { showToast } from '../utils/toast';

export default function AuthCallback() {
  const { t } = useTranslation();
  const navigate = useNavigate();

  useEffect(() => {
    const handleAuth = async () => {
      try {
        // Check for existing session
        const { data: { session }, error } = await supabase.auth.getSession();
        
        if (error) throw error;
        
        if (session) {
          showToast.success(t('authCallback.success'));
          // Redirect to dashboard or intended URL
          const returnTo = sessionStorage.getItem('returnTo') || '/dashboard';
          sessionStorage.removeItem('returnTo');
          navigate(returnTo);
        } else {
          // If no session, redirect to login
          navigate('/login');
        }
      } catch (error) {
        console.error('Auth error:', error);
        showToast.error(t('authCallback.error'));
        navigate('/login');
      }
    };

    handleAuth();
  }, [navigate, t]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
        <p className="text-gray-600">{t('authCallback.processing')}</p>
      </div>
    </div>
  );
}
