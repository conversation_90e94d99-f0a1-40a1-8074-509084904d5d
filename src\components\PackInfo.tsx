import React from 'react'
import { useTranslation } from 'react-i18next'
import { Package, Check, AlertCircle, RotateCcw, Printer } from 'lucide-react'

interface PackInfoProps {
  className?: string
}

export default function PackInfo({ className = '' }: PackInfoProps) {
  const { t } = useTranslation()

  return (
    <div className={`bg-blue-50 border border-blue-200 rounded-lg p-4 ${className}`}>
      <div className="flex items-start space-x-3">
        <Package className="w-6 h-6 text-blue-600 flex-shrink-0 mt-1" />
        <div className="flex-1">
          <h3 className="font-semibold text-blue-800 mb-2">{t('packInfo.title')}</h3>
          
          <div className="space-y-3 text-sm text-blue-700">
            <div className="flex items-start space-x-2">
              <Check className="w-4 h-4 text-green-600 flex-shrink-0 mt-0.5" />
              <div>
                <span className="font-medium">{t('packInfo.eachPackIncludes')}</span>
                <ul className="ml-2 mt-1 space-y-1">
                  <li>{t('packInfo.includesPlatform')}</li>
                  <li>{t('packInfo.includesCovers')}</li>
                  <li>{t('packInfo.includesPoster')}</li>
                </ul>
              </div>
            </div>
            
            <div className="flex items-start space-x-2">
              <RotateCcw className="w-4 h-4 text-blue-600 flex-shrink-0 mt-0.5" />
              <div>
                <span className="font-medium">{t('packInfo.coverRegen')}</span>
                <p className="ml-2 mt-1">{t('packInfo.coverRegenDesc')}</p>
              </div>
            </div>
            
            <div className="flex items-start space-x-2">
              <AlertCircle className="w-4 h-4 text-orange-600 flex-shrink-0 mt-0.5" />
              <div>
                <span className="font-medium text-orange-700">{t('packInfo.finalPoster')}</span>
                <p className="ml-2 mt-1 text-orange-700">{t('packInfo.finalPosterDesc')}</p>
              </div>
            </div>
            
            <div className="flex items-start space-x-2">
              <Printer className="w-4 h-4 text-purple-600 flex-shrink-0 mt-0.5" />
              <div>
                <span className="font-medium">{t('packInfo.printablePoster')}</span>
                <p className="ml-2 mt-1">{t('packInfo.printablePosterDesc')}</p>
              </div>
            </div>
          </div>
          
          <div className="mt-3 p-2 bg-blue-100 rounded text-xs text-blue-800">
            💡 <strong>{t('packInfo.tip')}</strong> {t('packInfo.tipText')}
          </div>
        </div>
      </div>
    </div>
  )
} 