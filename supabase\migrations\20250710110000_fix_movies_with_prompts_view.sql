-- Drop the existing view if it exists
DROP VIEW IF EXISTS public.movies_with_prompts;

-- Recreate the view with fixed movie_generation_stats column names
CREATE OR REPLACE VIEW public.movies_with_prompts AS
SELECT
    m.id,
    m.title,
    m.streaming_platform,
    m.is_active,
    m.created_at,
    m.updated_at,
    COALESCE(m.default_creativity_level, 'Equilibrado') AS default_creativity_level,
    
    -- Base prompt
    COALESCE(
        m.base_prompt_override,
        (SELECT pt.template_text FROM prompt_templates pt WHERE pt.id = m.base_prompt_template_id)
    ) AS base_prompt,
    
    -- Safe prompt
    COALESCE(
        m.safe_prompt_override,
        (SELECT pt.template_text FROM prompt_templates pt WHERE pt.id = m.safe_prompt_template_id)
    ) AS safe_prompt,
    
    -- Male prompt
    COALESCE(
        m.gender_male_prompt_override,
        (SELECT pt.template_text FROM prompt_templates pt WHERE pt.id = m.gender_male_prompt_template_id)
    ) AS gender_male_prompt,
    
    -- Female prompt
    COALESCE(
        m.gender_female_prompt_override,
        (SELECT pt.template_text FROM prompt_templates pt WHERE pt.id = m.gender_female_prompt_template_id)
    ) AS gender_female_prompt,
    
    -- Couple prompt
    COALESCE(
        m.couple_prompt_override,
        (SELECT pt.template_text FROM prompt_templates pt WHERE pt.id = m.couple_prompt_template_id)
    ) AS couple_prompt,
    
    -- Template names for reference
    (SELECT pt.name FROM prompt_templates pt WHERE pt.id = m.base_prompt_template_id) AS base_template_name,
    (SELECT pt.name FROM prompt_templates pt WHERE pt.id = m.safe_prompt_template_id) AS safe_template_name,
    (SELECT pt.name FROM prompt_templates pt WHERE pt.id = m.gender_male_prompt_template_id) AS male_template_name,
    (SELECT pt.name FROM prompt_templates pt WHERE pt.id = m.gender_female_prompt_template_id) AS female_template_name,
    (SELECT pt.name FROM prompt_templates pt WHERE pt.id = m.couple_prompt_template_id) AS couple_template_name,
    
    -- Original template IDs and variables
    m.base_prompt_template_id,
    m.base_prompt_variables,
    m.safe_prompt_template_id,
    m.safe_prompt_variables,
    m.gender_male_prompt_template_id,
    m.gender_male_prompt_variables,
    m.gender_female_prompt_template_id,
    m.gender_female_prompt_variables,
    m.couple_prompt_template_id,
    m.couple_prompt_variables,
    
    -- Stats linking
    m.id as movie_id
FROM
    public.movies m; 