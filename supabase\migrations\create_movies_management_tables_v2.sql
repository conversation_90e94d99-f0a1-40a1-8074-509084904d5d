-- Create prompt templates table for reusable components
CREATE TABLE IF NOT EXISTS prompt_templates (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    template_text TEXT NOT NULL,
    description TEXT,
    variables JSONB DEFAULT '[]', -- Array of variable names like ["C<PERSON><PERSON><PERSON><PERSON>", "SERIES_NAME"]
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create movies table to replace the hardcoded movieDatabase
CREATE TABLE IF NOT EXISTS movies (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    streaming_platform VARCHAR(50) NOT NULL CHECK (streaming_platform IN ('netflix', 'disney', 'amazon')),
    
    -- Main prompts using template system
    base_prompt_template_id UUID REFERENCES prompt_templates(id),
    base_prompt_variables JSONB DEFAULT '{}', -- Key-value pairs for template variables
    
    -- Alternative prompts
    safe_prompt_template_id UUID REFERENCES prompt_templates(id),
    safe_prompt_variables JSONB DEFAULT '{}',
    
    gender_male_prompt_template_id UUID REFERENCES prompt_templates(id),
    gender_male_prompt_variables JSONB DEFAULT '{}',
    
    gender_female_prompt_template_id UUID REFERENCES prompt_templates(id),
    gender_female_prompt_variables JSONB DEFAULT '{}',
    
    couple_prompt_template_id UUID REFERENCES prompt_templates(id),
    couple_prompt_variables JSONB DEFAULT '{}',
    
    -- Direct prompt overrides (for custom cases)
    base_prompt_override TEXT,
    safe_prompt_override TEXT,
    gender_male_prompt_override TEXT,
    gender_female_prompt_override TEXT,
    couple_prompt_override TEXT,
    
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES auth.users(id),
    UNIQUE(title, streaming_platform)
);

-- Create movie generation statistics table
CREATE TABLE IF NOT EXISTS movie_generation_stats (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    movie_id UUID REFERENCES movies(id) ON DELETE CASCADE,
    total_attempts INTEGER DEFAULT 0,
    successful_attempts INTEGER DEFAULT 0,
    failed_attempts INTEGER DEFAULT 0,
    e005_errors INTEGER DEFAULT 0, -- Sensitive content errors
    last_attempt_at TIMESTAMP WITH TIME ZONE,
    last_error_message TEXT,
    success_rate DECIMAL(5,2) GENERATED ALWAYS AS (
        CASE 
            WHEN total_attempts > 0 THEN (successful_attempts::DECIMAL / total_attempts::DECIMAL) * 100
            ELSE 0
        END
    ) STORED,
    UNIQUE(movie_id)
);

-- Function to resolve prompt from template + variables or override
CREATE OR REPLACE FUNCTION resolve_movie_prompt(
    p_template_id UUID,
    p_variables JSONB,
    p_override TEXT
)
RETURNS TEXT AS $$
DECLARE
    template_text TEXT;
    variable_key TEXT;
    variable_value TEXT;
    result_prompt TEXT;
BEGIN
    -- If override exists, use it
    IF p_override IS NOT NULL AND p_override != '' THEN
        RETURN p_override;
    END IF;
    
    -- If no template, return null
    IF p_template_id IS NULL THEN
        RETURN NULL;
    END IF;
    
    -- Get template text
    SELECT template_text INTO template_text 
    FROM prompt_templates 
    WHERE id = p_template_id;
    
    IF template_text IS NULL THEN
        RETURN NULL;
    END IF;
    
    result_prompt := template_text;
    
    -- Replace variables in template
    FOR variable_key, variable_value IN SELECT * FROM jsonb_each_text(p_variables)
    LOOP
        result_prompt := replace(result_prompt, '{' || variable_key || '}', variable_value);
    END LOOP;
    
    RETURN result_prompt;
END;
$$ LANGUAGE plpgsql;

-- Function to get all resolved prompts for a movie
CREATE OR REPLACE FUNCTION get_movie_prompts(p_movie_id UUID)
RETURNS TABLE(
    base_prompt TEXT,
    safe_prompt TEXT,
    gender_male_prompt TEXT,
    gender_female_prompt TEXT,
    couple_prompt TEXT
) AS $$
DECLARE
    movie_record RECORD;
BEGIN
    SELECT * INTO movie_record FROM movies WHERE id = p_movie_id;
    
    IF NOT FOUND THEN
        RETURN;
    END IF;
    
    RETURN QUERY SELECT
        resolve_movie_prompt(movie_record.base_prompt_template_id, movie_record.base_prompt_variables, movie_record.base_prompt_override),
        resolve_movie_prompt(movie_record.safe_prompt_template_id, movie_record.safe_prompt_variables, movie_record.safe_prompt_override),
        resolve_movie_prompt(movie_record.gender_male_prompt_template_id, movie_record.gender_male_prompt_variables, movie_record.gender_male_prompt_override),
        resolve_movie_prompt(movie_record.gender_female_prompt_template_id, movie_record.gender_female_prompt_variables, movie_record.gender_female_prompt_override),
        resolve_movie_prompt(movie_record.couple_prompt_template_id, movie_record.couple_prompt_variables, movie_record.couple_prompt_override);
END;
$$ LANGUAGE plpgsql;

-- Function to update movie stats
CREATE OR REPLACE FUNCTION update_movie_stats(
    p_movie_title VARCHAR(255),
    p_streaming_platform VARCHAR(50),
    p_success BOOLEAN,
    p_error_message TEXT DEFAULT NULL
)
RETURNS VOID AS $$
DECLARE
    v_movie_id UUID;
BEGIN
    -- Get movie ID
    SELECT id INTO v_movie_id 
    FROM movies 
    WHERE title = p_movie_title AND streaming_platform = p_streaming_platform;
    
    IF v_movie_id IS NULL THEN
        RETURN; -- Movie not found, skip stats update
    END IF;
    
    -- Insert or update stats
    INSERT INTO movie_generation_stats (movie_id, total_attempts, successful_attempts, failed_attempts, e005_errors, last_attempt_at, last_error_message)
    VALUES (
        v_movie_id,
        1,
        CASE WHEN p_success THEN 1 ELSE 0 END,
        CASE WHEN p_success THEN 0 ELSE 1 END,
        CASE WHEN p_error_message LIKE '%E005%' THEN 1 ELSE 0 END,
        NOW(),
        p_error_message
    )
    ON CONFLICT (movie_id) DO UPDATE SET
        total_attempts = movie_generation_stats.total_attempts + 1,
        successful_attempts = movie_generation_stats.successful_attempts + CASE WHEN p_success THEN 1 ELSE 0 END,
        failed_attempts = movie_generation_stats.failed_attempts + CASE WHEN p_success THEN 0 ELSE 1 END,
        e005_errors = movie_generation_stats.e005_errors + CASE WHEN p_error_message LIKE '%E005%' THEN 1 ELSE 0 END,
        last_attempt_at = NOW(),
        last_error_message = CASE WHEN NOT p_success THEN p_error_message ELSE movie_generation_stats.last_error_message END;
END;
$$ LANGUAGE plpgsql;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_movies_platform ON movies(streaming_platform);
CREATE INDEX IF NOT EXISTS idx_movies_active ON movies(is_active);
CREATE INDEX IF NOT EXISTS idx_movie_stats_success_rate ON movie_generation_stats(success_rate);
CREATE INDEX IF NOT EXISTS idx_movie_stats_e005 ON movie_generation_stats(e005_errors);
CREATE INDEX IF NOT EXISTS idx_prompt_templates_name ON prompt_templates(name);

-- Enable RLS
ALTER TABLE prompt_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE movies ENABLE ROW LEVEL SECURITY;
ALTER TABLE movie_generation_stats ENABLE ROW LEVEL SECURITY;

-- RLS Policies for prompt_templates
CREATE POLICY "Admin can manage prompt templates" ON prompt_templates
    FOR ALL USING (
        auth.jwt() ->> 'user_metadata' ->> 'role' = 'admin'
    );

CREATE POLICY "Users can read prompt templates" ON prompt_templates
    FOR SELECT USING (true);

-- RLS Policies for movies table
CREATE POLICY "Admin can manage movies" ON movies
    FOR ALL USING (
        auth.jwt() ->> 'user_metadata' ->> 'role' = 'admin'
    );

CREATE POLICY "Users can read active movies" ON movies
    FOR SELECT USING (is_active = true);

-- RLS Policies for movie stats table
CREATE POLICY "Admin can manage movie stats" ON movie_generation_stats
    FOR ALL USING (
        auth.jwt() ->> 'user_metadata' ->> 'role' = 'admin'
    );

CREATE POLICY "Users can read movie stats" ON movie_generation_stats
    FOR SELECT USING (true); 