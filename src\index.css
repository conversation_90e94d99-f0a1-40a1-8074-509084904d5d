@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;700;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --bg: white;
  --text: #111111;
}

html, body {
  background-color: var(--bg);
  color: var(--text);
}

/* Force light theme for all daisyUI elements */
:root {
  color-scheme: light only;
}

[data-theme="light"] {
  background-color: white !important;
  color: #111111 !important;
}

.btn-ghost {
  background-color: transparent !important;
}

.btn {
  color: inherit;
}

@layer base {
  body {
    @apply bg-brand-bg text-brand-text font-sans;
  }
}

/* Custom animations */
.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-4000 {
  animation-delay: 4s;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Removing custom scrollbar for neo-brutalism style */
/*
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}
*/
