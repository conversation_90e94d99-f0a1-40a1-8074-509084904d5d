import { supabase } from './supabase';

export interface PosterData {
  TITLE?: string;
  DESCRIPTION?: string;
  CONTINUE?: string;
  ID: string; // Template ID do Canva
  CAPA?: string;
  AVATAR?: string;
  [key: string]: string | undefined; // Para campos PHOTO_01, PHOTO_02, etc.
}

export interface CanvaPosterResponse {
  id: string;
  status: 'processing' | 'completed' | 'failed';
  designUrl?: string;
  templateId: string;
}

// Função para gerar o poster usando o serviço do Canva
export async function generateCanvaPoster(data: PosterData): Promise<CanvaPosterResponse> {
  try {
    const { data: responseData, error } = await supabase.functions.invoke('canva-poster', {
      body: data,
    });

    if (error) {
      console.error('Erro ao gerar poster:', error);
      throw new Error(error.message);
    }

    return responseData;
  } catch (err) {
    console.error('Erro na geração do poster Canva:', err);
    throw err;
  }
}

// Função para verificar o status de uma geração de poster
export async function checkPosterStatus(id: string): Promise<any> {
  try {
    // Uso da URL para passar o id como parâmetro
    const { data, error } = await supabase.functions.invoke(`canva-poster?id=${id}`, {
      method: 'GET',
    });

    if (error) {
      console.error('Erro ao verificar status do poster:', error);
      throw new Error(error.message);
    }

    return data;
  } catch (err) {
    console.error('Erro ao verificar status:', err);
    throw err;
  }
}
