import React, { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { signInWithGoogle, signInWithEmail, signUpWithEmail } from '../lib/supabase'
import { showToast } from '../utils/toast'
import <PERSON><PERSON><PERSON>ackground from './PatternBackground'

interface AuthFormProps {
  onAuthSuccess?: (user: any) => void
}

export default function AuthForm({ onAuthSuccess }: AuthFormProps) {
  const { t } = useTranslation()
  const [mode, setMode] = useState<'login' | 'signup'>('login')
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [fullName, setFullName] = useState('')
  const [loading, setLoading] = useState(false)

  const handleGoogleLogin = async () => {
    setLoading(true)
    try {
      await signInWithGoogle()
      showToast.success(t('authForm.googleRedirect'))
    } catch (error: any) {
      showToast.error(error.message || t('authForm.googleError'))
    } finally {
      setLoading(false)
    }
  }

  const handleEmailAuth = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    try {
      if (mode === 'login') {
        const data = await signInWithEmail(email, password)
        showToast.success(t('authForm.loginSuccess'))
        onAuthSuccess?.(data.user)
      } else {
        const data = await signUpWithEmail(email, password, fullName)
        showToast.success(t('authForm.signupSuccess'))
        onAuthSuccess?.(data.user)
      }
    } catch (error: any) {
      showToast.error(error.message || t('authForm.authError'))
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center p-4 relative">
      <PatternBackground />
      <div className="relative w-full max-w-3xl mx-auto px-10 py-12 bg-white shadow-brutal-lg border-2 border-black z-10">
      {/* Decorative elements */}
      <div className="absolute -top-3 -left-3 w-6 h-6 bg-pink-500 border-2 border-black"></div>
      <div className="absolute -bottom-3 -right-3 w-6 h-6 bg-yellow-400 border-2 border-black"></div>
      
      <div className="text-center mb-8">
        <div className="flex justify-center mb-6">
          <img src="/img/logo-final-svg.png" alt="Logo" className="h-24 w-auto" />
        </div>
        <p className="text-gray-700 font-medium">
          {mode === 'login' 
            ? t('authForm.loginToContinue') 
            : t('authForm.joinToStart')}
        </p>
      </div>

      <button
        onClick={handleGoogleLogin}
        disabled={loading}
        className="w-full flex items-center justify-center gap-3 bg-white text-black font-bold py-4 px-6 mb-6 
        border-2 border-black shadow-[4px_4px_0_0_#000] hover:shadow-[2px_2px_0_0_#000] 
        active:shadow-[0px_0px_0_0_#000] active:translate-x-1 active:translate-y-1
        transition-all duration-150"
      >
        <svg width="20" height="20" viewBox="0 0 48 48" className="flex-shrink-0">
          <g clipPath="url(#clip0_17_40)">
            <path d="M47.5 24.552c0-1.636-.147-3.2-.42-4.704H24v9.02h13.22c-.57 3.08-2.28 5.68-4.86 7.44v6.16h7.86c4.6-4.24 7.28-10.5 7.28-17.916z" fill="#4285F4"/>
            <path d="M24 48c6.48 0 11.92-2.16 15.9-5.88l-7.86-6.16c-2.2 1.48-5.02 2.36-8.04 2.36-6.18 0-11.42-4.18-13.3-9.8H2.7v6.24C6.68 43.34 14.62 48 24 48z" fill="#34A853"/>
            <path d="M10.7 28.52A13.98 13.98 0 0 1 9.2 24c0-1.56.28-3.08.78-4.52v-6.24H2.7A23.98 23.98 0 0 0 0 24c0 3.98.98 7.74 2.7 10.76l8-6.24z" fill="#FBBC05"/>
            <path d="M24 9.52c3.52 0 6.66 1.2 9.14 3.56l6.84-6.84C35.92 2.16 30.48 0 24 0 14.62 0 6.68 4.66 2.7 13.24l8 6.24c1.88-5.62 7.12-9.8 13.3-9.8z" fill="#EA4335"/>
          </g>
        </svg>
        {t('authForm.continueWithGoogle')}
      </button>

      <div className="relative mb-6">
        <div className="absolute inset-0 flex items-center">
          <div className="w-full border-t-2 border-black"></div>
        </div>
        <div className="relative flex justify-center">
          <span className="px-4 bg-white text-black font-bold">{t('authForm.or')}</span>
        </div>
      </div>

      <form onSubmit={handleEmailAuth} className="space-y-6">
        {mode === 'signup' && (
          <div>
            <input
              type="text"
              placeholder={t('authForm.fullName')}
              value={fullName}
              onChange={e => setFullName(e.target.value)}
              required
              className="w-full px-4 py-3 bg-white border-2 border-black focus:ring-2 focus:ring-pink-500 focus:border-transparent text-black placeholder-gray-500 font-medium
              shadow-[4px_4px_0_0_#000] focus:shadow-[2px_2px_0_0_#000] focus:outline-none transition-all"
            />
          </div>
        )}
        <div>
          <input
            type="email"
            placeholder={t('authForm.email')}
            value={email}
            onChange={e => setEmail(e.target.value)}
            required
            className="w-full px-4 py-3 bg-white border-2 border-black focus:ring-2 focus:ring-pink-500 focus:border-transparent text-black placeholder-gray-500 font-medium
            shadow-[4px_4px_0_0_#000] focus:shadow-[2px_2px_0_0_#000] focus:outline-none transition-all"
          />
        </div>
        <div>
          <input
            type="password"
            placeholder={t('authForm.password')}
            value={password}
            onChange={e => setPassword(e.target.value)}
            required
            className="w-full px-4 py-3 bg-white border-2 border-black focus:ring-2 focus:ring-pink-500 focus:border-transparent text-black placeholder-gray-500 font-medium
            shadow-[4px_4px_0_0_#000] focus:shadow-[2px_2px_0_0_#000] focus:outline-none transition-all"
          />
        </div>
        <button
        type="submit"
        disabled={loading}
        className="w-full bg-brand-primary text-brand-black font-bold py-4 px-6 rounded-lg border-2 border-brand-black 
        shadow-brutal-lg hover:shadow-brutal-hover active:shadow-brutal-pressed transition-all mb-6"
      >
          {loading ? (
            <div className="flex items-center justify-center gap-2">
              <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              {t('authForm.loading')}
            </div>
          ) : (
            mode === 'login' ? t('authForm.login') : t('authForm.createAccount')
          )}
        </button>
      </form>

      <div className="mt-6 text-center">
        {mode === 'login' ? (
          <p className="text-black font-medium">
            {t('authForm.dontHaveAccount')} {' '}
            <button
              type="button"
              className="text-pink-600 font-bold hover:underline focus:outline-none"
              onClick={() => setMode('signup')}
            >
              {t('authForm.signUp')}
            </button>
          </p>
        ) : (
          <p className="text-black font-medium">
            {t('authForm.alreadyHaveAccount')} {' '}
            <button
              type="button"
              className="text-pink-600 font-bold hover:underline focus:outline-none"
              onClick={() => setMode('login')}
            >
              {t('authForm.doLogin')}
            </button>
          </p>
        )}
      </div>
      </div>
    </div>
  )
}