import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertTriangle, 
  RotateCcw, 
  Filter,
  Calendar,
  Eye,
  Download,
  Loader2,
  Play,
  Pause,
  RefreshCw
} from 'lucide-react'
import { supabase } from '../lib/supabase'
import { showToast } from '../utils/toast'
import { formatDateTime } from '../utils/dateUtils'
import { useTranslation } from 'react-i18next'

interface GenerationLog {
  id: string
  user_id: string
  streaming_platform: string
  photo_type: string
  original_image_url: string
  status: 'processing' | 'completed' | 'failed'
  generated_covers: any[]
  created_at: string
  completed_at?: string
}

interface CoverDetail {
  movie_title: string
  success: boolean
  image_url?: string
  error?: string
  fallback_used?: boolean
  fallback_movie?: string
  safe_prompt_used?: boolean
  generated_at: string
}

interface GenerationLogsProps {
  userId: string
}

export default function GenerationLogs({ userId }: GenerationLogsProps) {
  const { t } = useTranslation()
  const [logs, setLogs] = useState<GenerationLog[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedLog, setSelectedLog] = useState<GenerationLog | null>(null)
  const [filterStatus, setFilterStatus] = useState<string>('all')
  const [filterPlatform, setFilterPlatform] = useState<string>('all')
  const [sortBy, setSortBy] = useState<'date' | 'status'>('date')

  useEffect(() => {
    fetchLogs()
  }, [userId])

  useEffect(() => {
    if (!userId) return

    const channel = supabase
      .channel('cover_generations_changes')
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'cover_generations',
          filter: `user_id=eq.${userId}`
        },
        (payload) => {
          console.log('🔄 Geração atualizada em tempo real:', payload)
          fetchLogs()
        }
      )
      .subscribe()

    return () => {
      supabase.removeChannel(channel)
    }
  }, [userId])

  const fixOrphanedRecords = async () => {
    try {
      console.log('🔧 Verificando registros órfãos...')
      
      const { data: orphanedRecords, error } = await supabase
        .from('cover_generations')
        .select('id, generated_covers, created_at')
        .eq('user_id', userId)
        .eq('status', 'processing')
        .is('completed_at', null)

      if (error) throw error

      if (orphanedRecords && orphanedRecords.length > 0) {
        console.log(`🔧 Encontrados ${orphanedRecords.length} registros órfãos, corrigindo...`)
        
        for (const record of orphanedRecords) {
          const hasGeneratedCovers = record.generated_covers && record.generated_covers.length > 0
          const isOld = new Date(record.created_at) < new Date(Date.now() - 15 * 60 * 1000) // 15 minutos
          
          if (hasGeneratedCovers || isOld) {
            const newStatus = hasGeneratedCovers ? 'completed' : 'failed'
            const completedAt = hasGeneratedCovers && record.generated_covers[record.generated_covers.length - 1]?.created_at
              ? new Date(record.generated_covers[record.generated_covers.length - 1].created_at).toISOString()
              : new Date(Date.now() - 5 * 60 * 1000).toISOString() // 5 minutos atrás como fallback

            const { error: updateError } = await supabase
              .from('cover_generations')
              .update({
                status: newStatus,
                completed_at: completedAt,
                progress: hasGeneratedCovers ? 100 : 0,
                current_movie: hasGeneratedCovers ? t('generationLogs.status.completed') : t('generationLogs.status.failed')
              })
              .eq('id', record.id)

            if (updateError) {
              console.error(`❌ Erro ao corrigir registro ${record.id}:`, updateError)
            } else {
              console.log(`✅ Registro ${record.id} corrigido para status: ${newStatus}`)
            }
          }
        }
        
        showToast.success(`${t('generationLogs.messages.orphanFixed')} ${orphanedRecords.length} ${t('generationLogs.messages.orphanFixed')}`)
        return orphanedRecords.length
      }
      return 0
    } catch (error) {
      console.error('❌ Erro ao corrigir registros órfãos:', error)
      showToast.error(t('generationLogs.messages.errorFixing'))
      return 0
    }
  }

  const fetchLogs = async () => {
    try {
      setLoading(true)
      
      await fixOrphanedRecords()
      
      const { data, error } = await supabase
        .from('cover_generations')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(50)

      if (error) throw error

      setLogs(data || [])
    } catch (error) {
      console.error('Error fetching logs:', error)
      showToast.error(t('generationLogs.messages.errorFetching'))
    } finally {
      setLoading(false)
    }
  }

  const manualFix = async () => {
    const fixedCount = await fixOrphanedRecords()
    if (fixedCount === 0) {
      showToast.info(t('generationLogs.messages.noOrphanFound'))
    }
    fetchLogs()
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-green-600" />
      case 'failed':
        return <XCircle className="w-5 h-5 text-red-600" />
      case 'processing':
        return <Loader2 className="w-5 h-5 text-blue-600 animate-spin" />
      default:
        return <Clock className="w-5 h-5 text-gray-600" />
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return t('generationLogs.status.completed')
      case 'failed':
        return t('generationLogs.status.failed')
      case 'processing':
        return t('generationLogs.status.processing')
      default:
        return t('generationLogs.status.unknown')
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'failed':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'processing':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getPlatformBrand = (platform: string) => {
    switch (platform) {
      case 'netflix':
        return { name: t('generationLogs.platforms.netflix'), color: 'bg-[#E50914] text-white' }
      case 'disney':
        return { name: t('generationLogs.platforms.disney'), color: 'bg-[#1D3A8A] text-white' }
      case 'amazon':
        return { name: t('generationLogs.platforms.amazon'), color: 'bg-[#00A8E1] text-white' }
      default:
        return { name: platform.toUpperCase(), color: 'bg-gray-700 text-white' }
    }
  }

  const formatDate = formatDateTime

  const getDuration = (log: GenerationLog) => {
    if (!log.completed_at) return t('generationLogs.duration.inProgress')
    
    const start = new Date(log.created_at)
    const end = new Date(log.completed_at)
    const diffMs = end.getTime() - start.getTime()
    const diffSecs = Math.round(diffMs / 1000)
    
    if (diffSecs < 60) return `${diffSecs}${t('generationLogs.duration.seconds')}`
    const diffMins = Math.round(diffSecs / 60)
    return `${diffMins}${t('generationLogs.duration.minutes')}`
  }

  const getSuccessStats = (covers: any[]) => {
    if (!Array.isArray(covers)) return { success: 0, failed: 0, total: 0 }
    
    const success = covers.filter(cover => cover.success).length
    const failed = covers.filter(cover => !cover.success).length
    return { success, failed, total: covers.length }
  }

  const filteredLogs = logs
    .filter(log => {
      const statusMatch = filterStatus === 'all' || log.status === filterStatus
      const platformMatch = filterPlatform === 'all' || log.streaming_platform === filterPlatform
      return statusMatch && platformMatch
    })
    .sort((a, b) => {
      if (sortBy === 'date') {
        return new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      }
      return a.status.localeCompare(b.status)
    })

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="w-8 h-8 animate-spin text-brand-primary" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="bg-brand-white border-2 border-brand-black rounded-lg p-4 shadow-brutal">
        <div className="flex flex-wrap gap-4 items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Filter className="w-4 h-4" />
              <span className="font-semibold">{t('generationLogs.filters')}</span>
            </div>
            
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="border-2 border-brand-black rounded-lg px-3 py-2 font-semibold"
            >
              <option value="all">{t('generationLogs.allStatus')}</option>
              <option value="completed">{t('generationLogs.completed')}</option>
              <option value="failed">{t('generationLogs.failed')}</option>
              <option value="processing">{t('generationLogs.processing')}</option>
            </select>

            <select
              value={filterPlatform}
              onChange={(e) => setFilterPlatform(e.target.value)}
              className="border-2 border-brand-black rounded-lg px-3 py-2 font-semibold"
            >
              <option value="all">{t('generationLogs.allPlatforms')}</option>
              <option value="netflix">{t('generationLogs.netflix')}</option>
              <option value="disney">{t('generationLogs.disney')}</option>
              <option value="amazon">{t('generationLogs.amazon')}</option>
            </select>
          </div>

          <div className="flex items-center space-x-2">
            <button
              onClick={manualFix}
              className="flex items-center space-x-2 px-3 py-2 bg-yellow-500 border-2 border-brand-black rounded-lg font-semibold shadow-brutal-sm hover:shadow-brutal-hover transition-all text-white"
              title={t('generationLogs.fixTooltip')}
            >
              <AlertTriangle className="w-4 h-4" />
              <span>{t('generationLogs.fix')}</span>
            </button>
            
            <button
              onClick={fetchLogs}
              className="flex items-center space-x-2 px-4 py-2 bg-brand-secondary border-2 border-brand-black rounded-lg font-semibold shadow-brutal-sm hover:shadow-brutal-hover transition-all"
            >
              <RefreshCw className="w-4 h-4" />
              <span>{t('generationLogs.update')}</span>
            </button>
          </div>
        </div>
      </div>

      <div className="space-y-4">
        {filteredLogs.length === 0 ? (
          <div className="text-center py-12 bg-brand-white border-2 border-brand-black rounded-lg">
            <Clock className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600 font-semibold">{t('generationLogs.noGenerations')}</p>
          </div>
        ) : (
          filteredLogs.map((log) => {
            const stats = getSuccessStats(log.generated_covers)
            const platform = getPlatformBrand(log.streaming_platform)
            
            return (
              <motion.div
                key={log.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-brand-white border-2 border-brand-black rounded-lg p-4 shadow-brutal cursor-pointer hover:shadow-brutal-hover transition-all"
                onClick={() => setSelectedLog(log)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    {getStatusIcon(log.status)}
                    
                    <div>
                      <div className="flex items-center space-x-2 mb-1">
                        <span className={`px-2 py-1 rounded-md text-xs font-bold border ${platform.color}`}>
                          {platform.name}
                        </span>
                        <span className={`px-2 py-1 rounded-md text-xs font-bold border-2 ${getStatusColor(log.status)}`}>
                          {getStatusText(log.status)}
                        </span>
                      </div>
                      
                      <p className="text-sm text-gray-600">
                        {formatDate(log.created_at)} • {getDuration(log)}
                      </p>
                    </div>
                  </div>

                  <div className="text-right">
                    <div className="flex items-center space-x-2 mb-1">
                      {stats.success > 0 && (
                        <span className="flex items-center space-x-1 text-green-600 text-sm font-semibold">
                          <CheckCircle className="w-4 h-4" />
                          <span>{stats.success}</span>
                        </span>
                      )}
                      {stats.failed > 0 && (
                        <span className="flex items-center space-x-1 text-red-600 text-sm font-semibold">
                          <XCircle className="w-4 h-4" />
                          <span>{stats.failed}</span>
                        </span>
                      )}
                    </div>
                    <p className="text-xs text-gray-500">
                      {stats.total} {stats.total === 1 ? t('generationLogs.stats.movie') : t('generationLogs.stats.movies')}
                    </p>
                  </div>
                </div>
              </motion.div>
            )
          })
        )}
      </div>

      <AnimatePresence>
        {selectedLog && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
            onClick={() => setSelectedLog(null)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-brand-white border-4 border-brand-black rounded-lg max-w-4xl max-h-[90vh] overflow-hidden shadow-brutal"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="bg-brand-secondary border-b-4 border-brand-black p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    {getStatusIcon(selectedLog.status)}
                    <h3 className="text-xl font-bold">
                      {t('generationLogs.modal.title')}
                    </h3>
                  </div>
                  <button
                    onClick={() => setSelectedLog(null)}
                    className="p-2 bg-brand-white border-2 border-brand-black rounded-lg font-semibold shadow-brutal-sm hover:shadow-brutal-hover"
                  >
                    {t('generationLogs.modal.close')}
                  </button>
                </div>
              </div>

              <div className="p-6 max-h-[70vh] overflow-y-auto">
                <div className="grid grid-cols-2 gap-4 mb-6">
                  <div className="space-y-2">
                    <p><strong>{t('generationLogs.modal.platform')}</strong> {getPlatformBrand(selectedLog.streaming_platform).name}</p>
                    <p><strong>{t('generationLogs.modal.type')}</strong> {selectedLog.photo_type === 'individual' ? t('generationLogs.photoTypes.individual') : t('generationLogs.photoTypes.couple')}</p>
                    <p><strong>{t('generationLogs.modal.status')}</strong> {getStatusText(selectedLog.status)}</p>
                  </div>
                  <div className="space-y-2">
                    <p><strong>{t('generationLogs.modal.started')}</strong> {formatDate(selectedLog.created_at)}</p>
                    {selectedLog.completed_at && (
                      <p><strong>{t('generationLogs.modal.finished')}</strong> {formatDate(selectedLog.completed_at)}</p>
                    )}
                    <p><strong>{t('generationLogs.modal.duration')}</strong> {getDuration(selectedLog)}</p>
                  </div>
                </div>

                <div className="space-y-4">
                  <h4 className="text-lg font-bold border-b-2 border-brand-black pb-2">
                    {t('generationLogs.modal.processedMovies')} ({selectedLog.generated_covers?.length || 0})
                  </h4>
                  
                  {selectedLog.generated_covers?.map((cover: CoverDetail, index: number) => (
                    <div
                      key={index}
                      className={`p-4 border-2 rounded-lg ${
                        cover.success 
                          ? 'border-green-300 bg-green-50' 
                          : 'border-red-300 bg-red-50'
                      }`}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          {cover.success ? (
                            <CheckCircle className="w-5 h-5 text-green-600" />
                          ) : (
                            <XCircle className="w-5 h-5 text-red-600" />
                          )}
                          <span className="font-semibold">{cover.movie_title}</span>
                        </div>
                        
                        <div className="flex items-center space-x-2">
                          {cover.safe_prompt_used && (
                            <span className="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-md border">
                              {t('generationLogs.modal.badges.safePrompt')}
                            </span>
                          )}
                          {cover.fallback_used && (
                            <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-md border">
                              {t('generationLogs.modal.badges.fallback')} {cover.fallback_movie}
                            </span>
                          )}
                        </div>
                      </div>
                      
                      {cover.error && (
                        <p className="text-sm text-red-600 mt-2">
                          <strong>{t('generationLogs.modal.error')}</strong> {cover.error}
                        </p>
                      )}
                      
                      {cover.image_url && (
                        <div className="mt-3">
                          <img
                            src={cover.image_url}
                            alt={cover.movie_title}
                            className="w-24 h-32 object-cover rounded-lg border-2 border-brand-black shadow-brutal-sm"
                          />
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
} 