# Sistema de Banco de Dados - Explicação Completa

## 🧠 Como Funciona o Sistema

### 1. **cover_generations** - Tabela Principal de Gerações

Esta é a tabela que controla cada processo de geração iniciado pelo usuário:

```sql
-- Estrutura da tabela
CREATE TABLE cover_generations (
  id UUID PRIMARY KEY,
  user_id UUID REFERENCES auth.users,
  original_image_url TEXT NOT NULL,
  photo_type TEXT CHECK (photo_type IN ('individual', 'casal')),
  streaming_platform TEXT CHECK (streaming_platform IN ('netflix', 'disney', 'amazon')),
  generated_covers JSONB DEFAULT '[]',  -- A<PERSON>y de objetos com detalhes de cada filme
  status TEXT DEFAULT 'processing' CHECK (status IN ('processing', 'completed', 'failed')),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  completed_at TIMESTAMPTZ
);
```

**Campo `generated_covers` (JSONB)**:
```json
[
  {
    "movie_title": "Stranger Things 5",
    "image_url": "https://...",
    "success": true,
    "aspect_ratio": "5:4",
    "generated_at": "2025-01-24T10:30:00Z"
  },
  {
    "movie_title": "Round 6",
    "success": false,
    "error": "E005 - Content flagged as sensitive",
    "fallback_used": true,
    "fallback_movie": "Wednesday"
  }
]
```

### 2. **movies_series** - Banco de Filmes Extras (Fallback)

Esta tabela contém filmes extras para usar quando os filmes locais falham:

```sql
-- Estrutura da tabela
CREATE TABLE movies_series (
  id UUID PRIMARY KEY,
  title TEXT NOT NULL,
  streaming_platform TEXT CHECK (streaming_platform IN ('netflix', 'disney', 'amazon')),
  type TEXT CHECK (type IN ('movie', 'series')),
  year INTEGER,
  genre TEXT,
  poster_style_prompt TEXT NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW()
);
```

**Exemplo de dados**:
```sql
-- Netflix tem filmes extras no banco
INSERT INTO movies_series VALUES 
('uuid1', 'Stranger Things 5', 'netflix', 'series', 2024, 'Sci-Fi Horror', 'Dark supernatural thriller poster...'),
('uuid2', 'Wednesday Addams', 'netflix', 'series', 2023, 'Comedy Horror', 'Gothic dark comedy poster...');
```

### 3. **cover_images** - Histórico de Imagens Geradas

Cada imagem gerada com sucesso é salva aqui:

```sql
-- Estrutura da tabela
CREATE TABLE cover_images (
  id UUID PRIMARY KEY,
  user_id UUID REFERENCES auth.users,
  image_url TEXT NOT NULL,
  movie_title TEXT NOT NULL,
  streaming_platform TEXT CHECK (streaming_platform IN ('netflix', 'disney', 'amazon')),
  photo_type TEXT CHECK (photo_type IN ('individual', 'casal')),
  aspect_ratio TEXT NOT NULL,
  is_regenerated BOOLEAN DEFAULT FALSE,
  generation_id UUID,  -- Referência para cover_generations
  created_at TIMESTAMPTZ DEFAULT NOW()
);
```

## 🔄 Fluxo de Processamento

### Passo 1: Início da Geração
```sql
-- Cria registro na cover_generations
INSERT INTO cover_generations (user_id, streaming_platform, photo_type, original_image_url)
VALUES ('user-uuid', 'netflix', 'individual', 'https://image.jpg');
```

### Passo 2: Processamento Individual
Para cada filme (1 por vez):

1. **Tenta filme local** do `movieDatabase.ts`
2. **Se falhar** (erro E005, timeout, etc):
   ```sql
   -- Busca filme alternativo no banco
   SELECT * FROM movies_series 
   WHERE streaming_platform = 'netflix' 
   AND title NOT IN (
     SELECT movie_title FROM cover_images 
     WHERE user_id = 'user-uuid' 
     AND streaming_platform = 'netflix'
   )
   LIMIT 1;
   ```

3. **Atualiza progresso** em tempo real:
   ```sql
   -- Adiciona resultado ao array JSONB
   UPDATE cover_generations 
   SET generated_covers = generated_covers || '[{"movie_title": "Elite", "success": true, "image_url": "https://..."}]'
   WHERE id = 'generation-uuid';
   ```

### Passo 3: Salva Imagem com Sucesso
```sql
-- Salva na tabela cover_images
INSERT INTO cover_images (user_id, movie_title, image_url, streaming_platform, generation_id)
VALUES ('user-uuid', 'Elite', 'https://image.jpg', 'netflix', 'generation-uuid');
```

### Passo 4: Finalização
```sql
-- Marca como completo
UPDATE cover_generations 
SET status = 'completed', completed_at = NOW()
WHERE id = 'generation-uuid';
```

## 🎯 Vantagens do Sistema

### ✅ **Rastreamento Completo**
- Cada filme tentado é registrado (sucesso ou falha)
- Histórico completo de erros para debug
- Progresso em tempo real na interface

### ✅ **Fallback Inteligente**
- Se filme local falha → busca no banco
- Evita filmes já gerados para o usuário
- Garante que sempre haverá conteúdo

### ✅ **Regeneração Seletiva**
- Interface pode mostrar quais filmes falharam
- Usuário pode regenerar apenas filmes específicos
- Histórico de regenerações

### ✅ **Dashboard Completo**
- Todas as gerações aparecem no dashboard
- Filtros por plataforma, data, status
- Estatísticas de sucesso/falha

## 🔍 Queries Úteis para Debug

### Ver progresso de uma geração:
```sql
SELECT 
  id,
  streaming_platform,
  status,
  jsonb_array_length(generated_covers) as total_processed,
  created_at
FROM cover_generations 
WHERE user_id = 'user-uuid'
ORDER BY created_at DESC;
```

### Ver filmes que falharam:
```sql
SELECT 
  jsonb_array_elements(generated_covers) as cover_details
FROM cover_generations 
WHERE id = 'generation-uuid'
AND jsonb_array_elements(generated_covers)->>'success' = 'false';
```

### Filmes disponíveis para fallback:
```sql
SELECT title, genre, year 
FROM movies_series 
WHERE streaming_platform = 'netflix'
AND title NOT IN (
  SELECT movie_title FROM cover_images 
  WHERE user_id = 'user-uuid' 
  AND streaming_platform = 'netflix'
);
```

## 📊 Exemplo Prático

**Usuário gera Netflix com 12 filmes:**

1. **Filmes locais tentados**: Round 6, Wednesday, Stranger Things 5, Elite...
2. **Round 6 falha** (E005) → busca "Ozark" no banco
3. **Progresso salvo**: `generated_covers` tem 4 objetos
4. **Interface mostra**: 3 sucessos, 1 fallback usado
5. **Dashboard**: Usuário vê todas as 4 imagens geradas

Esse sistema garante **escalabilidade**, **rastreamento completo** e **experiência fluida** para o usuário! 🚀 