# 🚀 Setup Automático OAuth Canva

## 😤 **O Problema**

O Canva tem uma API **mal projetada** que:
- ❌ NÃO suporta `client_credentials` (como qualquer API decente)
- 🤡 Força OAuth interativo SEMPRE (mesmo para server-to-server)
- 🙄 Exige autorização manual via navegador

**Isso é uma MERDA para automação!** 💩

## ✅ **A Solução**

Criei um script que **automatiza o processo** uma única vez:

### **O que o script faz:**
1. 🔐 Gera PKCE automaticamente 
2. 🌐 Abre navegador para autorização
3. 🔄 Captura o authorization code
4. 🎯 Troca por access_token + refresh_token
5. ⚙️ Configura tudo no Supabase automaticamente
6. ✅ Edge Function pronta para usar!

### **Depois do setup:**
- 🔄 **Renovação automática** quando tokens expiram
- 🚫 **Zero configuração manual**
- ⚡ **Funciona para sempre**

## 🛠️ **Como Usar**

### **1. Executar Setup (Uma Vez Só)**

```bash
npm run setup-canva
```

### **2. Seguir Instruções**

O script vai:
1. Abrir seu navegador automaticamente
2. Mostrar página de autorização do Canva
3. Processar tokens automaticamente
4. Configurar Supabase

### **3. Autorizar no Navegador**

- Faça login no Canva (se necessário)
- Clique **"Authorize"** 
- Página confirma sucesso
- **PRONTO!** ✅

## 📋 **O que Acontece**

### **Durante o Setup:**
```
🚀 Iniciando setup automático do OAuth Canva...
🔐 PKCE gerado
🔗 URL de autorização criada
🌐 Abrindo navegador para autorização...
⏳ Aguardando autorização...
🔄 Trocando authorization code por tokens...
✅ Tokens obtidos com sucesso!
⏰ Access token expira em: 14400 segundos (4h)
🔧 Configurando secrets no Supabase...
✅ Secret 1/4 configurado
✅ Secret 2/4 configurado  
✅ Secret 3/4 configurado
✅ Secret 4/4 configurado

🎉 SETUP CONCLUÍDO COM SUCESSO!
✅ Tokens configurados no Supabase
✅ Edge Function pronta para usar
✅ Renovação automática configurada
```

### **Secrets Configurados:**
```bash
CANVA_ACCESS_TOKEN="Bearer ey..."     # Token atual (4h)
CANVA_REFRESH_TOKEN="JAB..."          # Para renovação
CANVA_CLIENT_ID="OC-AZdf..."          # Credenciais
CANVA_CLIENT_SECRET="cnvca9..."       # Credenciais
```

## 🧪 **Testar**

Depois do setup:

1. Acesse `http://localhost:5175/`
2. Dashboard → Aba **"Test"** 🧪  
3. Selecione **"Renovação Automática"**
4. Clique **"Testar"** (sem precisar colar nada!)
5. **Funciona!** ✅

## ⚠️ **Importante**

### **Só Precisa Fazer UMA VEZ**
- Setup = Processo único 
- Depois = Zero manutenção
- Tokens = Renovam automaticamente

### **Se Der Erro**
```bash
# Verificar se Supabase CLI está logado
supabase login

# Verificar se está no projeto certo
supabase status

# Rodar setup novamente
npm run setup-canva
```

### **Para Produção**
O script funciona tanto local quanto produção. Só execute onde tem:
- Supabase CLI configurado
- Acesso ao projeto
- Permissão para configurar secrets

## 🎯 **Vantagens**

- ✅ **Setup em 2 minutos** vs horas de configuração manual
- ✅ **Funciona para sempre** após configuração  
- ✅ **Zero manutenção** - renova tokens automaticamente
- ✅ **Substitui N8N** completamente
- ✅ **Controle total** no Supabase

## 🔥 **Conclusão**

O Canva tem uma API de merda, mas **agora você não precisa se preocupar com isso!**

Execute o script uma vez e **esquece** - a Edge Function funciona automaticamente para sempre! 🎉

---

**💡 Dica:** Depois do setup, você pode deletar o N8N flow do Canva e usar só a Edge Function! 