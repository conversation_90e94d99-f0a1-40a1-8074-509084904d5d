import { supabase } from './supabase'

export async function generateMovieCovers(
  imageUrl: string,
  photoType: 'individual' | 'casal',
  streamingPlatform: 'netflix' | 'disney' | 'amazon',
  language: 'english' | 'portuguese',
  quantity: number,
  generateTitles: boolean
) {
  console.log('Starting cover generation with:', { imageUrl, photoType, streamingPlatform, language, quantity, generateTitles })

  try {
    const apiUrl = `${import.meta.env.VITE_SUPABASE_URL}/functions/v1/generate-covers`

    const { data: { session } } = await supabase.auth.getSession()
    if (!session) {
      throw new Error('User not authenticated')
    }
    const accessToken = session.access_token

    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        imageUrl,
        photoType,
        streamingPlatform,
        language,
        quantity,
        generateTitles
      })
    })

    if (!response.ok) {
      const errorText = await response.text()
      console.error('Edge function error response:', errorText)
      throw new Error(`Edge function error: ${response.status} - ${errorText}`)
    }

    const data = await response.json()
    
    if (data.error) {
      throw new Error(data.error)
    }

    console.log('Edge function response:', data)
    return data.covers || []

  } catch (error) {
    console.error('Error calling edge function:', error)
    throw error
  }
}

export async function regenerateIndividualCover(
  imageUrl: string,
  photoType: 'individual' | 'casal',
  streamingPlatform: 'netflix' | 'disney' | 'amazon',
  language: 'english' | 'portuguese',
  coverIndex: number,
  generateTitles: boolean
) {
  console.log('Starting individual cover regeneration with:', { imageUrl, photoType, streamingPlatform, language, coverIndex, generateTitles })

  try {
    const apiUrl = `${import.meta.env.VITE_SUPABASE_URL}/functions/v1/regenerate-cover`

    const { data: { session } } = await supabase.auth.getSession()
    if (!session) {
      throw new Error('User not authenticated')
    }
    const accessToken = session.access_token

    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        imageUrl,
        photoType,
        streamingPlatform,
        language,
        coverIndex,
        generateTitles
      })
    })

    if (!response.ok) {
      const errorText = await response.text()
      console.error('Regenerate edge function error response:', errorText)
      throw new Error(`Regenerate edge function error: ${response.status} - ${errorText}`)
    }

    const data = await response.json()
    
    if (data.error) {
      throw new Error(data.error)
    }

    console.log('Regenerate edge function response:', data)
    return data.cover || null

  } catch (error) {
    console.error('Error calling regenerate edge function:', error)
    throw error
  }
}