import React from 'react'
import { useTranslation } from 'react-i18next'
import { Zap, Plus, Clock, CheckCircle, TrendingUp, AlertTriangle } from 'lucide-react'

interface CreditHistoryItem {
  id: string
  amount: number
  type: 'purchase' | 'usage'
  description: string
  created_at: string
}

interface CreditUsageItem {
  id: string
  amount: number
  description: string
  created_at: string
}

interface CreditBalanceProps {
  balance: {
    totalCredits: number
    availableCredits: number
    usedCredits: number
    creditHistory: CreditHistoryItem[]
    recentUsage: CreditUsageItem[]
  }
  onBuyMore: () => void
  loading?: boolean
}

export default function CreditBalance({ balance, onBuyMore, loading }: CreditBalanceProps) {
  const { t } = useTranslation();
  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6 animate-pulse">
        <div className="h-6 bg-gray-200 rounded mb-4"></div>
        <div className="h-4 bg-gray-200 rounded mb-2"></div>
        <div className="h-4 bg-gray-200 rounded"></div>
      </div>
    )
  }

  const isLowOnCredits = balance.availableCredits < 10

  return (
    <div className="bg-white rounded-lg shadow-md p-6 border-2 border-brand-black">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className={`w-8 h-8 rounded-lg flex items-center justify-center border-2 border-brand-black ${
            isLowOnCredits ? 'bg-red-100' : 'bg-yellow-100'
          }`}>
            <Zap className={`w-5 h-5 ${isLowOnCredits ? 'text-red-600' : 'text-yellow-600'}`} />
          </div>
          <h3 className="text-lg font-semibold text-brand-text">{t('creditBalance.title')}</h3>
        </div>
        <button
          onClick={onBuyMore}
          className="flex items-center space-x-2 px-4 py-2 bg-brand-primary text-brand-black rounded-lg border-2 border-brand-black shadow-brutal-sm hover:shadow-brutal-hover transition-all font-bold"
        >
          <Plus size={16} />
          <span>{t('creditBalance.buyMore')}</span>
        </button>
      </div>

      {/* Credit Statistics */}
      <div className="grid grid-cols-3 gap-4 mb-6">
        <div className={`text-center p-4 rounded-lg border-2 border-brand-black ${
          isLowOnCredits ? 'bg-red-50' : 'bg-yellow-50'
        }`}>
          <div className={`text-2xl font-bold ${isLowOnCredits ? 'text-red-600' : 'text-yellow-600'}`}>
            {balance.availableCredits}
          </div>
          <div className={`text-sm ${isLowOnCredits ? 'text-red-700' : 'text-yellow-700'}`}>
            {t('creditBalance.available')}
          </div>
        </div>
        <div className="text-center p-4 bg-green-50 rounded-lg border-2 border-brand-black">
          <div className="text-2xl font-bold text-green-600">{balance.usedCredits}</div>
          <div className="text-sm text-green-700">{t('creditBalance.used')}</div>
        </div>
        <div className="text-center p-4 bg-gray-50 rounded-lg border-2 border-brand-black">
          <div className="text-2xl font-bold text-gray-600">{balance.totalCredits}</div>
          <div className="text-sm text-gray-700">{t('creditBalance.total')}</div>
        </div>
      </div>

      {/* Usage Information */}
      <div className="mb-6 p-4 bg-blue-50 border-2 border-blue-300 rounded-lg">
        <h5 className="font-bold text-blue-800 mb-2 flex items-center space-x-2">
          <TrendingUp className="w-4 h-4" />
          <span>{t('creditBalance.howToUse')}</span>
        </h5>
        <div className="grid md:grid-cols-2 gap-3 text-sm text-blue-700">
          <div className="flex items-center space-x-2">
            <CheckCircle className="w-4 h-4 text-green-500" />
            <span>{t('creditBalance.coverCost')}</span>
          </div>
          <div className="flex items-center space-x-2">
            <CheckCircle className="w-4 h-4 text-green-500" />
            <span>{t('creditBalance.posterCost')}</span>
          </div>
          <div className="flex items-center space-x-2">
            <CheckCircle className="w-4 h-4 text-blue-500" />
            <span>{t('creditBalance.regenCost')}</span>
          </div>
          <div className="flex items-center space-x-2">
            <CheckCircle className="w-4 h-4 text-green-500" />
            <span>{t('creditBalance.hdDownload')}</span>
          </div>
        </div>
      </div>

      {/* Recent Usage */}
      {balance.recentUsage && balance.recentUsage.length > 0 && (
        <div>
          <h4 className="font-semibold text-brand-text mb-3 flex items-center space-x-2">
            <Clock size={16} />
            <span>{t('creditBalance.recentUsage')}</span>
          </h4>
          <div className="space-y-2">
            {balance.recentUsage.slice(0, 5).map((usage: CreditUsageItem, index: number) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg border-2 border-gray-200">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center border-2 border-red-300">
                    <Zap className="w-4 h-4 text-red-600" />
                  </div>
                  <div>
                    <div className="text-sm font-medium text-brand-text">
                      {usage.description}
                    </div>
                    <div className="text-xs text-brand-text/60">
                      -{usage.amount} crédito{usage.amount > 1 ? 's' : ''}
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-xs text-brand-text/60">
                    {new Date(usage.created_at).toLocaleDateString('pt-BR')}
                  </div>
                  <div className="text-xs text-brand-text/50">
                    {new Date(usage.created_at).toLocaleTimeString('pt-BR', { 
                      hour: '2-digit', 
                      minute: '2-digit' 
                    })}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Credit History */}
      {balance.creditHistory && balance.creditHistory.length > 0 && (
        <div className="mt-6">
          <h4 className="font-semibold text-brand-text mb-3 flex items-center space-x-2">
            <TrendingUp size={16} />
            <span>{t('creditBalance.creditHistory')}</span>
          </h4>
          <div className="space-y-2">
            {balance.creditHistory.slice(0, 5).map((item: CreditHistoryItem, index: number) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg border-2 border-gray-200">
                <div className="flex items-center space-x-3">
                  <div className={`w-8 h-8 rounded-lg flex items-center justify-center border-2 ${
                    item.type === 'purchase' 
                      ? 'bg-green-100 border-green-300' 
                      : 'bg-red-100 border-red-300'
                  }`}>
                    {item.type === 'purchase' ? (
                      <Plus className="w-4 h-4 text-green-600" />
                    ) : (
                      <Zap className="w-4 h-4 text-red-600" />
                    )}
                  </div>
                  <div>
                    <div className="text-sm font-medium text-brand-text">
                      {item.description}
                    </div>
                    <div className={`text-xs ${
                      item.type === 'purchase' ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {item.type === 'purchase' ? '+' : '-'}{item.amount} crédito{item.amount > 1 ? 's' : ''}
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-xs text-brand-text/60">
                    {new Date(item.created_at).toLocaleDateString('pt-BR')}
                  </div>
                  <div className="text-xs text-brand-text/50">
                    {new Date(item.created_at).toLocaleTimeString('pt-BR', { 
                      hour: '2-digit', 
                      minute: '2-digit' 
                    })}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Empty State */}
      {balance.totalCredits === 0 && (
        <div className="text-center py-8">
          <Zap className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h4 className="text-lg font-medium text-gray-600 mb-2">{t('creditBalance.noCredits')}</h4>
          <p className="text-gray-500 mb-4">
            {t('creditBalance.buyFirstCredits')}
          </p>
          <button
            onClick={onBuyMore}
            className="px-6 py-3 bg-brand-primary text-brand-black rounded-lg border-2 border-brand-black shadow-brutal-sm hover:shadow-brutal-hover transition-all font-bold"
          >
            {t('creditBalance.buyMore')}
          </button>
        </div>
      )}

      {/* Low Balance Warning */}
      {balance.availableCredits > 0 && balance.availableCredits <= 10 && (
        <div className="mt-4 p-3 bg-yellow-50 border-2 border-yellow-400 rounded-lg">
          <div className="flex items-center space-x-2">
            <AlertTriangle className="w-5 h-5 text-yellow-600" />
            <span className="text-sm text-yellow-800 font-semibold">
              {t('creditBalance.lowBalanceWarning', { count: balance.availableCredits })}
              {t('creditBalance.considerBuying')}
            </span>
          </div>
        </div>
      )}
    </div>
  )
} 