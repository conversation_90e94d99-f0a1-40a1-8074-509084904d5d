import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { 
  <PERSON><PERSON>, 
  <PERSON>ader2, 
  CheckCircle, 
  XCircle, 
  Eye, 
  ExternalLink,
  TestTube,
  AlertTriangle
} from 'lucide-react'
import { supabase } from '../lib/supabase'
import { showToast } from '../utils/toast'

interface CanvaTestResponse {
  id: string
  status: string
  designUrl?: string
  templateId: string
}

export default function CanvaTestComponent() {
  const [isGenerating, setIsGenerating] = useState(false)
  const [result, setResult] = useState<CanvaTestResponse | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [canvaToken, setCanvaToken] = useState('')
  const [refreshToken, setRefreshToken] = useState('')
  const [testMode, setTestMode] = useState<'manual' | 'auto'>('manual')

  // 🎨 Payload de teste com dados realistas baseado no fluxo N8N
  const getTestPayload = async () => {
    // Obter user_id do usuário logado
    const { data: { user } } = await supabase.auth.getUser()
    
    return {
      ID: "EAGqDjHn_M4", // Template ID do Canva do fluxo N8N
      streaming_platform: "netflix", // Campo necessário para poster_images
      photo_type: "individual", // Campo necessário para poster_images  
      user_name: "Teste Edge Function", // Campo necessário para poster_images
      user_id: user?.id, // User ID do usuário logado ← IMPORTANTE!
      // Incluir tokens baseado no modo de teste
      ...(testMode === 'manual' && canvaToken.trim() ? { CANVA_ACCESS_TOKEN: canvaToken.trim() } : {}),
      ...(testMode === 'auto' && refreshToken.trim() ? { CANVA_REFRESH_TOKEN: refreshToken.trim() } : {}),
      TITLE: "Posters NETFLIX - Teste Edge Function",
      DESCRIPTION: "Teste da integração direta com a API do Canva via Edge Function do Supabase, sem depender do fluxo N8N.",
      CONTINUE: "Continue testando a nova função",
      CAPA: "https://uptmptfpumgrnlxukwau.supabase.co/storage/v1/object/public/generated-images/covers/1750121164890-cover.jpg",
      AVATAR: "https://uptmptfpumgrnlxukwau.supabase.co/storage/v1/object/public/cover-images/uploads/1750120752460-97o4zjhmxkj.jpg",
      PHOTO_01: "https://uptmptfpumgrnlxukwau.supabase.co/storage/v1/object/public/generated-images/covers/1750120776244-1-wednesday.jpg",
      PHOTO_02: "https://uptmptfpumgrnlxukwau.supabase.co/storage/v1/object/public/generated-images/covers/1750120790038-2-stranger-things-5.jpg",
      PHOTO_03: "https://uptmptfpumgrnlxukwau.supabase.co/storage/v1/object/public/generated-images/covers/1750120803627-3-elite.jpg",
      PHOTO_04: "https://uptmptfpumgrnlxukwau.supabase.co/storage/v1/object/public/generated-images/covers/1750120817024-4-money-heist--la-casa-de-papel-.jpg",
      PHOTO_05: "https://uptmptfpumgrnlxukwau.supabase.co/storage/v1/object/public/generated-images/covers/1750120838581-6-ozark.jpg",
      PHOTO_06: "https://uptmptfpumgrnlxukwau.supabase.co/storage/v1/object/public/generated-images/covers/1750120851895-7-dark.jpg",
      PHOTO_07: "https://uptmptfpumgrnlxukwau.supabase.co/storage/v1/object/public/generated-images/covers/1750120865491-8-narcos.jpg",
      PHOTO_08: "https://uptmptfpumgrnlxukwau.supabase.co/storage/v1/object/public/generated-images/covers/1750120879699-9-the-crown.jpg",
      PHOTO_09: "https://uptmptfpumgrnlxukwau.supabase.co/storage/v1/object/public/generated-images/covers/1750120893446-10-orange-is-the-new-black.jpg",
      // PHOTO_10, 11, 12 serão opcionais
    }
  }

  const testCanvaFunction = async () => {
    try {
      setIsGenerating(true)
      setError(null)
      setResult(null)

      const testPayload = await getTestPayload()
      console.log('🧪 Testando Edge Function do Canva com payload:', testPayload)

      // Chamar a Edge Function
      const { data, error } = await supabase.functions.invoke('canva-poster', {
        body: testPayload
      })

      if (error) {
        throw new Error(error.message || 'Erro na Edge Function')
      }

      console.log('✅ Resposta da Edge Function:', data)
      setResult(data as CanvaTestResponse)
      showToast.success('Poster gerado com sucesso!')

    } catch (error: any) {
      console.error('❌ Erro no teste:', error)
      setError(error.message || 'Erro desconhecido')
      showToast.error(`Erro: ${error.message}`)
    } finally {
      setIsGenerating(false)
    }
  }

  const checkGenerationStatus = async (id: string) => {
    try {
      const { data, error } = await supabase.functions.invoke('canva-poster', {
        method: 'GET',
        body: { id }
      })

      if (error) throw error

      console.log('📊 Status da geração:', data)
      return data
    } catch (error: any) {
      console.error('❌ Erro ao verificar status:', error)
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-brand-white border-2 border-brand-black rounded-lg p-6 shadow-brutal">
        <div className="flex items-center space-x-3 mb-4">
          <div className="p-2 bg-purple-500 rounded-lg">
            <TestTube className="w-6 h-6 text-white" />
          </div>
          <div>
            <h2 className="text-xl font-black text-brand-text">
              Teste Edge Function Canva
            </h2>
            <p className="text-sm text-gray-600">
              Teste a integração direta com a API do Canva (substituto do fluxo N8N)
            </p>
          </div>
        </div>

        {/* Botão de Teste */}
        <div className="space-y-4">
          {/* Seleção do Modo de Teste */}
          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              🧪 Modo de Teste
            </label>
            <div className="flex space-x-4">
              <label className="flex items-center">
                <input
                  type="radio"
                  value="manual"
                  checked={testMode === 'manual'}
                  onChange={(e) => setTestMode(e.target.value as 'manual' | 'auto')}
                  className="mr-2"
                />
                <span className="text-sm">Manual (Access Token)</span>
              </label>
              <label className="flex items-center">
                <input
                  type="radio"
                  value="auto"
                  checked={testMode === 'auto'}
                  onChange={(e) => setTestMode(e.target.value as 'manual' | 'auto')}
                  className="mr-2"
                />
                <span className="text-sm">🔄 Renovação Automática (Refresh Token)</span>
              </label>
            </div>
          </div>

          {/* Campo para Access Token (Modo Manual) */}
          {testMode === 'manual' && (
            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                🔑 Access Token do Canva
              </label>
              <input
                type="text"
                value={canvaToken}
                onChange={(e) => setCanvaToken(e.target.value)}
                placeholder="Deixe vazio para usar token das Secrets ou cole seu token aqui"
                className="w-full px-3 py-2 border-2 border-gray-300 rounded-lg focus:border-purple-500 focus:outline-none font-mono text-sm"
              />
              <p className="text-xs text-gray-500 mt-1">
                💡 Deixe vazio para usar CANVA_ACCESS_TOKEN das secrets ou cole token manual
              </p>
            </div>
          )}

          {/* Campo para Refresh Token (Modo Automático) */}
          {testMode === 'auto' && (
            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                🔄 Refresh Token do Canva
              </label>
              <input
                type="text"
                value={refreshToken}
                onChange={(e) => setRefreshToken(e.target.value)}
                placeholder="refresh_token_aqui (sem Bearer)"
                className="w-full px-3 py-2 border-2 border-gray-300 rounded-lg focus:border-purple-500 focus:outline-none font-mono text-sm"
              />
              <p className="text-xs text-gray-500 mt-1">
                🔄 Testa renovação automática (requer client_id + client_secret configurados)
              </p>
            </div>
          )}

          <button
            onClick={testCanvaFunction}
            disabled={isGenerating || (testMode === 'auto' && !refreshToken.trim())}
            className="flex items-center space-x-2 px-6 py-3 bg-purple-500 border-2 border-brand-black rounded-lg font-semibold text-white shadow-brutal-sm hover:shadow-brutal-hover transition-all disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isGenerating ? (
              <>
                <Loader2 className="w-5 h-5 animate-spin" />
                <span>{testMode === 'auto' ? 'Renovando Token e Gerando...' : 'Gerando Poster...'}</span>
              </>
            ) : (
              <>
                <Palette className="w-5 h-5" />
                <span>🧪 {testMode === 'auto' ? 'Testar Renovação Automática' : 'Testar com Access Token'}</span>
              </>
            )}
          </button>
        </div>
      </div>

      {/* Payload de Teste */}
      <div className="bg-brand-white border-2 border-brand-black rounded-lg p-4 shadow-brutal">
        <h3 className="font-bold mb-3 flex items-center space-x-2">
          <Palette className="w-4 h-4" />
          <span>Payload de Teste</span>
        </h3>
        <div className="bg-gray-100 border-2 border-gray-300 rounded-lg p-3 text-sm">
          <pre className="overflow-x-auto text-xs">
{JSON.stringify({
  ID: "EAGqDjHn_M4",
  streaming_platform: "netflix",
  photo_type: "individual",
  user_name: "Teste Edge Function",
  user_id: "[Seu User ID será inserido automaticamente]",
  ...(testMode === 'manual' && canvaToken.trim() ? { CANVA_ACCESS_TOKEN: "[Token fornecido]" } : {}),
  ...(testMode === 'auto' && refreshToken.trim() ? { CANVA_REFRESH_TOKEN: "[Refresh token fornecido]" } : {}),
  TITLE: "Posters NETFLIX - Teste Edge Function",
  DESCRIPTION: "Teste da integração direta com a API do Canva...",
  CONTINUE: "Continue testando a nova função",
  CAPA: "https://uptmptfpumgrnlxukwau.supabase.co/storage/v1/object/public/generated-images/covers/1750121164890-cover.jpg",
  AVATAR: "https://uptmptfpumgrnlxukwau.supabase.co/storage/v1/object/public/cover-images/uploads/1750120752460-97o4zjhmxkj.jpg",
  "PHOTO_01 a PHOTO_09": "[URLs das imagens de teste...]"
}, null, 2)}
          </pre>
        </div>
      </div>

      {/* Loading Estado */}
      {isGenerating && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-blue-50 border-2 border-blue-200 rounded-lg p-4"
        >
          <div className="flex items-center space-x-3">
            <Loader2 className="w-6 h-6 text-blue-600 animate-spin" />
            <div>
              <p className="font-semibold text-blue-800">Processando...</p>
              <p className="text-sm text-blue-600">
                Fazendo upload das imagens e criando design no Canva
              </p>
            </div>
          </div>
        </motion.div>
      )}

      {/* Erro */}
      {error && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-red-50 border-2 border-red-200 rounded-lg p-4"
        >
          <div className="flex items-center space-x-3">
            <XCircle className="w-6 h-6 text-red-600" />
            <div>
              <p className="font-semibold text-red-800">Erro no Teste</p>
              <p className="text-sm text-red-600">{error}</p>
            </div>
          </div>
        </motion.div>
      )}

      {/* Resultado de Sucesso */}
      {result && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-green-50 border-2 border-green-200 rounded-lg p-4"
        >
          <div className="flex items-center space-x-3 mb-4">
            <CheckCircle className="w-6 h-6 text-green-600" />
            <div>
              <p className="font-semibold text-green-800">Teste Concluído com Sucesso! 🎉</p>
              <p className="text-sm text-green-600">
                Edge Function funcionando corretamente
              </p>
            </div>
          </div>

          <div className="bg-white border border-green-200 rounded-lg p-3 space-y-2">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-semibold">ID da Geração:</span>
                <p className="text-gray-600 font-mono">{result.id}</p>
              </div>
              <div>
                <span className="font-semibold">Template ID:</span>
                <p className="text-gray-600">{result.templateId}</p>
              </div>
              <div>
                <span className="font-semibold">Status:</span>
                <p className="text-green-600 font-semibold">{result.status}</p>
              </div>
              {result.designUrl && (
                <div>
                  <span className="font-semibold">URL do Design:</span>
                  <a 
                    href={result.designUrl} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="flex items-center space-x-1 text-blue-600 hover:text-blue-800"
                  >
                    <ExternalLink className="w-4 h-4" />
                    <span>Ver no Canva</span>
                  </a>
                </div>
              )}
            </div>

            {result.designUrl && (
              <div className="pt-2 border-t border-green-200">
                <button
                  onClick={() => window.open(result.designUrl, '_blank')}
                  className="flex items-center space-x-2 px-4 py-2 bg-green-500 border-2 border-brand-black rounded-lg font-semibold text-white shadow-brutal-sm hover:shadow-brutal-hover transition-all"
                >
                  <Eye className="w-4 h-4" />
                  <span>🎨 Visualizar Poster</span>
                </button>
              </div>
            )}
          </div>
        </motion.div>
      )}

      {/* Informações sobre o Teste */}
      <div className="bg-gray-50 border-2 border-gray-200 rounded-lg p-4">
        <h3 className="font-bold mb-2">ℹ️ Sobre este Teste</h3>
        <ul className="text-sm text-gray-600 space-y-1">
          <li>• <strong>Template:</strong> Usa o mesmo template do fluxo N8N (EAGqDjHn_M4)</li>
          <li>• <strong>Imagens:</strong> URLs reais de covers já gerados no sistema</li>
          <li>• <strong>Processo:</strong> Upload → Canva API → Design → URL final</li>
          <li>• <strong>Banco:</strong> Salva o progresso na tabela `poster_images`</li>
          <li>• <strong>Secrets:</strong> Usa CANVA_ACCESS_TOKEN das secrets se campo estiver vazio</li>
        </ul>
      </div>

      {/* Instruções para obter o token */}
      <div className="bg-yellow-50 border-2 border-yellow-200 rounded-lg p-4 shadow-brutal">
        <h3 className="font-bold mb-2 flex items-center space-x-2">
          <AlertTriangle className="w-4 h-4 text-yellow-600" />
          <span>📝 Como obter tokens do Canva</span>
        </h3>
        <div className="text-sm text-gray-700 space-y-3">
          <div>
            <p><strong>🔑 Para Access Token (Modo Manual):</strong></p>
            <ol className="list-decimal list-inside space-y-1 ml-4">
              <li>Abra o N8N: <code className="bg-gray-200 px-1 rounded">https://automate.felvieira.com.br</code></li>
              <li>Execute qualquer nó do Canva no fluxo</li>
              <li>Abra <strong>DevTools (F12)</strong> → aba <strong>Network</strong></li>
              <li>Procure request para <code className="bg-gray-200 px-1 rounded">api.canva.com</code></li>
              <li>Na aba <strong>Headers</strong>, copie o valor após <code className="bg-gray-200 px-1 rounded">Authorization: Bearer</code></li>
            </ol>
          </div>
          
          <div>
            <p><strong>🔄 Para Refresh Token (Renovação Automática):</strong></p>
            <ol className="list-decimal list-inside space-y-1 ml-4">
              <li>No <strong>DevTools</strong>, procure request para <code className="bg-gray-200 px-1 rounded">api.canva.com/rest/v1/oauth/token</code></li>
              <li>Na aba <strong>Response</strong>, copie o valor <code className="bg-gray-200 px-1 rounded">"refresh_token"</code></li>
              <li>Ou vá em <strong>Credentials → Canva Connection</strong> no N8N</li>
            </ol>
          </div>

          <div className="bg-green-50 border border-green-200 rounded p-2 mt-3">
            <p className="text-green-800 text-xs">
              💡 <strong>Dica:</strong> O modo de renovação automática testa se a Edge Function consegue renovar tokens sozinha, 
              simulando o comportamento em produção sem intervenção manual.
            </p>
          </div>

          <div className="bg-blue-50 border border-blue-200 rounded p-2 mt-2">
            <p className="text-blue-800 text-xs">
              🔐 <strong>Token das Secrets:</strong> Se você já configurou CANVA_ACCESS_TOKEN nas secrets do Supabase, 
              pode deixar o campo vazio no modo manual que a Edge Function usará automaticamente!
            </p>
          </div>
        </div>
      </div>
    </div>
  )
} 