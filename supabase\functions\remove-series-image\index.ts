import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { generationId, imageIndex } = await req.json()

    console.log('Removing image from series:', { generationId, imageIndex })

    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Authenticate user
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      return new Response(
        JSON.stringify({ error: 'No authorization header' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    const token = authHeader.replace('Bearer ', '')
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)
    
    if (authError || !user) {
      console.error('Authentication error:', authError)
      return new Response(
        JSON.stringify({ error: 'User not authenticated' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Get the current generation
    const { data: generation, error: fetchError } = await supabase
      .from('cover_generations')
      .select('generated_covers')
      .eq('id', generationId)
      .eq('user_id', user.id)
      .single()

    if (fetchError || !generation) {
      console.error('Error fetching generation:', fetchError)
      return new Response(
        JSON.stringify({ error: 'Generation not found' }),
        { status: 404, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Remove the specific image from the array
    const currentCovers = generation.generated_covers || []
    if (imageIndex < 0 || imageIndex >= currentCovers.length) {
      return new Response(
        JSON.stringify({ error: 'Invalid image index' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Create new array without the specified image
    const updatedCovers = currentCovers.filter((_, index) => index !== imageIndex)

    // Update the generation in the database
    const { error: updateError } = await supabase
      .from('cover_generations')
      .update({ generated_covers: updatedCovers })
      .eq('id', generationId)
      .eq('user_id', user.id)

    if (updateError) {
      console.error('Error updating generation:', updateError)
      return new Response(
        JSON.stringify({ error: 'Failed to update generation' }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    console.log(`Successfully removed image at index ${imageIndex} from generation ${generationId}`)

    return new Response(
      JSON.stringify({ 
        success: true, 
        message: 'Image removed successfully',
        remainingImages: updatedCovers.length
      }),
      { 
        status: 200, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )

  } catch (error) {
    console.error('Error in remove-series-image function:', error)
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
}) 