import { ReactNode } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { X } from 'lucide-react'

interface BrutalistModalProps {
  isOpen: boolean
  onClose: () => void
  title: string
  icon?: ReactNode
  children: ReactNode
  footerActions?: ReactNode
  maxWidth?: string
  maxHeight?: string
}

export default function BrutalistModal({
  isOpen,
  onClose,
  title,
  icon,
  children,
  footerActions,
  maxWidth = "max-w-4xl",
  maxHeight = "max-h-full"
}: BrutalistModalProps) {
  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black bg-opacity-80 flex items-center justify-center z-50 p-4"
          onClick={onClose}
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0, y: 20 }}
            animate={{ scale: 1, opacity: 1, y: 0 }}
            exit={{ scale: 0.9, opacity: 0, y: 20 }}
            className={`relative ${maxWidth} ${maxHeight} bg-brand-white border-4 border-brand-black shadow-brutal-lg rounded-xl overflow-hidden`}
            onClick={(e) => e.stopPropagation()}
          >
            {/* Header with close button */}
            <div className="bg-brand-primary border-b-4 border-brand-black p-4 flex items-center justify-between">
              <div className="flex items-center space-x-3">
                {icon && (
                  <div className="w-8 h-8 bg-brand-white border-2 border-brand-black rounded-lg flex items-center justify-center">
                    {icon}
                  </div>
                )}
                <h3 className="font-black text-lg text-brand-black">{title}</h3>
              </div>
              <button
                onClick={onClose}
                className="p-2 bg-brand-accent text-white border-2 border-brand-black shadow-brutal-sm hover:shadow-brutal-hover active:shadow-brutal-pressed transition-all rounded-md"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
            
            {/* Content */}
            <div className="p-6 bg-brand-white">
              {children}
            </div>
            
            {/* Footer Actions */}
            {footerActions && (
              <div className="bg-brand-secondary border-t-4 border-brand-black p-4">
                {footerActions}
              </div>
            )}
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}