# 🎬 AI Movie Cover Generator

Um gerador de capas de filmes usando IA, integrado com **sistema de créditos** e pagamentos via Stripe.

## ✨ Funcionalidades

- 🎨 **Geração de Capas**: Crie capas personalizadas para Netflix, Disney+ e Amazon Prime
- 🖼️ **Tipos de Foto**: Individual ou casal
- 🌍 **Multi-idioma**: Português e Inglês
- 💳 **Sistema de Créditos**: Compre créditos e use conforme necessário
- 🔄 **Regeneração**: Regenere capas individuais gastando créditos
- 📱 **Interface Responsiva**: Funciona em desktop e mobile
- 🔐 **Autenticação**: Sistema completo com Supabase Auth
- 💰 **Pagamentos**: Integração completa com Stripe

## 🎯 Sistema de Créditos

### Como Funciona
- **1 crédito = 1 capa de filme**
- **3 créditos = 1 poster final**
- **Regeneração custa créditos adicionais**

### Planos Disponíveis
- **Starter Credits**: 30 créditos por R$ 49,90
- **Popular Credits**: 100 créditos por R$ 99,90 (melhor custo-ben<PERSON><PERSON><PERSON>)
- **Master Credits**: 300 créditos por R$ 249,90 (uso profissional)

### Exemplos de Uso
- **Netflix (12 capas)**: 12 créditos + 3 para poster = 15 créditos
- **Disney+ (9 capas)**: 9 créditos + 3 para poster = 12 créditos
- **Amazon (11 capas)**: 11 créditos + 3 para poster = 14 créditos

## 🛠️ Tecnologias

- **Frontend**: React + TypeScript + Vite
- **Backend**: Supabase (PostgreSQL + Edge Functions)
- **Pagamentos**: Stripe
- **Styling**: TailwindCSS
- **IA**: Stable Diffusion via API
- **Deploy**: Railway

## 📁 Estrutura do Projeto

```
ai-streaming-poster/
├── src/
│   ├── components/          # Componentes React
│   ├── hooks/              # Custom hooks
│   ├── lib/                # Configurações (Supabase, Stripe)
│   ├── pages/              # Páginas da aplicação
│   ├── types/              # Tipos TypeScript
│   └── utils/              # Utilitários
├── supabase/
│   ├── functions/          # Edge Functions
│   └── migrations/         # Migrações do banco
└── public/                 # Arquivos estáticos
```

## 🚀 Configuração

### 1. Variáveis de Ambiente

```bash
# Supabase
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# Stripe
VITE_STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key
STRIPE_SECRET_KEY=your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=your_webhook_secret

# Stripe Product IDs (Créditos)
VITE_STRIPE_STARTER_CREDITS_ID=prod_xxx
VITE_STRIPE_POPULAR_CREDITS_ID=prod_xxx
VITE_STRIPE_MASTER_CREDITS_ID=prod_xxx
```

### 2. Banco de Dados

Execute as migrações para criar as tabelas necessárias:

```sql
-- Tabela de compras de créditos
CREATE TABLE credit_purchases (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id),
  credits INTEGER NOT NULL,
  amount INTEGER NOT NULL,
  currency VARCHAR(3) DEFAULT 'brl',
  stripe_payment_intent_id VARCHAR,
  status VARCHAR DEFAULT 'pending',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE
);

-- Tabela de uso de créditos
CREATE TABLE credit_usage (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id),
  credits_used INTEGER NOT NULL,
  description TEXT,
  generation_id UUID,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## 🔧 Edge Functions

### Funções de Créditos
- **`check-credit-balance`**: Verifica saldo de créditos
- **`consume-credits`**: Consome créditos para gerações
- **`create-payment-intent`**: Cria intenção de pagamento para créditos
- **`stripe-webhook`**: Processa webhooks do Stripe

### Funções de Geração
- **`generate-covers`**: Gera capas completas
- **`generate-single-cover`**: Gera capa individual
- **`regenerate-cover`**: Regenera capa específica
- **`generate-print-poster`**: Gera poster final

## 💳 Integração Stripe

### Configuração
1. Crie produtos no Stripe para cada plano de créditos
2. Configure webhooks para processar pagamentos
3. Defina as variáveis de ambiente com os IDs dos produtos

### Fluxo de Pagamento
1. Usuário seleciona plano de créditos
2. Sistema cria payment intent via `create-payment-intent`
3. Usuário completa pagamento no frontend
4. Webhook processa pagamento e adiciona créditos
5. Usuário pode usar créditos para gerar capas

## 🎨 Uso da Aplicação

### 1. Cadastro/Login
- Crie uma conta ou faça login
- Sistema mostra saldo de créditos disponíveis

### 2. Geração de Capas
- Faça upload de uma foto
- Escolha tipo (individual/casal)
- Selecione plataforma (Netflix/Disney/Amazon)
- Defina idioma e configurações
- Sistema verifica se há créditos suficientes
- Gera capas automaticamente

### 3. Gerenciamento
- Visualize histórico de gerações
- Acompanhe uso de créditos
- Regenere capas específicas
- Baixe resultados em alta qualidade

## 📊 Dashboard

O dashboard oferece:
- **Visão Geral**: Saldo de créditos e estatísticas
- **Compras**: Histórico de compras de créditos
- **Uso**: Histórico de consumo de créditos
- **Gerações**: Todas as capas geradas

## 🚀 Deploy

### Railway
1. Conecte repositório ao Railway
2. Configure variáveis de ambiente
3. Deploy automático via Git

### Supabase
1. Configure projeto no Supabase
2. Execute migrações
3. Deploy das Edge Functions

## 📝 Licença

Este projeto está sob a licença MIT. Veja o arquivo [LICENSE](LICENSE) para mais detalhes.

## 🤝 Contribuição

Contribuições são bem-vindas! Por favor, abra uma issue ou pull request.

## 📞 Contato

Para dúvidas ou suporte, entre em contato através do formulário na aplicação ou email: <EMAIL># teste
