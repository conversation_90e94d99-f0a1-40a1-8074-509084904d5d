import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  BarChart3, 
  Image as ImageIcon, 
  Film, 
  FileImage, 
  User,
  Calendar,
  TrendingUp,
  Eye,
  Download,
  Play,
  Star,
  Clock,
  ChevronDown,
  X,
  Grid3X3
} from 'lucide-react'
import { supabase } from '../lib/supabase'
import { showToast } from '../utils/toast'
import { formatDate as formatDateUtil } from '../utils/dateUtils'

interface DashboardStats {
  totalCovers: number // Imagens de filmes/séries individuais
  totalPosters: number // Produtos finais completos
  totalBaseImages: number // Fotos originais do usuário
  recentActivity: {
    date: string
    type: 'cover' | 'poster' | 'base'
    count: number
  }[]
  platformBreakdown: {
    netflix: number
    disney: number
    amazon: number
  }
}

interface RecentGeneration {
  id: string
  movie_title: string
  streaming_platform: string
  created_at: string
  cover_count: number
  poster_url?: string
  latest_cover_url?: string // 🔥 NOVO: URL da última capa gerada
  all_covers?: string[] // 🔥 NOVO: Todas as capas para o modal
}

interface DashboardProps {
  user: any
  setActiveTab: (tab: 'profile' | 'generations' | 'logs' | 'packs' | 'payments') => void;
}

// 🔥 NOVO: Tipos de agrupamento
type GroupingType = 'title' | 'platform'

export default function Dashboard({ user, setActiveTab }: DashboardProps) {
  const [stats, setStats] = useState<DashboardStats>({
    totalCovers: 0,
    totalPosters: 0,
    totalBaseImages: 0,
    recentActivity: [],
    platformBreakdown: { netflix: 0, disney: 0, amazon: 0 }
  })
  const [isLoading, setIsLoading] = useState(true)
  const [recentImages, setRecentImages] = useState<any[]>([])
  const [recentGenerations, setRecentGenerations] = useState<RecentGeneration[]>([])
  
  // 🔥 NOVOS ESTADOS
  const [groupingType, setGroupingType] = useState<GroupingType>('title')
  const [selectedGeneration, setSelectedGeneration] = useState<RecentGeneration | null>(null)
  const [isModalOpen, setIsModalOpen] = useState(false)
  
  // 🔥 NOVO: Estado para modal de imagem ampliada
  const [selectedImageModal, setSelectedImageModal] = useState<string | null>(null)

  // 🔥 NOVA FUNÇÃO: Obter aspect ratio por tipo
  const getImageAspectRatio = (item: any) => {
    if (item.type === 'poster') return 'aspect-[16/9]'
    if (item.type === 'capa' || item.type === 'cover') return 'aspect-[2/3]'
    return 'aspect-square' // fallback
  }

  // 🔥 NOVA FUNÇÃO: Obter label do tipo
  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'capa':
        return 'Capa de Filme/Série';
      case 'cover':
        return 'Cover';
      case 'poster':
        return 'Poster';
      default:
        return 'Item';
    }
  }

  useEffect(() => {
    if (user) {
      loadDashboardData()
    }
  }, [user, groupingType]) // 🔥 Recarregar quando mudar agrupamento

  const loadDashboardData = async () => {
    try {
      setIsLoading(true)

      // 🔥 Load all cover generations for the user
      const { data: allGenerations, error: generationsError } = await supabase
        .from('cover_generations')
        .select('streaming_platform, generated_covers')
        .eq('user_id', user.id);

      if (generationsError) {
        console.error("Error fetching generations data:", generationsError);
        throw generationsError;
      }

      // 🔥 Correctly calculate total covers and platform breakdown
      let totalCoversCount = 0;
      const platformBreakdown = { netflix: 0, disney: 0, amazon: 0 };

      allGenerations?.forEach(generation => {
        const coversInGeneration = Array.isArray(generation.generated_covers) ? generation.generated_covers.length : 0;
        totalCoversCount += coversInGeneration;

        if (generation.streaming_platform && platformBreakdown.hasOwnProperty(generation.streaming_platform)) {
          platformBreakdown[generation.streaming_platform as keyof typeof platformBreakdown] += coversInGeneration;
        }
      });

      // 🔥 Load poster images count (produtos finais)
      const { count: postersCount } = await supabase
        .from('poster_images')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', user.id)

      // 🔥 Load base images count (fotos originais)
      const { count: baseCount } = await supabase
        .from('base_images')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', user.id)

      // 🔥 BUSCAR APENAS CAPAS E POSTERS (SEM FOTO BASE)!
      
      // 1️⃣ COVER_IMAGES - Capas de filmes/séries + Covers destacados
      const { data: allCoverImages } = await supabase
        .from('cover_images')
        .select('*')
        .eq('user_id', user.id)
        .not('movie_title', 'is', null)
        .order('created_at', { ascending: false })
        .limit(50)

      // 2️⃣ POSTER_IMAGES - Posters finais
      const { data: allPosterImages } = await supabase
        .from('poster_images')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .limit(20)





      // 3️⃣ JUNTAR APENAS CAPAS E POSTERS (SEM FOTO BASE)
      const allRecentImages = [
        // De cover_images (capas + covers)
        ...(allCoverImages || []).map(cover => ({
          ...cover,
          type: cover.movie_title?.startsWith('Capa ') ? 'cover' : 'capa',
          url: cover.image_url,
          displayTitle: cover.movie_title,
                              aspectRatio: '3:4'
        })),
        // De poster_images - 🔥 CORRIGIDO: usar poster_url em vez de cover_url
        ...(allPosterImages || []).map(poster => ({
          ...poster,
          type: 'poster',
          url: poster.poster_url || poster.cover_url, // 🔥 FALLBACK: poster_url primeiro, depois cover_url
          displayTitle: `Poster ${poster.streaming_platform?.toUpperCase()}`,
                              aspectRatio: '21:9'
        }))
      ]

      // 4️⃣ ORDENAR POR DATA E PEGAR OS 12 MAIS RECENTES
      allRecentImages.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
      const recentImages = allRecentImages.slice(0, 12)



      // 🔥 NOVO: Load data baseado no tipo de agrupamento
      await loadGroupedGenerations()

      setStats({
        totalCovers: totalCoversCount,
        totalPosters: postersCount || 0,
        totalBaseImages: baseCount || 0,
        recentActivity: [], // Implementar depois se necessário
        platformBreakdown
      })

      setRecentImages(recentImages || [])
    } catch (error) {
      console.error('Error loading dashboard data:', error)
      showToast.error('Erro ao carregar dados do dashboard')
    } finally {
      setIsLoading(false)
    }
  }

  // 🔥 NOVA FUNÇÃO: Carregar dados agrupados
  const loadGroupedGenerations = async () => {
    try {
      console.log('🔥 loadGroupedGenerations - Tipo:', groupingType);
      if (groupingType === 'title') {
        // Agrupar por título (apenas CAPAS de séries/filmes reais)
        const { data: generationData, error: titleError } = await supabase
          .from('cover_images')
          .select('id, movie_title, streaming_platform, created_at, image_url')
          .eq('user_id', user.id)
          .not('movie_title', 'is', null)
          .order('created_at', { ascending: false })
        
        if (titleError) {
          console.error('🔥 Erro na query (título):', titleError);
          return;
        }

        console.log('🔥 Dados brutos (título):', generationData);
        // Agrupar por título + plataforma
        const groupedByTitle = generationData?.reduce((acc: any[], current) => {
          const key = `${current.movie_title}-${current.streaming_platform}`
          const existing = acc.find(g => `${g.movie_title}-${g.streaming_platform}` === key)
          
          if (!existing) {
            acc.push({
              ...current,
              id: key,
              all_covers: [current.image_url],
              latest_cover_url: current.image_url
            })
          } else {
            existing.all_covers.push(current.image_url)
            // Manter a mais recente como latest
            if (new Date(current.created_at) > new Date(existing.created_at)) {
              existing.latest_cover_url = current.image_url
              existing.created_at = current.created_at
            }
          }
          return acc
        }, []) || []

        // Buscar contagem e posters para cada grupo
        const groupsWithDetails = await Promise.all(
          groupedByTitle.slice(0, 8).map(async (group) => {
            const { data: posterData } = await supabase
              .from('poster_images')
              .select('poster_url')
              .eq('user_id', user.id)
              .order('created_at', { ascending: false })
              .limit(1)

            return {
              ...group,
              cover_count: group.all_covers.length,
              poster_url: posterData?.[0]?.poster_url
            }
          })
        )

        console.log('🔥 Grupos por título:', groupsWithDetails);
        setRecentGenerations(groupsWithDetails)

      } else {
        // Agrupar por plataforma (apenas CAPAS de séries/filmes reais)
        const { data: generationData, error: platformError } = await supabase
          .from('cover_images')
          .select('streaming_platform, created_at, image_url, movie_title')
          .eq('user_id', user.id)
          .not('streaming_platform', 'is', null)
          .order('created_at', { ascending: false })
        
        if (platformError) {
          console.error('🔥 Erro na query (plataforma):', platformError);
          return;
        }

        console.log('🔥 Dados brutos (plataforma):', generationData);
        // Agrupar por plataforma
        const groupedByPlatform = generationData?.reduce((acc: { [key: string]: any }, current) => {
          const platform = current.streaming_platform;
          
          if (!acc[platform]) {
            acc[platform] = {
              id: platform,
              movie_title: `${getPlatformName(platform)} - Todas as séries`,
              streaming_platform: platform,
              created_at: current.created_at,
              all_covers: [current.image_url],
              latest_cover_url: current.image_url
            };
          } else {
            acc[platform].all_covers.push(current.image_url);
            if (new Date(current.created_at) > new Date(acc[platform].created_at)) {
              acc[platform].latest_cover_url = current.image_url;
              acc[platform].created_at = current.created_at;
            }
          }
          
          return acc;
        }, {}) || {};

        // Converter objeto de volta para array
        const groupedArray = Object.values(groupedByPlatform);

        // Adicionar contagem de capas a cada grupo
        const platformsWithDetails = groupedArray.map(group => ({
          ...group,
          cover_count: group.all_covers.length
        }));

        console.log('🔥 Grupos por plataforma:', platformsWithDetails);
        setRecentGenerations(platformsWithDetails)
      }
    } catch (error) {
      console.error('Error loading grouped generations:', error)
    }
  }

  const getPlatformColor = (platform: string) => {
    switch (platform) {
      case 'netflix': return 'bg-red-500'
      case 'disney': return 'bg-blue-500'
      case 'amazon': return 'bg-gray-800'
      default: return 'bg-gray-400'
    }
  }

  const getPlatformName = (platform: string) => {
    switch (platform) {
      case 'netflix': return 'Netflix'
      case 'disney': return 'Disney+'
      case 'amazon': return 'Amazon Prime'
      default: return platform
    }
  }

  // 🔥 Usar função utilitária para formatação consistente
  const formatDate = formatDateUtil

  // 🔥 NOVA FUNÇÃO: Abrir modal com imagens filtradas
  const handleViewCovers = (generation: RecentGeneration) => {
    setSelectedGeneration(generation)
    setIsModalOpen(true)
  }

  // 🔥 NOVA FUNÇÃO: Download de todas as capas do grupo
  const handleDownloadAll = async (generation: RecentGeneration) => {
    if (!generation.all_covers || generation.all_covers.length === 0) return

    showToast.info(`Baixando ${generation.all_covers.length} capas...`)
    
    for (let i = 0; i < generation.all_covers.length; i++) {
      try {
        const response = await fetch(generation.all_covers[i])
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        
        const link = document.createElement('a')
        link.href = url
        link.download = `${generation.movie_title.replace(/[^a-zA-Z0-9]/g, '-')}-${i + 1}.jpg`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        
        window.URL.revokeObjectURL(url)
        
        // Delay entre downloads
        if (i < generation.all_covers.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 500))
        }
      } catch (error) {
        console.error(`Erro ao baixar capa ${i + 1}:`, error)
      }
    }
    
    showToast.success('Download concluído!')
  }

  if (isLoading) {
    return (
      <div className="p-8 bg-brand-white border-2 border-brand-black shadow-brutal rounded-lg">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 rounded w-1/3"></div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-8">
     {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Total Covers - NOMENCLATURA ATUALIZADA */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-brand-white border-2 border-brand-black shadow-brutal rounded-lg p-6"
        >
          <div className="flex items-center justify-between mb-4">
            <div className="w-12 h-12 bg-brand-secondary rounded-lg flex items-center justify-center border-2 border-brand-black">
              <Film className="w-6 h-6 text-brand-black" />
            </div>
            <span className="text-3xl font-black text-brand-text">{stats.totalCovers}</span>
          </div>
          <h3 className="text-lg font-bold text-brand-text mb-1">Capas Geradas</h3>
          <p className="text-sm text-brand-text/70">Total de capas de filmes/séries</p>
        </motion.div>

        {/* Total Posters - NOMENCLATURA ATUALIZADA */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-brand-white border-2 border-brand-black shadow-brutal rounded-lg p-6"
        >
          <div className="flex items-center justify-between mb-4">
            <div className="w-12 h-12 bg-brand-primary rounded-lg flex items-center justify-center border-2 border-brand-black">
              <FileImage className="w-6 h-6 text-brand-black" />
            </div>
            <span className="text-3xl font-black text-brand-text">{stats.totalPosters}</span>
          </div>
          <h3 className="text-lg font-bold text-brand-text mb-1">Posters Finais</h3>
          <p className="text-sm text-brand-text/70">Produtos completos criados</p>
        </motion.div>

        {/* Total Base Images - NOMENCLATURA ATUALIZADA */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-brand-white border-2 border-brand-black shadow-brutal rounded-lg p-6"
        >
          <div className="flex items-center justify-between mb-4">
            <div className="w-12 h-12 bg-yellow-400 rounded-lg flex items-center justify-center border-2 border-brand-black">
              <User className="w-6 h-6 text-brand-black" />
            </div>
            <span className="text-3xl font-black text-brand-text">{stats.totalBaseImages}</span>
          </div>
          <h3 className="text-lg font-bold text-brand-text mb-1">Fotos Base</h3>
          <p className="text-sm text-brand-text/70">Fotos originais enviadas</p>
        </motion.div>
      </div>

      {/* Platform Breakdown */}
      <div className="bg-brand-white border-2 border-brand-black shadow-brutal rounded-lg p-6">
        <h3 className="text-xl font-black text-brand-text mb-6 flex items-center space-x-2">
          <TrendingUp className="w-5 h-5" />
          <span>Distribuição por Plataforma</span>
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {Object.entries(stats.platformBreakdown).map(([platform, count]) => (
            <div key={platform} className="flex items-center space-x-3 p-4 bg-gray-50 rounded-lg border border-gray-200">
              <div className={`w-4 h-4 rounded-full ${getPlatformColor(platform)}`}></div>
              <div className="flex-1">
                <div className="font-bold text-brand-text">{getPlatformName(platform)}</div>
                <div className="text-sm text-brand-text/70">{count} capas de séries/filmes</div>
              </div>
              <div className="text-2xl font-black text-brand-text">{count}</div>
            </div>
          ))}
        </div>
      </div>

      {/* 🔥 NOVA SEÇÃO: Séries e Filmes Recentes com Dropdown */}
      {(
        <div className="bg-brand-white border-2 border-brand-black shadow-brutal rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-2">
              <Play className="w-5 h-5" />
              <h3 className="text-xl font-black text-brand-text">Séries e Filmes</h3>
            </div>
            
            {/* 🔥 NOVO DROPDOWN */}
            <div className="relative">
              <select
                value={groupingType}
                onChange={(e) => setGroupingType(e.target.value as GroupingType)}
                className="appearance-none bg-brand-white border-2 border-brand-black rounded-lg px-4 py-2 pr-8 font-semibold text-brand-text hover:shadow-brutal-sm focus:ring-2 focus:ring-brand-accent focus:border-brand-accent transition-all"
              >
                <option value="title">Agrupar por Título</option>
                <option value="platform">Agrupar por Streaming</option>
              </select>
              <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-brand-text pointer-events-none" />
            </div>
          </div>
          
          <p className="text-sm text-brand-text/70 mb-4">
            📊 {groupingType === 'title' 
              ? 'Agrupados por título - mostrando o total de capas geradas para cada filme/série' 
              : 'Agrupados por plataforma - mostrando todas as capas de cada streaming'
            }
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {recentGenerations.length === 0 ? (
              <div className="col-span-full text-center py-8">
                <p className="text-brand-text/70">Nenhuma geração encontrada. Gerações: {recentGenerations.length}</p>
                <p className="text-xs text-brand-text/50 mt-2">Agrupamento: {groupingType}</p>
              </div>
            ) : (
              recentGenerations.map((generation, index) => (
              <motion.div
                key={generation.id}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: index * 0.1 }}
                className="group relative bg-gray-50 rounded-lg border-2 border-gray-200 hover:border-brand-black transition-all duration-200 overflow-hidden"
              >
                {/* 🔥 NOVO: Preview da última capa */}
                {generation.latest_cover_url && (
                  <div className="relative h-32 overflow-hidden">
                    <img
                      src={generation.latest_cover_url}
                      alt={generation.movie_title}
                      className="w-full h-full object-cover"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
                    <div className="absolute bottom-2 left-2 text-white text-xs font-bold">
                      +{generation.cover_count} capas
                    </div>
                  </div>
                )}

                <div className="p-4">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1">
                      <h4 className="font-bold text-brand-text text-sm line-clamp-2 mb-1">
                        {generation.movie_title || 'Título não definido'}
                      </h4>
                      <div className="flex items-center space-x-2 text-xs text-brand-text/70">
                        <Clock className="w-3 h-3" />
                        <span>{formatDate(generation.created_at)}</span>
                      </div>
                    </div>
                    {generation.streaming_platform && (
                      <div className={`px-2 py-1 text-xs font-bold text-white rounded ${getPlatformColor(generation.streaming_platform)}`}>
                        {getPlatformName(generation.streaming_platform).charAt(0)}
                      </div>
                    )}
                  </div>
                  
                  <div className="flex items-center justify-between text-xs">
                    <div className="flex items-center space-x-2 text-brand-text/70">
                      <Film className="w-3 h-3" />
                      <span>{generation.cover_count} capas geradas</span>
                    </div>
                    {generation.poster_url && (
                      <div className="flex items-center space-x-1 text-green-600">
                        <Star className="w-3 h-3" />
                        <span>Poster</span>
                      </div>
                    )}
                  </div>
                </div>
                
                {/* 🔥 MELHORADO: Hover actions */}
                <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center space-x-2">
                  <button 
                    onClick={() => handleViewCovers(generation)}
                    className="w-10 h-10 bg-brand-white rounded-full flex items-center justify-center border border-brand-black hover:bg-gray-100 transition-colors"
                    title="Ver todas as capas"
                  >
                    <Eye className="w-5 h-5 text-brand-black" />
                  </button>
                  <button 
                    onClick={() => handleDownloadAll(generation)}
                    className="w-10 h-10 bg-brand-white rounded-full flex items-center justify-center border border-brand-black hover:bg-gray-100 transition-colors"
                    title="Baixar todas as capas"
                  >
                    <Download className="w-5 h-5 text-brand-black" />
                  </button>
                  {generation.all_covers && generation.all_covers.length > 1 && (
                    <button className="w-10 h-10 bg-brand-secondary rounded-full flex items-center justify-center border border-brand-black hover:bg-brand-accent transition-colors">
                      <Grid3X3 className="w-5 h-5 text-brand-black" />
                    </button>
                  )}
                </div>
              </motion.div>
              ))
            )}
          </div>
        </div>
      )}

      {/* 🔥 NOVA SEÇÃO: Capas de Filmes e Séries Recentes */}
      {recentImages.length > 0 && (
        <div className="bg-brand-white border-2 border-brand-black shadow-brutal rounded-lg p-6">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-2">
              <Calendar className="w-5 h-5" />
              <h3 className="text-xl font-black text-brand-text">Últimas Gerações</h3>
            </div>
            <span className="text-sm text-brand-text/70 bg-gray-100 px-3 py-1 rounded-full border border-gray-300">
              {recentImages.length} itens gerados
            </span>
          </div>
          
          <p className="text-sm text-brand-text/70 mb-4">
            🎬 Histórico completo: capas de séries/filmes, covers destacados e posters ordenados por data - clique no olho para ampliar
          </p>
          
          {/* Grid horizontal scrollável similar à imagem */}
          <div className="overflow-x-auto pb-2">
            <div className="flex space-x-4 min-w-max">
              {recentImages.map((image, index) => (
                <motion.div
                  key={image.id}
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="group relative flex-shrink-0 w-48 h-72 bg-gray-100 rounded-xl overflow-hidden border-2 border-gray-200 hover:border-brand-black transition-all duration-300 shadow-lg hover:shadow-xl"
                >
                  <img
                    src={image.url}
                    alt={image.displayTitle}
                    className="w-full h-full object-cover"
                    loading="lazy"
                  />
                  
                  {/* Gradient overlay */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent" />
                  
                  {/* Platform badge - Top Left */}
                  {image.streaming_platform && (
                    <div className={`absolute top-3 left-3 px-3 py-1 text-xs font-bold text-white rounded-md shadow-md ${getPlatformColor(image.streaming_platform)}`}>
                      {getPlatformName(image.streaming_platform).toUpperCase()}
                    </div>
                  )}
                  
                  {/* Title and date - Bottom */}
                  <div className="absolute bottom-3 left-3 right-3">
                    <h4 className="text-white font-bold text-sm line-clamp-2 mb-1">
                      {image.displayTitle}
                    </h4>
                    <div className="flex items-center justify-between text-xs text-white/80">
                      <div className="flex items-center space-x-2">
                        <Clock className="w-3 h-3" />
                        <span>{formatDate(image.created_at)}</span>
                      </div>
                      <span className={`px-2 py-1 rounded text-xs font-bold ${
                        image.type === 'poster' ? 'bg-purple-600' : 
                        image.type === 'cover' ? 'bg-blue-600' : 'bg-green-600'
                      }`}>
                        {image.aspectRatio}
                      </span>
                    </div>
                  </div>

                  
                  {/* Type badge - Top Right */}
                  <div className={`absolute top-3 right-12 px-2 py-1 text-xs font-bold rounded-md shadow-md ${
                    image.type === 'poster' ? 'bg-purple-500 text-white' :
                    image.type === 'cover' ? 'bg-blue-500 text-white' : 'bg-green-500 text-white'
                  }`}>
                    {image.type === 'poster' ? 'Poster' : 
                     image.type === 'cover' ? 'Cover' : 'Capa'}
                  </div>
                  
                  {/* Hover actions overlay */}
                  <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center space-x-3">
                    <button 
                      onClick={() => setSelectedImageModal(image.url)}
                      className="w-12 h-12 bg-brand-white rounded-full flex items-center justify-center border-2 border-brand-black hover:bg-gray-100 transition-colors shadow-lg"
                      title="Ampliar imagem"
                    >
                      <Eye className="w-5 h-5 text-brand-black" />
                    </button>
                    <button 
                      onClick={() => {
                        const link = document.createElement('a')
                        link.href = image.url
                        link.download = `${image.type}-${image.displayTitle?.replace(/[^a-zA-Z0-9]/g, '-') || 'image'}.jpg`
                        link.click()
                        showToast.success('Download iniciado!')
                      }}
                      className="w-12 h-12 bg-brand-primary rounded-full flex items-center justify-center border-2 border-brand-black hover:bg-brand-accent transition-colors shadow-lg"
                      title="Baixar imagem"
                    >
                      <Download className="w-5 h-5 text-brand-black" />
                    </button>
                  </div>
                  
                  {/* Number indicator */}
                  <div className="absolute top-3 right-3 w-8 h-8 bg-black/70 text-white rounded-full flex items-center justify-center text-sm font-bold">
                    {index + 1}
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
          
          {/* Action buttons */}
          <div className="flex items-center justify-between mt-6 pt-4 border-t border-gray-200">
            <div className="text-sm text-brand-text/70">
              Mostrando os {recentImages.length} itens mais recentes
            </div>
            <div className="flex space-x-3">
              <button
                onClick={() => {
                  setActiveTab('generations')
                  window.scrollTo({ top: 0, behavior: 'smooth' })
                }}
                className="px-4 py-2 bg-brand-white text-brand-black rounded-lg border-2 border-brand-black shadow-brutal-sm hover:shadow-brutal-hover transition-all font-bold flex items-center space-x-2"
              >
                <Eye className="w-4 h-4" />
                <span>Ver Todos</span>
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 🔥 NOVO MODAL: Visualizar todas as capas */}
      <AnimatePresence>
        {isModalOpen && selectedGeneration && (
          <motion.div 
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/80 backdrop-blur-sm flex items-center justify-center z-50 p-4"
            onClick={() => setIsModalOpen(false)}
          >
            <motion.div 
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="relative bg-brand-white rounded-lg border-2 border-brand-black shadow-brutal-lg max-w-6xl w-full max-h-[90vh] overflow-hidden"
              onClick={(e) => e.stopPropagation()}
            >
              {/* Header do Modal */}
              <div className="p-6 border-b-2 border-brand-black">
                <div className="flex justify-between items-center">
                  <div>
                    <h3 className="text-2xl font-black text-brand-text">{selectedGeneration.movie_title}</h3>
                    <div className="flex items-center space-x-4 mt-2">
                      <div className={`px-3 py-1 text-sm font-bold text-white rounded ${getPlatformColor(selectedGeneration.streaming_platform)}`}>
                        {getPlatformName(selectedGeneration.streaming_platform)}
                      </div>
                      <span className="text-sm text-brand-text/70">
                        {selectedGeneration.cover_count} capas geradas
                      </span>
                      <span className="text-sm text-brand-text/70">
                        {formatDate(selectedGeneration.created_at)}
                      </span>
                    </div>
                  </div>
                  <button
                    onClick={() => setIsModalOpen(false)}
                    className="p-3 bg-brand-white rounded-lg border-2 border-brand-black shadow-brutal-sm hover:shadow-brutal-hover transition-all"
                  >
                    <X size={20} className="text-brand-black" />
                  </button>
                </div>
              </div>

              {/* Grid de Capas */}
              <div className="p-6 max-h-[calc(90vh-200px)] overflow-y-auto">
                <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                  {selectedGeneration.all_covers?.map((coverUrl, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, scale: 0.9 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ delay: index * 0.05 }}
                      className="group relative aspect-[2/3] bg-gray-100 rounded-lg overflow-hidden border-2 border-gray-200 hover:border-brand-black transition-all duration-200"
                    >
                      <img
                        src={coverUrl}
                        alt={`Capa ${index + 1}`}
                        className="w-full h-full object-cover cursor-pointer"
                        onClick={() => window.open(coverUrl, '_blank')}
                      />
                      <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center">
                        <button 
                          onClick={() => {
                            const link = document.createElement('a')
                            link.href = coverUrl
                            link.download = `${selectedGeneration.movie_title.replace(/[^a-zA-Z0-9]/g, '-')}-${index + 1}.jpg`
                            link.click()
                          }}
                          className="w-10 h-10 bg-brand-white rounded-full flex items-center justify-center border border-brand-black hover:bg-gray-100 transition-colors"
                        >
                          <Download className="w-5 h-5 text-brand-black" />
                        </button>
                      </div>
                      <div className="absolute top-2 left-2 px-2 py-1 bg-black/70 text-white text-xs font-bold rounded">
                        #{index + 1}
                      </div>
                    </motion.div>
                  ))}
                </div>
              </div>

              {/* Footer do Modal */}
              <div className="p-4 border-t-2 border-brand-black bg-gray-50">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-brand-text/70">
                    {selectedGeneration.all_covers?.length || 0} capas encontradas
                  </span>
                  <button
                    onClick={() => handleDownloadAll(selectedGeneration)}
                    className="px-4 py-2 bg-brand-primary text-brand-black rounded-lg border-2 border-brand-black shadow-brutal-sm hover:shadow-brutal-hover transition-all font-bold flex items-center space-x-2"
                  >
                    <Download className="w-4 h-4" />
                    <span>Baixar Todas</span>
                  </button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* 🔥 NOVO MODAL: Ampliar Imagem Individual */}
      <AnimatePresence>
        {selectedImageModal && (
          <motion.div 
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/90 backdrop-blur-sm flex items-center justify-center z-50 p-4"
            onClick={() => setSelectedImageModal(null)}
          >
            <motion.div 
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="relative max-w-4xl max-h-[90vh] overflow-hidden"
              onClick={(e) => e.stopPropagation()}
            >
              {/* Botão fechar */}
              <button
                onClick={() => setSelectedImageModal(null)}
                className="absolute -top-4 -right-4 w-12 h-12 bg-brand-white text-brand-black rounded-full border-2 border-brand-black hover:bg-gray-100 transition-colors z-10 shadow-lg flex items-center justify-center"
              >
                <X size={24} />
              </button>
              
              {/* Imagem ampliada */}
              <img
                src={selectedImageModal}
                alt="Imagem ampliada"
                className="w-full h-full object-contain rounded-lg border-2 border-brand-black"
                style={{ maxHeight: '90vh' }}
              />
              
              {/* Botões de ação */}
              <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-3">
                <button
                  onClick={() => window.open(selectedImageModal, '_blank')}
                  className="px-4 py-2 bg-brand-white text-brand-black rounded-lg border-2 border-brand-black shadow-lg hover:bg-gray-100 transition-colors font-bold flex items-center space-x-2"
                >
                  <Eye className="w-4 h-4" />
                  <span>Abrir Original</span>
                </button>
                <button
                  onClick={() => {
                    const link = document.createElement('a')
                    link.href = selectedImageModal
                    link.download = `imagem-ampliada-${Date.now()}.jpg`
                    link.click()
                    showToast.success('Download iniciado!')
                  }}
                  className="px-4 py-2 bg-brand-primary text-brand-black rounded-lg border-2 border-brand-black shadow-lg hover:bg-brand-accent transition-colors font-bold flex items-center space-x-2"
                >
                  <Download className="w-4 h-4" />
                  <span>Baixar</span>
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
} 