-- Migra os dados da tabela movies para a tabela unificada
INSERT INTO unified_movies_series (
    id,
    title,
    streaming_platform,
    type,
    base_prompt,
    safe_prompt,
    gender_male_prompt,
    gender_female_prompt,
    couple_prompt,
    is_active,
    default_creativity_level_id,
    created_at,
    updated_at,
    total_attempts,
    successful_attempts,
    failed_attempts,
    e005_errors,
    success_rate,
    last_attempt_at,
    last_error_message
)
SELECT 
    m.id,
    m.title,
    m.streaming_platform,
    'movie' as type,
    m.base_prompt,
    m.safe_prompt,
    m.gender_male_prompt,
    m.gender_female_prompt,
    m.couple_prompt,
    COALESCE(m.is_active, true) as is_active,
    m.default_creativity_level_id,
    COALESCE(m.created_at, NOW()) as created_at,
    COALESCE(m.updated_at, NOW()) as updated_at,
    COALESCE(gs.total_attempts, 0) as total_attempts,
    COALESCE(gs.successful_attempts, 0) as successful_attempts,
    COALESCE(gs.failed_attempts, 0) as failed_attempts,
    COALESCE(gs.e005_errors, 0) as e005_errors,
    COALESCE(gs.success_rate, 0) as success_rate,
    gs.last_attempt_at,
    gs.last_error_message
FROM 
    movies m
LEFT JOIN LATERAL (
    SELECT 
        movie_id,
        COUNT(*) as total_attempts,
        COUNT(*) FILTER (WHERE success = true) as successful_attempts,
        COUNT(*) FILTER (WHERE success = false) as failed_attempts,
        COUNT(*) FILTER (WHERE error_code = 'E005') as e005_errors,
        CASE 
            WHEN COUNT(*) > 0 THEN ROUND((COUNT(*) FILTER (WHERE success = true)::NUMERIC / COUNT(*)::NUMERIC) * 100, 2)
            ELSE 0 
        END as success_rate,
        MAX(created_at) as last_attempt_at,
        (SELECT error_code FROM movie_generation_stats WHERE movie_id = m.id AND error_code IS NOT NULL ORDER BY created_at DESC LIMIT 1) as last_error_message
    FROM 
        movie_generation_stats
    WHERE 
        movie_id = m.id
    GROUP BY 
        movie_id
) gs ON true
ON CONFLICT (id) DO NOTHING;

-- Migra os dados da tabela movies_series para a tabela unificada (apenas se não existir pelo ID)
INSERT INTO unified_movies_series (
    id,
    title,
    streaming_platform,
    type,
    year,
    genre,
    poster_style_prompt,
    is_active,
    created_at,
    updated_at
)
SELECT 
    gen_random_uuid() as id,
    ms.title,
    ms.streaming_platform,
    LOWER(ms.type) as type,
    ms.year,
    ms.genre,
    ms.poster_style_prompt,
    true as is_active,
    NOW() as created_at,
    NOW() as updated_at
FROM 
    movies_series ms
WHERE NOT EXISTS (
    SELECT 1 FROM unified_movies_series ums 
    WHERE LOWER(ums.title) = LOWER(ms.title) 
    AND ums.streaming_platform = ms.streaming_platform
)
ON CONFLICT (id) DO NOTHING;

-- Atualiza a view para garantir que está usando a tabela unificada
CREATE OR REPLACE VIEW public.movies_with_prompts AS
SELECT 
    m.id,
    m.title,
    m.streaming_platform,
    m.type,
    m.year,
    m.genre,
    m.is_active,
    COALESCE(m.base_prompt, '') as base_prompt,
    COALESCE(m.safe_prompt, '') as safe_prompt,
    COALESCE(m.gender_male_prompt, '') as gender_male_prompt,
    COALESCE(m.gender_female_prompt, '') as gender_female_prompt,
    COALESCE(m.couple_prompt, '') as couple_prompt,
    m.default_creativity_level_id,
    cl.name as default_creativity_level_name,
    cl.prompt_enhancement as default_creativity_prompt_enhancement,
    cl.temperature as default_creativity_temperature,
    m.total_attempts,
    m.successful_attempts,
    m.failed_attempts,
    m.e005_errors,
    m.success_rate,
    m.last_attempt_at,
    m.last_error_message,
    m.created_at,
    m.updated_at
FROM 
    unified_movies_series m
LEFT JOIN 
    creativity_levels cl ON m.default_creativity_level_id = cl.id;
