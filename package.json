{"name": "movie-cover-generator", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "setup-canva": "node scripts/setup-canva-oauth.js"}, "dependencies": {"@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.3.1", "@supabase/supabase-js": "^2.51.0", "@tanstack/react-query": "^5.81.5", "@types/node": "^24.0.3", "@types/react-router-dom": "^5.3.3", "clsx": "^2.1.1", "daisyui": "^5.0.46", "express": "^5.1.0", "framer-motion": "^12.23.0", "i18next": "^25.3.0", "i18next-browser-languagedetector": "^8.2.0", "lucide-react": "^0.344.0", "motion": "^12.23.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hot-toast": "^2.4.1", "react-i18next": "^15.5.3", "react-router-dom": "^6.22.3", "replicate": "^0.34.1", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "open": "^10.1.2", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "terser": "^5.43.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}