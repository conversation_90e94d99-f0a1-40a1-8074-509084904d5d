import React from 'react'
import { useTranslation } from 'react-i18next'
import { Check, Palette } from 'lucide-react'
import { motion } from 'framer-motion'

interface TemplateSelectorProps {
  selectedTemplate: string
  onTemplateChange: (template: string) => void
}

export default function TemplateSelector({ selectedTemplate, onTemplateChange }: TemplateSelectorProps) {
  const { t } = useTranslation()

  const templates = [
    {
      id: 'netflix',
      name: 'Netflix',
      description: t('templateSelector.netflixDesc'),
      color: 'bg-red-500',
      textColor: 'text-white',
      image: '/img/template-netflix.png',
      photos: 12
    },
    {
      id: 'disney',
      name: 'Disney+',
      description: t('templateSelector.disneyDesc'),
      color: 'bg-blue-800',
      textColor: 'text-white',
      image: '/img/template-disney.png',
      photos: 9
    },
    {
      id: 'amazon',
      name: 'Prime Video',
      description: t('templateSelector.amazonDesc'),
      color: 'bg-cyan-500',
      textColor: 'text-white',
      image: '/img/template-amazon.png',
      photos: 11
    }
  ]

  const templateColors: { [key: string]: string } = {
    netflix: 'bg-red-500',
    disney: 'bg-blue-800',
    amazon: 'bg-cyan-500',
  }

  return (
    <div className="w-full max-w-5xl mx-auto">
      <div className="text-center mb-10">
        <div className="w-20 h-20 bg-brand-white rounded-xl flex items-center justify-center mx-auto mb-6 border-2 border-brand-black shadow-brutal">
          <Palette className="w-10 h-10 text-brand-black" />
        </div>
        <h3 className="text-3xl font-black text-brand-text mb-2">{t('templateSelector.title')}</h3>
        <p className="text-lg text-brand-text/80">{t('templateSelector.subtitle')}</p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        {templates.map((template, index) => (
          <div
            key={template.id}
            onClick={() => onTemplateChange(template.id)}
            className={`
              relative cursor-pointer rounded-xl transition-all duration-200 overflow-hidden border-2 border-brand-black
              ${selectedTemplate === template.id 
                ? 'bg-brand-primary shadow-brutal-pressed' 
                : 'bg-brand-white shadow-brutal hover:shadow-brutal-hover'
              }
            `}
          >
            <div className={`${templateColors[template.id]} text-white p-4`}>
              <div className="flex items-center justify-between">
                <h3 className="font-black text-2xl uppercase">{template.name}</h3>
                {selectedTemplate === template.id && (
                  <div className="bg-white rounded-full p-1 border-2 border-brand-black">
                    <Check size={20} className="text-brand-black" />
                  </div>
                )}
              </div>
            </div>

            <div className="p-4 bg-gray-100 h-48 border-y-2 border-brand-black">
              <img
                src={template.image}
                alt={`Template ${template.name}`}
                className="w-full h-full object-contain rounded-lg"
              />
            </div>

            <div className="p-4">
              <p className="text-sm text-brand-text/80 mb-3 h-10">{template.description}</p>
              <div className={`
                px-3 py-1 rounded-lg text-sm font-bold text-center border-2 border-brand-black
                ${selectedTemplate === template.id 
                  ? 'bg-brand-secondary text-brand-black' 
                  : 'bg-gray-200 text-brand-text'
                }
              `}>
                {template.photos} {t('templateSelector.photos')}
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="mt-10 p-6 bg-brand-white rounded-xl border-2 border-brand-black shadow-brutal">
        <div className="flex items-start space-x-4">
          <div className="w-12 h-12 bg-blue-200 rounded-lg flex items-center justify-center flex-shrink-0 border-2 border-brand-black">
            <Palette className="w-6 h-6 text-brand-black" />
          </div>
          <div>
            <h4 className="font-bold text-brand-text mb-2">{t('templateSelector.aboutTemplates')}</h4>
            <p className="text-sm text-brand-text/80 leading-relaxed">
              {t('templateSelector.aboutTemplatesDesc')}
              <strong className="text-red-600"> {t('templateSelector.netflixPhotos')}</strong>, 
              <strong className="text-blue-800"> {t('templateSelector.disneyPhotos')}</strong>, e 
              <strong className="text-cyan-600"> {t('templateSelector.amazonPhotos')}</strong>.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
} 