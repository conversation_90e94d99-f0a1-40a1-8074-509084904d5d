import React, { Component, ReactNode } from 'react'
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>fresh<PERSON><PERSON>, <PERSON>ting<PERSON>, ExternalLink } from 'lucide-react'

interface Props {
  children: ReactNode
}

interface State {
  hasError: boolean
  error: Error | null
  errorInfo: any
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = { hasError: false, error: null, errorInfo: null }
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error, errorInfo: null }
  }

  componentDidCatch(error: Error, errorInfo: any) {
    console.error('🚨 Error Boundary capturou um erro:', error, errorInfo)
    this.setState({ error, errorInfo })
    
    // Detectar erros de CORS
    if (error.message.includes('NetworkError') || error.message.includes('CORS')) {
      console.error('🔒 CORS Error detectado pelo Error Boundary')
    }
  }

  private isCORSError(): boolean {
    const error = this.state.error
    if (!error) return false
    
    return (
      error.message.includes('NetworkError') ||
      error.message.includes('CORS') ||
      error.message.includes('cross-origin') ||
      error.message.includes('fetch')
    )
  }

  private isEnvironmentError(): boolean {
    const error = this.state.error
    if (!error) return false
    
    return (
      error.message.includes('environment') ||
      error.message.includes('VITE_SUPABASE') ||
      error.message.includes('Missing Supabase')
    )
  }

  private handleRefresh = () => {
    window.location.reload()
  }

  private handleReset = () => {
    // Limpar localStorage e sessionStorage
    localStorage.clear()
    sessionStorage.clear()
    window.location.reload()
  }

  render() {
    if (this.state.hasError) {
      const error = this.state.error
      const isCORS = this.isCORSError()
      const isEnv = this.isEnvironmentError()

      return (
        <div className="min-h-screen bg-red-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-2xl border-2 border-red-500 shadow-2xl max-w-2xl w-full p-8">
            {/* Header */}
            <div className="text-center mb-6">
              <AlertTriangle className="w-16 h-16 text-red-500 mx-auto mb-4" />
              <h1 className="text-3xl font-black text-red-600 mb-2">
                Oops! Algo deu errado
              </h1>
              <p className="text-gray-600">
                A aplicação encontrou um erro inesperado
              </p>
            </div>

            {/* Error Type Specific Content */}
            {isCORS && (
              <div className="bg-red-100 border-2 border-red-400 rounded-lg p-6 mb-6">
                <h2 className="text-xl font-bold text-red-800 mb-3">
                  🔒 Erro de CORS Detectado
                </h2>
                <div className="text-sm text-red-700 space-y-2">
                  <p><strong>Causa:</strong> Configurações de segurança do navegador bloquearam a requisição.</p>
                  
                  <div className="mt-4">
                    <p className="font-semibold mb-2">✅ Soluções:</p>
                    <ol className="list-decimal list-inside space-y-1">
                      <li>Acesse o <a href="https://supabase.com/dashboard" target="_blank" rel="noopener noreferrer" className="text-blue-600 underline">Dashboard do Supabase</a></li>
                      <li>Vá em: <strong>Settings → Authentication → URL Configuration</strong></li>
                      <li>Adicione: <code className="bg-gray-200 px-1 rounded">{window.location.origin}</code></li>
                      <li>Salve e aguarde alguns minutos</li>
                    </ol>
                  </div>
                </div>
              </div>
            )}

            {isEnv && (
              <div className="bg-yellow-100 border-2 border-yellow-400 rounded-lg p-6 mb-6">
                <h2 className="text-xl font-bold text-yellow-800 mb-3">
                  ⚙️ Erro de Configuração
                </h2>
                <div className="text-sm text-yellow-700 space-y-2">
                  <p><strong>Causa:</strong> Variáveis de ambiente não estão configuradas.</p>
                  
                  <div className="mt-4">
                    <p className="font-semibold mb-2">✅ Soluções:</p>
                    <ol className="list-decimal list-inside space-y-1">
                      <li>Verifique se existe o arquivo <code className="bg-gray-200 px-1 rounded">.env</code> na raiz do projeto</li>
                      <li>O arquivo deve conter as variáveis do Supabase</li>
                      <li>Reinicie o servidor de desenvolvimento</li>
                    </ol>
                  </div>
                </div>
              </div>
            )}

            {/* Error Details */}
            <div className="bg-gray-100 border border-gray-300 rounded-lg p-4 mb-6">
              <details className="cursor-pointer">
                <summary className="font-semibold text-gray-700 mb-2">
                  📋 Detalhes do Erro (clique para expandir)
                </summary>
                <div className="text-xs font-mono bg-white border rounded p-2 overflow-auto max-h-40">
                  <div className="text-red-600 mb-2">
                    <strong>Erro:</strong> {error?.message}
                  </div>
                  {error?.stack && (
                    <div className="text-gray-600">
                      <strong>Stack:</strong>
                      <pre className="whitespace-pre-wrap">{error.stack}</pre>
                    </div>
                  )}
                </div>
              </details>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-3">
              <button
                onClick={this.handleRefresh}
                className="flex-1 bg-blue-500 text-white font-bold py-3 px-6 rounded-lg border-2 border-blue-600 shadow-lg hover:bg-blue-600 transition-all flex items-center justify-center gap-2"
              >
                <RefreshCw className="w-5 h-5" />
                Recarregar Página
              </button>
              
              <button
                onClick={this.handleReset}
                className="flex-1 bg-orange-500 text-white font-bold py-3 px-6 rounded-lg border-2 border-orange-600 shadow-lg hover:bg-orange-600 transition-all flex items-center justify-center gap-2"
              >
                <Settings className="w-5 h-5" />
                Limpar Cache
              </button>
              
              {isCORS && (
                <a
                  href="https://supabase.com/dashboard"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex-1 bg-green-500 text-white font-bold py-3 px-6 rounded-lg border-2 border-green-600 shadow-lg hover:bg-green-600 transition-all flex items-center justify-center gap-2"
                >
                  <ExternalLink className="w-5 h-5" />
                  Supabase Dashboard
                </a>
              )}
            </div>

            {/* Help Text */}
            <div className="mt-6 text-center text-sm text-gray-600">
              <p>Se o problema persistir, verifique o console do navegador (F12) para mais detalhes.</p>
            </div>
          </div>
        </div>
      )
    }

    return this.props.children
  }
}

export default ErrorBoundary 