export type StreamingPlatform = 'netflix' | 'disney' | 'amazon'
export type PhotoType = 'individual' | 'casal'
export type Language = 'english' | 'portuguese'

export interface CoverGeneration {
  id: string
  originalImageUrl: string
  photoType: PhotoType
  streamingPlatform: StreamingPlatform
  generatedCovers: string[]
  status: 'processing' | 'completed' | 'failed'
  createdAt: string
  completedAt?: string
}

export interface MovieSeries {
  id: string
  title: string
  streamingPlatform: StreamingPlatform
  type: 'movie' | 'series'
  year: number
  genre?: string
  posterStylePrompt: string
}

export interface PaymentPlan {
  id: string
  name: string
  price: number
  priceDisplay: string
  features: string[]
  maxCovers: number
}

export interface UserSubscription {
  id: string
  userId: string
  planId: string
  status: 'active' | 'inactive' | 'expired'
  stripePaymentIntentId?: string
  activatedAt: string
  expiresAt?: string
  createdAt: string
  updatedAt: string
}

export interface PaymentIntent {
  id: string
  stripePaymentIntentId: string
  userId: string
  planId: string
  amount: number
  currency: string
  status: 'created' | 'succeeded' | 'failed'
  createdAt: string
  updatedAt: string
}

export interface PaymentState {
  hasSubscription: boolean
  currentPlan: string | null
  loading: boolean
  error: string | null
}