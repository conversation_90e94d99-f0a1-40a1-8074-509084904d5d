import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { 
  buildPrompt, 
  createSafePrompt, 
  pollForResult 
} from '../_shared/promptBuilder.ts'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  // 1. AUTHENTICATION
  const authHeader = req.headers.get('Authorization')
  if (!authHeader) {
    return new Response(JSON.stringify({ error: 'Falta o cabeçalho de autenticação' }), { status: 401, headers: corsHeaders })
  }
  
  const supabase = createClient(
    Deno.env.get('SUPABASE_URL')!,
    Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!,
  )
  
  const { data: { user } } = await supabase.auth.getUser(authHeader.replace('Bearer ', ''));
  if (!user) {
    return new Response(JSON.stringify({ error: 'Token inválido ou expirado' }), { status: 401, headers: corsHeaders })
  }

  try {
    const { 
      imageUrl, 
      photoType, 
      streamingPlatform, 
      aspectRatio, 
      gender, 
      language = 'portuguese', 
      generateTitles = false, 
      creativityLevel = 'rostoFiel',
      useBackupPrompt = false,
      attemptNumber = 1
    } = await req.json()

    // 🔥 CHECAR CRÉDITOS ANTES DE GERAR
    const creditsNeeded = 1; // Capa principal custa 1 crédito
    
    // Verificar saldo de créditos
    const { data: purchases } = await supabase
      .from('credit_purchases')
      .select('credits')
      .eq('user_id', user.id)
      .eq('status', 'completed')

    const { data: usage } = await supabase
      .from('credit_usage')
      .select('credits_used')
      .eq('user_id', user.id)

    const totalCredits = purchases?.reduce((sum, p) => sum + p.credits, 0) || 0
    const usedCredits = usage?.reduce((sum, u) => sum + u.credits_used, 0) || 0
    const availableCredits = totalCredits - usedCredits

    if (availableCredits < creditsNeeded) {
      return new Response(
        JSON.stringify({ 
          error: 'Créditos insuficientes para gerar capa principal',
          availableCredits,
          requiredCredits: creditsNeeded,
          message: 'Compre mais créditos para continuar'
        }),
        { status: 402, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }
    
    console.log(`💳 Usuário tem ${availableCredits} créditos. Necessário: ${creditsNeeded}`);
    
    console.log('Generating cover image with:', { 
      imageUrl, 
      photoType, 
      gender, 
      streamingPlatform, 
      aspectRatio, 
      creativityLevel,
      useBackupPrompt,
      attemptNumber
    })

    // Initialize Supabase client
    const REPLICATE_API_TOKEN = Deno.env.get('REPLICATE_API_TOKEN')
    
    if (!REPLICATE_API_TOKEN) {
      throw new Error('Missing REPLICATE_API_TOKEN environment variable')
    }

    // 🔥 BUSCAR FILMES JÁ USADOS NA GERAÇÃO ATUAL
    let usedMovieTitles: string[] = []
    
    // Tentar buscar a geração atual do usuário se tiver autenticação
    if (authHeader) {
      try {
        const token = authHeader.replace('Bearer ', '')
        const { data: { user } } = await supabase.auth.getUser(token)
        
        if (user) {
          // Buscar a última geração do usuário
          const { data: lastGeneration } = await supabase
            .from('cover_generations')
            .select('generated_covers')
            .eq('user_id', user.id)
            .order('created_at', { ascending: false })
            .limit(1)
            .single()
          
          if (lastGeneration?.generated_covers) {
            usedMovieTitles = lastGeneration.generated_covers.map((cover: any) => cover.movie_title)
            console.log(`🔍 Found ${usedMovieTitles.length} used movies:`, usedMovieTitles)
          }
        }
      } catch (error) {
        console.log('Could not fetch user generation history:', error)
      }
    }

    // 🔥 BUSCAR FILMES DA PLATAFORMA
    const { data: allMovies, error: movieError } = await supabase
      .from('movies_series')
      .select('*')
      .eq('streaming_platform', streamingPlatform)
      .order('id', { ascending: true }) // Ordem consistente

    if (movieError || !allMovies || allMovies.length === 0) {
      throw new Error(`No movies found for platform: ${streamingPlatform}`)
    }

    // 🔥 USAR APENAS FILMES SOBRA (posições 13+) PARA CAPA PRINCIPAL
    // Netflix: 12 filmes principais (0-11) + sobra (12+)
    // Disney: 12 filmes principais (0-11) + sobra (12+) 
    // Amazon: 12 filmes principais (0-11) + sobra (12+)
    const backupMovies = allMovies.slice(12) // Pega filmes a partir da posição 13
    
    console.log(`🎯 Using backup movies (positions 13+) for main cover: ${backupMovies.length} available`)
    console.log(`📋 Main movies (1-12): ${allMovies.slice(0, 12).map(m => m.title).join(', ')}`)
    console.log(`🎲 Backup movies (13+): ${backupMovies.map(m => m.title).join(', ')}`)

    if (backupMovies.length === 0) {
      throw new Error(`No backup movies (positions 13+) found for platform: ${streamingPlatform}. Main cover needs backup movies.`)
    }

    // 🔥 FILTRAR FILMES SOBRA NÃO USADOS
    const unusedBackupMovies = backupMovies.filter(movie => !usedMovieTitles.includes(movie.title))
    console.log(`🎯 Found ${unusedBackupMovies.length} unused backup movies out of ${backupMovies.length} total backup`)

    // Se não tiver filmes sobra não usados, usar todos os filmes sobra
    const moviesToChooseFrom = unusedBackupMovies.length > 0 ? unusedBackupMovies : backupMovies

    // 🔥 ESCOLHER FILME BASEADO NO ÍNDICE SEQUENCIAL
    let randomMovie
    if (attemptNumber === 1) {
      // Primeira tentativa: filme aleatório
      randomMovie = moviesToChooseFrom[Math.floor(Math.random() * moviesToChooseFrom.length)]
    } else {
      // Tentativas subsequentes: escolher filme sequencial baseado no movieIndex
      const movieIndex = (attemptNumber - 1) % moviesToChooseFrom.length
      randomMovie = moviesToChooseFrom[movieIndex]
      console.log(`🎬 Using sequential movie selection: index ${movieIndex} of ${moviesToChooseFrom.length} available`)
    }
    
    console.log(`🎬 Selected movie for main cover (attempt ${attemptNumber}): ${randomMovie.title}`)

    // 🔥 DECIDIR QUAL PROMPT USAR BASEADO NO useBackupPrompt
    let promptToUse = randomMovie.poster_style_prompt
    let targetCreativityLevel = creativityLevel
    
    if (useBackupPrompt) {
      console.log('🛡️ Using backup prompt strategy due to E005 fallback')
      
      // Criar prompt safe
      const safePromptObj = createSafePrompt({ [language]: randomMovie.poster_style_prompt }, photoType)
      promptToUse = safePromptObj[language]
      targetCreativityLevel = 'estrito' // Forçar modo estrito para prompt safe
    }

    // 🔥 USAR buildPrompt PARA INCLUIR CONFIGURAÇÕES
    const optimizedPrompt = buildPrompt(promptToUse, {
      language: language as 'english' | 'portuguese',
      photoType: photoType as 'individual' | 'casal',
      gender: gender as 'male' | 'female' | undefined,
      addTitle: generateTitles,
      movieTitle: randomMovie.title,
      coverIndex: Math.floor(Math.random() * 12),
      creativityLevel: targetCreativityLevel as 'criativo' | 'rostoFiel' | 'estrito'
    })

    console.log(`🎨 Cover prompt (${optimizedPrompt.length} chars):`, optimizedPrompt.substring(0, 200) + '...')

    // Função auxiliar para tentar gerar com um prompt
    async function attemptGeneration(prompt: string, attemptName: string): Promise<{ success: boolean, imageUrl?: string, error?: string }> {
      try {
        // Make API call using Flux Kontext
        const response = await fetch('https://api.replicate.com/v1/models/black-forest-labs/flux-kontext-max/predictions', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${REPLICATE_API_TOKEN}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            input: {
              prompt: prompt,
              input_image: imageUrl,
              aspect_ratio: aspectRatio || '21:9',
              output_format: 'jpg',
              safety_tolerance: 5,
              num_inference_steps: 28,
              guidance_scale: 3.5
            },
          }),
        })

        if (!response.ok) {
          const errorText = await response.text()
          console.error(`${attemptName} - HTTP error! status: ${response.status}, response: ${errorText}`)
          return { success: false, error: errorText }
        }

        const prediction = await response.json()
        console.log(`${attemptName} - Prediction created:`, prediction.id)

        if (prediction.id) {
          // Poll for completion
          const pollResult = await pollForResult(prediction.id, randomMovie.title, REPLICATE_API_TOKEN)
          
          if (pollResult.imageUrl) {
            // Download image from Replicate and upload to Supabase
            const supabaseImageUrl = await downloadAndUploadToSupabase(pollResult.imageUrl, supabase, 'cover')
            
            if (supabaseImageUrl) {
              return { success: true, imageUrl: supabaseImageUrl }
            } else {
              return { success: false, error: 'Failed to upload to Supabase Storage' }
            }
          } else {
            return { success: false, error: pollResult.error || 'Unknown polling error' }
          }
        } else {
          return { success: false, error: 'Failed to create prediction - no prediction ID returned' }
        }
      } catch (error) {
        console.error(`${attemptName} - Error:`, error)
        return { success: false, error: error.message }
      }
    }

    // 🎬 TENTATIVA 1: PROMPT NORMAL
    console.log('🎬 Attempt 1: Normal prompt')
    let result = await attemptGeneration(optimizedPrompt, 'Attempt 1')

    // 🎬 TENTATIVA 2: PROMPT SAFE (se E005)
    if (!result.success && result.error?.includes('E005')) {
      console.log('🛡️ Attempt 2: Safe prompt due to E005 error')
      
      const safePromptObj = createSafePrompt({ [language]: randomMovie.poster_style_prompt }, photoType)
      const safeOptimizedPrompt = buildPrompt(safePromptObj[language], {
        language: language as 'english' | 'portuguese',
        photoType: photoType as 'individual' | 'casal',
        gender: gender as 'male' | 'female' | undefined,
        addTitle: generateTitles,
        movieTitle: randomMovie.title,
        coverIndex: Math.floor(Math.random() * 12),
        creativityLevel: 'estrito' // Usar estrito para safe prompt
      })
      
      result = await attemptGeneration(safeOptimizedPrompt, 'Attempt 2 (Safe)')
    }

    if (result.success && result.imageUrl) {
      // 🔥 CONSUMIR CRÉDITO APENAS APÓS SUCESSO
      console.log(`💳 Consumindo ${creditsNeeded} crédito pela capa principal...`)
      
      const consumeResponse = await supabase.functions.invoke('consume-credits', {
        body: { 
          amount: creditsNeeded, 
          description: `Capa principal ${streamingPlatform} - ${randomMovie.title}` 
        },
        headers: { Authorization: authHeader }
      })

      if (consumeResponse.error || !consumeResponse.data?.success) {
        console.error('❌ Erro ao consumir crédito após sucesso:', consumeResponse.error)
        // Não falhar a geração por causa disso, apenas logar
      } else {
        console.log(`✅ Crédito consumido com sucesso para capa principal`)
      }
      
      // Update user stats
      try {
        const { data: { user } } = await supabase.auth.getUser()
        if (user) {
          const { error: statsError } = await supabase.rpc('update_user_stats', {
            p_user_id: user.id,
            p_covers_generated: 1,
            p_poster_generated: false
          })
          
          if (statsError) {
            console.error('Error updating user stats:', statsError)
          }

          console.log('Main cover generated successfully')
        }
      } catch (error) {
        console.error('Error in post-generation processing:', error)
      }

      return new Response(
        JSON.stringify({ coverUrl: result.imageUrl }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        },
      )
    } else {
      const errorMessage = result.error || 'Failed to generate cover image'
      console.error('Cover generation failed:', errorMessage)
      
      return new Response(
        JSON.stringify({ 
          error: errorMessage,
          details: result.error || 'All attempts failed'
        }),
        {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        },
      )
    }

  } catch (error) {
    console.error('Cover generation error:', error)
    return new Response(
      JSON.stringify({ 
        error: error.message || 'Failed to generate cover image',
        details: error.stack || 'Unknown error occurred'
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      },
    )
  }
})

async function downloadAndUploadToSupabase(imageUrl: string, supabase: any, type: string): Promise<string | null> {
  try {
    console.log(`Downloading ${type} image from Replicate...`)
    
    // Download image from Replicate
    const imageResponse = await fetch(imageUrl)
    if (!imageResponse.ok) {
      throw new Error(`Failed to download image: ${imageResponse.status}`)
    }
    
    const imageBlob = await imageResponse.blob()
    const imageBuffer = await imageBlob.arrayBuffer()
    
    // Generate unique filename
    const timestamp = Date.now()
    const filename = `${type}s/${timestamp}-${type}.jpg`
    
    console.log(`Uploading ${type} to Supabase Storage: ${filename}`)
    
    // Upload to Supabase Storage
    const { data, error } = await supabase.storage
      .from('generated-images')
      .upload(filename, imageBuffer, {
        contentType: 'image/jpeg',
        upsert: false
      })
    
    if (error) {
      console.error(`Supabase upload error for ${type}:`, error)
      return null
    }
    
    // Get public URL
    const { data: { publicUrl } } = supabase.storage
      .from('generated-images')
      .getPublicUrl(filename)
    
    console.log(`Successfully uploaded ${type} to Supabase: ${publicUrl}`)
    return publicUrl
    
  } catch (error) {
    console.error(`Error downloading/uploading ${type}:`, error)
    return null
  }
}