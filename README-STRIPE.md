# Stripe Integration Setup

## Environment Variables

Add these environment variables to your `.env.local` file:

```bash
# Supabase Configuration
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key

# Stripe Configuration (Frontend)
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_51PlGJpDOvhLb9hmWCxdc7fFHVqc3Cifn2KCfgLjhFYwiQKy8sxgYdqCdcdUFENebYdTueAbOrfVP2vrNzVUf5auY00mIuAepTw

# For production, use:
# VITE_STRIPE_PUBLISHABLE_KEY=pk_live_51PlGJpDOvhLb9hmW1NEqUXb3QOeJXtTKC3vsqGIbBaFuWcrGDR6qgNYIOyY6SZ9cqyXsmhsNG0T0IFayaArpbrRF00VETq2vwc
```

## Supabase Edge Functions Environment Variables

Configure these in your Supabase project settings:

```bash
# Stripe Secret Keys (for Edge Functions)
STRIPE_SECRET_KEY=sk_test_51PlGJpDOvhLb9hmW88SlnQFl9qCOVwA3aLH44rCxCVubVjYZcoeqQ6oEC5tLssstgAIhEccnjJhdwuyXvYN9ANFZ00M7t1NppZ

# For production:
# STRIPE_SECRET_KEY=***********************************************************************************************************

# Webhook Secret (get from Stripe Dashboard)
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret
```

## Deployment Steps

### 1. Deploy Supabase Edge Functions

```bash
# Deploy payment intent creation function
supabase functions deploy create-payment-intent

# Deploy subscription check function
supabase functions deploy check-subscription

# Deploy webhook handler
supabase functions deploy stripe-webhook
```

### 2. Configure Stripe Webhooks

1. Go to your Stripe Dashboard
2. Navigate to Developers > Webhooks
3. Click "Add endpoint"
4. Set the endpoint URL to: `https://your-project.supabase.co/functions/v1/stripe-webhook`
5. Select these events:
   - `payment_intent.succeeded`
   - `payment_intent.payment_failed`
6. Copy the webhook signing secret and add it to your Supabase environment variables

### 3. Test the Integration

1. Use Stripe test cards:
   - Success: `****************`
   - Decline: `****************`
   - 3D Secure: `****************`

2. Monitor payments in:
   - Stripe Dashboard
   - Supabase Database (payment_intents and user_subscriptions tables)

## Pricing Plans

The system includes two pricing plans:

- **Basic Plan**: R$ 9,97
  - Up to 12 movie covers
  - Main cover image
  - Print poster
  - High-quality downloads
  - Email support

- **Premium Plan**: R$ 19,97
  - Unlimited covers
  - Multiple main covers
  - Unlimited print posters
  - High-quality downloads
  - Priority support
  - Access to new templates

## Database Schema

The integration creates these tables:

- `payment_intents`: Tracks Stripe payment intents
- `user_subscriptions`: Manages user subscription status

## Security Notes

- Never expose secret keys in frontend code
- Use environment variables for all sensitive data
- Verify webhook signatures in production
- Enable RLS (Row Level Security) on all tables
- Use HTTPS for all webhook endpoints

## Troubleshooting

### Common Issues

1. **Payment fails silently**
   - Check Stripe Dashboard for error details
   - Verify webhook endpoint is accessible
   - Check Supabase function logs

2. **Subscription not updating**
   - Verify webhook is configured correctly
   - Check database permissions
   - Review edge function logs

3. **Frontend errors**
   - Ensure all environment variables are set
   - Check browser console for Stripe errors
   - Verify Stripe publishable key is correct

### Logs

- Stripe Dashboard: View payment and webhook logs
- Supabase: Check edge function logs in the dashboard
- Browser: Check console for frontend errors 